import io
from dataclasses import dataclass
from pathlib import Path, PurePosixPath
from typing import BinaryIO, Callable, Dict, Generator, List, Literal, MutableMapping, Protocol, Sequence, Tuple

from pydantic import BaseModel

from qw_basic_s3.impl.common import check_bucket_name, normalize_path, path_has_trailing_slash, path_to_str
from qw_basic_s3.interface import S3Object, S3ObjectBytes, S3StorageError
from qw_basic_s3.schema import S3StorageSchema
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class S3MemoryObjectBytes(S3ObjectBytes):
    def __init__(self, payload: bytes, content_type: str, object_id: int):
        super().__init__(payload, content_type)
        self.object_id = object_id


class S3MemoryDirectoryDoesNotExist(S3StorageError):
    pass


@dataclass
class MemoryObject:
    id: int
    data: bytes
    content_type: str

    def write_data_to(self, p: Path) -> None:
        with p.open("wb") as f:
            f.write(self.data)


@dataclass
class MemoryDirectory:
    objs: Dict[str, MemoryObject]
    dirs: Dict[str, "MemoryDirectory"]

    @classmethod
    def new_empty_dir(cls) -> "MemoryDirectory":
        return cls(objs={}, dirs={})

    def name_should_be_object(self, name: str, path: Sequence[str] = ()) -> None:
        if name not in self.objs:
            obj_path = "/".join(list(path) + [name])
            raise S3StorageError(f"{obj_path} is not an object")

    def name_should_not_be_object(self, name: str, path: Sequence[str] = ()) -> None:
        if name in self.objs:
            obj_path = "/".join(list(path) + [name])
            raise S3StorageError(f"{obj_path} is an object")

    def name_should_not_be_directory(self, name: str, path: Sequence[str] = ()) -> None:
        if name in self.dirs:
            obj_path = "/".join(list(path) + [name])
            raise S3StorageError(f"{obj_path} is an directory")

    def traverse(self, path: PurePosixPath, index: int, create_if_not_exists: bool = False) -> "MemoryDirectory":
        if len(path.parts) == 0 or len(path.parts) == index:
            return self
        name = path.parts[index]
        parts = path.parts[:index]
        self.name_should_not_be_object(name, parts)
        if name not in self.dirs and create_if_not_exists:
            self.dirs[name] = MemoryDirectory.new_empty_dir()
        if name not in self.dirs:
            obj_path = "/".join(parts)
            raise S3MemoryDirectoryDoesNotExist(f"'{obj_path}' cannot be entered")
        return self.dirs[name].traverse(path, index + 1, create_if_not_exists)

    def traverse_objects(
        self, path: PurePosixPath, name_prefix: str, recursive: bool
    ) -> Generator[Tuple[S3Object, MemoryObject | None], None, None]:
        for name, obj in self.objs.items():
            if name.startswith(name_prefix):
                yield S3Object(path=path / name, is_dir=False), obj
        for name, directory in self.dirs.items():
            if name.startswith(name_prefix):
                dir_path = path / name
                # one of the S3 quirks: in case the lookup is recursive, only objects are returned, no directories
                if recursive:
                    yield from directory.traverse_objects(dir_path, "", recursive)
                else:
                    yield S3Object(path=dir_path, is_dir=True), None

    def traverse_all_objects(self) -> Generator[Tuple[S3Object, MemoryObject | None], None, None]:
        yield from self.traverse_objects(PurePosixPath(), "", recursive=True)

    def put_object(
        self,
        new_id: Callable[[], int],
        name: str,
        bio: BinaryIO,
        size: int = -1,
        content_type: str = "application/octet-stream",
        part_size: int = 0,
        path: Sequence[str] = (),
    ) -> MemoryObject:
        self.name_should_not_be_directory(name, path)
        if part_size > 0:
            bio2 = io.BytesIO()
            keep_reading = True
            while keep_reading:
                chunk = bio.read(part_size)
                bio2.write(chunk)
                keep_reading = len(chunk) == part_size
            data = bio2.read()
        elif size > 0:
            data = bio.read(size)
        else:
            raise RuntimeError("Cannot read object data")
        if name not in self.objs:
            self.objs[name] = MemoryObject(new_id(), data, content_type)
        else:
            self.objs[name].data = data
            self.objs[name].content_type = content_type
        return self.objs[name]

    def remove_object(self, name: str, path: Sequence[str] = ()) -> MemoryObject:
        self.name_should_not_be_directory(name, path)
        self.name_should_be_object(name, path)
        return self.objs.pop(name)

    def get_object(self, name: str, offset: int = 0, length: int = 0, path: Sequence[str] = ()) -> S3MemoryObjectBytes:
        self.name_should_not_be_directory(name, path)
        self.name_should_be_object(name, path)
        obj = self.objs[name]
        obj_size = len(obj.data)
        i1 = offset
        i2 = max(obj_size, i1 + (length if length > 0 else obj_size))
        return S3MemoryObjectBytes(obj.data[i1:i2], obj.content_type, obj.id)


class SerializeHook(Protocol):
    def __call__(self, only_put_id: int | None = None, only_remove_id: int | None = None) -> None:
        ...


class MemoryS3Storage(object):
    def __init__(
        self, schema: S3StorageSchema, lf: LogFactory = NO_LOG_FACTORY, serialize_hook: SerializeHook | None = None
    ):
        self.schema = schema
        self.buckets: MutableMapping[str, MemoryDirectory] = {}
        self.next_obj_id = 1
        self.serialize_hook = serialize_hook
        self.logger = lf.get_logger(__name__)

    def get_next_obj_id(self) -> int:
        obj_id = self.next_obj_id
        self.next_obj_id += 1
        return obj_id

    def serialize(self, only_put_id: int | None = None, only_remove_id: int | None = None) -> None:
        if self.serialize_hook is not None:
            self.serialize_hook(only_put_id, only_remove_id)

    def bucket_should_exist(self, name: str) -> None:
        if not self.bucket_exists(name):
            raise S3StorageError(f"Bucket {name} does not exist")

    def bucket_should_not_exist(self, name: str) -> None:
        if self.bucket_exists(name):
            raise S3StorageError(f"Bucket {name} already exists")

    # --- interface implementation below ---

    def get_name(self) -> str:
        return ":memory:"

    def get_schema(self) -> S3StorageSchema:
        return self.schema

    def create_bucket(self, name: str) -> bool:
        check_bucket_name(name)
        self.bucket_should_not_exist(name)
        self.buckets[name] = MemoryDirectory.new_empty_dir()
        self.serialize()
        self.logger.info(f"Created bucket '{name}'")
        return True

    def remove_bucket(self, name: str) -> bool:
        self.bucket_should_exist(name)
        self.buckets.pop(name)
        self.serialize()
        self.logger.info(f"Removed bucket '{name}'")
        return True

    def bucket_exists(self, name: str) -> bool:
        return name in self.buckets

    def list_buckets(self) -> List[str]:
        return list(self.buckets.keys())

    def put_object(
        self,
        bucket: str,
        path: PurePosixPath,
        bio: BinaryIO,
        size: int = -1,
        content_type: str = "application/octet-stream",
        part_size: int = 0,
    ) -> bool:
        self.bucket_should_exist(bucket)
        if (size <= 0 and part_size <= 0) or (size > 0 and part_size > 0):
            raise ValueError("Either entire size or part_size (chunked read) needs to be provided")
        path = normalize_path(path)
        path_as_str = path_to_str(path, trailing_slash="")
        directory = self.buckets[bucket].traverse(path.parent, index=0, create_if_not_exists=True)
        obj = directory.put_object(self.get_next_obj_id, path.name, bio, size, content_type, part_size=part_size)
        self.serialize(only_put_id=obj.id)
        self.logger.info(f"Put object '{path_as_str}'")
        return True

    def remove_object(self, bucket: str, path: PurePosixPath) -> bool:
        self.bucket_should_exist(bucket)
        path = normalize_path(path)
        path_as_str = path_to_str(path, trailing_slash="")
        directory = self.buckets[bucket].traverse(path.parent, index=0, create_if_not_exists=False)
        obj = directory.remove_object(path.name, path.parts[:-1])
        self.serialize(only_remove_id=obj.id)
        self.logger.info(f"Removed object '{path_as_str}'")
        return True

    def get_object(self, bucket: str, path: PurePosixPath, offset: int = 0, length: int = 0) -> S3MemoryObjectBytes:
        self.bucket_should_exist(bucket)
        path = normalize_path(path)
        directory = self.buckets[bucket].traverse(path.parent, index=0, create_if_not_exists=False)
        return directory.get_object(path.name, offset, length, path.parts[:-1])

    def list_objects(
        self,
        bucket: str,
        prefix: PurePosixPath | None,
        trailing_slash: Literal["", "/", "auto"] = "auto",
        recursive: bool = False,
    ) -> Generator[S3Object, None, None]:
        self.bucket_should_exist(bucket)
        bucket_directory = self.buckets[bucket]

        no_path = PurePosixPath()
        prefix_path = no_path if prefix is None else prefix
        prefix_path = normalize_path(prefix_path)
        prefix_name = ""
        has_trailing_slash = path_has_trailing_slash(prefix_path, trailing_slash, can_be_empty=True)
        if not has_trailing_slash:
            # the name of the path is not necessarily a directory name, it might match various directories and objects
            prefix_name = prefix_path.name
            prefix_path = prefix_path.parent
        if prefix_path == no_path and has_trailing_slash:
            return

        try:
            directory = bucket_directory.traverse(prefix_path, index=0, create_if_not_exists=False)
        except S3MemoryDirectoryDoesNotExist:
            return
        for ref, _ in directory.traverse_objects(prefix_path, prefix_name, recursive):
            yield ref


class MemoryS3StorageConfig(BaseModel):
    type: Literal["memory"] = "memory"

    def build(self, storage_schema: S3StorageSchema, lf: LogFactory = NO_LOG_FACTORY) -> MemoryS3Storage:
        return MemoryS3Storage(storage_schema, lf=lf)
