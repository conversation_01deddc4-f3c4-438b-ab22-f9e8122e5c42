{"name": "DIN_EN_ISO_13920:1996-11", "tables": [{"name": "Tabelle 1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 1.999, "inclusive": true}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 8000, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 8000, "inclusive": false}, "upperValueLimit": {"value": 12000, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 12000, "inclusive": false}, "upperValueLimit": {"value": 16000, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 16000, "inclusive": false}, "upperValueLimit": {"value": 20000, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 20000, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["A"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 1.999, "inclusive": true}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 8000, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 8000, "inclusive": false}, "upperValueLimit": {"value": 12000, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 12000, "inclusive": false}, "upperValueLimit": {"value": 16000, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 12, "lower": -12}}, {"lowerValueLimit": {"value": 16000, "inclusive": false}, "upperValueLimit": {"value": 20000, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 14, "lower": -14}}, {"lowerValueLimit": {"value": 20000, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["B"]], "tolerance": {"upper": 16, "lower": -16}}, {"lowerValueLimit": {"value": 1.999, "inclusive": true}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 11, "lower": -11}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 8000, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 14, "lower": -14}}, {"lowerValueLimit": {"value": 8000, "inclusive": false}, "upperValueLimit": {"value": 12000, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 18, "lower": -18}}, {"lowerValueLimit": {"value": 12000, "inclusive": false}, "upperValueLimit": {"value": 16000, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 21, "lower": -21}}, {"lowerValueLimit": {"value": 16000, "inclusive": false}, "upperValueLimit": {"value": 20000, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 24, "lower": -24}}, {"lowerValueLimit": {"value": 20000, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["C"]], "tolerance": {"upper": 27, "lower": -27}}, {"lowerValueLimit": {"value": 1.999, "inclusive": true}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 12, "lower": -12}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 16, "lower": -16}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 8000, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 21, "lower": -21}}, {"lowerValueLimit": {"value": 8000, "inclusive": false}, "upperValueLimit": {"value": 12000, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 27, "lower": -27}}, {"lowerValueLimit": {"value": 12000, "inclusive": false}, "upperValueLimit": {"value": 16000, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 32, "lower": -32}}, {"lowerValueLimit": {"value": 16000, "inclusive": false}, "upperValueLimit": {"value": 20000, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 36, "lower": -36}}, {"lowerValueLimit": {"value": 20000, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["D"]], "tolerance": {"upper": 40, "lower": -40}}]}, {"name": "Tabelle 2", "valueType": "angular", "valueUnit": "deg", "toleranceUnit": "deg", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["A <=400"]], "tolerance": {"upper": 0.333333333, "lower": -0.333333333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["B <=400"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["C <=400"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["D <=400"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["A <=1000"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["B <=1000"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["C <=1000"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["D <=1000"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["A >1000"]], "tolerance": {"upper": 0.166666667, "lower": -0.166666667}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["B >1000"]], "tolerance": {"upper": 0.333333333, "lower": -0.333333333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["C >1000"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["D >1000"]], "tolerance": {"upper": 1, "lower": -1}}]}]}