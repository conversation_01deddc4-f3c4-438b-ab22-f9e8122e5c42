/* tslint:disable */
/* eslint-disable */
import { QwImpactTest } from '../models/qw-impact-test';
import { QwTensileTest } from '../models/qw-tensile-test';

/**
 * [schema: MechanicalProperties] Mechanical properties with foreign key references.
 * Treated like a database table - always includes batch_number and heat_number.
 * Contains tensile test results and a list of impact tests.
 */
export interface QwMechanicalProperties {

  /**
   * Batch/item identifier - must match HeaderDetails
   */
  batch_number: string;

  /**
   * Heat/Smelt number - must match HeaderDetails
   */
  heat_number?: (string | null);

  /**
   * List of impact test results (can be multiple tests at different temperatures)
   */
  impact_tests?: Array<QwImpactTest>;

  /**
   * Tensile test results
   */
  tensile_test?: (QwTensileTest | null);
}
