import json
from pathlib import Path
from typing import Any, Dict, List, Sequence

from pydantic import ValidationError
from typing_extensions import Self

from qw_drawing_toolkit.norm.model import ToleranceStandard


class ToleranceCollection:
    def __init__(self, standards: Sequence[ToleranceStandard]):
        self.standards = standards

    @classmethod
    def from_directory(cls, json_folder_path: Path) -> Self:
        standards = cls._import_all_tolerance_standards(json_folder_path)
        return cls(standards)

    @classmethod
    def from_default_directory(cls) -> Self:
        default_path = Path(__file__).parent / "standards"
        return cls.from_directory(default_path)

    @staticmethod
    def _import_all_tolerance_standards(json_folder_path: Path) -> List[ToleranceStandard]:
        tolerance_standards = []
        path = Path(json_folder_path).resolve()
        for filename in path.glob("*.json"):
            try:
                json_data = ToleranceCollection._load_json_file(filename)
                tolerance_standard = ToleranceCollection._parse_tolerance_standard(json_data)
                tolerance_standards.append(tolerance_standard)
            except ValidationError as e:
                print(f"Error parsing {filename}: {e}")
        return tolerance_standards

    @staticmethod
    def _load_json_file(file_path: Path) -> Dict[str, Any]:
        with file_path.open("r", encoding="utf-8") as file:
            data: Dict[str, Any] = json.load(file)
            return data

    @staticmethod
    def _parse_tolerance_standard(data: Dict[str, Any]) -> ToleranceStandard:
        return ToleranceStandard.model_validate(data)

    def find_standard(self, standard_name: str) -> ToleranceStandard | None:
        for standard in self.standards:
            if standard.name == standard_name:
                return standard
        return None

    def get_standard(self, standard_name: str) -> ToleranceStandard:
        standard = self.find_standard(standard_name)
        if standard is None:
            raise LookupError(f"Tolerance standard with id '{standard_name}' not found")
        return standard

    def list_available_standards(self) -> Sequence[ToleranceStandard]:
        return self.standards
