{"name": "DIN_1688-4:1986-08", "tables": [{"name": "Tabelle 1", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1250, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1250, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.22, "lower": -0.22}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.26, "lower": -0.26}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.31, "lower": -0.31}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.37, "lower": -0.37}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.44, "lower": -0.44}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1250, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.42, "lower": -0.42}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.46, "lower": -0.46}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.51, "lower": -0.51}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.57, "lower": -0.57}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.64, "lower": -0.64}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1250, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.17, "lower": -0.17}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.32, "lower": -0.32}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg."]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 13 Formg."]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 13 Formg."]], "tolerance": {"upper": 0.17, "lower": -0.17}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 13 Formg."]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 13 Formg."]], "tolerance": {"upper": 0.23, "lower": -0.23}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 13 Formg."]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 13 Formg."]], "tolerance": {"upper": 0.32, "lower": -0.32}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 13 n. Formg."]], "tolerance": {"upper": 0.24, "lower": -0.24}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 13 n. Formg."]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 13 n. Formg."]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 13 n. Formg."]], "tolerance": {"upper": 0.33, "lower": -0.33}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 13 n. Formg."]], "tolerance": {"upper": 0.37, "lower": -0.37}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 13 n. Formg."]], "tolerance": {"upper": 0.42, "lower": -0.42}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 Formg."]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 Formg."]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 Formg."]], "tolerance": {"upper": 0.16, "lower": -0.16}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 n. Formg."]], "tolerance": {"upper": 0.21, "lower": -0.21}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 n. Formg."]], "tolerance": {"upper": 0.24, "lower": -0.24}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 n. Formg."]], "tolerance": {"upper": 0.2, "lower": -0.2}}]}, {"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 Formg."]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 14/5 n. Formg."]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 Formg."]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 13/5 n. Formg"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 13 Formg."]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 13 Formg."]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 13 Formg."]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 13 n. Formg."]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 13 n. Formg."]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 13 n. Formg."]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 Formg."]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 Formg."]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 Formg."]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 n. Formg"]], "tolerance": {"upper": 0.23, "lower": -0.23}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 n. Formg"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 12/5 n. Formg"]], "tolerance": {"upper": 0.28, "lower": -0.28}}]}]}