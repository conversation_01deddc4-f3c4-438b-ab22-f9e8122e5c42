"""Tests for traffic steering middleware."""
import os
import pytest
from unittest.mock import patch, MagicMock
import falcon
import httpx

from qw_pfoertner.middleware.traffic_steering import AgentTrafficSteeringMiddleware
from qw_log_interface import NO_LOG_FACTORY


@pytest.fixture
def middleware():
    """Create traffic steering middleware for testing."""
    return AgentTrafficSteeringMiddleware(NO_LOG_FACTORY)


@pytest.fixture
def mock_request():
    """Create a mock Falcon request."""
    req = MagicMock(spec=falcon.Request)
    req.path = "/api/v1/agent/process-prompt"
    req.method = "POST"
    req.headers = {"Content-Type": "application/json"}
    req.cookies = {"session": "test-session"}
    req.params = {}
    req.bounded_stream.read.return_value = b'{"prompt": "test"}'
    return req


@pytest.fixture
def mock_response():
    """Create a mock Falcon response."""
    resp = MagicMock(spec=falcon.Response)
    return resp


def test_middleware_initialization():
    """Test middleware initialization with default settings."""
    with patch.dict(os.environ, {"USE_FASTAPI_AGENT": "true"}):
        middleware = AgentTrafficSteeringMiddleware(NO_LOG_FACTORY)
        assert middleware.fastapi_enabled is True
        assert middleware.agent_service_url == "http://qw-mono-dev-agent-service:8000"


def test_middleware_disabled(middleware, mock_request, mock_response):
    """Test middleware when FastAPI routing is disabled."""
    middleware.fastapi_enabled = False

    # Should return without processing
    result = middleware.process_request(mock_request, mock_response)
    assert result is None
    # Mock objects always have attributes, so check if complete was set
    assert not getattr(mock_response, 'complete', False)


def test_non_agent_request(middleware, mock_request, mock_response):
    """Test middleware with non-agent request."""
    mock_request.path = "/api/v1/other/endpoint"

    # Should return without processing
    result = middleware.process_request(mock_request, mock_response)
    assert result is None
    # Mock objects always have attributes, so check if complete was set
    assert not getattr(mock_response, 'complete', False)


@patch('qw_pfoertner.middleware.traffic_steering.httpx.Client')
def test_successful_forwarding(mock_client_class, middleware, mock_request, mock_response):
    """Test successful request forwarding to FastAPI."""
    middleware.fastapi_enabled = True

    # Mock httpx response
    mock_response_obj = MagicMock()
    mock_response_obj.status_code = 200
    mock_response_obj.reason_phrase = "OK"
    mock_response_obj.headers = {"content-type": "application/json"}
    mock_response_obj.content = b'{"message": "success"}'
    mock_response_obj.text = '{"message": "success"}'

    mock_client = MagicMock()
    mock_client.request.return_value = mock_response_obj
    mock_client_class.return_value.__enter__.return_value = mock_client

    # Process request
    middleware.process_request(mock_request, mock_response)

    # Verify forwarding
    mock_client.request.assert_called_once()
    call_args = mock_client.request.call_args
    assert call_args[1]['method'] == 'POST'
    assert call_args[1]['url'] == 'http://qw-mono-dev-agent-service:8000/api/v1/agent/process-prompt'

    # Verify response copying
    assert mock_response.status == "200 OK"
    assert mock_response.text == '{"message": "success"}'
    assert mock_response.complete is True


@patch('qw_pfoertner.middleware.traffic_steering.httpx.Client')
def test_forwarding_error_fallback(mock_client_class, middleware, mock_request, mock_response):
    """Test fallback to Falcon when FastAPI forwarding fails."""
    middleware.fastapi_enabled = True

    # Mock httpx to raise an exception
    mock_client = MagicMock()
    mock_client.request.side_effect = httpx.RequestError("Connection failed")
    mock_client_class.return_value.__enter__.return_value = mock_client

    # Process request - should not raise exception
    result = middleware.process_request(mock_request, mock_response)

    # Should return None to let Falcon handle the request
    assert result is None
    # Mock objects always have attributes, so check if complete was set
    assert not getattr(mock_response, 'complete', False)


def test_from_config():
    """Test creating middleware from configuration."""
    middleware = AgentTrafficSteeringMiddleware.from_config(NO_LOG_FACTORY)
    assert middleware is not None
    assert hasattr(middleware, 'fastapi_enabled')
