from pathlib import Path
from typing import Literal, Sequence

from pydantic import AnyHttpUrl, BaseModel

from qw_basic_keycloak.impl.memory.state import MemoryIamState
from qw_basic_keycloak.interface import CodeFlowAuthInit
from qw_basic_keycloak.openid.dtos.authorization import SCOPE_OPEN_ID, AuthenticationRequest, AuthenticationResponse
from qw_basic_keycloak.openid.dtos.token import TokenResponse
from qw_basic_keycloak.openid.inspector import TokenInspector
from qw_basic_keycloak.realm import Realm
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class MemoryOpenIdClient(object):
    def __init__(self, iam_state: MemoryIamState, client_id: str, auth_url: str, redirect_uri: str):
        self.iam_state = iam_state
        self.client_id = client_id
        self.auth_url = auth_url
        self.redirect_uri = redirect_uri

    def get_redirect_uri(self) -> str:
        return self.redirect_uri

    def flow_code_initiate(self, state: str, nonce: str, additional_scopes: Sequence[str] = ()) -> CodeFlowAuthInit:
        scopes = [SCOPE_OPEN_ID] + list(additional_scopes)
        req = AuthenticationRequest(
            scope=" ".join(scopes),
            response_type="code",
            redirect_uri=AnyHttpUrl(self.redirect_uri),
            state=state,
            nonce=nonce,
            client_id=self.client_id,
        )
        return CodeFlowAuthInit(auth_url=AnyHttpUrl(self.auth_url), req=req)

    def flow_code_conclude(self, authorization_code: str, timeout_seconds: float = 10) -> TokenResponse:
        return self.iam_state.convert_auth_code(self.client_id, authorization_code)

    def try_refresh_token(self, refresh_token: str, timeout_seconds: float = 10) -> TokenResponse:
        return self.iam_state.convert_refresh_token(self.client_id, refresh_token)

    def initiate_logout(
        self, id_token: str, post_logout_redirect_uri: str | None = None, timeout_seconds: float = 10
    ) -> None:
        self.iam_state.end_sessions(id_token)


class MemoryIam(object):
    def __init__(
        self,
        openid_issuer: str,
        openid_redirect_uri: str,
        openid_client_id: str,
        realm: Realm,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> None:
        self.openid_issuer = openid_issuer.rstrip("/")
        self.openid_auth_url = f"{openid_issuer}/auth"
        self.openid_redirect_uri = openid_redirect_uri
        self.openid_client_id = openid_client_id
        self.realm = realm
        self.state = MemoryIamState(realm, openid_issuer)
        self.lf = lf

    def get_openid_issuer(self) -> str:
        return self.openid_issuer

    def get_openid_client(self) -> MemoryOpenIdClient:
        return MemoryOpenIdClient(self.state, self.openid_client_id, self.openid_auth_url, self.openid_redirect_uri)

    def get_openid_token_inspector(self) -> TokenInspector:
        return TokenInspector(self.state.key_data, self.openid_issuer, lf=self.lf)

    def get_health(self) -> bool:
        return True

    # --- non interface methods

    def auth(self, init_data: CodeFlowAuthInit, username: str, password: str) -> AuthenticationResponse:
        if not self.realm.verify_user_password(username, password):
            raise ValueError(f"Cannot authenticate user '{username}', the password is incorrect")
        authorization_code = self.state.generate_auth_code(username, init_data.req.nonce)
        return AuthenticationResponse(code=authorization_code, state=init_data.req.state)


class MemoryIamConfig(BaseModel):
    type: Literal["memory"] = "memory"
    keycloak_realm_file: Path
    keycloak_realm_secrets_file: Path
    keycloak_host: AnyHttpUrl  # used to generate the trusted host
    openid_redirect_uri: AnyHttpUrl
    openid_client_id: str

    def build(self, lf: LogFactory = NO_LOG_FACTORY) -> MemoryIam:
        realm = Realm.from_json_files(self.keycloak_realm_file, self.keycloak_realm_secrets_file)
        return MemoryIam(str(self.keycloak_host), str(self.openid_redirect_uri), self.openid_client_id, realm, lf=lf)
