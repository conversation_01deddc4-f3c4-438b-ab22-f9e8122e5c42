import collections
import json
from datetime import date, datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List, Literal, Set, Tuple

import pytest
from pydantic import AnyHttpUrl, ValidationError

import qw_inspection.spec.plan as plan_spec
import qw_inspection.spec.result as result_spec
from qw_drawing_toolkit.pdf.models.base_models import DrawingUnit
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysis
from qw_mono_test.app import QwMonoTestApp
from qw_mono_test.e2e.common import (
    DEMO_DRAWING_FILE,
    DEMO_DRAWING_FILE_2,
    add_inspection_plan,
    add_inspection_plan_and_create_task,
    add_order_line,
    add_pdf_drawing,
    add_text_file,
    create_inspection_task,
)
from qw_monodb.table.trunk.resource import FileResourceLabel
from qw_pfoertner.api.v1.chat.add_chat_message import ChatAddMessageInput
from qw_pfoertner.api.v1.file.add_file_resource_revision import (
    NewFileResourceRevisionInput,
    NewFileResourceRevisionMaterialLink,
)
from qw_pfoertner.api.v1.file.drawing.add_drawing_discussion import NewDrawingDiscussionInput
from qw_pfoertner.api.v1.file.drawing.list_drawing_discussions import ListDrawingDiscussionsInput
from qw_pfoertner.api.v1.file.list_file_resources import ListFileResourcesInput
from qw_pfoertner.api.v1.file.list_file_resources_metadata import ListFileResourcesMetadataInput
from qw_pfoertner.api.v1.inspection.add_inspection_action_result import NewInspectionActionResultInput
from qw_pfoertner.api.v1.inspection.get_inspection_plan import InspectionPlanIdParam
from qw_pfoertner.api.v1.inspection.get_inspection_result_as_pdf import GenerateInspectionPdfInput
from qw_pfoertner.api.v1.inspection.list_inspection_plans import ListInspectionPlansInput
from qw_pfoertner.api.v1.inspection.list_inspection_plans_metadata import ListInspectionPlansMetadataInput
from qw_pfoertner.api.v1.inspection.list_inspections import ListInspectionsInput
from qw_pfoertner.api.v1.inspection.update_inspection_plan import UpdateInspectionPlanInput
from qw_pfoertner.api.v1.order.add_order_line import NewOrderLineInput, NewPurchaseOrderLineInfo
from qw_pfoertner.api.v1.order.add_order_line_task import (
    NewOrderLineTaskInput,
    NewOrderLineTaskInspectionInput,
    NewOrderLineTaskInspectionOutput,
)
from qw_pfoertner.api.v1.order.list_orders import ListOrdersInput
from qw_pfoertner.api.v1.order.update_order_line_inspection_due_date import UpdateOrderLineTaskDueDateInput
from qw_pfoertner.client.interface import QwPfoertnerClient
from qw_tenant_config.registry import TenantConfigRegistry


def _get_drawing_analysis_from_file(drawing_file_path: Path) -> DrawingAnalysis:
    """
    Get pre-analyzed drawing analysis results from a JSON file.

    This function is used as a workaround for tests since drawing analysis
    is now performed asynchronously in a background task.

    Args:
        drawing_file_path: Path to the drawing file (e.g., DEMO_DRAWING_FILE)

    Returns:
        DrawingAnalysis object loaded from the corresponding JSON file
    """
    # Get the base name without extension
    base_name = drawing_file_path.stem

    # Construct the path to the analysis JSON file
    json_path = drawing_file_path.parent / f"{base_name}.json"

    # Load the JSON file
    with open(json_path, "r") as f:
        analysis_data = json.load(f)

    # Create a DrawingAnalysis object from the JSON data
    return DrawingAnalysis(**analysis_data)


class TestPurchaseOrder(object):
    def test_add_purchase_order_and_attach_drawing(self, mono: QwMonoTestApp, tenants: TenantConfigRegistry) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")
        client_other, _ = mono.login_as("<EMAIL>")

        supplier = tenants.find_tenant_by_code("precision_parts")
        assert supplier is not None

        session_user = client.get_session_user()
        assert session_user.tenant_id is not None
        assert session_user.tenant_id != supplier.id

        new_pol_data = NewOrderLineInput(
            internal_reference_material="MAT_001",
            internal_reference_order="PO_001",
            internal_reference_order_line="POL_001",
            material_name="SOME_FLANGE",
            count=10,
            order_date=date(2024, 1, 1),
            expected_delivery_date=date(2024, 2, 2),
            info=NewPurchaseOrderLineInfo(supplier_tenant_id=supplier.id),
            requirements=[FileResourceLabel.TECHNICAL_DRAWING, FileResourceLabel.MATERIAL_CERTIFICATE],
        )

        new_pol_result = client.add_order_line(new_pol_data).get()
        assert not new_pol_result.material_existed_before
        assert not new_pol_result.order_existed_before
        assert not new_pol_result.order_line_existed_before

        for c, role, can_access_material, can_access_po in [
            (client, "customer", True, True),
            (client_supplier, "supplier", False, True),
            (client_other, "other", False, False),
        ]:
            po_resp = c.get_order(new_pol_result.order_id)
            if not can_access_po:
                assert po_resp.status_code == 403, role
            else:
                po = po_resp.get()
                assert po.order_date == new_pol_data.order_date, role
                assert [line.id for line in po.line_items] == [new_pol_result.order_line_id], role

            # check direct endpoint as well
            pol_resp = c.get_order_line(new_pol_result.order_line_id)
            if not can_access_po:
                assert pol_resp.status_code == 403, role
            else:
                pol = pol_resp.get()
                assert pol.id == new_pol_result.order_line_id, role

            material_resp = c.get_material(new_pol_result.material_id)
            if not can_access_material:
                assert material_resp.status_code == 403, role
            else:
                material = material_resp.get()
                assert material.name == new_pol_data.material_name, role
                assert material.owner.id == session_user.tenant_id

        file_info = add_pdf_drawing(
            client, DEMO_DRAWING_FILE, new_pol_result.order_line_id, share_with_other_tenant=True
        )

        file_resource = client.get_file_resource(file_info.file_resource_id).get()

        assert file_resource.label == FileResourceLabel.TECHNICAL_DRAWING
        assert [r.id for r in file_resource.revisions] == [file_info.file_resource_revision_id]

        # checking metadata for access detail
        file_metadata_output = client.list_file_resources_metadata(
            params=ListFileResourcesMetadataInput(in_file_resource_ids=[file_resource.id])
        )
        assert file_metadata_output.is_ok
        assert len(file_metadata_output.get().metadata_items) == 1
        item = file_metadata_output.get().metadata_items[0]
        expected_rights = {(file_resource.owner.id, True), (supplier.id, False)}
        assert {(ar.tenant.id, ar.is_owner) for ar in item.access_rights} == expected_rights

    def test_add_chat(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")
        client_other, _ = mono.login_as("<EMAIL>")

        # ---------------------------------------------------------------------------------
        #                                Add Chat
        # ---------------------------------------------------------------------------------

        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        pol_output = client.add_order_line(
            NewOrderLineInput(
                internal_reference_material="R-023",
                internal_reference_order="PO-21345",
                internal_reference_order_line="POL-01111",
                material_name="Moonstone",
                order_date=date.today(),
                expected_delivery_date=date.today() + timedelta(days=1),
                count=10,
                info=NewPurchaseOrderLineInfo(supplier_tenant_id=supplier_tenant_id),
            )
        )

        assert pol_output is not None and pol_output.get().order_line_id is not None

        pol_view = client.get_order_line(pol_output.get().order_line_id)

        assert pol_view is not None and pol_view.get().chat_id is not None

        chat_id = pol_view.get().chat_id

        # ---------------------------------------------------------------------------------
        #                             Add Chat Message
        # ---------------------------------------------------------------------------------

        fixed_timezone = timezone(timedelta(hours=1))

        response = client.add_chat_message(chat_id, "Johnny's back!", datetime.now(timezone.utc))
        response_2 = client_supplier.add_chat_message(chat_id, "Jane's happy!", datetime.now(fixed_timezone))
        response_3 = client_other.add_chat_message(chat_id, "Who cares!", datetime.now(timezone.utc))

        assert response.status_code == response_2.status_code == response_3.status_code == 200
        assert response.get().message_id is not None
        assert response_2.get().message_id is not None
        assert response_3.get().message_id is not None

        # ---------------------------------------------------------------------------------
        #                             Edit Chat Message
        # ---------------------------------------------------------------------------------
        chat_message_id = response_3.get().message_id

        response_edit = client_other.edit_chat_message(chat_message_id, "Yeah!", None)

        assert response_edit.status_code == 200

        response_edit = client_other.edit_chat_message(
            response_2.get().message_id, "May I?", datetime.now(timezone.utc)
        )

        assert response_edit.status_code == 403

        # ---------------------------------------------------------------------------------
        #                             Get Chat Messages
        # ---------------------------------------------------------------------------------

        response_messages = client.get_chat_messages(chat_id, 2, 1)

        assert response_messages.status_code == 200
        assert len(response_messages.get().messages) == 2
        assert response_messages.get().messages[0].content == "Jane's happy!"
        assert response_messages.get().messages[1].content == "Johnny's back!"

        # response_messages = client.get_chat_messages(chat_id, 10, -1)

    def test_drawing_discussion_happy_path(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")
        client_other, _ = mono.login_as("<EMAIL>")

        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        pol_output = client.add_order_line(
            NewOrderLineInput(
                internal_reference_material="whatever",
                internal_reference_order="PO-123",
                internal_reference_order_line="POL-123",
                material_name="Really badly designed part that needs lots of discussing!",
                order_date=date.today(),
                expected_delivery_date=date.today() + timedelta(days=1),
                count=1000000000,
                info=NewPurchaseOrderLineInfo(supplier_tenant_id=supplier_tenant_id),
            )
        )

        assert pol_output.is_ok
        pol_id = pol_output.get().order_line_id

        revision_info = add_pdf_drawing(client, DEMO_DRAWING_FILE, pol_id)

        message0 = "We can not manufacture to this tolerance"
        message1 = "Oh well, ok"
        message2 = "Thanks, bruh! Smashing the resolved button now"

        new_discussion_resp = client_supplier.add_drawing_discussion(
            NewDrawingDiscussionInput(
                file_resource_id=revision_info.file_resource_id,
                first_message=ChatAddMessageInput(
                    content=message0,
                    ts_sent=datetime.now(timezone.utc),
                ),
                drawing_quad=plan_spec.InspectionDrawingQuad(
                    drawing_revision_id=revision_info.file_resource_revision_id,
                    page_index=0,
                    x1=0.1,
                    y1=0.1,
                    x2=0.2,
                    y2=0.1,
                    x3=0.2,
                    y3=0.2,
                    x4=0.1,
                    y4=0.2,
                ),
            )
        )
        assert new_discussion_resp.is_ok
        dd_id = new_discussion_resp.get().drawing_discussion_id

        discussion_resp = client.get_drawing_discussion(dd_id)
        assert discussion_resp.is_ok
        discussion = discussion_resp.get()
        assert not discussion.resolved
        assert discussion.file_resource_id == revision_info.file_resource_id

        client.add_chat_message(discussion.chat_id, message1, datetime.now(timezone.utc))
        client_supplier.add_chat_message(discussion.chat_id, message2, datetime.now(timezone.utc))
        client_supplier.set_drawing_discussion_status(discussion.id, resolved=True)

        discussion_resp2 = client.get_drawing_discussion(dd_id)
        assert discussion_resp2.is_ok
        assert discussion_resp2.get().resolved

        messages_resp = client.get_chat_messages(discussion.chat_id, 10, newest_first=True)
        assert messages_resp.is_ok
        assert [m.content for m in messages_resp.get().messages] == [message2, message1, message0]

        # start another discussion to check the list endpoint
        new_discussion_resp2 = client.add_drawing_discussion(
            NewDrawingDiscussionInput(
                file_resource_id=revision_info.file_resource_id,
                first_message=ChatAddMessageInput(
                    content="How about this, any concerns?",
                    ts_sent=datetime.now(timezone.utc),
                ),
                drawing_quad=plan_spec.InspectionDrawingQuad(
                    drawing_revision_id=revision_info.file_resource_revision_id,
                    page_index=0,
                    x1=0.2,
                    y1=0.2,
                    x2=0.3,
                    y2=0.2,
                    x3=0.3,
                    y3=0.3,
                    x4=0.2,
                    y4=0.3,
                ),
            )
        )
        assert new_discussion_resp2.is_ok
        dd2_id = new_discussion_resp2.get().drawing_discussion_id

        params = ListDrawingDiscussionsInput(count=10, offset=0)
        list_resp = client.list_drawing_discussions(params)
        list_resp_supplier = client_supplier.list_drawing_discussions(params)
        list_resp_other = client_other.list_drawing_discussions(params)
        assert {dd.id for dd in list_resp.get().drawing_discussions} == {dd_id, dd2_id}
        assert {dd.id for dd in list_resp_supplier.get().drawing_discussions} == {dd_id, dd2_id}
        assert len(list_resp_other.get().drawing_discussions) == 0

    def test_matrix_inspection_flow(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")

        tenant_id = client.get_session_user().tenant_id
        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        # create purchase order line and add a technical drawing
        pol_output = add_order_line(client, supplier_tenant_id=supplier_tenant_id)
        pol_id = pol_output.order_line_id
        revision_info = add_pdf_drawing(client, DEMO_DRAWING_FILE, pol_id)
        frr_id = revision_info.file_resource_id

        # get drawing and pick some dimension
        # Note: Using pre-analyzed results since analysis is now asynchronous
        drawing_analysis = _get_drawing_analysis_from_file(DEMO_DRAWING_FILE)
        assert len(drawing_analysis.result.lengths) > 0
        test_dimension = drawing_analysis.result.lengths[0]
        assert test_dimension.length.unit == DrawingUnit.MILLIMETER

        # create an inspection plan and use it for a task
        sample_count = 4
        inspection_plan = plan_spec.InspectionPlan(
            title="test plan",
            steps=[
                plan_spec.InspectionPlanStep(
                    drawing_quad=None,
                    actions=[plan_spec.InspectionPlanActionQuestionAnswer(question="Which caliper is used?")],
                    repeat_for_each_sample=False,
                ),
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=frr_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=test_dimension.length.value,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=-1,
                            value_upper_tolerance=1,
                        ),
                        plan_spec.InspectionPlanActionImage(
                            instruction=None,
                            optional=True,
                        ),
                    ],
                    repeat_for_each_sample=True,
                ),
                plan_spec.InspectionPlanStep(
                    drawing_quad=None,
                    actions=[plan_spec.InspectionPlanActionVideo(instruction="Follow the welding seams")],
                    repeat_for_each_sample=True,
                ),
            ],
        )

        new_task_due_date = datetime.now().date() + timedelta(days=5)
        new_task, new_plan = add_inspection_plan_and_create_task(
            client=client,
            order_line_id=pol_id,
            inspection_plan=inspection_plan,
            inspector_tenant_id=supplier_tenant_id,
            sample_count=sample_count,
            due_date=new_task_due_date,
        )

        # checking metadata for access detail
        plan_metadata_output = client.list_inspection_plans_metadata(
            params=ListInspectionPlansMetadataInput(in_inspection_plan_ids=[new_plan.inspection_plan_id])
        )
        assert plan_metadata_output.is_ok
        assert len(plan_metadata_output.get().metadata_items) == 1
        item = plan_metadata_output.get().metadata_items[0]
        expected_rights = {(tenant_id, True), (supplier_tenant_id, False)}
        assert {(ar.tenant.id, ar.is_owner) for ar in item.access_rights} == expected_rights

        # inspection data prep
        def get_binary_dummy_data(rid: result_spec.InspectionActionResultIdentifier) -> bytes:
            return rid.model_dump_json(by_alias=True).encode()

        skip_count = 2
        inspection_results: List[result_spec.InspectionActionResult] = (
            [
                result_spec.InspectionActionResult(
                    result_id=result_spec.InspectionActionResultIdentifier.from_tuple(0, 0, None),
                    evidence=result_spec.InspectionActionQuestionAnswerEvidence(answer="Mitutoyo 500-197-30"),
                )
            ]
            + [
                result_spec.InspectionActionResult(
                    result_id=result_spec.InspectionActionResultIdentifier.from_tuple(1, 0, si),
                    evidence=result_spec.InspectionActionMeasurementEvidence(
                        measured_value=test_dimension.length.value + si,
                        binary_value=False,
                    ),
                )
                for si in range(sample_count)
            ]
            + [
                result_spec.InspectionActionResult(
                    result_id=result_spec.InspectionActionResultIdentifier.from_tuple(1, 1, si),
                    evidence=result_spec.InspectionActionImageEvidence(),
                )
                for si in range(sample_count)
                if si >= skip_count  # we ignore two on purpose (action is optional)
            ]
            + [
                result_spec.InspectionActionResult(
                    result_id=result_spec.InspectionActionResultIdentifier.from_tuple(2, 0, si),
                    evidence=result_spec.InspectionActionVideoEvidence(),
                )
                for si in range(sample_count)
            ]
        )
        inspection_results_lookup: Dict[Tuple[int, int, int | None], result_spec.InspectionActionResult] = {
            ir.result_id.as_tuple(): ir for ir in inspection_results
        }

        # start the journey on the supplier side
        pol_tasks_output = client_supplier.get_order_line_tasks(pol_id)
        assert pol_tasks_output.is_ok
        assert [t.id for t in pol_tasks_output.get().tasks_for_inspection] == [new_task.task_id]
        assert [t.due_date for t in pol_tasks_output.get().tasks_for_inspection] == [new_task_due_date]

        new_task_due_date_updated = datetime.now().date() + timedelta(days=10)
        update_pol_task = client.update_order_line_inspection_due_date(
            order_line_task_id=new_task.task_id,
            params=UpdateOrderLineTaskDueDateInput(due_date=new_task_due_date_updated),
        )
        assert update_pol_task.is_ok

        pol_tasks_output_due_date_updated = client_supplier.get_order_line_tasks(pol_id)
        assert pol_tasks_output_due_date_updated.is_ok
        assert [t.id for t in pol_tasks_output_due_date_updated.get().tasks_for_inspection] == [new_task.task_id]
        assert [t.due_date for t in pol_tasks_output_due_date_updated.get().tasks_for_inspection] == [
            new_task_due_date_updated
        ]

        inspection1_output = client_supplier.get_inspection(new_task.inspection_id)
        assert inspection1_output.is_ok
        inspection1 = inspection1_output.get()
        assert inspection1.plan == inspection_plan
        assert inspection1.summary.sample_count == sample_count
        assert len(inspection1.result.available_results) == 0
        assert not inspection1.result.all_mandatory_results_are_available
        assert inspection1.summary.inspection_finish is None

        # submit data and verify that it can be retrieved again
        for ir in inspection_results:
            is_image = ir.evidence.type == plan_spec.InspectionPlanActionType.IMAGE
            is_video = ir.evidence.type == plan_spec.InspectionPlanActionType.VIDEO
            action_result_output = client_supplier.add_inspection_action_result(
                params=NewInspectionActionResultInput(inspection_id=new_task.inspection_id, result=ir),
                image_bytes=get_binary_dummy_data(ir.result_id) if is_image else None,
                video_bytes=get_binary_dummy_data(ir.result_id) if is_video else None,
            )
            assert action_result_output.is_ok and action_result_output.get().has_no_errors

        inspection2_output = client_supplier.get_inspection(new_task.inspection_id)
        assert inspection2_output.is_ok
        inspection2 = inspection2_output.get()
        assert inspection2.result.all_mandatory_results_are_available
        assert len(inspection2.result.missing_results) == sample_count - skip_count
        assert inspection2.summary.inspection_finish is None

        for available_result in inspection2.result.available_results:
            expected_result = inspection_results_lookup.get(available_result.result_id.as_tuple())
            assert expected_result is not None
            assert expected_result == available_result

            if expected_result.evidence.type == plan_spec.InspectionPlanActionType.IMAGE:
                inspection_image_output = client_supplier.get_inspection_result_image(
                    inspection_id=new_task.inspection_id,
                    step_index=expected_result.result_id.step_index,
                    action_index=expected_result.result_id.action_index,
                    sample_index=expected_result.result_id.sample_index,
                )
                assert inspection_image_output.status_code == 200
                assert inspection_image_output.http.body == get_binary_dummy_data(expected_result.result_id)

            if expected_result.evidence.type == plan_spec.InspectionPlanActionType.VIDEO:
                inspection_video_output = client_supplier.get_inspection_result_video(
                    inspection_id=new_task.inspection_id,
                    step_index=expected_result.result_id.step_index,
                    action_index=expected_result.result_id.action_index,
                    sample_index=expected_result.result_id.sample_index,
                )
                assert inspection_video_output.status_code == 200
                assert inspection_video_output.http.body == get_binary_dummy_data(expected_result.result_id)

        # try generating the inspection PDF for the first time (should fail before inspection is finished)
        inspection_frontend_url = AnyHttpUrl(f"https://www.super-inspections.com/inspection/{new_task.inspection_id}")
        pdf_output1 = client.get_inspection_result_pdf(
            new_task.inspection_id,
            GenerateInspectionPdfInput(frontend_url=inspection_frontend_url),
        )
        assert pdf_output1.status_code == 412

        # finish inspection
        finish_output = client_supplier.finish_inspection(new_task.inspection_id)
        assert finish_output.is_ok and finish_output.get().has_no_errors

        # check the results from the customer client
        pol_tasks2_output = client.get_order_line_tasks(pol_id)
        assert pol_tasks2_output.is_ok
        assert len(pol_tasks2_output.get().tasks_for_inspection) == 1
        inspection_task = pol_tasks2_output.get().tasks_for_inspection[0]
        assert inspection_task.id == new_task.task_id
        assert inspection_task.inspection_id == new_task.inspection_id
        assert inspection_task.inspection_is_finished

        inspection3_output = client.get_inspection(new_task.inspection_id)
        assert inspection3_output.is_ok
        inspection3 = inspection3_output.get()
        assert inspection3.result == inspection2.result
        assert inspection3.summary.inspection_finish is not None
        assert inspection3.summary.inspection_finish.creator.id == client_supplier.get_session_user().id
        assert inspection3.summary.inspection_finish.count_non_conform_measurements == 2

        # try generating the inspection PDF for the second time
        pdf_output2 = client.get_inspection_result_pdf(
            new_task.inspection_id,
            GenerateInspectionPdfInput(frontend_url=inspection_frontend_url),
        )
        assert pdf_output2.status_code == 200
        assert pdf_output2.http.body.startswith(b"%PDF")

    def test_rename_file_resource(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")
        new_display_name = "just-got-renamed.pdf"

        # create purchase order line and add a technical drawing
        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        pol_output = add_order_line(client, supplier_tenant_id=supplier_tenant_id)
        revision_info = add_pdf_drawing(client, DEMO_DRAWING_FILE, pol_output.order_line_id)

        file_resource = client.get_file_resource(revision_info.file_resource_id).get()
        client.rename_file_resource(file_resource.id, new_display_name)
        file_resource_renamed = client.get_file_resource(file_resource.id).get()

        assert file_resource.display_name != file_resource_renamed.display_name
        assert file_resource_renamed.display_name == new_display_name

    def test_list_pols_with_stats(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")
        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        # data setup summary:
        # PO-1 POL-1: 1 drawing,  0 inspections,
        # PO-1 POL-2: 2 drawings, 2 completed inspections, 1 completed with ncs,
        # PO-1 POL-3: 0 drawings, 0 inspections
        # PO-2 POL-1: 1 drawing,  2 incompleted inspections

        po_1_ref = "PO-1"
        po_2_ref = "PO-2"
        pol_1_ref = "POL-1"
        pol_2_ref = "POL-2"
        pol_3_ref = "POL-3"

        pol_inputs = {
            (po_ref, pol_ref): NewOrderLineInput(
                internal_reference_material=f"{pol_ref}_{po_ref}",
                internal_reference_order=po_ref,
                internal_reference_order_line=pol_ref,
                material_name="flange",
                order_date=date.today(),
                expected_delivery_date=date.today() + timedelta(days=1),
                count=10,
                info=NewPurchaseOrderLineInfo(supplier_tenant_id=supplier_tenant_id),
            )
            for po_ref, pol_ref in [
                (po_1_ref, pol_1_ref),
                (po_1_ref, pol_2_ref),
                (po_1_ref, pol_3_ref),
                (po_2_ref, pol_1_ref),
            ]
        }

        drawing_inputs = {
            (po_1_ref, pol_1_ref): [DEMO_DRAWING_FILE],
            (po_1_ref, pol_2_ref): [DEMO_DRAWING_FILE, DEMO_DRAWING_FILE_2],
            (po_2_ref, pol_1_ref): [DEMO_DRAWING_FILE],
        }

        inspection_inputs: Dict[Tuple[str, str, str], List[Literal["incomplete", "complete", "complete_with_ncs"]]] = {
            (po_1_ref, pol_2_ref, DEMO_DRAWING_FILE.name): ["complete", "complete_with_ncs"],
            (po_2_ref, pol_1_ref, DEMO_DRAWING_FILE.name): ["incomplete", "incomplete"],
        }

        # actual data setup!

        pol_ids = {}
        for (po_ref, pol_ref), new_pol_input in pol_inputs.items():
            result = client.add_order_line(new_pol_input)
            assert result.is_ok, f"Could not create pol {po_ref}/{pol_ref}"
            pol_ids[(po_ref, pol_ref)] = result.get().order_line_id

        drawing_ids = {}
        for (po_ref, pol_ref), drawing_files in drawing_inputs.items():
            for drawing_file in drawing_files:
                pol_id = pol_ids[(po_ref, pol_ref)]
                revision_info = add_pdf_drawing(client, drawing_file, pol_id, share_with_other_tenant=True)
                drawing_ids[(po_ref, pol_ref, drawing_file.name)] = revision_info.file_resource_revision_id

        exp_value = 1
        tolerance = 0.5
        for (po_ref, pol_ref, drawing_name), inspection_defs in inspection_inputs.items():
            for inspection_def in inspection_defs:
                pol_id = pol_ids[(po_ref, pol_ref)]
                drawing_frr_id = drawing_ids[(po_ref, pol_ref, drawing_name)]

                # create inspection plan
                inspection_plan = plan_spec.InspectionPlan(
                    title="test plan",
                    steps=[
                        plan_spec.InspectionPlanStep(
                            drawing_quad=plan_spec.InspectionDrawingQuad(
                                drawing_revision_id=drawing_frr_id,
                                page_index=0,
                                x1=0,
                                y1=0,
                                x2=0.1,
                                y2=0,
                                x3=0.1,
                                y3=0.1,
                                x4=0,
                                y4=0.1,
                            ),
                            actions=[
                                plan_spec.InspectionPlanActionMeasurement(
                                    value_expected=exp_value,
                                    value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                                    value_lower_tolerance=-tolerance,
                                    value_upper_tolerance=tolerance,
                                ),
                            ],
                            repeat_for_each_sample=True,
                        )
                    ],
                )

                new_task, _ = add_inspection_plan_and_create_task(
                    client=client,
                    order_line_id=pol_id,
                    inspection_plan=inspection_plan,
                    inspector_tenant_id=supplier_tenant_id,
                    sample_count=1,
                )
                inspection_id = new_task.inspection_id

                if inspection_def == "incomplete":
                    continue

                # run inspection
                value = exp_value + 2 * tolerance if inspection_def == "complete_with_ncs" else exp_value
                action_result_output = client_supplier.add_inspection_action_result(
                    params=NewInspectionActionResultInput(
                        inspection_id=inspection_id,
                        result=result_spec.InspectionActionResult(
                            result_id=result_spec.InspectionActionResultIdentifier.from_tuple(0, 0, 0),
                            evidence=result_spec.InspectionActionMeasurementEvidence(
                                measured_value=value, binary_value=False
                            ),
                        ),
                    ),
                )
                assert action_result_output.is_ok

                finish_output = client_supplier.finish_inspection(inspection_id)
                assert finish_output.is_ok

        # verifying data setup
        list_params = ListOrdersInput(
            count=100,
            offset=0,
            in_order_line_ids=list(pol_ids.values()),
            include_order_line_document_stats=True,
            include_order_line_inspection_stats=True,
        )
        list_output = client.list_orders(list_params)
        assert list_output.is_ok

        list_result = list_output.get()
        assert list_result.overall_count_pols == len(pol_ids.values())

        po1 = {po.internal_reference: po for po in list_result.pos}[po_1_ref]
        po2 = {po.internal_reference: po for po in list_result.pos}[po_2_ref]

        po1_pol1 = {li.internal_reference: li for li in po1.line_items}[pol_1_ref]
        po1_pol2 = {li.internal_reference: li for li in po1.line_items}[pol_2_ref]
        po1_pol3 = {li.internal_reference: li for li in po1.line_items}[pol_3_ref]
        po2_pol1 = {li.internal_reference: li for li in po2.line_items}[pol_1_ref]

        assert po1_pol1.document_stats is not None
        assert po1_pol1.document_stats.count_technical_drawings == 1
        assert po1_pol2.document_stats is not None
        assert po1_pol2.document_stats.count_technical_drawings == 2
        assert po1_pol3.document_stats is not None
        assert po1_pol3.document_stats.count_technical_drawings == 0
        assert po2_pol1.document_stats is not None
        assert po2_pol1.document_stats.count_technical_drawings == 1

        assert po1_pol1.inspection_stats is not None
        assert po1_pol1.inspection_stats.count_incomplete_inspections == 0
        assert po1_pol1.inspection_stats.count_completed_inspections == 0
        assert po1_pol1.inspection_stats.count_completed_inspections_with_non_conformity == 0
        assert po1_pol2.inspection_stats is not None
        assert po1_pol2.inspection_stats.count_incomplete_inspections == 0
        assert po1_pol2.inspection_stats.count_completed_inspections == 2
        assert po1_pol2.inspection_stats.count_completed_inspections_with_non_conformity == 1
        assert po1_pol3.inspection_stats is not None
        assert po1_pol3.inspection_stats.count_incomplete_inspections == 0
        assert po1_pol3.inspection_stats.count_completed_inspections == 0
        assert po1_pol3.inspection_stats.count_completed_inspections_with_non_conformity == 0
        assert po2_pol1.inspection_stats is not None
        assert po2_pol1.inspection_stats.count_incomplete_inspections == 2
        assert po2_pol1.inspection_stats.count_completed_inspections == 0
        assert po2_pol1.inspection_stats.count_completed_inspections_with_non_conformity == 0

    def test_delete_file_resources(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")

        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        pol_output = add_order_line(client, supplier_tenant_id=supplier_tenant_id)
        pol_id = pol_output.order_line_id

        drawing_file_resource = add_pdf_drawing(client, DEMO_DRAWING_FILE, pol_id)
        drawing_file_resource_id = drawing_file_resource.file_resource_id
        drawing_file_resource_revision_id = drawing_file_resource.file_resource_revision_id

        drawing_file_resource_id_2 = add_pdf_drawing(client, DEMO_DRAWING_FILE_2, pol_id).file_resource_id
        text_file_resource_id = add_text_file(client, "hello!", pol_id).file_resource_id

        file_resources_output = client.list_file_resources(
            params=ListFileResourcesInput(
                linked_to_order_line_ids=[pol_id],
                count=10,
                offset=0,
            )
        )
        assert file_resources_output.is_ok
        file_resources = file_resources_output.get().file_resources
        assert len(file_resources) == 3
        file_resource_ids = {fr.id for fr in file_resources}
        assert file_resource_ids == {drawing_file_resource_id, drawing_file_resource_id_2, text_file_resource_id}

        delete_output = client.delete_file_resources(
            file_resource_ids=[drawing_file_resource_id, text_file_resource_id]
        )
        assert delete_output.is_ok

        file_resources_output_after_delete = client.list_file_resources(
            params=ListFileResourcesInput(
                linked_to_order_line_ids=[pol_id],
                count=10,
                offset=0,
            )
        )
        assert file_resources_output_after_delete.is_ok
        file_resources_after_delete = file_resources_output_after_delete.get().file_resources
        assert len(file_resources_after_delete) == 1
        remaining_file_resource_ids = {fr.id for fr in file_resources_after_delete}
        assert remaining_file_resource_ids == {drawing_file_resource_id_2}

        page_index = 0
        # Note: Skip checking deleted file analysis since we're using pre-analyzed results
        # In the real system, this would return 404 for a deleted file
        # drawing_analysis_output = client.get_drawing_analysis(
        #     file_resource_revision_id=drawing_file_resource_revision_id
        # )
        # assert drawing_analysis_output.is_ok
        # drawing_analysis = drawing_analysis_output.get()
        # assert len(drawing_analysis.document.pages) > 0

        image_output = client.get_drawing_revision_page_image(
            frr_id=drawing_file_resource_revision_id,
            page_index=page_index,
        )
        assert image_output.http.status_code == 200
        assert len(image_output.http.body) > 0

    def test_inspection_flow_with_binary_measurement(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")

        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        pol_output = add_order_line(client, supplier_tenant_id=supplier_tenant_id)
        pol_id = pol_output.order_line_id

        revision_info = add_pdf_drawing(client, DEMO_DRAWING_FILE, pol_id)
        frr_id = revision_info.file_resource_id

        # Note: Using pre-analyzed results since analysis is now asynchronous
        drawing_analysis = _get_drawing_analysis_from_file(DEMO_DRAWING_FILE)
        assert len(drawing_analysis.result.lengths) > 0
        test_dimension = drawing_analysis.result.lengths[0]
        assert test_dimension.length.unit == DrawingUnit.MILLIMETER

        inspection_plan = plan_spec.InspectionPlan(
            title="binary measurement test plan",
            steps=[
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=frr_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=1.5,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=None,
                            value_upper_tolerance=None,
                            importance=plan_spec.InspectionCharacteristicImportance.COMMON,
                            result_type=plan_spec.InspectionMeasurementResultType.BINARY,
                            tolerance_source="explicit",
                            detail=plan_spec.InspectionMeasurementThreadDetail(pitch=1.5, iso965_identifier=None),
                        )
                    ],
                    repeat_for_each_sample=False,
                ),
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=frr_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=1.5,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=-0.3,
                            value_upper_tolerance=0.3,
                            importance=plan_spec.InspectionCharacteristicImportance.COMMON,
                            result_type=plan_spec.InspectionMeasurementResultType.BINARY,
                            tolerance_source="ISO_286",
                            detail=plan_spec.InspectionMeasurementDiameterDetail(iso286_identifier="f6"),
                        )
                    ],
                    repeat_for_each_sample=False,
                ),
            ],
        )

        new_task, _ = add_inspection_plan_and_create_task(
            client=client,
            order_line_id=pol_id,
            inspection_plan=inspection_plan,
            inspector_tenant_id=supplier_tenant_id,
            sample_count=1,
        )

        # The following should fail because the plan expects a binary result
        continuous_value_result = result_spec.InspectionActionResult(
            result_id=result_spec.InspectionActionResultIdentifier.from_tuple(0, 0, None),
            evidence=result_spec.InspectionActionMeasurementEvidence(
                measured_value=5.0,
                result_type=plan_spec.InspectionMeasurementResultType.CONTINUOUS,
            ),
        )
        action_result_output = client_supplier.add_inspection_action_result(
            params=NewInspectionActionResultInput(inspection_id=new_task.inspection_id, result=continuous_value_result),
        )
        assert action_result_output.is_ok
        assert not action_result_output.get().has_no_errors

        successful_binary_result = result_spec.InspectionActionResult(
            result_id=result_spec.InspectionActionResultIdentifier.from_tuple(0, 0, None),
            evidence=result_spec.InspectionActionMeasurementEvidence(
                result_type=plan_spec.InspectionMeasurementResultType.BINARY,
                binary_value=True,
            ),
        )
        successful_binary_result_2 = result_spec.InspectionActionResult(
            result_id=result_spec.InspectionActionResultIdentifier.from_tuple(1, 0, None),
            evidence=result_spec.InspectionActionMeasurementEvidence(
                result_type=plan_spec.InspectionMeasurementResultType.BINARY,
                binary_value=False,
            ),
        )
        with pytest.raises(ValidationError):
            result_spec.InspectionActionResult(
                result_id=result_spec.InspectionActionResultIdentifier.from_tuple(1, 0, None),
                evidence=result_spec.InspectionActionMeasurementEvidence(
                    measured_value=5.0,
                    result_type=plan_spec.InspectionMeasurementResultType.BINARY,
                    binary_value=False,
                ),
            )

        action_result_output = client_supplier.add_inspection_action_result(
            params=NewInspectionActionResultInput(
                inspection_id=new_task.inspection_id, result=successful_binary_result
            ),
        )
        assert action_result_output.is_ok

        action_result_output = client_supplier.add_inspection_action_result(
            params=NewInspectionActionResultInput(
                inspection_id=new_task.inspection_id, result=successful_binary_result_2
            ),
        )
        assert action_result_output.is_ok

        inspection_output = client_supplier.get_inspection(new_task.inspection_id)
        assert inspection_output.is_ok
        inspection = inspection_output.get()
        assert inspection.plan == inspection_plan

        finish_output = client_supplier.finish_inspection(new_task.inspection_id)
        assert finish_output.is_ok

        inspection_finish_output = client.get_inspection(new_task.inspection_id)
        assert inspection_finish_output.is_ok
        inspection_finish = inspection_finish_output.get()
        assert len(inspection_finish.result.available_results) == 2
        assert inspection_finish.summary.inspection_finish is not None
        assert inspection_finish.summary.inspection_finish.count_non_conform_measurements == 1

        inspection_frontend_url = AnyHttpUrl(f"https://www.super-inspections.com/inspection/{new_task.inspection_id}")
        pdf_output = client.get_inspection_result_pdf(
            new_task.inspection_id,
            GenerateInspectionPdfInput(frontend_url=inspection_frontend_url),
        )
        assert pdf_output.status_code == 200
        assert pdf_output.http.body.startswith(b"%PDF")

    def test_multiple_inspection_action_submissions_are_not_possible(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")

        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        pol_output = add_order_line(client, supplier_tenant_id=supplier_tenant_id)
        pol_id = pol_output.order_line_id

        revision_info = add_pdf_drawing(client, DEMO_DRAWING_FILE, pol_id)
        frr_id = revision_info.file_resource_id

        # Note: Using pre-analyzed results since analysis is now asynchronous
        drawing_analysis = _get_drawing_analysis_from_file(DEMO_DRAWING_FILE)
        assert len(drawing_analysis.result.lengths) > 0
        test_dimension = drawing_analysis.result.lengths[0]
        assert test_dimension.length.unit == DrawingUnit.MILLIMETER

        inspection_plan = plan_spec.InspectionPlan(
            title="simple plan",
            steps=[
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=frr_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=1.5,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=None,
                            value_upper_tolerance=None,
                        )
                    ],
                    repeat_for_each_sample=True,
                )
            ],
        )

        new_task, _ = add_inspection_plan_and_create_task(
            client=client,
            order_line_id=pol_id,
            inspector_tenant_id=supplier_tenant_id,
            inspection_plan=inspection_plan,
            sample_count=1,
        )

        continuous_value_result = result_spec.InspectionActionResult(
            result_id=result_spec.InspectionActionResultIdentifier.from_tuple(0, 0, 0),
            evidence=result_spec.InspectionActionMeasurementEvidence(measured_value=5.0),
        )
        output1 = client_supplier.add_inspection_action_result(
            params=NewInspectionActionResultInput(inspection_id=new_task.inspection_id, result=continuous_value_result),
        )
        assert output1.is_ok
        assert output1.get().has_no_errors

        output2 = client_supplier.add_inspection_action_result(
            params=NewInspectionActionResultInput(inspection_id=new_task.inspection_id, result=continuous_value_result),
        )
        assert output2.is_ok
        resp = output2.get()
        assert resp.error_bad_input is not None and "there exists already a result for" in resp.error_bad_input.lower()

    def test_inspection_plan_and_task_access_variations(self, mono: QwMonoTestApp) -> None:
        client_customer, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")

        customer_tid = client_customer.get_session_user().tenant_id
        supplier_tid = client_supplier.get_session_user().tenant_id
        assert customer_tid is not None
        assert supplier_tid is not None

        expected_inspection_task_ids_by_tenant_id: Dict[int, Set[int]] = {
            customer_tid: set(),
            supplier_tid: set(),
        }
        expected_inspection_plan_ids_by_tenant_id: Dict[int, Set[int]] = {
            customer_tid: set(),
            supplier_tid: set(),
        }

        inspection_plan = plan_spec.InspectionPlan(
            title="dummy inspection",
            steps=[
                plan_spec.InspectionPlanStep(
                    repeat_for_each_sample=True,
                    drawing_quad=None,
                    actions=[plan_spec.InspectionPlanActionImage(instruction="take a snapshot!")],
                )
            ],
        )

        order_line_output = add_order_line(client_customer, supplier_tenant_id=supplier_tid)
        order_line_id = order_line_output.order_line_id

        def create_another_task(
            client_: QwPfoertnerClient, ol_id: int, iplan_id: int, inspector_tenant_id: int
        ) -> NewOrderLineTaskInspectionOutput:
            return (
                client_.add_order_line_task(
                    order_line_id=ol_id,
                    params=NewOrderLineTaskInput(
                        new_task_for_inspection=NewOrderLineTaskInspectionInput(
                            inspection_plan_id=iplan_id,
                            inspector_tenant_id=inspector_tenant_id,
                        )
                    ),
                )
                .get()
                .new_inspection_task
            )

        # add a private inspection plan from a customer user and create a task
        out_task, out_plan = add_inspection_plan_and_create_task(
            client_customer, order_line_id, inspection_plan, customer_tid, share_inspection_plan=False
        )
        expected_inspection_plan_ids_by_tenant_id[customer_tid].add(out_plan.inspection_plan_id)
        expected_inspection_task_ids_by_tenant_id[customer_tid].add(out_task.task_id)

        # add a private inspection plan from a supplier user and create a task
        out_task, out_plan = add_inspection_plan_and_create_task(
            client_supplier, order_line_id, inspection_plan, supplier_tid, share_inspection_plan=False
        )
        expected_inspection_plan_ids_by_tenant_id[supplier_tid].add(out_plan.inspection_plan_id)
        expected_inspection_task_ids_by_tenant_id[supplier_tid].add(out_task.task_id)

        # add shared inspection plan from a customer user and create one task for the customer, one for the supplier
        # and another one for the supplier (but the last by the supplier)
        out_task, out_plan = add_inspection_plan_and_create_task(
            client_customer, order_line_id, inspection_plan, customer_tid, share_inspection_plan=True
        )
        expected_inspection_plan_ids_by_tenant_id[customer_tid].add(out_plan.inspection_plan_id)
        expected_inspection_plan_ids_by_tenant_id[supplier_tid].add(out_plan.inspection_plan_id)
        expected_inspection_task_ids_by_tenant_id[customer_tid].add(out_task.task_id)

        out_task = create_another_task(client_customer, order_line_id, out_plan.inspection_plan_id, supplier_tid)
        expected_inspection_task_ids_by_tenant_id[customer_tid].add(out_task.task_id)
        expected_inspection_task_ids_by_tenant_id[supplier_tid].add(out_task.task_id)

        out_task = create_another_task(client_supplier, order_line_id, out_plan.inspection_plan_id, supplier_tid)
        expected_inspection_task_ids_by_tenant_id[supplier_tid].add(out_task.task_id)

        # add shared inspection plan from a supplier user and create one task for the customer, one for the supplier
        # and another one for the customer (but the last by the customer)
        out_task, out_plan = add_inspection_plan_and_create_task(
            client_supplier, order_line_id, inspection_plan, supplier_tid, share_inspection_plan=True
        )
        expected_inspection_plan_ids_by_tenant_id[customer_tid].add(out_plan.inspection_plan_id)
        expected_inspection_plan_ids_by_tenant_id[supplier_tid].add(out_plan.inspection_plan_id)
        expected_inspection_task_ids_by_tenant_id[supplier_tid].add(out_task.task_id)

        out_task = create_another_task(client_supplier, order_line_id, out_plan.inspection_plan_id, customer_tid)
        expected_inspection_task_ids_by_tenant_id[customer_tid].add(out_task.task_id)
        expected_inspection_task_ids_by_tenant_id[supplier_tid].add(out_task.task_id)

        out_task = create_another_task(client_customer, order_line_id, out_plan.inspection_plan_id, customer_tid)
        expected_inspection_task_ids_by_tenant_id[customer_tid].add(out_task.task_id)

        # verification time
        for client in (client_customer, client_supplier):
            tid = client.get_session_user().tenant_id
            assert tid is not None

            # verifying the expected inspection plans are accessible
            list_plans_out = client.list_inspection_plans(
                ListInspectionPlansInput(linked_to_order_line_ids=[order_line_id], offset=0, count=100)
            )
            assert list_plans_out.is_ok
            inspection_plan_ids = {ip.id for ip in list_plans_out.get().inspection_plans}
            assert expected_inspection_plan_ids_by_tenant_id[tid] == inspection_plan_ids

            # verifying the expected tasks and - by extension - inspections are accessible
            get_tasks_out = client.get_order_line_tasks(order_line_id)
            assert get_tasks_out.is_ok
            task_ids = {t.id for t in get_tasks_out.get().tasks_for_inspection}
            expected_inspection_ids = {t.inspection_id for t in get_tasks_out.get().tasks_for_inspection}
            assert expected_inspection_task_ids_by_tenant_id[tid] == task_ids

            list_inspections_out = client.list_inspections(
                ListInspectionsInput(in_order_line_ids=[order_line_id], offset=0, count=100)
            )
            assert get_tasks_out.is_ok
            inspection_ids = {i.id for i in list_inspections_out.get().inspections}
            assert expected_inspection_ids == inspection_ids

    def test_deletion_of_purchase_orders(self, mono: QwMonoTestApp) -> None:
        client_customer, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")

        supplier_tid = client_supplier.get_session_user().tenant_id
        assert supplier_tid is not None

        o_ids_by_ref: Dict[str, int] = {}
        ol_ids_by_ref: Dict[str, List[int]] = collections.defaultdict(list)
        order_ref1 = "order1"
        order_ref2 = "order2"
        for order_ref in [order_ref1, order_ref2, "order3"]:
            for i in range(3):
                out = add_order_line(
                    client_customer,
                    supplier_tid,
                    internal_reference_order=order_ref,
                    internal_reference_order_line=str(i + 1),
                    require_new_order=i == 0,
                )

                o_ids_by_ref[order_ref] = out.order_id
                ol_ids_by_ref[order_ref].append(out.order_line_id)

        # limiting by order ids so when running all tests there is no interference from other tests
        list_input = ListOrdersInput(offset=0, count=10, in_order_ids=list(o_ids_by_ref.values()))
        assert client_customer.list_orders(list_input).get().overall_count_pols == 9
        assert client_supplier.list_orders(list_input).get().overall_count_pols == 9

        # delete single order line
        o_id = o_ids_by_ref[order_ref1]
        ol_id = ol_ids_by_ref[order_ref1].pop()
        assert client_supplier.delete_order_lines([ol_id]).status_code == 403  # only customer is allowed to delete!
        assert client_customer.delete_order_lines([ol_id]).is_ok
        for c in [client_customer, client_supplier]:
            assert c.get_order(o_id).is_ok
            assert c.get_order_line(ol_id).status_code == 404
            assert c.list_orders(list_input).get().overall_count_pols == 8
            assert c.get_order(o_id).is_ok
            assert c.get_order_line(ol_id).status_code == 404
            assert c.list_orders(list_input).get().overall_count_pols == 8

        # delete remaining, make sure that order is deleted as well
        assert client_customer.delete_order_lines(ol_ids_by_ref[order_ref1]).is_ok
        for c in [client_customer, client_supplier]:
            assert c.get_order(o_id).status_code == 404
            assert c.list_orders(list_input).get().overall_count_pols == 6
            assert c.get_order(o_id).status_code == 404
            assert c.list_orders(list_input).get().overall_count_pols == 6

        # deleting an order should delete all its lines
        o_id = o_ids_by_ref[order_ref2]
        assert client_customer.delete_orders([o_id]).is_ok
        for c in [client_customer, client_supplier]:
            assert c.get_order(o_id).status_code == 404
            assert c.list_orders(list_input).get().overall_count_pols == 3
            assert c.get_order(o_id).status_code == 404
            assert c.list_orders(list_input).get().overall_count_pols == 3

        # adding order and line with previously existing internal references should be possible
        out = add_order_line(
            client_customer,
            supplier_tid,
            internal_reference_order=order_ref2,
            internal_reference_order_line="1",
            internal_reference_material="some_mat",
            require_new_order=True,
        )
        assert not out.order_existed_before
        assert not out.order_line_existed_before
        assert not out.material_existed_before

        # on second try nothing should change, as the importer ignores existing entries
        out2 = add_order_line(
            client_customer,
            supplier_tid,
            internal_reference_order=order_ref2,
            internal_reference_order_line="1",
            internal_reference_material="some_mat",
            require_new_order=False,
        )
        assert out2.order_existed_before
        assert out2.order_line_existed_before
        assert out2.material_existed_before
        assert out.order_line_id == out2.order_line_id

    def test_update_inspection_plan(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")

        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        pol_output = add_order_line(client, supplier_tenant_id=supplier_tenant_id)
        pol_id = pol_output.order_line_id

        revision_info = add_pdf_drawing(client, DEMO_DRAWING_FILE, pol_id)
        frr_id = revision_info.file_resource_id

        # Note: Using pre-analyzed results since analysis is now asynchronous
        drawing_analysis = _get_drawing_analysis_from_file(DEMO_DRAWING_FILE)
        assert len(drawing_analysis.result.lengths) > 0
        test_dimension = drawing_analysis.result.lengths[0]
        assert test_dimension.length.unit == DrawingUnit.MILLIMETER

        inspection_plan_initial = plan_spec.InspectionPlan(
            title="binary measurement test plan",
            steps=[
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=frr_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=1.5,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=None,
                            value_upper_tolerance=None,
                            importance=plan_spec.InspectionCharacteristicImportance.COMMON,
                            result_type=plan_spec.InspectionMeasurementResultType.BINARY,
                            tolerance_source="explicit",
                            detail=plan_spec.InspectionMeasurementThreadDetail(pitch=1.5, iso965_identifier=None),
                        )
                    ],
                    repeat_for_each_sample=False,
                ),
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=frr_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=1.5,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=-0.3,
                            value_upper_tolerance=0.3,
                            importance=plan_spec.InspectionCharacteristicImportance.COMMON,
                            result_type=plan_spec.InspectionMeasurementResultType.BINARY,
                            tolerance_source="ISO_286",
                            detail=plan_spec.InspectionMeasurementDiameterDetail(iso286_identifier="f6"),
                        )
                    ],
                    repeat_for_each_sample=False,
                ),
            ],
        )

        inspection_plan_2 = plan_spec.InspectionPlan(
            title="Test inspection plan title update",
            steps=[
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=frr_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=1.5,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=None,
                            value_upper_tolerance=None,
                            importance=plan_spec.InspectionCharacteristicImportance.COMMON,
                            result_type=plan_spec.InspectionMeasurementResultType.BINARY,
                            tolerance_source="explicit",
                            detail=plan_spec.InspectionMeasurementThreadDetail(pitch=1.5, iso965_identifier=None),
                        )
                    ],
                    repeat_for_each_sample=False,
                ),
            ],
        )

        inspection_plan_3 = plan_spec.InspectionPlan(
            title="Test inspection plan title update again",
            steps=[
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=frr_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=1.5,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=-0.3,
                            value_upper_tolerance=0.3,
                            importance=plan_spec.InspectionCharacteristicImportance.COMMON,
                            result_type=plan_spec.InspectionMeasurementResultType.BINARY,
                            tolerance_source="ISO_286",
                            detail=plan_spec.InspectionMeasurementDiameterDetail(iso286_identifier="f6"),
                        )
                    ],
                    repeat_for_each_sample=False,
                ),
                plan_spec.InspectionPlanStep(
                    repeat_for_each_sample=True,
                    drawing_quad=None,
                    actions=[plan_spec.InspectionPlanActionImage(instruction="take a snapshot!")],
                ),
                plan_spec.InspectionPlanStep(
                    repeat_for_each_sample=True,
                    drawing_quad=None,
                    actions=[plan_spec.InspectionPlanActionImage(instruction="take a snapshot!")],
                ),
            ],
        )

        # Add the first inspection plan and fetch it.
        add_plan_out = add_inspection_plan(client, pol_id, inspection_plan_initial)
        inspection_plan_id = add_plan_out.inspection_plan_id
        inspection_plan_out = client.get_inspection_plan(InspectionPlanIdParam(inspection_plan_id=inspection_plan_id))
        assert inspection_plan_out.is_ok
        inspection_plan_first = inspection_plan_out.get()
        assert not inspection_plan_first.is_readonly
        assert len(inspection_plan_first.inspection_plan.steps) == 2

        # Update the inspection plan as the supplier. Expected to fail.
        edit_plan_supplier_out = client_supplier.update_inspection_plan(
            params=UpdateInspectionPlanInput(
                inspection_plan=inspection_plan_2, edit_timestamp=inspection_plan_first.edit_timestamp
            ),
            inspection_plan_id=inspection_plan_id,
        )
        assert not edit_plan_supplier_out.is_ok
        assert edit_plan_supplier_out.status_code == 403

        # Update the inspection plan as the customer and fetch the new updated plan. Expected to succeed.
        edit_plan_customer_out = client.update_inspection_plan(
            params=UpdateInspectionPlanInput(
                inspection_plan=inspection_plan_2, edit_timestamp=inspection_plan_first.edit_timestamp
            ),
            inspection_plan_id=inspection_plan_id,
        )
        assert edit_plan_customer_out.is_ok
        inspection_plan_out_2 = client.get_inspection_plan(InspectionPlanIdParam(inspection_plan_id=inspection_plan_id))
        assert inspection_plan_out_2.is_ok
        inspection_plan_second = inspection_plan_out_2.get()
        assert inspection_plan_second.inspection_plan.title == inspection_plan_2.title
        assert not inspection_plan_second.is_readonly
        assert len(inspection_plan_second.inspection_plan.steps) == 1

        # Update the inspection plan as the customer with a timestamp that is invalid. Expected to fail.
        edit_plan_customer_out_2 = client.update_inspection_plan(
            params=UpdateInspectionPlanInput(
                inspection_plan=inspection_plan_3, edit_timestamp=datetime.now(timezone.utc)
            ),
            inspection_plan_id=inspection_plan_id,
        )
        assert not edit_plan_customer_out_2.is_ok
        assert edit_plan_customer_out_2.status_code == 412

        # Create the inspection task. Which should trigger a read-only mode.
        create_inspection_task(client, pol_id, inspection_plan_id, supplier_tenant_id)

        # Attempt to update the inspection plan with valid data in a read-only mode. Expected to fail.
        edit_plan_customer_out_3 = client.update_inspection_plan(
            params=UpdateInspectionPlanInput(
                inspection_plan=inspection_plan_3, edit_timestamp=inspection_plan_second.edit_timestamp
            ),
            inspection_plan_id=inspection_plan_id,
        )
        assert not edit_plan_customer_out_3.is_ok
        assert edit_plan_customer_out_3.status_code == 400

        # Fetch the inspection plan again and verify that it matches the updated plan.
        inspection_plan_out_3 = client.get_inspection_plan(InspectionPlanIdParam(inspection_plan_id=inspection_plan_id))
        assert inspection_plan_out_3.is_ok
        inspection_plan_third = inspection_plan_out_3.get()
        assert inspection_plan_third.inspection_plan.title == inspection_plan_2.title
        assert inspection_plan_third.is_readonly
        assert len(inspection_plan_third.inspection_plan.steps) == 1

    def test_update_order_line_requirements(self, mono: QwMonoTestApp) -> None:
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")

        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        pol_output = add_order_line(client, supplier_tenant_id=supplier_tenant_id)
        pol_id = pol_output.order_line_id
        requirements_list = [FileResourceLabel.TECHNICAL_DRAWING]

        update_order_request = client.update_order_line_requirements(pol_id, requirements_list)
        assert update_order_request.is_ok
        order_out = client.get_order_line(order_line_id=pol_id)
        assert order_out.is_ok
        order_response = order_out.get()
        assert len(order_response.requirements) == 1
        assert order_response.requirements[0] == FileResourceLabel.TECHNICAL_DRAWING

        requirements_list = [FileResourceLabel.TECHNICAL_DRAWING, FileResourceLabel.MATERIAL_CERTIFICATE]

        update_order_request = client.update_order_line_requirements(pol_id, requirements_list)
        assert update_order_request.is_ok
        order_out = client.get_order_line(order_line_id=pol_id)
        assert order_out.is_ok
        order_response = order_out.get()
        assert len(order_response.requirements) == 2
        assert order_response.requirements[0] == FileResourceLabel.TECHNICAL_DRAWING
        assert order_response.requirements[1] == FileResourceLabel.MATERIAL_CERTIFICATE

        requirements_list = [FileResourceLabel.MATERIAL_CERTIFICATE, FileResourceLabel.MATERIAL_CERTIFICATE]

        update_order_request = client.update_order_line_requirements(pol_id, requirements_list)
        assert not update_order_request.is_ok
        assert update_order_request.status_code == 400
        order_out = client.get_order_line(order_line_id=pol_id)
        assert order_out.is_ok

        requirements_list = []

        update_order_request = client.update_order_line_requirements(pol_id, requirements_list)
        assert update_order_request.is_ok
        order_out = client.get_order_line(order_line_id=pol_id)
        assert order_out.is_ok
        order_response = order_out.get()
        assert len(order_response.requirements) == 0

    def test_delete_inspection_plans(self, mono: QwMonoTestApp) -> None:
        # create purchase order line and add a technical drawing
        client, _ = mono.login_as("<EMAIL>")
        client_supplier, _ = mono.login_as("<EMAIL>")

        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert supplier_tenant_id is not None

        pol_output = add_order_line(client, supplier_tenant_id=supplier_tenant_id)
        pol_id = pol_output.order_line_id
        revision_info = add_pdf_drawing(client, DEMO_DRAWING_FILE, pol_id)
        frr_id = revision_info.file_resource_id

        # get drawing and pick some dimension
        # Note: Using pre-analyzed results since analysis is now asynchronous
        drawing_analysis = _get_drawing_analysis_from_file(DEMO_DRAWING_FILE)
        assert len(drawing_analysis.result.lengths) > 0
        test_dimension = drawing_analysis.result.lengths[0]
        assert test_dimension.length.unit == DrawingUnit.MILLIMETER

        # create an inspection plan and use it for a task
        sample_count = 4
        inspection_plan = plan_spec.InspectionPlan(
            title="test plan",
            steps=[
                plan_spec.InspectionPlanStep(
                    drawing_quad=None,
                    actions=[plan_spec.InspectionPlanActionQuestionAnswer(question="Which caliper is used?")],
                    repeat_for_each_sample=False,
                ),
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=frr_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=test_dimension.length.value,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=-1,
                            value_upper_tolerance=1,
                        ),
                        plan_spec.InspectionPlanActionImage(
                            instruction=None,
                            optional=True,
                        ),
                    ],
                    repeat_for_each_sample=True,
                ),
                plan_spec.InspectionPlanStep(
                    drawing_quad=None,
                    actions=[plan_spec.InspectionPlanActionVideo(instruction="Follow the welding seams")],
                    repeat_for_each_sample=True,
                ),
            ],
        )

        new_task_due_date = datetime.now().date() + timedelta(days=5)
        new_task, new_plan = add_inspection_plan_and_create_task(
            client=client,
            order_line_id=pol_id,
            inspection_plan=inspection_plan,
            inspector_tenant_id=supplier_tenant_id,
            sample_count=sample_count,
            due_date=new_task_due_date,
        )
        inspection_plan_ids = [new_plan.inspection_plan_id]
        inspection_ids = [new_task.inspection_id]
        # delete the inspection plan as the supplier. Expected to fail.
        delete_inspection_plans_out = client_supplier.delete_inspection_plans(inspection_plan_ids=inspection_plan_ids)
        assert not delete_inspection_plans_out.is_ok
        assert delete_inspection_plans_out.status_code == 403

        # delete the inspection plan as the customer with active Inspections. Expected to fail.
        delete_inspection_plans_out = client.delete_inspection_plans(inspection_plan_ids=inspection_plan_ids)
        assert not delete_inspection_plans_out.is_ok

        list_plans_out = client.list_inspection_plans(
            ListInspectionPlansInput(linked_to_order_line_ids=[pol_id], offset=0, count=100)
        )
        inspection_plan_resp = client.get_inspection_plan(
            InspectionPlanIdParam(inspection_plan_id=inspection_plan_ids[0])
        )
        assert len(list_plans_out.get().inspection_plans) == 1
        assert inspection_plan_resp.is_ok
        assert inspection_plan_resp.get().is_readonly

        # delete inpection plan as customer with no active Inspections. Expected to succeed.
        delete_inspection_out = client.delete_inspections(inspection_ids=inspection_ids)
        assert delete_inspection_out.is_ok
        delete_inspection_plans_out = client.delete_inspection_plans(inspection_plan_ids=inspection_plan_ids)
        assert delete_inspection_plans_out.is_ok

        list_plans_out = client.list_inspection_plans(
            ListInspectionPlansInput(linked_to_order_line_ids=[pol_id], offset=0, count=100)
        )
        inspection_plan_resp = client.get_inspection_plan(
            InspectionPlanIdParam(inspection_plan_id=inspection_plan_ids[0])
        )
        assert inspection_plan_resp.is_ok
        assert not inspection_plan_resp.get().is_readonly
        assert len(list_plans_out.get().inspection_plans) == 0

    def test_file_resource_sharing(self, mono: QwMonoTestApp) -> None:
        # Setup users from different tenants
        client, _ = mono.login_as("<EMAIL>")  # customer
        client_supplier, _ = mono.login_as("<EMAIL>")  # supplier
        client_other, _ = mono.login_as("<EMAIL>")  # other tenant

        # Create a purchase order line and attach a drawing
        pol_output = client.add_order_line(
            NewOrderLineInput(
                internal_reference_material="test-material",
                internal_reference_order="PO-123",
                internal_reference_order_line="POL-123",
                material_name="Part for sharing test",
                order_date=date.today(),
                expected_delivery_date=date.today() + timedelta(days=1),
                count=1,
                info=NewPurchaseOrderLineInfo(supplier_tenant_id=2),
            )
        )
        assert pol_output.is_ok
        pol_id = pol_output.get().order_line_id

        # Add drawing initially without sharing
        file_info = add_pdf_drawing(client, DEMO_DRAWING_FILE, pol_id, share_with_other_tenant=False)

        # Verify initial access rights
        def check_file_access(c: QwPfoertnerClient, expected_status: int) -> None:
            resp = c.get_file_resource(file_info.file_resource_id)
            assert resp.status_code == expected_status

        # Initial access checks
        check_file_access(client, 200)  # Owner can access
        check_file_access(client_supplier, 403)  # Supplier cannot access yet
        check_file_access(client_other, 403)  # Other tenant cannot access

        # Share with supplier
        share_resp = client.toggle_file_resource_revision_share(
            file_info.file_resource_revision_id, tenant_id=2, enabled=True  # supplier tenant id
        )
        assert share_resp.is_ok

        # Verify access rights after sharing
        check_file_access(client, 200)  # Owner can still access
        check_file_access(client_supplier, 200)  # Supplier can now access
        check_file_access(client_other, 403)  # Other tenant still cannot access

        # Verify metadata shows correct access rights
        metadata_output = client.list_file_resources_metadata(
            params=ListFileResourcesMetadataInput(in_file_resource_ids=[file_info.file_resource_id])
        )
        assert metadata_output.is_ok
        assert len(metadata_output.get().metadata_items) == 1

        item = metadata_output.get().metadata_items[0]
        expected_rights = {(client.get_session_user().tenant_id, True), (2, False)}  # Owner and supplier
        assert {(ar.tenant.id, ar.is_owner) for ar in item.access_rights} == expected_rights

        # Revoke access
        revoke_resp = client.toggle_file_resource_revision_share(
            file_info.file_resource_revision_id, tenant_id=2, enabled=False
        )
        assert revoke_resp.is_ok

        # Verify access rights after revoking
        check_file_access(client, 200)  # Owner can still access
        check_file_access(client_supplier, 403)  # Supplier cannot access anymore
        check_file_access(client_other, 403)  # Other tenant still cannot access

    def test_inspection_plan_creation_with_material_inherited_files(self, mono: QwMonoTestApp) -> None:
        """
        Test complete workflow for inspection plan creation with material-inherited technical drawings:
        1. Customer uploads technical drawing linked to material
        2. Customer creates order with that material
        3. Customer shares files with supplier
        4. Supplier creates inspection plan using material-inherited drawings
        5. Verify supplier can access and use the drawings for inspection plans
        """
        # Setup users from different tenants
        client_customer, _ = mono.login_as("<EMAIL>")  # customer tenant
        client_supplier, _ = mono.login_as("<EMAIL>")  # supplier tenant

        customer_tenant_id = client_customer.get_session_user().tenant_id
        supplier_tenant_id = client_supplier.get_session_user().tenant_id
        assert customer_tenant_id is not None
        assert supplier_tenant_id is not None

        # Step 1: Customer creates a material and uploads technical drawing linked to material
        material_ref = "MAT-SHARED-001"
        material_name = "Shared Component with Drawing"

        # Create order line to get material ID (materials are created through order line creation)
        pol_output = add_order_line(
            client_customer,
            supplier_tenant_id=supplier_tenant_id,
            internal_reference_material=material_ref,
            material_name=material_name,
        )
        order_line_id = pol_output.order_line_id
        material_id = pol_output.material_id
        assert material_id is not None

        # Upload technical drawing linked directly to the material
        drawing_file_info = None
        with DEMO_DRAWING_FILE.open("rb") as f:
            resp = client_customer.add_file_resource_revision(
                data=NewFileResourceRevisionInput(
                    file_resource_id=None,
                    file_name=DEMO_DRAWING_FILE.name,
                    file_label=FileResourceLabel.TECHNICAL_DRAWING,
                    initial_links_to_materials=[NewFileResourceRevisionMaterialLink(material_id=material_id)],
                    ignore_hash_collisions=True,
                ),
                file_bytes=f.read(),
                file_content_type="application/pdf",
            )

        assert resp.is_ok and resp.get().has_no_errors
        drawing_file_info = resp.get().info
        assert drawing_file_info is not None

        # Step 2: Verify initial access - supplier cannot access material-linked file yet
        def check_drawing_access(client: QwPfoertnerClient, expected_status: int) -> None:
            resp = client.get_file_resource(drawing_file_info.file_resource_id)
            assert resp.status_code == expected_status

        check_drawing_access(client_customer, 200)  # Customer can access
        check_drawing_access(client_supplier, 403)  # Supplier cannot access yet

        # Step 3: Customer shares the material-linked file with supplier
        share_resp = client_customer.toggle_file_resource_revision_share(
            drawing_file_info.file_resource_revision_id, tenant_id=supplier_tenant_id, enabled=True
        )
        assert share_resp.is_ok

        # Verify supplier can now access the material-linked drawing
        check_drawing_access(client_customer, 200)  # Customer can still access
        check_drawing_access(client_supplier, 200)  # Supplier can now access

        # Step 4: Verify supplier can list technical drawings for the order line
        # This tests the getTechnicalDrawingsByOrderLineOrMaterialId functionality
        drawings_output = client_supplier.list_file_resources(
            params=ListFileResourcesInput(
                linked_to_material_ids=[material_id],
                in_file_resource_labels=[FileResourceLabel.TECHNICAL_DRAWING],
                count=10,
                offset=0,
            )
        )
        assert drawings_output.is_ok
        supplier_drawings = drawings_output.get().file_resources
        assert len(supplier_drawings) == 1
        assert supplier_drawings[0].id == drawing_file_info.file_resource_id

        # Step 5: Supplier creates inspection plan using the material-inherited drawing
        # Note: Using pre-analyzed results since analysis is now asynchronous
        drawing_analysis = _get_drawing_analysis_from_file(DEMO_DRAWING_FILE)
        assert len(drawing_analysis.result.lengths) > 0
        test_dimension = drawing_analysis.result.lengths[0]
        assert test_dimension.length.unit == DrawingUnit.MILLIMETER

        inspection_plan = plan_spec.InspectionPlan(
            title="Inspection Plan with Material-Inherited Drawing",
            steps=[
                plan_spec.InspectionPlanStep(
                    drawing_quad=plan_spec.InspectionDrawingQuad(
                        drawing_revision_id=drawing_file_info.file_resource_revision_id,
                        page_index=test_dimension.bounding_box.page_index,
                        x1=test_dimension.bounding_box.p1.x,
                        y1=test_dimension.bounding_box.p1.y,
                        x2=test_dimension.bounding_box.p2.x,
                        y2=test_dimension.bounding_box.p1.y,
                        x3=test_dimension.bounding_box.p2.x,
                        y3=test_dimension.bounding_box.p2.y,
                        x4=test_dimension.bounding_box.p1.x,
                        y4=test_dimension.bounding_box.p2.y,
                    ),
                    actions=[
                        plan_spec.InspectionPlanActionMeasurement(
                            value_expected=test_dimension.length.value,
                            value_unit=plan_spec.InspectionMeasurementUnit.MILLIMETER,
                            value_lower_tolerance=-0.1,
                            value_upper_tolerance=0.1,
                        ),
                        plan_spec.InspectionPlanActionImage(
                            instruction="Document the measurement area",
                        ),
                    ],
                    repeat_for_each_sample=True,
                ),
                plan_spec.InspectionPlanStep(
                    drawing_quad=None,
                    actions=[
                        plan_spec.InspectionPlanActionQuestionAnswer(
                            question="Is the material-inherited drawing clearly visible?"
                        )
                    ],
                    repeat_for_each_sample=False,
                ),
            ],
        )

        # Supplier creates the inspection plan - this should succeed
        plan_output = add_inspection_plan(
            client_supplier,
            order_line_id=order_line_id,
            inspection_plan=inspection_plan,
            share_inspection_plan=True,
        )
        inspection_plan_id = plan_output.inspection_plan_id

        # Step 6: Verify the inspection plan was created successfully
        plan_detail_output = client_supplier.get_inspection_plan(
            InspectionPlanIdParam(inspection_plan_id=inspection_plan_id)
        )
        assert plan_detail_output.is_ok
        plan_detail = plan_detail_output.get()
        assert plan_detail.inspection_plan.title == inspection_plan.title
        assert len(plan_detail.inspection_plan.steps) == 2

        # Verify the drawing quad references the material-inherited drawing
        measurement_step = plan_detail.inspection_plan.steps[0]
        assert measurement_step.drawing_quad is not None
        assert measurement_step.drawing_quad.drawing_revision_id == drawing_file_info.file_resource_revision_id

        # Step 7: Customer should also be able to see the shared inspection plan
        customer_plans_output = client_customer.list_inspection_plans(
            ListInspectionPlansInput(linked_to_order_line_ids=[order_line_id], offset=0, count=100)
        )
        assert customer_plans_output.is_ok
        customer_plans = customer_plans_output.get().inspection_plans
        plan_ids = {plan.id for plan in customer_plans}
        assert inspection_plan_id in plan_ids

        # Step 8: Create inspection task to verify end-to-end workflow
        task_output = create_inspection_task(
            client_customer,  # Customer creates task
            order_line_id=order_line_id,
            inspection_plan_id=inspection_plan_id,
            inspector_tenant_id=supplier_tenant_id,  # Assigns to supplier
            sample_count=2,
        )

        # Verify task was created successfully
        assert task_output.task_id is not None
        assert task_output.inspection_id is not None

        # Step 9: Verify supplier can access the inspection task
        supplier_tasks_output = client_supplier.get_order_line_tasks(order_line_id)
        assert supplier_tasks_output.is_ok
        supplier_tasks = supplier_tasks_output.get().tasks_for_inspection
        task_ids = {task.id for task in supplier_tasks}
        assert task_output.task_id in task_ids

        # Step 10: Verify file metadata shows correct sharing status
        metadata_output = client_supplier.list_file_resources_metadata(
            params=ListFileResourcesMetadataInput(in_file_resource_ids=[drawing_file_info.file_resource_id])
        )
        assert metadata_output.is_ok
        metadata_items = metadata_output.get().metadata_items
        assert len(metadata_items) == 1

        metadata_item = metadata_items[0]
        # Should show access rights for both customer (owner) and supplier (shared)
        access_rights = {(ar.tenant.id, ar.is_owner) for ar in metadata_item.access_rights}
        expected_rights = {(customer_tenant_id, True), (supplier_tenant_id, False)}
        assert access_rights == expected_rights
