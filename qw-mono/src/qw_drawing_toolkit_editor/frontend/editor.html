<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Drawing Analysis Editor</title>
    <link rel="icon" type="image/x-icon" href="/frontend/favicon.ico">

    <!-- docs at https://alpinejs.dev/ -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.1/dist/cdn.min.js"></script>

    <!-- docs at https://getuikit.com/ -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/css/uikit.min.css"/>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.21.6/dist/js/uikit-icons.min.js"></script>

    <script src="client.js"></script>
    <script src="editor.js"></script>
</head>
<body>
<div class="uk-flex"
     style="width: 100vw; height: 100vh;"
     x-data="{ sidebarOpen: false, selectedDrawing: '', drawingScale: 1.0 }"
>
    <div class="uk-width-expand uk-background-default uk-padding-small">

        <!-- top bar -->
        <div class="uk-flex uk-flex-middle">
            <div class="uk-width-4-5">
                <select class="uk-select"
                        x-model="selectedDrawing"
                        @change="$store.state.selectDrawing($event.target.value)"
                        :disabled="$store.state.getChangeCount() > 0"
                        :uk-tooltip="$store.state.getChangeCount() > 0 ? 'Sync changes first' : ''">
                    <option value="" disabled>No Drawing Selected</option>
                    <template x-for="info in $store.state.client.getDrawingInfos()">
                        <option x-text="info.path" :value="info.id"></option>
                    </template>
                </select>
            </div>
            <div class="uk-width-expand"><!-- whitespace element --></div>
            <div class="uk-width-auto">
                <span style="cursor: pointer;"
                      :uk-icon="`icon: ${sidebarOpen ? 'chevron-right' : 'chevron-left'}; ratio: 1.5;`"
                      @click="sidebarOpen = !sidebarOpen"></span>
            </div>
        </div>
        <template x-if="$store.state.drawing !== null && $store.state.drawing.pageImage !== null">
            <div class="uk-flex uk-flex-middle">
                <div class="uk-width-auto">
                    <div style="padding: 7px 10px; border: 1px solid #e5e5e5; border-top: none; user-select: none;">
                        Page
                        <span style="cursor: pointer;" uk-icon="icon: chevron-left;"
                              @click="$store.state.drawing.previousPage()"></span>
                        <span x-text="$store.state.drawing.pageIndex + 1"></span>
                        <span>/</span>
                        <span x-text="$store.state.drawing.pageCount"></span>
                        <span style="cursor: pointer;" uk-icon="icon: chevron-right;"
                              @click="$store.state.drawing.nextPage()"></span>
                    </div>
                </div>
                <div class="uk-width-expand">
                    <div class="uk-flex uk-flex-middle"
                         style="padding: 7px 10px; border: 1px solid #e5e5e5; border-top: none; border-left: none; user-select: none;">
                        <div x-text="`Scale ${parseInt(drawingScale * 100)}%`" style="min-width: 120px"></div>
                        <input class="uk-range" type="range" x-model="drawingScale" min="0.5" max="5" step="0.05"
                               aria-label="Scale">
                    </div>
                </div>
                <div class="uk-width-auto">
                    <div class="uk-flex uk-flex-middle"
                         style="padding: 7px 10px; border: 1px solid #e5e5e5; border-top: none; border-left: none; user-select: none; cursor: pointer;"
                         @click="$store.state.drawing.createNewItem()">
                        <span uk-icon="icon: plus; ratio: 1"></span>&nbsp;<span>Add Box</span>
                    </div>
                </div>
                <div class="uk-width-auto">
                    <div class="uk-flex uk-flex-middle"
                         style="padding: 7px 10px; border: 1px solid #e5e5e5; border-top: none; border-left: none; user-select: none;"
                         :style="{ cursor: $store.state.getChangeCount() > 0 ? 'pointer' : null }"
                         :class="$store.state.getChangeCount() > 0 ? '' : 'uk-text-muted'"
                         @click="$store.state.syncChanges()">
                        <span uk-icon="icon: upload; ratio: 1"></span>&nbsp;<span
                            x-text="`Sync ${$store.state.getChangeCount()} changes`"></span>
                    </div>
                </div>
                <div class="uk-width-1-5"><!-- whitespace element --></div>
            </div>
        </template>

        <!-- drawing area -->
        <div class="uk-margin-small-top" style="height: calc(100vh - 120px); user-select: none;" uk-overflow-auto>
            <template x-if="$store.state.drawing !== null && $store.state.drawing.pageImage !== null">
                <div class="uk-position-relative"
                     :style="{width: `${drawingScale * 100}%`}"
                     style="margin: auto;"
                     @click="$store.state.drawing.selectedBoxItem = null;">
                    <img id="drawingImage"
                         style="width: 100%;"
                         :width="$store.state.drawing.pageImage.naturalWidth"
                         :height="$store.state.drawing.pageImage.naturalHeight"
                         :src="$store.state.drawing.pageImage.src">
                    <template x-for="bbi in $store.state.drawing.getItems($store.state.drawing.pageIndex, false)">
                        <div class="uk-position-absolute"
                             :style="{
                                    left: `${bbi.x1 * 100}%`,
                                    top: `${bbi.y1 * 100}%`,
                                    right: `${(1 - bbi.x2) * 100}%`,
                                    bottom: `${(1 - bbi.y2) * 100}%`,
                                    cursor: $store.state.drawing.getSelectedBoxIndex() === bbi.index ? 'move' : 'pointer',
                                    background: bbi.getCssColorString($store.state.drawing.getSelectedBoxIndex() === bbi.index ? 0.5 : 0.15),
                                    border: `${$store.state.drawing.getSelectedBoxIndex() === bbi.index ? 2 : 1}px solid ${bbi.getCssColorString(1)}`,
                                 }"
                             @click="$event.stopPropagation();
                                         if ($store.state.drawing.selectedBoxItem !== null) {
                                            $store.state.drawing.selectedBoxItem.resetJsonStringToLastValid();
                                         };
                                         $store.state.drawing.selectedBoxItem = bbi;
                                         sidebarOpen = true;"
                             @mousedown="$store.state.drawing.startDrag($event, bbi, 'drawingImage', false)"
                        ></div>
                    </template>
                    <template x-if="$store.state.drawing.selectedBoxItem !== null">
                        <div class="uk-position-absolute"
                             :style="{
                                    left: `${$store.state.drawing.selectedBoxItem.x2 * 100}%`,
                                    top: `${$store.state.drawing.selectedBoxItem.y2 * 100}%`,
                                    width: '12px',
                                    height: '12px',
                                    background: $store.state.drawing.selectedBoxItem.getCssColorString(0.5),
                                 }"
                             style="cursor: nwse-resize;"
                             @mousedown="$store.state.drawing.startDrag($event, null, 'drawingImage', true)"
                             @click="$event.stopPropagation()"
                        ></div>
                    </template>
                </div>
            </template>
        </div>
    </div>

    <!-- sidebar -->
    <div class="uk-width-large uk-background-muted uk-padding-small" :class="sidebarOpen || 'uk-hidden'"
         style="height: 100vh;" uk-overflow-auto>
        <template x-if="$store.state.drawing !== null">
            <div>
                <div class="uk-text-meta" x-show="$store.state.drawing.selectedBoxItem === null">No Box
                    Selected
                </div>
                <template x-if="$store.state.drawing.selectedBoxItem !== null">
                    <div>
                        <div class="uk-flex uk-flex-middle">
                            <div class="uk-margin-small-right">Status</div>
                            <div>
                                    <span class="uk-label"
                                          :class="$store.state.drawing.selectedBoxItem.status === 'synced' ? 'uk-label-success' : 'uk-label-warning'"
                                          x-text="$store.state.drawing.selectedBoxItem.status"></span>
                            </div>
                            <div class="uk-width-expand"></div>
                            <div>
                                <button class="uk-button uk-button-default uk-button-small"
                                        style="padding: 0 5px;"
                                        :disabled="!$store.state.drawing.selectedBoxItem.canBeCopied"
                                        @click="$store.state.drawing.copySelectedItem();">
                                    <span uk-icon="copy"></span>
                                </button>
                                <button class="uk-button uk-button-default uk-button-small"
                                        style="padding: 0 5px;"
                                        @click="$store.state.drawing.selectedBoxItem.resetJsonStringToOriginal();">
                                    <span uk-icon="history"></span>
                                </button>
                                <button class="uk-button uk-button-default uk-button-small"
                                        style="padding: 0 5px;"
                                        @click="$store.state.drawing.deleteSelectedItem();">
                                    <span uk-icon="trash"></span>
                                </button>
                            </div>
                        </div>
                        <textarea class="uk-textarea uk-margin-small-top"
                                  :class="$store.state.drawing.selectedBoxItem.jsonValidationError !== null ? 'uk-form-danger' : ''"
                                  style="min-width: 100%; max-width: 100%; min-height: 320px; height: 540px; font-family: revert;"
                                  spellcheck="false"
                                  x-model.debounce.1000ms="$store.state.drawing.selectedBoxItem.jsonString"
                                  @input="$store.state.drawing.selectedBoxItem.updateFromJsonString($event.target.value)"
                        ></textarea>
                        <template x-if="$store.state.drawing.selectedBoxItem.jsonValidationError !== null">
                            <div class="uk-margin-small-top uk-text-meta">
                                <div><b>Error Details</b></div>
                                <p class="uk-margin-remove-top"
                                   x-show="!$store.state.drawing.selectedBoxItem.jsonValidationError.multiline"
                                   x-text="$store.state.drawing.selectedBoxItem.jsonValidationError.msg"></p>
                                <pre class="uk-margin-remove-top"
                                     x-show="$store.state.drawing.selectedBoxItem.jsonValidationError.multiline"
                                     x-text="$store.state.drawing.selectedBoxItem.jsonValidationError.msg"></pre>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
        </template>
    </div>
</div>
</body>
</html>