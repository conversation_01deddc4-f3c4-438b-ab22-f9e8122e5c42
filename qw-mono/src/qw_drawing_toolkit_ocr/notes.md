# Drawing Toolkit OCR System

This document provides an overview of how the Drawing Toolkit OCR system works, including a sequence diagram and explanation of the key components.

## System Overview

The Drawing Toolkit OCR system is designed to process technical drawings, extract text and symbols using OCR, and identify Product Manufacturing Information (PMI) elements. The system uses a combination of traditional OCR techniques, Google Vision API, and AI-based processing to achieve accurate results.

## Key Components

1. **DrawingOcrService**: The main orchestrator that coordinates the entire OCR process.
2. **RapidOcrEngine**: Handles the initial text detection using RapidOCR.
3. **TileManager**: Manages the tiling of large drawings, grouping, and coordinate transformations.
4. **VisionProcessor**: Processes OCR results using Google Vision API for document text detection.
5. **PmiProcessor**: Processes document text to identify and classify PMI elements.
6. **PmiAgentProcessor**: Uses an LLM-based agent (GPT-4) to analyze and enhance PMI blocks.
7. **StandardsExtractor**: Extracts ISO standards information from the drawing.
8. **MaterialExtractor**: Extracts material information from the drawing.
9. **PostProcessResults**: Post-processes the PMI results for final output.

## Sequence Diagram

```mermaid
sequenceDiagram
    participant Client
    participant DrawingOcrService
    participant TileManager
    participant RapidOcrEngine
    participant VisionProcessor
    participant PmiProcessor
    participant PmiAgentProcessor
    participant StandardsExtractor
    participant MaterialExtractor
    participant PostProcessResults

    Client->>DrawingOcrService: process_drawing(bucket, tile_infos)
    
    %% Step 1: Prepare tiles
    DrawingOcrService->>DrawingOcrService: _prepare_tiles(bucket, tile_infos)
    DrawingOcrService->>DrawingOcrService: _fetch_tiles_in_parallel(bucket, tile_infos)
    
    %% Step 2: Calculate canvas dimensions
    DrawingOcrService->>DrawingOcrService: _calculate_canvas_dimensions(tiles)
    
    %% Step 3: Process tile groups with Rapid OCR
    DrawingOcrService->>DrawingOcrService: _process_tile_groups(tiles, tile_size, canvas_width, canvas_height)
    DrawingOcrService->>TileManager: group_tiles(tiles)
    
    loop For each tile group
        DrawingOcrService->>TileManager: stitch_tile_group(group, tile_size)
        DrawingOcrService->>RapidOcrEngine: process_image_array(stitched_image)
        DrawingOcrService->>TileManager: transform_coordinates(group_result, offset_x, offset_y)
    end
    
    %% Step 4: Create background image
    DrawingOcrService->>DrawingOcrService: _create_background_image(tiles, canvas_height, canvas_width)
    
    %% Step 5: Process OCR results
    DrawingOcrService->>DrawingOcrService: _process_ocr_results(combined_result, background_image)
    
    %% Process with Google Vision API
    DrawingOcrService->>VisionProcessor: process_ocr_result_sync(masked_image, combined_result)
    VisionProcessor-->>DrawingOcrService: document_text
    
    %% Process with PMI processor
    DrawingOcrService->>PmiProcessor: process_document_text(document_text)
    PmiProcessor-->>DrawingOcrService: classified_doc
    
    %% Process PMI blocks with agent
    DrawingOcrService->>PmiAgentProcessor: process_blocks(classified_doc, background_image)
    
    loop For each PMI block
        PmiAgentProcessor->>PmiAgentProcessor: _process_block(block, image)
        PmiAgentProcessor->>PmiAgentProcessor: _crop_image_from_polygon(image, bounding_poly)
        PmiAgentProcessor->>OpenAI: Agent.run(text_prompt, image)
        OpenAI-->>PmiAgentProcessor: PMI analysis results
    end
    
    PmiAgentProcessor-->>DrawingOcrService: raw_results
    
    %% Extract standards
    DrawingOcrService->>StandardsExtractor: extract_standards(classified_doc)
    StandardsExtractor-->>DrawingOcrService: extracted_standards
    
    %% Extract materials
    DrawingOcrService->>MaterialExtractor: extract_materials(classified_doc)
    MaterialExtractor-->>DrawingOcrService: extracted_materials
    
    %% Post-process results
    DrawingOcrService->>PostProcessResults: post_process_pmi_results(raw_results, image, extracted_standards)
    PostProcessResults-->>DrawingOcrService: normalized_results
    
    %% Return final results
    DrawingOcrService-->>Client: DrawingAnalysisResult
```

## Process Flow

1. **Tile Preparation**:
   - The system receives a list of tile information (S3 object IDs and positions).
   - Tiles are fetched in parallel from S3 storage.
   - Tile dimensions are calculated to determine the full canvas size.

2. **OCR Processing**:
   - Tiles are grouped into overlapping groups for processing.
   - Each tile group is stitched together and processed with RapidOCR.
   - Coordinates are transformed to be relative to the full drawing.

3. **Background Image Creation**:
   - A full background image is created by placing all tiles in their correct positions.

4. **Text Detection and Classification**:
   - The image with text regions is processed using Google Vision API.
   - The resulting document text is classified by the PMI processor to identify different types of text blocks (PMI candidates, standards, materials, etc.).

5. **PMI Analysis**:
   - PMI candidate blocks are processed in parallel using an LLM-based agent (GPT-4).
   - Each block is cropped from the image and sent to the agent along with the text.
   - The agent analyzes the image and text to extract detailed PMI information.

6. **Standards and Materials Extraction**:
   - Standards information (tolerances, etc.) is extracted from relevant text blocks.
   - Material information is extracted using a specialized extractor.

7. **Post-Processing**:
   - Results are normalized and combined into a final structure.
   - Visualization images may be created for debugging purposes.

8. **Result Return**:
   - A `DrawingAnalysisResult` object containing all PMI results and metadata is returned.

## Key Algorithms and Techniques

1. **Tiling and Stitching**: Large drawings are processed in manageable chunks by dividing them into tiles and processing overlapping groups of tiles.

2. **Multi-angle OCR**: Text is detected at multiple rotation angles (0° and 90°) to handle text in different orientations.

3. **Hybrid OCR Approach**: Initial text region detection with RapidOCR followed by detailed text extraction with Google Vision API.

4. **AI-based PMI Analysis**: LLM (GPT-4) is used to analyze and interpret PMI elements from the drawing, providing high-accuracy extraction of manufacturing information.

5. **Parallel Processing**: Multiple PMI blocks are processed concurrently to improve performance.
