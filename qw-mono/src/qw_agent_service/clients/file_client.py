"""HTTP client for file resource services."""
from typing import Any, Dict, List, Optional

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.clients.base_client import BaseHttpClient


class FileResourceHttpClient:
    """HTTP client for file resource operations."""
    
    def __init__(
        self,
        base_url: str,
        session_token: str,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the file resource HTTP client.
        """
        self.client = BaseHttpClient(base_url, session_token, timeout, lf)
        self.logger = lf.get_logger(__name__)
    
    async def get_file_info(self, file_resource_revision_id: int) -> Optional[Dict[str, Any]]:
        """
        Get file resource information.
        """
        try:
            self.logger.info(f"Getting file info for revision: {file_resource_revision_id}")
            
            response = await self.client.get(
                f"/api/v1/file-resource/{file_resource_revision_id}"
            )
            
            if response.get("status") == "SUCCESS":
                return response.get("file_info")
            elif response.get("status") == "NOT_FOUND":
                self.logger.info(f"File not found for revision: {file_resource_revision_id}")
                return None
            else:
                self.logger.error(f"Error getting file info: {response.get('error')}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting file info: {e}")
            return None
    
    async def list_files(
        self, 
        limit: int = 50, 
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        List file resources with optional filters.
        """
        try:
            params = {
                "limit": limit,
                "offset": offset
            }
            
            if filters:
                params.update(filters)
            
            response = await self.client.get("/api/v1/file-resource", params=params)
            
            if response.get("status") == "SUCCESS":
                return response.get("files", [])
            else:
                self.logger.error(f"Error listing files: {response.get('error')}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error listing files: {e}")
            return []
    
    async def get_file_metadata(self, file_resource_revision_id: int) -> Optional[Dict[str, Any]]:
        """
        Get file metadata including labels and properties.
        """
        try:
            response = await self.client.get(
                f"/api/v1/file-resource/{file_resource_revision_id}/metadata"
            )
            
            if response.get("status") == "SUCCESS":
                return response.get("metadata")
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting file metadata: {e}")
            return None
    
    async def update_file_labels(
        self, 
        file_resource_revision_id: int, 
        labels: List[str]
    ) -> Dict[str, Any]:
        """
        Update file resource labels.
        """
        try:
            response = await self.client.post(
                f"/api/v1/file-resource/{file_resource_revision_id}/labels",
                json_data={"labels": labels}
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error updating file labels: {e}")
            return {"status": "ERROR", "error": str(e)}
    
    @classmethod
    def from_config(
        cls,
        base_url: str,
        session_token: str,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> "FileResourceHttpClient":
        """
        Create FileResourceHttpClient from configuration.
        """
        return cls(
            base_url=base_url,
            session_token=session_token,
            timeout=timeout,
            lf=lf
        )
