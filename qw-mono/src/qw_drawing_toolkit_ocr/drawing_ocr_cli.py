"""
Command-line interface for the Drawing OCR Service.
This CLI allows users to process drawings with OCR from the command line.
"""
import argparse
import json
import re
import sys
import time
from pathlib import Path, PurePosixPath
from typing import Any, Dict, List, Optional, Union

from qw_basic_s3.schema import S3StorageSchema
from qw_drawing_toolkit_ocr.drawing_ocr_service import DrawingOcrService, TileInfo
from qw_log.factory import QwLogFactory

# Import based on whether we're running from the mono repo or standalone
try:
    from qw_basic_s3.builder import S3StorageConfig
    from qw_basic_s3.impl.minio import MinioS3StorageConfig
except ImportError:
    sys.path.append(str(Path(__file__).parent.parent.parent))
    from qw_basic_s3.builder import S3StorageConfig
    from qw_basic_s3.impl.minio import MinioS3StorageConfig


def setup_logger() -> QwLogFactory:
    """Set up and configure logger"""
    log_factory = QwLogFactory("0.1.0")
    QwLogFactory.init_logs()
    return log_factory


def create_s3_client(args: argparse.Namespace) -> Any:
    """Create an S3 client based on CLI arguments"""

    # Create S3 storage config
    s3_config = S3StorageConfig(
        backend=MinioS3StorageConfig(
            host=args.host, access_key=args.access_key, secret_key=args.secret_key, secure=not args.insecure
        ),
        verify_buckets=True,
        create_buckets_if_not_exist=False,
    )

    schema = S3StorageSchema.create_simple([args.bucket])

    return s3_config.build(schema, lf=setup_logger())


def extract_tile_info_from_path(path_str: str) -> Optional[Dict[str, int]]:
    """
    Extract tile coordinates from path string.
    Expected format: something/X_Y.webp or something/X_Y.png
    """
    # Extract the filename from the path
    filename = path_str.split("/")[-1]

    # Extract X and Y coordinates from filename
    pattern = r"(\d+)_(\d+)\.(webp|png)"
    match = re.match(pattern, filename)

    if match:
        tile_x = int(match.group(1))
        tile_y = int(match.group(2))
        return {
            "tile_x": tile_x,
            "tile_y": tile_y,
        }

    return None


def list_tiles_from_s3(s3_client: Any, bucket: str, prefix: str) -> List[Dict[str, Union[str, int]]]:
    """
    List all tiles in a given S3 path and extract their information.

    Args:
        s3_client: S3Storage instance
        bucket: Bucket name
        prefix: Path prefix in the bucket (e.g., 'file_resources/123/v1/page_0_tiles/dpi150/')
    """
    logger = setup_logger().get_logger("s3_tile_lister")
    logger.info(f"Listing tiles in {bucket}/{prefix}")

    tiles: List[Dict[str, Union[str, int]]] = []

    # List objects in the bucket with the given prefix
    for obj in s3_client.list_objects(bucket, PurePosixPath(prefix), recursive=True):
        # Skip directories
        if obj.is_dir:
            continue

        path_str = str(obj.path)
        tile_info = extract_tile_info_from_path(path_str)

        if tile_info:
            # Add additional information
            tile_info_with_id: Dict[str, Union[str, int]] = {**tile_info, "tile_obj_id": path_str}  # Use path as ID
            tiles.append(tile_info_with_id)

    logger.info(f"Found {len(tiles)} tiles")
    return tiles


def save_args_to_file(args: argparse.Namespace, output_dir: Path) -> None:
    """Save CLI arguments to a file for reference"""
    args_dict = vars(args)
    with open(output_dir / "cli_args.json", "w") as f:
        json.dump(args_dict, f, indent=2)


def convert_to_tile_info(data: List[Dict[str, Any]]) -> List[TileInfo]:
    """Convert dictionary data to TileInfo objects for type safety"""
    result: List[TileInfo] = []
    for item in data:
        tile_info = TileInfo(
            tile_obj_id=item["tile_obj_id"],
            tile_x=item["tile_x"],
            tile_y=item["tile_y"],
        )
        result.append(tile_info)
    return result


def main() -> int:
    """Main entry point for CLI
    Example use in CLI:
    ```
    python -m qw_drawing_toolkit_ocr.drawing_ocr_cli
    --bucket tenant-quality-manufacturing
    --tile-prefix file_resources/b56f4c8f-7e4c-4daf-966d-c04f85deffc7/v1/page_0_tiles/dpi300/
    --save-images-for-debugging
    ```
    """
    parser = argparse.ArgumentParser(description="OCR for technical drawings stored in S3")

    # S3 connection parameters
    parser.add_argument("--host", default="localhost:9000", help="S3 host (e.g., minio.example.com:9000)")
    parser.add_argument("--access-key", default="minio_admin", help="S3 access key")
    parser.add_argument("--secret-key", default="minio_admin", help="S3 secret key")
    parser.add_argument("--insecure", action="store_true", default=True, help="Use HTTP instead of HTTPS")
    parser.add_argument("--bucket", required=True, help="S3 bucket containing the tiles")

    # Tile information
    parser.add_argument(
        "--tile-prefix",
        required=True,
        help="S3 prefix for tile directory (e.g., 'file_resources/uuid/v1/page_0_tiles/dpi150/')",
    )
    parser.add_argument(
        "--tile-file",
        help="Optional JSON file containing tile information (if not provided, tiles will be listed from S3)",
    )

    # OCR parameters
    parser.add_argument("--group-width", type=int, default=2, help="Tile group width (default: 2)")
    parser.add_argument("--group-height", type=int, default=2, help="Tile group height (default: 2)")
    parser.add_argument("--slide-x", type=int, default=1, help="Sliding step in x-direction (default: 1)")
    parser.add_argument("--slide-y", type=int, default=1, help="Sliding step in y-direction (default: 1)")

    # Output and debug options
    parser.add_argument("--save_images_for_debugging", action="store_true", help="Save images")
    parser.add_argument("--save_json_for_debugging", action="store_true", help="Save results")

    args = parser.parse_args()

    log_factory = setup_logger()
    logger = log_factory.get_logger("drawing_ocr_cli")

    output_dir = Path("./ocr_results")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Save arguments for reference
    save_args_to_file(args, output_dir)

    try:
        s3_client = create_s3_client(args)

        raw_tile_infos = list_tiles_from_s3(s3_client, args.bucket, args.tile_prefix)

        tile_infos = convert_to_tile_info(raw_tile_infos)

        if not tile_infos:
            logger.error("No tiles found. Please check the bucket and prefix.")
            sys.exit(1)

        # Save tile information for debugging
        with open(output_dir / "tile_infos.json", "w") as f:
            # Convert TileInfo objects back to dictionaries for JSON serialization
            serializable_infos = [ti.model_dump() for ti in tile_infos]
            json.dump(serializable_infos, f, indent=2)

        # Create Drawing OCR Service
        ocr_service = DrawingOcrService(
            s3_storage=s3_client,
            logger=log_factory.get_logger("drawing_ocr_service"),
            group_width=args.group_width,
            group_height=args.group_height,
            slide_step_x=args.slide_x,
            slide_step_y=args.slide_y,
            save_images_for_debugging=args.save_images_for_debugging,
            save_json_for_debugging=args.save_json_for_debugging,
        )

        # Process the drawing
        start_time = time.time()

        # Process the drawing with the base image
        result = ocr_service.process_drawing(
            bucket=args.bucket,
            tile_infos=tile_infos,
        )

        elapsed = time.time() - start_time
        logger.info(f"OCR processing completed in {elapsed:.2f}s with {len(result.results)} detections")

    except Exception as e:
        logger.error(f"Error: {str(e)}", exc_info=e)
        sys.exit(1)

    return 0


if __name__ == "__main__":
    sys.exit(main())
