from datetime import datetime, timedelta, timezone

from joserfc import jwk, jwt
from pytest import fixture

from qw_basic_keycloak.openid.inspector import TokenInspector
from qw_basic_keycloak.openid.session import SessionTokenGateway


@fixture(scope="session")
def inspector() -> TokenInspector:
    key = jwk.RSAKey.generate_key()
    return TokenInspector(key, "https://idp.org")


@fixture(scope="function")
def sample_at(inspector: TokenInspector) -> jwt.Token:
    ts_now = datetime.now(timezone.utc)
    header = {
        "alg": "RS256",
        "typ": "JWT",
    }
    claims = {
        "exp": int((ts_now + timedelta(minutes=5)).timestamp()),
        "iat": int(ts_now.timestamp()),
        "auth_time": int(ts_now.timestamp()),
        "jti": "0c8c78e0-1149-4157-b9a2-3c0ccf144f0a",
        "iss": inspector.trusted_idp,
        "aud": "some-audience",
        "sub": "3289cd64-463f-4ebe-ad56-6e51521b8dfd",
        "typ": "Bearer",
        "azp": "some-client",
        "acr": "1",
        "scope": "openid email profile",
        "session_state": "737b4d17-101d-4d36-8bdb-1ea62b677a93",
        "sid": "737b4d17-101d-4d36-8bdb-1ea62b677a93",
        "given_name": "John",
        "family_name": "Doe",
        "email_verified": True,
        "email": "<EMAIL>",
        "qw_groups": ["/tenant/huber_meier_co_kg"],
        "qw_roles": ["TenantAdmin"],
    }
    return jwt.Token(header, claims)


@fixture(scope="session")
def gateway() -> SessionTokenGateway:
    return SessionTokenGateway(salt=31)


class TestOpenIdTooling(object):
    def test_inspector_with_proper_token(self, inspector: TokenInspector, sample_at: jwt.Token) -> None:
        token_b64 = jwt.encode(sample_at.header, sample_at.claims, inspector.key_data)
        assert inspector.validate_token(token_b64) is not None

    def test_inspector_fails_on_wrong_signature(self, inspector: TokenInspector) -> None:
        another_key = jwk.RSAKey.generate_key()
        assert another_key != inspector.key_data
        token_b64 = jwt.encode({"alg": "RS256"}, {"iss": inspector.trusted_idp}, another_key)
        assert inspector.validate_token(token_b64) is None

    def test_inspector_fails_on_bad_dict(self, inspector: TokenInspector, sample_at: jwt.Token) -> None:
        sample_at.claims["iss"] = {"roles": {"nested": {"further": {"than": "expected"}}}}
        token_b64 = jwt.encode(sample_at.header, sample_at.claims, inspector.key_data)
        assert inspector.validate_token(token_b64) is None

    def test_inspector_fails_on_untrusted_idp(self, inspector: TokenInspector, sample_at: jwt.Token) -> None:
        another_idp = "https://idp2.org"
        assert another_idp != inspector.trusted_idp
        sample_at.claims["iss"] = another_idp
        token_b64 = jwt.encode(sample_at.header, sample_at.claims, inspector.key_data)
        assert inspector.validate_token(token_b64) is None

    def test_inspector_fails_on_expired_token(self, inspector: TokenInspector, sample_at: jwt.Token) -> None:
        ts_expired = datetime.now(timezone.utc) - timedelta(seconds=1)
        sample_at.claims["exp"] = int(ts_expired.timestamp())
        token_b64 = jwt.encode(sample_at.header, sample_at.claims, inspector.key_data)
        assert inspector.validate_token(token_b64) is None

    def test_inspector_fails_on_bad_token_type(self, inspector: TokenInspector, sample_at: jwt.Token) -> None:
        sample_at.claims["typ"] = "ID"
        token_b64 = jwt.encode(sample_at.header, sample_at.claims, inspector.key_data)
        assert inspector.validate_token(token_b64) is None

    def test_gateway_with_proper_token(self, gateway: SessionTokenGateway) -> None:
        session_id = "my-session-id-123"
        session_token = gateway.create_session_token(session_id)
        session_id_2 = gateway.read_session_token(session_token)
        assert session_id == session_id_2

    def test_gateway_fails_on_different_salt(self, gateway: SessionTokenGateway) -> None:
        session_id = "my-session-id-123"
        gateway2 = SessionTokenGateway(salt=gateway.salt * 7)
        assert gateway.salt != gateway2.salt
        session_token = gateway2.create_session_token(session_id)
        assert session_token != gateway.create_session_token(session_id)
        session_id_2 = gateway.read_session_token(session_token)
        assert session_id_2 is None

    def test_gateway_fails_on_botched_token(self, gateway: SessionTokenGateway) -> None:
        session_id = "my-session-id-123"
        session_token = gateway.create_session_token(session_id)
        session_token_botched_1 = session_token.replace("-", "")
        session_token_botched_2 = session_token + session_token
        session_token_botched_3 = session_token[:-1]
        assert gateway.read_session_token(session_token_botched_1) is None
        assert gateway.read_session_token(session_token_botched_2) is None
        assert gateway.read_session_token(session_token_botched_3) is None

    def test_gateway_fails_on_wrong_checksum(self, gateway: SessionTokenGateway) -> None:
        session_id = "my-session-id-123"
        session_token = gateway.create_session_token(session_id)
        bad_session_token = session_token[:-2] + "ff"
        assert len(session_token) == len(bad_session_token)
        assert session_token != bad_session_token
        assert gateway.read_session_token(bad_session_token) is None
