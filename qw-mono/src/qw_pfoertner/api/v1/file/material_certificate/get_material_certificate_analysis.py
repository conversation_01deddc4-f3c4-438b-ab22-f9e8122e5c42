import json

import falcon

from qw_falcon_openapi.controller import OpenApiPath<PERSON>ontroller
from qw_falcon_openapi.model import OpenApiBaseModel
from qw_falcon_openapi.operation import NoParams, OpenApiOpOutput, OpenApiOpParams
from qw_log_interface import NO_LOG_FACTORY, LogFactory, Logger
from qw_material_certificate_analysis.agents.models import MaterialCertificateAnalysisResponse
from qw_monodb.table.trunk.material_certificate import MaterialCertificateAnalysis
from qw_pfoertner.api.auth import AuthContext, AuthOpenApiOpJsonOut
from qw_pfoertner.api.v1.file.policy.file_resource_revision import get_file_revision_and_eval_policy
from qw_pfoertner.service.session import SessionService
from qw_trunk.service.material_certificate.material_certificate import MaterialCertificateService
from qw_trunk.service.resource.file import FileResourceService
from qw_trunk.service.resource.s3_object import S3ObjectService


class FileResourceRevisionIdParam(OpenApiBaseModel):
    file_resource_revision_id: int


Params = OpenApiOpParams[FileResourceRevisionIdParam, NoParams, NoParams]


class GetMaterialCertificateAnalysis(
    AuthOpenApiOpJsonOut[MaterialCertificateAnalysisResponse, FileResourceRevisionIdParam, NoParams, NoParams]
):
    def __init__(
        self,
        session_service: SessionService,
        file_service: FileResourceService,
        material_cert_service: MaterialCertificateService,
        object_service: S3ObjectService,
        logger: Logger,
    ):
        super().__init__(
            MaterialCertificateAnalysisResponse,
            FileResourceRevisionIdParam,
            NoParams,
            NoParams,
            session_service,
            logger,
        )
        self.file_service = file_service
        self.material_cert_service = material_cert_service
        self.object_service = object_service

    def on_request_after_auth(
        self, auth_ctx: AuthContext, params: Params
    ) -> OpenApiOpOutput[MaterialCertificateAnalysisResponse]:
        file_resource_revision_id = params.path.file_resource_revision_id

        # Check permissions
        get_file_revision_and_eval_policy(
            file_service=self.file_service,
            file_resource_revision_id=file_resource_revision_id,
            auth_ctx=auth_ctx,
            ignore_deleted=False,
        )

        # Get analysis record from S3
        analysis: MaterialCertificateAnalysis | None = (
            self.material_cert_service.db_service.find_material_certificate_analysis(file_resource_revision_id)
        )

        if analysis is None:
            raise falcon.HTTPNotFound()

        if analysis.analysis_obj_id is None:
            raise falcon.HTTPNotFound(description="Analysis result not found")

        analysis_obj = self.object_service.get_object_stream(analysis.analysis_obj_id)
        data = json.load(analysis_obj)

        try:
            # Try to parse as new agentic analysis format
            response = MaterialCertificateAnalysisResponse(**data)
        except Exception as e:
            # Handle parsing failure gracefully - return minimal valid response
            self.logger.warning(
                f"Failed parsing material certificate analysis data: {str(e)}. "
                "The analysis is outdated or in legacy format. Re-run the analysis."
            )
            # Create minimal valid response for legacy/corrupted data
            from qw_material_certificate_analysis.agents.models import CertificateMetadata, ProductType

            minimal_metadata = CertificateMetadata(
                product_type=ProductType.OTHER,
                total_product_count=0,
                batch_numbers=[],
                pages_with_header_details=[],
                pages_with_chemical_composition=[],
                pages_with_mechanical_properties=[],
                analysis_context="Legacy analysis format detected. Please re-run the analysis to get updated results.",
            )
            response = MaterialCertificateAnalysisResponse(metadata=minimal_metadata, products=[])

        return OpenApiOpOutput(response)


class GetMaterialCertificateAnalysisController(OpenApiPathController):
    def __init__(
        self,
        session_service: SessionService,
        file_service: FileResourceService,
        material_cert_service: MaterialCertificateService,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        logger = lf.get_logger(__name__)
        object_service = file_service.s3_service
        super().__init__(
            route="/api/v1/file-resource/material-certificate/revision/{fileResourceRevisionId}/analysis",
            get=GetMaterialCertificateAnalysis(
                session_service, file_service, material_cert_service, object_service, logger
            ),
            logger=logger,
        )
