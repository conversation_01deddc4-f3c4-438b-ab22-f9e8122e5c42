# this openapi specification is autogenerated
openapi: 3.1.0
info:
  title: qw-mono
  version: 0.0.0.0
paths:
  /api/v1/session/login/init:
    post:
      tags:
      - session
      operationId: InitiateLogin
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
        required: true
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodeFlowAuthInit'
      deprecated: false
    parameters: []
  /api/v1/session/login/conclude:
    get:
      tags:
      - session
      operationId: ConcludeLogin
      parameters:
      - name: code
        in: query
        required: true
        deprecated: false
        schema:
          title: Code
          type: string
      - name: state
        in: query
        required: true
        deprecated: false
        schema:
          title: State
          type: string
      responses:
        '302':
          description: User agent redirect if configured in the init request
          content: {}
        '400':
          description: Inconsistent or missing data
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewSession'
      deprecated: false
    parameters: []
  /api/v1/session:
    get:
      tags:
      - session
      operationId: GetSessionInfo
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionInfo'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/session/logout/init:
    post:
      tags:
      - session
      operationId: InitiateLogout
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LogoutRequest'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LogoutResponse'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/tenant/{tenantId}:
    get:
      tags:
      - tenant
      operationId: GetTenant
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '404':
          description: Could not find tenant
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: tenantId
      in: path
      required: true
      deprecated: false
      schema:
        title: Tenantid
        type: integer
  /api/v1/tenant-details:
    get:
      tags:
      - tenant
      operationId: GetTenantDetails
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '404':
          description: Could not find tenant
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TenantExtendedView'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/tenants:
    post:
      tags:
      - tenant
      operationId: ListTenants
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListTenantsInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListTenantsOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/user/whoami:
    get:
      tags:
      - user
      operationId: GetUserWhoami
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WhoamiView'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/user/{userId}:
    get:
      tags:
      - user
      operationId: GetUser
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '404':
          description: Could not find user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: userId
      in: path
      required: true
      deprecated: false
      schema:
        title: Userid
        type: integer
  /api/v1/users:
    post:
      tags:
      - user
      operationId: ListUsers
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListUsersInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListUsersOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/search:
    post:
      tags:
      - search
      operationId: GetSearchMatches
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSearchMatchesInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSearchMatchesOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/file-resource/revision:
    post:
      tags:
      - file-resource
      operationId: AddFileResourceRevision
      parameters: []
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MultipartSchema_api_v1_file-resource_revision'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewFileResourceRevisionOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/file-resource/{fileResourceId}:
    get:
      tags:
      - file-resource
      operationId: GetFileResource
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileResourceView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourceid
        type: integer
  /api/v1/file-resource/revision/{fileResourceRevisionId}:
    get:
      tags:
      - file-resource
      operationId: GetFileResourceRevision
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: Success
          content:
            '*/*': {}
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
  /api/v1/file-resources:
    post:
      tags:
      - file-resource
      operationId: ListFileResources
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListFileResourcesInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListFileResourcesOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/file-resources-metadata:
    post:
      tags:
      - file-resource
      operationId: ListFileResourcesMetadata
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListFileResourcesMetadataInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListFileResourcesMetadataOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/file-resource/drawing/revision/{fileResourceRevisionId}/analysis:
    get:
      tags:
      - file-resource
      operationId: GetDrawingRevisionAnalysis
      parameters:
      - name: pageIndex
        in: query
        required: false
        deprecated: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          default: null
          title: Pageindex
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TechnicalDrawingAnalysisResult'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
  /api/v1/file-resource/drawing/revision/{fileResourceRevisionId}/analysis/status:
    get:
      tags:
      - file-resource
      operationId: GetDrawingRevisionAnalysisStatus
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TechnicalDrawingAnalysisStatus'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
  /api/v1/file-resource/drawing/revision/{fileResourceRevisionId}/page/{pageIndex}/image:
    get:
      tags:
      - file-resource
      operationId: GetDrawingRevisionPageImage
      parameters:
      - name: tileX
        in: query
        required: false
        deprecated: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          default: null
          title: Tilex
      - name: tileY
        in: query
        required: false
        deprecated: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          default: null
          title: Tiley
      - name: dpi
        in: query
        required: false
        deprecated: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          default: null
          title: Dpi
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: Success
          content:
            image/png: {}
            image/webp: {}
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
    - name: pageIndex
      in: path
      required: true
      deprecated: false
      schema:
        title: Pageindex
        type: integer
  /api/v1/file-resource/drawing/revision/{fileResourceRevisionId}/page/{pageIndex}/tiles:
    get:
      tags:
      - file-resource
      operationId: GetDrawingRevisionPageTiles
      parameters:
      - name: dpi
        in: query
        required: true
        deprecated: false
        schema:
          title: Dpi
          type: integer
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDrawingRevisionPageTilesOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
    - name: pageIndex
      in: path
      required: true
      deprecated: false
      schema:
        title: Pageindex
        type: integer
  /api/v1/file-resource/material-certificate/revision/{fileResourceRevisionId}/analysis:
    get:
      tags:
      - file-resource
      operationId: GetMaterialCertificateAnalysis
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaterialCertificateAnalysisResponse'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
  /api/v1/file-resource/material-certificate/revision/{fileResourceRevisionId}/page/{pageIndex}/image:
    get:
      tags:
      - file-resource
      operationId: GetMaterialCertificatePageImage
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: Success
          content:
            image/png: {}
            image/webp: {}
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
    - name: pageIndex
      in: path
      required: true
      deprecated: false
      schema:
        title: Pageindex
        type: integer
  /api/v1/file-resource/drawing-discussion:
    post:
      tags:
      - file-resource
      operationId: AddDrawingDiscussion
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NewDrawingDiscussionInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewDrawingDiscussionOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/file-resource/drawing-discussion/{drawingDiscussionId}:
    get:
      tags:
      - file-resource
      operationId: GetDrawingDiscussion
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DrawingDiscussionView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: drawingDiscussionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Drawingdiscussionid
        type: integer
  /api/v1/file-resource/drawing-discussion/{drawingDiscussionId}/status:
    post:
      tags:
      - file-resource
      operationId: SetDrawingDiscussionStatus
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetDrawingDiscussionStatusInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SetDrawingDiscussionStatusOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: drawingDiscussionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Drawingdiscussionid
        type: integer
  /api/v1/file-resource/drawing-discussions:
    post:
      tags:
      - file-resource
      operationId: ListDrawingDiscussions
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListDrawingDiscussionsInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListDrawingDiscussionsOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/file-resource/{fileResourceId}/rename:
    post:
      tags:
      - file-resource
      operationId: RenameFileResource
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FileResourceRenameInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FileResourceRenameOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourceid
        type: integer
  /api/v1/file-resources/delete:
    post:
      tags:
      - file-resource
      operationId: DeleteFileResources
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteFileResourcesInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteFileResourcesOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/file-resource/revision/{fileResourceRevisionId}/download:
    get:
      tags:
      - file-resource
      operationId: GetFileResourceDownload
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: Success
          content:
            '*/*': {}
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
  /api/v1/file-resource/revision/share:
    post:
      tags:
      - file-resource
      operationId: ToggleFileResourceRevisionAccess
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ToggleFileResourceRevisionAccessInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ToggleFileResourceRevisionAccessOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/file-resource-revision/link-to-material:
    post:
      tags:
      - file-resource
      operationId: LinkFileToMaterial
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LinkFileToMaterialInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LinkFileToMaterialOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/file-resource-revision/unlink-from-material:
    post:
      tags:
      - file-resource
      operationId: UnlinkFileFromMaterial
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UnlinkFileFromMaterialInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnlinkFileFromMaterialOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/material/{materialId}:
    get:
      tags:
      - material
      operationId: GetMaterial
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaterialView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: materialId
      in: path
      required: true
      deprecated: false
      schema:
        title: Materialid
        type: integer
  /api/v1/materials:
    post:
      tags:
      - material
      operationId: ListMaterials
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListMaterialsInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListMaterialsOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/order-line:
    post:
      tags:
      - order
      operationId: AddOrderLine
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NewOrderLineInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewOrderLineOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/order/{orderId}:
    get:
      tags:
      - order
      operationId: GetOrder
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: orderId
      in: path
      required: true
      deprecated: false
      schema:
        title: Orderid
        type: integer
  /api/v1/order-line/{orderLineId}:
    get:
      tags:
      - order
      operationId: GetOrderLine
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderLineView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: orderLineId
      in: path
      required: true
      deprecated: false
      schema:
        title: Orderlineid
        type: integer
  /api/v1/orders:
    post:
      tags:
      - order
      operationId: ListOrders
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListOrdersInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListOrdersOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/orders/delete:
    post:
      tags:
      - order
      operationId: DeleteOrders
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteOrdersInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteOrdersOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/order-lines/delete:
    post:
      tags:
      - order
      operationId: DeleteOrderLines
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteOrderLinesInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteOrderLinesOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/purchase-order-connections:
    get:
      tags:
      - order
      operationId: GetPurchaseOrderConnections
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrderConnectionsView'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/order-line/{orderLineId}/task:
    post:
      tags:
      - order
      operationId: AddOrderLineTask
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NewOrderLineTaskInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewOrderLineTaskOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: orderLineId
      in: path
      required: true
      deprecated: false
      schema:
        title: Orderlineid
        type: integer
  /api/v1/order-line/{orderLineId}/tasks:
    get:
      tags:
      - order
      operationId: GetOrderLineTasks
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderLineTasksView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: orderLineId
      in: path
      required: true
      deprecated: false
      schema:
        title: Orderlineid
        type: integer
  /api/v1/order-line-task/inspection/{orderLineInspectionTaskId}/due-date:
    post:
      tags:
      - order
      operationId: UpdateOrderLineInspectionDueDate
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateOrderLineTaskDueDateInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateOrderLineTaskDueDateOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: orderLineInspectionTaskId
      in: path
      required: true
      deprecated: false
      schema:
        title: Orderlineinspectiontaskid
        type: integer
  /api/v1/order-line/{orderLineId}/requirements/update:
    post:
      tags:
      - order
      operationId: UpdateOrderLineRequirements
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateOrderLineRequirementsInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateOrderLineRequirementsOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: orderLineId
      in: path
      required: true
      deprecated: false
      schema:
        title: Orderlineid
        type: integer
  /api/v1/inspection-plan:
    post:
      tags:
      - inspection
      operationId: AddInspectionPlan
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NewInspectionPlanInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewInspectionPlanOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/inspection-plan/{inspectionPlanId}:
    get:
      tags:
      - inspection
      operationId: GetInspectionPlan
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InspectionPlanView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: inspectionPlanId
      in: path
      required: true
      deprecated: false
      schema:
        title: Inspectionplanid
        type: integer
  /api/v1/inspection-plans:
    post:
      tags:
      - inspection
      operationId: ListInspectionPlans
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListInspectionPlansInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListInspectionPlansOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/inspection-plans-metadata:
    post:
      tags:
      - inspection
      operationId: ListInspectionPlansMetadata
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListInspectionPlansMetadataInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListInspectionPlansMetadataOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/inspection-action-result:
    post:
      tags:
      - inspection
      operationId: AddInspectionActionResult
      parameters: []
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MultipartSchema_api_v1_inspection-action-result'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewInspectionActionResultOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/inspection/{inspectionId}:
    get:
      tags:
      - inspection
      operationId: GetInspection
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InspectionView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: inspectionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Inspectionid
        type: integer
  /api/v1/inspection/{inspectionId}/finish:
    get:
      tags:
      - inspection
      operationId: FinishInspection
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FinishInspectionOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: inspectionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Inspectionid
        type: integer
  /api/v1/inspection/{inspectionId}/result/image:
    get:
      tags:
      - inspection
      operationId: GetInspectionResultStepImage
      parameters:
      - name: stepIndex
        in: query
        required: true
        deprecated: false
        schema:
          title: Stepindex
          type: integer
      - name: actionIndex
        in: query
        required: true
        deprecated: false
        schema:
          title: Actionindex
          type: integer
      - name: sampleIndex
        in: query
        required: false
        deprecated: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          default: null
          title: Sampleindex
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: Success
          content:
            image/jpeg: {}
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: inspectionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Inspectionid
        type: integer
  /api/v1/inspection/{inspectionId}/result/video:
    get:
      tags:
      - inspection
      operationId: GetInspectionResultStepVideo
      parameters:
      - name: stepIndex
        in: query
        required: true
        deprecated: false
        schema:
          title: Stepindex
          type: integer
      - name: actionIndex
        in: query
        required: true
        deprecated: false
        schema:
          title: Actionindex
          type: integer
      - name: sampleIndex
        in: query
        required: false
        deprecated: false
        schema:
          anyOf:
          - type: integer
          - type: 'null'
          default: null
          title: Sampleindex
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: Success
          content:
            video/mp4: {}
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: inspectionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Inspectionid
        type: integer
  /api/v1/inspection/{inspectionId}/result/pdf:
    post:
      tags:
      - inspection
      operationId: GetInspectionResultAsPDF
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateInspectionPdfInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '412':
          description: Inspection is not finished yet
          content: {}
        '200':
          description: Success
          content:
            application/pdf: {}
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: inspectionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Inspectionid
        type: integer
  /api/v1/inspections:
    post:
      tags:
      - inspection
      operationId: ListInspections
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListInspectionsInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListInspectionsOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/inspection-plan/{inspectionPlanId}/overwrite:
    post:
      tags:
      - inspection
      operationId: UpdateInspectionPlan
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInspectionPlanInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateInspectionPlanOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: inspectionPlanId
      in: path
      required: true
      deprecated: false
      schema:
        title: Inspectionplanid
        type: integer
  /api/v1/inspections/delete:
    post:
      tags:
      - inspection
      operationId: DeleteInspections
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteInspectionsInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteInspectionsOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/inspection-plans/delete:
    post:
      tags:
      - inspection
      operationId: DeleteInspectionPlans
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteInspectionPlansInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteInspectionPlansOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/agent/process-prompt:
    post:
      tags:
      - agent
      operationId: ProcessAgentPrompt
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AgentPromptRequest'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AgentPromptResponse'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/chat/{chatId}/messages:
    post:
      tags:
      - chat
      operationId: GetChatMessages
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatGetMessagesInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatGetMessagesOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: chatId
      in: path
      required: true
      deprecated: false
      schema:
        title: Chatid
        type: integer
  /api/v1/chat/messages/{messageId}/edit:
    post:
      tags:
      - chat
      operationId: EditChatMessage
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatEditMessageInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatEditMessageOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: messageId
      in: path
      required: true
      deprecated: false
      schema:
        title: Messageid
        type: integer
  /api/v1/chat/{chatId}/messages/add:
    post:
      tags:
      - chat
      operationId: AddChatMessage
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatAddMessageInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatAddMessageOutput'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: chatId
      in: path
      required: true
      deprecated: false
      schema:
        title: Chatid
        type: integer
  /api/v1/wopi/internal/discovery:
    get:
      tags:
      - wopi
      operationId: DiscoverHosting
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WopiDiscoveryView'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/wopi/internal/files/{fileResourceRevisionId}/access-token:
    get:
      tags:
      - wopi
      operationId: GetWopiAccessToken
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WopiAccessTokenView'
      security:
      - session_token: []
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
  /api/v1/wopi/files/{fileResourceRevisionId}:
    get:
      tags:
      - wopi
      operationId: CheckFileInfo
      parameters:
      - name: access_token
        in: query
        required: true
        deprecated: false
        schema:
          title: Access Token
          type: string
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WopiCheckFileInfoResponse'
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
  /api/v1/wopi/files/{fileResourceRevisionId}/contents:
    get:
      tags:
      - wopi
      operationId: GetFile
      parameters:
      - name: access_token
        in: query
        required: true
        deprecated: false
        schema:
          title: Access Token
          type: string
      responses:
        '200':
          description: Success
          content:
            '*/*': {}
      deprecated: false
    post:
      tags:
      - wopi
      operationId: PutFile
      parameters:
      - name: x-cool-wopi-ismodifiedbyuser
        in: header
        required: false
        deprecated: false
        schema:
          default: false
          title: X-Cool-Wopi-Ismodifiedbyuser
          type: boolean
      - name: x-cool-wopi-isautosave
        in: header
        required: false
        deprecated: false
        schema:
          default: false
          title: X-Cool-Wopi-Isautosave
          type: boolean
      - name: x-cool-wopi-isexitsave
        in: header
        required: false
        deprecated: false
        schema:
          default: false
          title: X-Cool-Wopi-Isexitsave
          type: boolean
      - name: access_token
        in: query
        required: true
        deprecated: false
        schema:
          title: Access Token
          type: string
      requestBody:
        content:
          '*/*':
            schema:
              type: string
              format: binary
        required: true
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WopiPutFileResponse'
      deprecated: false
    parameters:
    - name: fileResourceRevisionId
      in: path
      required: true
      deprecated: false
      schema:
        title: Fileresourcerevisionid
        type: integer
  /api/v1/tolerances:
    get:
      tags:
      - tolerances
      operationId: GetTolerances
      parameters: []
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TolerancesView'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/v1/tolerance:
    post:
      tags:
      - tolerances
      operationId: ToleranceLookup
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ToleranceLookupInput'
        required: true
      responses:
        '401':
          description: Session parameter is missing or invalid in some way
          content: {}
        '403':
          description: Requested resource or action is not available for this user
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ToleranceLookupOutput'
      security:
      - session_token: []
      deprecated: false
    parameters: []
  /api/info:
    get:
      tags:
      - internal
      operationId: GetInfo
      parameters: []
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InfoOutput'
      deprecated: false
    parameters: []
  /api/health:
    get:
      tags:
      - internal
      operationId: GetHealth
      parameters: []
      responses:
        '404':
          description: Service is not healthy
          content: {}
        '200':
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthOutput'
      deprecated: false
    parameters: []
  /api/openapi.json:
    get:
      tags:
      - spec
      operationId: GetOpenApiSpecjson
      parameters: []
      responses:
        '200':
          description: Spec generated
          content:
            application/json: {}
      deprecated: false
    parameters: []
  /api/openapi.yaml:
    get:
      tags:
      - spec
      operationId: GetOpenApiSpecyaml
      parameters: []
      responses:
        '200':
          description: Spec generated
          content:
            application/yaml: {}
      deprecated: false
    parameters: []
  /api/openapi.html:
    get:
      tags:
      - spec
      operationId: GetRenderedOpenApiSpec
      parameters: []
      responses:
        '200':
          description: Spec generated
          content:
            text/html: {}
      deprecated: false
    parameters: []
components:
  schemas:
    AgentAction:
      description: '[schema: AgentAction] Model for an action to be executed by the
        frontend.'
      properties:
        action_type:
          description: Type of action to perform
          title: Action Type
          type: string
        component:
          description: Component that should handle the action
          title: Component
          type: string
        parameters:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          default: null
          description: Parameters for the action
          title: Parameters
      required:
      - action_type
      - component
      title: AgentAction
      type: object
    AgentPromptRequest:
      description: '[schema: AgentPromptRequest] Request model for agent prompt processing.'
      properties:
        prompt:
          description: User prompt text
          title: Prompt
          type: string
        session_id:
          anyOf:
          - format: uuid
            type: string
          - type: 'null'
          default: null
          description: Session ID to continue an existing conversation. If None, a
            new session will be created.
          title: Session Id
        context:
          anyOf:
          - additionalProperties: true
            type: object
          - type: 'null'
          default: null
          description: Additional context (e.g., UI state)
          title: Context
      required:
      - prompt
      title: AgentPromptRequest
      type: object
    AgentPromptResponse:
      description: '[schema: AgentPromptResponse] Response model for agent prompt
        processing.'
      properties:
        message:
          description: Text response to display to the user
          title: Message
          type: string
        session_id:
          description: Session ID for this conversation
          format: uuid
          title: Session Id
          type: string
        actions:
          anyOf:
          - items:
              $ref: '#/components/schemas/AgentAction'
            type: array
          - type: 'null'
          default: null
          description: Actions to be executed by the frontend
          title: Actions
      required:
      - message
      - session_id
      title: AgentPromptResponse
      type: object
    AngleDetail:
      description: '[schema: AngleDetail] Details specific to angular measurements.'
      properties:
        nominal_value:
          description: Primary measurement value - if type is chamfer, this is the
            length value
          title: Nominal Value
          type: number
        lower_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Lower tolerance value
          title: Lower Tol
        upper_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Upper tolerance value
          title: Upper Tol
        unit:
          $ref: '#/components/schemas/Unit'
          description: Unit of measurement
        raw_callout:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Raw callout text
          title: Raw Callout
        type:
          const: ANGLE
          default: ANGLE
          title: Type
          type: string
      required:
      - nominal_value
      - unit
      title: AngleDetail
      type: object
    AuthenticationRequest:
      description: '[schema: AuthenticationRequest] https://openid.net/specs/openid-connect-core-1_0.html#AuthRequest'
      properties:
        scope:
          default: openid
          title: Scope
          type: string
        response_type:
          default: code
          enum:
          - code
          - none
          title: Response Type
          type: string
        client_id:
          title: Client Id
          type: string
        redirect_uri:
          format: uri
          minLength: 1
          title: Redirect Uri
          type: string
        state:
          title: State
          type: string
        nonce:
          title: Nonce
          type: string
      required:
      - client_id
      - redirect_uri
      - state
      - nonce
      title: AuthenticationRequest
      type: object
    CertificateMetadata:
      description: '[schema: CertificateMetadata] Comprehensive metadata collected
        by the metadata agent with chat history.

        This builds context progressively and enables chitchat benefits.'
      properties:
        product_type:
          $ref: '#/components/schemas/ProductType'
          description: Type of product this certificate covers
        total_product_count:
          description: Total number of individual products/items in the certificate
          title: Total Product Count
          type: integer
        batch_numbers:
          description: List of all batch numbers found in the certificate
          items:
            type: string
          title: Batch Numbers
          type: array
        pages_with_header_details:
          description: Page numbers containing header/batch details
          items:
            type: integer
          title: Pages With Header Details
          type: array
        pages_with_chemical_composition:
          description: Page numbers containing chemical composition data
          items:
            type: integer
          title: Pages With Chemical Composition
          type: array
        pages_with_mechanical_properties:
          description: Page numbers containing mechanical properties
          items:
            type: integer
          title: Pages With Mechanical Properties
          type: array
        analysis_context:
          description: Conversational context about the certificate structure, e.g.,
            'I found 2 steel plates on pages 1-2, with chemical data on page 1 and
            mechanical properties on page 2'
          title: Analysis Context
          type: string
      required:
      - product_type
      - total_product_count
      - batch_numbers
      - pages_with_header_details
      - pages_with_chemical_composition
      - pages_with_mechanical_properties
      - analysis_context
      title: CertificateMetadata
      type: object
    ChamferDetail:
      description: '[schema: ChamferDetail] Details specific to chamfers.'
      properties:
        nominal_value:
          description: Primary measurement value - if type is chamfer, this is the
            length value
          title: Nominal Value
          type: number
        lower_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Lower tolerance value
          title: Lower Tol
        upper_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Upper tolerance value
          title: Upper Tol
        unit:
          $ref: '#/components/schemas/Unit'
          description: Unit of measurement
        raw_callout:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Raw callout text
          title: Raw Callout
        type:
          const: CHAMFER
          default: CHAMFER
          title: Type
          type: string
        angle:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Angle of the chamfer
          title: Angle
        length:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Length of the chamfer
          title: Length
        length_unit:
          anyOf:
          - $ref: '#/components/schemas/Unit'
          - type: 'null'
          default: null
          description: Unit for length measurement
      required:
      - nominal_value
      - unit
      title: ChamferDetail
      type: object
    ChatAddMessageInput:
      description: "[schema: ChatAddMessageInput] Parameters required to add a new\
        \ message to a chat.\n\nAttributes:\n    content (str): The textual content\
        \ of the message.\n    ts_sent (datetime): The timestamp indicating when the\
        \ message was sent."
      properties:
        content:
          title: Content
          type: string
        tsSent:
          format: date-time
          title: Tssent
          type: string
      required:
      - content
      - tsSent
      title: ChatAddMessageInput
      type: object
    ChatAddMessageOutput:
      description: "[schema: ChatAddMessageOutput] Parameters required to add a new\
        \ message to a chat.\n\nAttributes:\n    message_id Optional[int]: the unique\
        \ identifier of the message that has been created."
      properties:
        messageId:
          title: Messageid
          type: integer
      required:
      - messageId
      title: ChatAddMessageOutput
      type: object
    ChatEditMessageInput:
      description: "[schema: ChatEditMessageInput] Represents the input parameters\
        \ required to edit a chat message.\n\nAttributes:\n    content (str): The\
        \ new content of the chat message. This is what the message will be updated\
        \ to.\n    ts_sent (Optional[datetime]): The timestamp representing when the\
        \ message was sent. If not provided,\n                                  the\
        \ current UTC time will be used. This can be useful for keeping track\n  \
        \                                of when edits were made to the message."
      properties:
        content:
          title: Content
          type: string
        tsSent:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          title: Tssent
      required:
      - content
      - tsSent
      title: ChatEditMessageInput
      type: object
    ChatEditMessageOutput:
      properties: {}
      title: ChatEditMessageOutput
      type: object
      description: '[schema: ChatEditMessageOutput]'
    ChatGetMessagesInput:
      description: "[schema: ChatGetMessagesInput] Parameters for querying chat messages\
        \ with optional filters for timestamps, pagination, and sorting.\n\nAttributes:\n\
        \    chat_id (int): The unique identifier of the chat from which messages\
        \ will be retrieved.\n                   This ID is used to specify which\
        \ chat's messages should be fetched.\n\n    count (int): The maximum number\
        \ of chat messages to retrieve. This parameter controls\n                \
        \ pagination by limiting the number of messages returned in a single request.\n\
        \n    offset (int): The offset from the start of the message list, used for\
        \ pagination. For example,\n                  if offset is 10, the list of\
        \ messages will start from the 11th message in the dataset.\n\n    ts_after\
        \ (Optional[datetime]): A timestamp filter to retrieve only messages sent\
        \ after this time.\n                                   If provided, only messages\
        \ with a sent timestamp greater than this\n                              \
        \     value will be included in the response. Default is None, meaning no\
        \ lower time\n                                   bound.\n\n    ts_before (Optional[datetime]):\
        \ A timestamp filter to retrieve only messages sent before this time.\n  \
        \                                  If provided, only messages with a sent\
        \ timestamp less than this value\n                                    will\
        \ be included in the response. Default is None, meaning no upper time bound.\n\
        \n    newest_first (bool): Determines the order of the messages in the response.\
        \ If True, messages will be\n                         ordered from newest\
        \ to oldest. If False, messages will be ordered from oldest to newest.\n \
        \                        Default is True, meaning messages are returned in\
        \ descending order of their sent timestamp."
      properties:
        chatId:
          title: Chatid
          type: integer
        count:
          title: Count
          type: integer
        offset:
          title: Offset
          type: integer
        tsAfter:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          default: null
          title: Tsafter
        tsBefore:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          default: null
          title: Tsbefore
        newestFirst:
          default: true
          title: Newestfirst
          type: boolean
      required:
      - chatId
      title: ChatGetMessagesInput
      type: object
    ChatGetMessagesOutput:
      description: "[schema: ChatGetMessagesOutput] Output model for fetching chat\
        \ messages. Extends the general chat request output model to include the specific\
        \ data\nrelated to chat messages retrieval.\n\nAttributes:\n    messages (List[ChatMessageView]):\
        \ A list of chat message views representing the messages fetched from the\
        \ chat.\n    count (int): The total number of messages returned in this response.\n\
        \    ts_before (Optional[datetime]): The timestamp filter to retrieve only\
        \ messages sent before this time.\n    ts_after (Optional[datetime]): The\
        \ timestamp filter to retrieve only messages sent after this time.\n    newest_first\
        \ (str): Indicates the sorting method used to order the messages in the response.\n\
        \                          It can be either 'newest_first' or 'oldest_first'."
      properties:
        messages:
          default: []
          items:
            $ref: '#/components/schemas/ChatMessageView'
          title: Messages
          type: array
        count:
          default: 0
          title: Count
          type: integer
        tsBefore:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          default: null
          title: Tsbefore
        tsAfter:
          anyOf:
          - format: date-time
            type: string
          - type: 'null'
          default: null
          title: Tsafter
        newestFirst:
          default: true
          title: Newestfirst
          type: boolean
      title: ChatGetMessagesOutput
      type: object
    ChatMessageView:
      description: "[schema: ChatMessageView] A view model representing a chat message,\
        \ combining data from both\nthe ChatMessage and ChatMessageContent tables.\n\
        \nAttributes:\n    id (int): The unique identifier of the chat message.\n\
        \    chat_id (int): The unique identifier of the chat to which this message\
        \ belongs.\n    creator_user_id (int): The unique identifier of the user who\
        \ created the message.\n    creator_name (str): The name of the creator\n\
        \    content (str): The textual content of the message.\n    ts_sent (datetime):\
        \ The timestamp indicating when the message was sent.\n\nClass Methods:\n\
        \    from_db_row(cls, msg: ChatMessage, content: ChatMessageContent): Constructs\
        \ a ChatMessageView instance from\n                          database row\
        \ objects."
      properties:
        id:
          title: Id
          type: integer
        chatId:
          title: Chatid
          type: integer
        content:
          title: Content
          type: string
        user:
          $ref: '#/components/schemas/UserView'
        tsSent:
          format: date-time
          title: Tssent
          type: string
      required:
      - id
      - chatId
      - content
      - user
      - tsSent
      title: ChatMessageView
      type: object
    ChemicalComposition:
      description: '[schema: ChemicalComposition] Chemical composition with foreign
        key references.

        Treated like a database table - always includes batch_number and heat_number.'
      properties:
        batch_number:
          description: Batch/item identifier - must match HeaderDetails
          title: Batch Number
          type: string
        heat_number:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Heat/Smelt number - must match HeaderDetails
          title: Heat Number
        C:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Carbon in %
          title: C
        Mn:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Manganese in %
          title: Mn
        Si:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Silicon in %
          title: Si
        P:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Phosphorus in %
          title: P
        S:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Sulfur in %
          title: S
        Cr:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Chromium in %
          title: Cr
        Ni:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Nickel in %
          title: Ni
        Mo:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Molybdenum in %
          title: Mo
        Cu:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Copper in %
          title: Cu
        Al:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Aluminum in %
          title: Al
        V:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Vanadium in %
          title: V
        Ti:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Titanium in %
          title: Ti
        Nb:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Niobium in %
          title: Nb
        B:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Boron in %
          title: B
        N:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Nitrogen in %
          title: N
        Ca:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Calcium in %
          title: Ca
        H:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Hydrogen in %
          title: H
        Sn:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Tin in %
          title: Sn
        As:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Arsenic in %
          title: As
        Ceq:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Carbon equivalent in %
          title: Ceq
      required:
      - batch_number
      title: ChemicalComposition
      type: object
    CodeFlowAuthInit:
      description: '[schema: CodeFlowAuthInit] All information that the user agent
        needs to initiate the authentication. The req needs to be sent encoded as

        application/x-www-form-urlencoded either with GET in the query string or with
        POST in the body.

        https://openid.net/specs/openid-connect-core-1_0.html#AuthRequest

        https://datatracker.ietf.org/doc/html/rfc6749#section-3.1'
      properties:
        auth_url:
          format: uri
          minLength: 1
          title: Auth Url
          type: string
        req:
          $ref: '#/components/schemas/AuthenticationRequest'
      required:
      - auth_url
      - req
      title: CodeFlowAuthInit
      type: object
    DeleteFileResourcesInput:
      properties:
        fileResourceIds:
          items:
            type: integer
          maxItems: 30
          minItems: 1
          title: Fileresourceids
          type: array
      required:
      - fileResourceIds
      title: DeleteFileResourcesInput
      type: object
      description: '[schema: DeleteFileResourcesInput]'
    DeleteFileResourcesOutput:
      properties: {}
      title: DeleteFileResourcesOutput
      type: object
      description: '[schema: DeleteFileResourcesOutput]'
    DeleteInspectionPlansInput:
      properties:
        inspectionPlanIds:
          items:
            type: integer
          maxItems: 30
          minItems: 1
          title: Inspectionplanids
          type: array
      required:
      - inspectionPlanIds
      title: DeleteInspectionPlansInput
      type: object
      description: '[schema: DeleteInspectionPlansInput]'
    DeleteInspectionPlansOutput:
      properties: {}
      title: DeleteInspectionPlansOutput
      type: object
      description: '[schema: DeleteInspectionPlansOutput]'
    DeleteInspectionsInput:
      properties:
        inspectionIds:
          items:
            type: integer
          maxItems: 30
          minItems: 1
          title: Inspectionids
          type: array
      required:
      - inspectionIds
      title: DeleteInspectionsInput
      type: object
      description: '[schema: DeleteInspectionsInput]'
    DeleteInspectionsOutput:
      properties: {}
      title: DeleteInspectionsOutput
      type: object
      description: '[schema: DeleteInspectionsOutput]'
    DeleteOrderLinesInput:
      properties:
        orderLineIds:
          items:
            type: integer
          maxItems: 30
          minItems: 1
          title: Orderlineids
          type: array
      required:
      - orderLineIds
      title: DeleteOrderLinesInput
      type: object
      description: '[schema: DeleteOrderLinesInput]'
    DeleteOrderLinesOutput:
      properties: {}
      title: DeleteOrderLinesOutput
      type: object
      description: '[schema: DeleteOrderLinesOutput]'
    DeleteOrdersInput:
      properties:
        orderIds:
          items:
            type: integer
          maxItems: 30
          minItems: 1
          title: Orderids
          type: array
      required:
      - orderIds
      title: DeleteOrdersInput
      type: object
      description: '[schema: DeleteOrdersInput]'
    DeleteOrdersOutput:
      properties: {}
      title: DeleteOrdersOutput
      type: object
      description: '[schema: DeleteOrdersOutput]'
    DiameterDetail:
      description: '[schema: DiameterDetail] Details specific to diameter measurements.'
      properties:
        nominal_value:
          description: Primary measurement value - if type is chamfer, this is the
            length value
          title: Nominal Value
          type: number
        lower_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Lower tolerance value
          title: Lower Tol
        upper_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Upper tolerance value
          title: Upper Tol
        unit:
          $ref: '#/components/schemas/Unit'
          description: Unit of measurement
        raw_callout:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Raw callout text
          title: Raw Callout
        type:
          const: DIAMETER
          default: DIAMETER
          title: Type
          type: string
        iso286_identifier:
          anyOf:
          - pattern: ^[a-zA-Z]{1,2}[0-9]{1,2}$
            type: string
          - type: 'null'
          default: null
          title: Iso286 Identifier
      required:
      - nominal_value
      - unit
      title: DiameterDetail
      type: object
    DrawingDiscussionView:
      properties:
        id:
          title: Id
          type: integer
        fileResourceId:
          title: Fileresourceid
          type: integer
        chatId:
          title: Chatid
          type: integer
        quad:
          $ref: '#/components/schemas/InspectionDrawingQuad'
        resolved:
          title: Resolved
          type: boolean
      required:
      - id
      - fileResourceId
      - chatId
      - quad
      - resolved
      title: DrawingDiscussionView
      type: object
      description: '[schema: DrawingDiscussionView]'
    DrawingPdfValidationErrorCode:
      enum:
      - COULD_NOT_OPEN
      title: DrawingPdfValidationErrorCode
      type: string
      description: '[schema: DrawingPdfValidationErrorCode]'
    ExtractedGeneralTolerance:
      description: '[schema: ExtractedGeneralTolerance] Extracted standard information
        from drawing text.'
      properties:
        standard_type:
          description: Type of standard (ISO 2768, ISO 286, or OTHER)
          enum:
          - ISO_2768
          - ISO_286
          - OTHER
          title: Standard Type
          type: string
        raw_text:
          description: Raw text of the standard as it appears in the drawing (e.g.,
            'ISO2768mK', 'ISO286')
          title: Raw Text
          type: string
        row_identifiers:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          default: null
          description: Row identifiers extracted from the standard text (e.g., ['m',
            'K'] for ISO2768mK)
          title: Row Identifiers
      required:
      - standard_type
      - raw_text
      title: ExtractedGeneralTolerance
      type: object
    ExtractedMaterial:
      description: '[schema: ExtractedMaterial] Extracted material information from
        drawing text.'
      properties:
        material_standard:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          default: null
          description: "Standard that defines the material if specified. \n(e.g.,\
            \ EN 10025, ASTM A36, DIN 1.430, EN AW-5083)"
          title: Material Standard
        raw_material_name:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: "Raw text of the material specification as it appears in the\
            \ drawing. \n(e.g., AlCuMgPb, [Al Mg4.5Mn0.7] H111, 1.4301, AlMgSi1, S355J2+N,\
            \ 1.0570 St52-3)"
          title: Raw Material Name
      title: ExtractedMaterial
      type: object
    FileResourceAccessRight:
      properties:
        tenant:
          $ref: '#/components/schemas/TenantView'
        isOwner:
          title: Isowner
          type: boolean
      required:
      - tenant
      - isOwner
      title: FileResourceAccessRight
      type: object
      description: '[schema: FileResourceAccessRight]'
    FileResourceLabel:
      enum:
      - APPEARANCE_APPROVAL_REPORT
      - CHECKING_AIDS
      - CONCESSION_REQUEST
      - CONTROL_PLAN
      - CUSTOMER_ENGINEERING_APPROVAL
      - CUSTOMER_SPECIFIC_REQUIREMENTS
      - DESIGN_FMEA
      - DESIGN_RECORDS
      - DIMENSIONAL_RESULTS
      - ENGINEERING_CHANGE_DOCUMENTS
      - GR_AND_R
      - INITIAL_PROCESS_CAPABILITY_STUDIES
      - INITIAL_SAMPLE_INSPECTION_REPORT
      - LABORATORY_ACCREDITATION
      - MATERIAL_CERTIFICATE
      - MATERIAL_TEST_AND_PERFORMANCE
      - MEASUREMENT_PROTOCOL
      - MEASUREMENT_SYSTEM_ANALYSIS
      - MEETING_REPORT
      - OTHER
      - PART_SUBMISSION_WARRANT
      - PROCESS_FLOW_DIAGRAM
      - PROCESS_FMEA
      - REACH_DECLARATION
      - ROHS_DECLARATION
      - TECHNICAL_DRAWING
      - WORKING_INSTRUCTION
      title: FileResourceLabel
      type: string
      description: '[schema: FileResourceLabel]'
    FileResourceMetadataView:
      properties:
        fileResourceId:
          title: Fileresourceid
          type: integer
        accessRights:
          items:
            $ref: '#/components/schemas/FileResourceAccessRight'
          title: Accessrights
          type: array
      required:
      - fileResourceId
      - accessRights
      title: FileResourceMetadataView
      type: object
      description: '[schema: FileResourceMetadataView]'
    FileResourceRenameInput:
      properties:
        newDisplayName:
          title: Newdisplayname
          type: string
      required:
      - newDisplayName
      title: FileResourceRenameInput
      type: object
      description: '[schema: FileResourceRenameInput]'
    FileResourceRenameOutput:
      properties: {}
      title: FileResourceRenameOutput
      type: object
      description: '[schema: FileResourceRenameOutput]'
    FileResourceRevisionView:
      properties:
        fileResourceId:
          title: Fileresourceid
          type: integer
        id:
          title: Id
          type: integer
        revisionNumber:
          title: Revisionnumber
          type: integer
        revisionIsLatest:
          title: Revisionislatest
          type: boolean
        hash:
          title: Hash
          type: string
        size:
          title: Size
          type: integer
        pageCount:
          anyOf:
          - type: integer
          - type: 'null'
          title: Pagecount
        creationUtcTimestamp:
          format: date-time
          title: Creationutctimestamp
          type: string
      required:
      - fileResourceId
      - id
      - revisionNumber
      - revisionIsLatest
      - hash
      - size
      - pageCount
      - creationUtcTimestamp
      title: FileResourceRevisionView
      type: object
      description: '[schema: FileResourceRevisionView]'
    FileResourceView:
      properties:
        id:
          title: Id
          type: integer
        owner:
          $ref: '#/components/schemas/TenantView'
        displayName:
          title: Displayname
          type: string
        mimeType:
          title: Mimetype
          type: string
        label:
          $ref: '#/components/schemas/FileResourceLabel'
        isVersioned:
          title: Isversioned
          type: boolean
        revisions:
          items:
            $ref: '#/components/schemas/FileResourceRevisionView'
          title: Revisions
          type: array
        creationUtcTimestamp:
          format: date-time
          title: Creationutctimestamp
          type: string
      required:
      - id
      - owner
      - displayName
      - mimeType
      - label
      - isVersioned
      - revisions
      - creationUtcTimestamp
      title: FileResourceView
      type: object
      description: '[schema: FileResourceView]'
    FinishInspectionOutput:
      properties:
        errorCannotBeFinished:
          title: Errorcannotbefinished
          type: boolean
        errorIsAlreadyFinished:
          title: Errorisalreadyfinished
          type: boolean
      required:
      - errorCannotBeFinished
      - errorIsAlreadyFinished
      title: FinishInspectionOutput
      type: object
      description: '[schema: FinishInspectionOutput]'
    GDTDetail:
      description: '[schema: GDTDetail] Details specific to Geometric Dimensioning
        and Tolerancing.'
      properties:
        nominal_value:
          description: Primary measurement value - if type is chamfer, this is the
            length value
          title: Nominal Value
          type: number
        lower_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Lower tolerance value
          title: Lower Tol
        upper_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Upper tolerance value
          title: Upper Tol
        unit:
          $ref: '#/components/schemas/Unit'
          description: Unit of measurement
        raw_callout:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Raw callout text
          title: Raw Callout
        type:
          const: GDT
          default: GDT
          title: Type
          type: string
        symbol:
          anyOf:
          - $ref: '#/components/schemas/qw_drawing_toolkit_ocr__post_processing__models__GDTSymbol'
          - type: 'null'
          default: null
          description: "GDT symbol (e.g., flatness, perpendicularity). \nVisual descriptions:\
            \ \nflatness: a horizontal parallelogram, slightly tilted, like a rhombus\
            \ lying on its side; \nstraightness: a single, straight, vertical line;\
            \ \ncylindricity: a circle with two short parallel vertical lines on either\
            \ side, \nforming a cylinder-like icon; \ncircularity: a single perfect\
            \ circle; \nperpendicularity: an upside-down capital 'T', like a vertical\
            \ line intersecting a horizontal base line; \nparallelism: two short parallel\
            \ diagonal lines leaning to the right; \nangularity: a right triangle\
            \ lying on its side, with the right angle at the bottom left; \nposition:\
            \ a circle with a crosshair (like a target or sight); \nprofile_of_surface:\
            \ a semicircle with the flat edge on the bottom; \nprofile_of_line: a\
            \ semicircle with the flat edge on the top; \ntotal_runout: two upward\
            \ diagonal arrows starting from the same base point; \ncircular_runout:\
            \ a single upward diagonal arrow; \nconcentricity: two concentric circles,\
            \ one inside the other (like a bullseye); \nsymmetry: a horizontal line\
            \ with two vertical lines mirrored above and below \n(like a bowtie or\
            \ stylized 'H')."
        datum_references:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          default: null
          description: Referenced datum features
          title: Datum References
        material_condition:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Material condition (e.g., MMC, LMC, RFS)
          title: Material Condition
        diameter_modifier:
          default: false
          description: "True if the FCF includes the \xD8 symbol"
          title: Diameter Modifier
          type: boolean
      required:
      - nominal_value
      - unit
      title: GDTDetail
      type: object
    GenerateInspectionPdfInput:
      properties:
        opts:
          $ref: '#/components/schemas/GenerateInspectionPdfOptions'
        frontendUrl:
          format: uri
          minLength: 1
          title: Frontendurl
          type: string
        details:
          $ref: '#/components/schemas/InspectionReportDetails'
      required:
      - frontendUrl
      title: GenerateInspectionPdfInput
      type: object
      description: '[schema: GenerateInspectionPdfInput]'
    GenerateInspectionPdfOptions:
      properties:
        lang:
          default: en
          enum:
          - zh
          - cs
          - da
          - en
          - de
          - es
          - fr
          - hi
          - it
          - pl
          - ro
          - ru
          - tr
          title: Lang
          type: string
        reportType:
          $ref: '#/components/schemas/ReportType'
          default: STANDARD
        dpiForDrawingImages:
          default: 125
          maximum: 300
          minimum: 50
          title: Dpifordrawingimages
          type: integer
      title: GenerateInspectionPdfOptions
      type: object
      description: '[schema: GenerateInspectionPdfOptions]'
    GetDrawingRevisionPageTilesOutput:
      properties:
        tiles:
          items:
            $ref: '#/components/schemas/TileInfo'
          title: Tiles
          type: array
        tileFormat:
          title: Tileformat
          type: string
        fullTileSize:
          title: Fulltilesize
          type: integer
        dpi:
          title: Dpi
          type: integer
      required:
      - tiles
      - tileFormat
      - fullTileSize
      - dpi
      title: GetDrawingRevisionPageTilesOutput
      type: object
      description: '[schema: GetDrawingRevisionPageTilesOutput]'
    GetSearchMatchesInput:
      properties:
        searchTerm:
          minLength: 3
          title: Searchterm
          type: string
      required:
      - searchTerm
      title: GetSearchMatchesInput
      type: object
      description: '[schema: GetSearchMatchesInput]'
    GetSearchMatchesOutput:
      properties:
        tenantMatches:
          items:
            $ref: '#/components/schemas/SearchMatch'
          title: Tenantmatches
          type: array
        materialMatches:
          items:
            $ref: '#/components/schemas/SearchMatch'
          title: Materialmatches
          type: array
        orderMatches:
          items:
            $ref: '#/components/schemas/SearchMatch'
          title: Ordermatches
          type: array
        fileResourceMatches:
          items:
            $ref: '#/components/schemas/SearchMatch'
          title: Fileresourcematches
          type: array
      required:
      - tenantMatches
      - materialMatches
      - orderMatches
      - fileResourceMatches
      title: GetSearchMatchesOutput
      type: object
      description: '[schema: GetSearchMatchesOutput]'
    HeaderDetails:
      description: '[schema: HeaderDetails] Header/batch details with foreign key
        references.

        Treated like a database table - always includes batch_number and heat_number.'
      properties:
        batch_number:
          description: Batch/item identifier - acts as primary key
          title: Batch Number
          type: string
        heat_number:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Heat/Smelt number - acts as secondary key
          title: Heat Number
        manufacturer:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Manufacturer name (capitalize first letter only)
          title: Manufacturer
        product_description:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Product description (e.g., Hot Rolled Heavy Plate)
          title: Product Description
        material_grade:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Steel grade (e.g., S355J2+N)
          title: Material Grade
        thickness_mm:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Thickness in mm
          title: Thickness Mm
        width_mm:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Width in mm
          title: Width Mm
        length_mm:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Length in mm
          title: Length Mm
        weight_kg:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Weight in kg
          title: Weight Kg
      required:
      - batch_number
      title: HeaderDetails
      type: object
    HealthComponent:
      properties:
        name:
          enum:
          - iam
          - db
          - s3
          - wopi
          title: Name
          type: string
        status:
          $ref: '#/components/schemas/HealthStatus'
      required:
      - name
      - status
      title: HealthComponent
      type: object
      description: '[schema: HealthComponent]'
    HealthOutput:
      properties:
        status:
          $ref: '#/components/schemas/HealthStatus'
        components:
          items:
            $ref: '#/components/schemas/HealthComponent'
          title: Components
          type: array
      required:
      - status
      - components
      title: HealthOutput
      type: object
      description: '[schema: HealthOutput]'
    HealthStatus:
      enum:
      - UP
      - DOWN
      title: HealthStatus
      type: string
      description: '[schema: HealthStatus]'
    ImpactTest:
      description: '[schema: ImpactTest] Impact test results (Charpy V-notch test).

        Multiple impact tests can be performed at different temperatures.'
      properties:
        test_direction:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: 'Impact test direction: L (Longitudinal) or T (Transverse).
            Most common is L.'
          title: Test Direction
        test_temperature_celsius:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: "Impact test temperature in \xB0C (e.g., 20, -20, -40, -60)"
          title: Test Temperature Celsius
        impact_energy_1_joules:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: First impact energy value in Joules
          title: Impact Energy 1 Joules
        impact_energy_2_joules:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Second impact energy value in Joules
          title: Impact Energy 2 Joules
        impact_energy_3_joules:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Third impact energy value in Joules
          title: Impact Energy 3 Joules
        impact_energy_average_joules:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: Average impact energy in Joules (calculate if not provided)
          title: Impact Energy Average Joules
      title: ImpactTest
      type: object
    Importance:
      description: '[schema: Importance] Importance level of a PMI element.'
      enum:
      - AUXILIARY
      - THEORETICAL
      - COMMON
      - CONTROL
      - SPECIAL
      title: Importance
      type: string
    InfoBackendSettings:
      properties:
        maxBodyPartBufferSizeInBytes:
          title: Maxbodypartbuffersizeinbytes
          type: integer
      required:
      - maxBodyPartBufferSizeInBytes
      title: InfoBackendSettings
      type: object
      description: '[schema: InfoBackendSettings]'
    InfoOutput:
      properties:
        version:
          title: Version
          type: string
        commit:
          title: Commit
          type: string
        settings:
          $ref: '#/components/schemas/InfoBackendSettings'
      required:
      - version
      - commit
      - settings
      title: InfoOutput
      type: object
      description: '[schema: InfoOutput]'
    InspectionActionImageEvidence:
      description: '[schema: InspectionActionImageEvidence] This is a placeholder
        the for a binary result which will be transported/retrievable separately'
      properties:
        type:
          const: IMAGE
          default: IMAGE
          title: Type
          type: string
      title: InspectionActionImageEvidence
      type: object
    InspectionActionMeasurementEvidence:
      properties:
        type:
          const: MEASUREMENT
          default: MEASUREMENT
          title: Type
          type: string
        resultType:
          default: CONTINUOUS
          enum:
          - CONTINUOUS
          - BINARY
          title: Resulttype
          type: string
        measuredValue:
          default: 0
          description: The value to consider for result type CONTINUOUS
          title: Measuredvalue
          type: number
        binaryValue:
          default: false
          description: The value to consider for result type BINARY, if is set to
            True the measurement is considered in tolerance
          title: Binaryvalue
          type: boolean
      title: InspectionActionMeasurementEvidence
      type: object
      description: '[schema: InspectionActionMeasurementEvidence]'
    InspectionActionMissingResult:
      properties:
        resultId:
          $ref: '#/components/schemas/InspectionActionResultIdentifier'
        type:
          $ref: '#/components/schemas/InspectionPlanActionType'
        optional:
          title: Optional
          type: boolean
      required:
      - resultId
      - type
      - optional
      title: InspectionActionMissingResult
      type: object
      description: '[schema: InspectionActionMissingResult]'
    InspectionActionQuestionAnswerEvidence:
      properties:
        type:
          const: QUESTION_ANSWER
          default: QUESTION_ANSWER
          title: Type
          type: string
        answer:
          title: Answer
          type: string
      required:
      - answer
      title: InspectionActionQuestionAnswerEvidence
      type: object
      description: '[schema: InspectionActionQuestionAnswerEvidence]'
    InspectionActionResult:
      properties:
        resultId:
          $ref: '#/components/schemas/InspectionActionResultIdentifier'
        evidence:
          discriminator:
            mapping:
              IMAGE: '#/components/schemas/InspectionActionImageEvidence'
              MEASUREMENT: '#/components/schemas/InspectionActionMeasurementEvidence'
              QUESTION_ANSWER: '#/components/schemas/InspectionActionQuestionAnswerEvidence'
              VIDEO: '#/components/schemas/InspectionActionVideoEvidence'
            propertyName: type
          oneOf:
          - $ref: '#/components/schemas/InspectionActionMeasurementEvidence'
          - $ref: '#/components/schemas/InspectionActionImageEvidence'
          - $ref: '#/components/schemas/InspectionActionVideoEvidence'
          - $ref: '#/components/schemas/InspectionActionQuestionAnswerEvidence'
          title: Evidence
      required:
      - resultId
      - evidence
      title: InspectionActionResult
      type: object
      description: '[schema: InspectionActionResult]'
    InspectionActionResultIdentifier:
      properties:
        stepIndex:
          description: The index in the steps array of the inspection plan
          title: Stepindex
          type: integer
        actionIndex:
          description: The index in the actions array of the inspection step
          title: Actionindex
          type: integer
        sampleIndex:
          anyOf:
          - type: integer
          - type: 'null'
          description: The index of the sample, expected to be null if InspectionPlanStep.repeat_for_each_sample
            is set to false
          title: Sampleindex
      required:
      - stepIndex
      - actionIndex
      - sampleIndex
      title: InspectionActionResultIdentifier
      type: object
      description: '[schema: InspectionActionResultIdentifier]'
    InspectionActionVideoEvidence:
      description: '[schema: InspectionActionVideoEvidence] This is a placeholder
        the for a binary result which will be transported/retrievable separately'
      properties:
        type:
          const: VIDEO
          default: VIDEO
          title: Type
          type: string
      title: InspectionActionVideoEvidence
      type: object
    InspectionCharacteristicImportance:
      enum:
      - AUXILIARY
      - THEORETICAL
      - COMMON
      - CONTROL
      - SPECIAL
      title: InspectionCharacteristicImportance
      type: string
      description: '[schema: InspectionCharacteristicImportance]'
    InspectionDrawingQuad:
      properties:
        drawingRevisionId:
          title: Drawingrevisionid
          type: integer
        pageIndex:
          title: Pageindex
          type: integer
        x1:
          maximum: 1
          minimum: 0
          title: X1
          type: number
        y1:
          maximum: 1
          minimum: 0
          title: Y1
          type: number
        x2:
          maximum: 1
          minimum: 0
          title: X2
          type: number
        y2:
          maximum: 1
          minimum: 0
          title: Y2
          type: number
        x3:
          maximum: 1
          minimum: 0
          title: X3
          type: number
        y3:
          maximum: 1
          minimum: 0
          title: Y3
          type: number
        x4:
          maximum: 1
          minimum: 0
          title: X4
          type: number
        y4:
          maximum: 1
          minimum: 0
          title: Y4
          type: number
      required:
      - drawingRevisionId
      - pageIndex
      - x1
      - y1
      - x2
      - y2
      - x3
      - y3
      - x4
      - y4
      title: InspectionDrawingQuad
      type: object
      description: '[schema: InspectionDrawingQuad]'
    InspectionFinishView:
      properties:
        id:
          title: Id
          type: integer
        countNonConformMeasurements:
          title: Countnonconformmeasurements
          type: integer
        creator:
          $ref: '#/components/schemas/UserView'
        tsCreated:
          format: date-time
          title: Tscreated
          type: string
      required:
      - id
      - countNonConformMeasurements
      - creator
      - tsCreated
      title: InspectionFinishView
      type: object
      description: '[schema: InspectionFinishView]'
    InspectionMeasurementAngleDetail:
      properties:
        type:
          const: ANGLE
          default: ANGLE
          title: Type
          type: string
      title: InspectionMeasurementAngleDetail
      type: object
      description: '[schema: InspectionMeasurementAngleDetail]'
    InspectionMeasurementChamferDetail:
      properties:
        type:
          const: CHAMFER
          default: CHAMFER
          title: Type
          type: string
        angle:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          title: Angle
        length:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          title: Length
        lengthUnit:
          anyOf:
          - $ref: '#/components/schemas/InspectionMeasurementUnit'
          - type: 'null'
          default: null
      title: InspectionMeasurementChamferDetail
      type: object
      description: '[schema: InspectionMeasurementChamferDetail]'
    InspectionMeasurementDiameterDetail:
      properties:
        type:
          const: DIAMETER
          default: DIAMETER
          title: Type
          type: string
        iso286Identifier:
          anyOf:
          - pattern: ^[a-zA-Z]{1,2}[0-9]{1,2}$
            type: string
          - type: 'null'
          default: null
          title: Iso286Identifier
      title: InspectionMeasurementDiameterDetail
      type: object
      description: '[schema: InspectionMeasurementDiameterDetail]'
    InspectionMeasurementGDTDetail:
      properties:
        type:
          const: GDT
          default: GDT
          title: Type
          type: string
        symbol:
          anyOf:
          - $ref: '#/components/schemas/qw_inspection__spec__plan__GDTSymbol'
          - type: 'null'
          default: null
        datumReferences:
          anyOf:
          - items:
              type: string
            type: array
          - type: 'null'
          default: null
          title: Datumreferences
        materialCondition:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Materialcondition
        diameterModifier:
          default: false
          title: Diametermodifier
          type: boolean
      title: InspectionMeasurementGDTDetail
      type: object
      description: '[schema: InspectionMeasurementGDTDetail]'
    InspectionMeasurementLinearDetail:
      properties:
        type:
          const: LINEAR
          default: LINEAR
          title: Type
          type: string
      title: InspectionMeasurementLinearDetail
      type: object
      description: '[schema: InspectionMeasurementLinearDetail]'
    InspectionMeasurementRadiusDetail:
      properties:
        type:
          const: RADIUS
          default: RADIUS
          title: Type
          type: string
      title: InspectionMeasurementRadiusDetail
      type: object
      description: '[schema: InspectionMeasurementRadiusDetail]'
    InspectionMeasurementResultType:
      enum:
      - CONTINUOUS
      - BINARY
      - CONTINUOUS_OR_BINARY
      title: InspectionMeasurementResultType
      type: string
      description: '[schema: InspectionMeasurementResultType]'
    InspectionMeasurementRoughnessDetail:
      properties:
        type:
          const: ROUGHNESS
          default: ROUGHNESS
          title: Type
          type: string
        roughnessCategory:
          anyOf:
          - $ref: '#/components/schemas/qw_inspection__spec__plan__RoughnessCategory'
          - type: 'null'
          default: null
      title: InspectionMeasurementRoughnessDetail
      type: object
      description: '[schema: InspectionMeasurementRoughnessDetail]'
    InspectionMeasurementThreadDetail:
      properties:
        type:
          const: THREAD
          default: THREAD
          title: Type
          type: string
        pitch:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          title: Pitch
        iso965Identifier:
          anyOf:
          - pattern: ^[0-9][a-zA-Z]([0-9][a-zA-Z])?$
            type: string
          - type: 'null'
          default: null
          title: Iso965Identifier
        threadStandard:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Threadstandard
        depth:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          title: Depth
        depthUnit:
          anyOf:
          - $ref: '#/components/schemas/InspectionMeasurementUnit'
          - type: 'null'
          default: null
      title: InspectionMeasurementThreadDetail
      type: object
      description: '[schema: InspectionMeasurementThreadDetail]'
    InspectionMeasurementUnit:
      enum:
      - MILLIMETER
      - MICROMETER
      - DEGREE
      title: InspectionMeasurementUnit
      type: string
      description: '[schema: InspectionMeasurementUnit]'
    InspectionPlan:
      properties:
        version:
          const: '1.0'
          default: '1.0'
          title: Version
          type: string
        title:
          title: Title
          type: string
        steps:
          description: The array index is the step_index
          items:
            $ref: '#/components/schemas/InspectionPlanStep'
          minItems: 1
          title: Steps
          type: array
      required:
      - title
      - steps
      title: InspectionPlan
      type: object
      description: '[schema: InspectionPlan]'
    InspectionPlanAccessRight:
      properties:
        tenant:
          $ref: '#/components/schemas/TenantView'
        isOwner:
          title: Isowner
          type: boolean
      required:
      - tenant
      - isOwner
      title: InspectionPlanAccessRight
      type: object
      description: '[schema: InspectionPlanAccessRight]'
    InspectionPlanActionImage:
      properties:
        type:
          const: IMAGE
          default: IMAGE
          title: Type
          type: string
        optional:
          default: false
          title: Optional
          type: boolean
        instruction:
          anyOf:
          - minLength: 3
            type: string
          - type: 'null'
          title: Instruction
      required:
      - instruction
      title: InspectionPlanActionImage
      type: object
      description: '[schema: InspectionPlanActionImage]'
    InspectionPlanActionMeasurement:
      properties:
        type:
          const: MEASUREMENT
          default: MEASUREMENT
          title: Type
          type: string
        optional:
          default: false
          title: Optional
          type: boolean
        valueExpected:
          title: Valueexpected
          type: number
        valueUnit:
          $ref: '#/components/schemas/InspectionMeasurementUnit'
        valueLowerTolerance:
          anyOf:
          - type: number
          - type: 'null'
          title: Valuelowertolerance
        valueUpperTolerance:
          anyOf:
          - type: number
          - type: 'null'
          title: Valueuppertolerance
        importance:
          $ref: '#/components/schemas/InspectionCharacteristicImportance'
          default: COMMON
        resultType:
          $ref: '#/components/schemas/InspectionMeasurementResultType'
          default: CONTINUOUS
        toleranceSource:
          anyOf:
          - $ref: '#/components/schemas/ToleranceSource'
          - type: string
          - type: 'null'
          default: null
          title: Tolerancesource
        detail:
          anyOf:
          - discriminator:
              mapping:
                ANGLE: '#/components/schemas/InspectionMeasurementAngleDetail'
                CHAMFER: '#/components/schemas/InspectionMeasurementChamferDetail'
                DIAMETER: '#/components/schemas/InspectionMeasurementDiameterDetail'
                GDT: '#/components/schemas/InspectionMeasurementGDTDetail'
                LINEAR: '#/components/schemas/InspectionMeasurementLinearDetail'
                RADIUS: '#/components/schemas/InspectionMeasurementRadiusDetail'
                ROUGHNESS: '#/components/schemas/InspectionMeasurementRoughnessDetail'
                THREAD: '#/components/schemas/InspectionMeasurementThreadDetail'
              propertyName: type
            oneOf:
            - $ref: '#/components/schemas/InspectionMeasurementDiameterDetail'
            - $ref: '#/components/schemas/InspectionMeasurementThreadDetail'
            - $ref: '#/components/schemas/InspectionMeasurementGDTDetail'
            - $ref: '#/components/schemas/InspectionMeasurementAngleDetail'
            - $ref: '#/components/schemas/InspectionMeasurementLinearDetail'
            - $ref: '#/components/schemas/InspectionMeasurementRadiusDetail'
            - $ref: '#/components/schemas/InspectionMeasurementChamferDetail'
            - $ref: '#/components/schemas/InspectionMeasurementRoughnessDetail'
          - type: 'null'
          default: null
          title: Detail
        count:
          default: 1
          title: Count
          type: integer
        rawCallout:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Rawcallout
      required:
      - valueExpected
      - valueUnit
      - valueLowerTolerance
      - valueUpperTolerance
      title: InspectionPlanActionMeasurement
      type: object
      description: '[schema: InspectionPlanActionMeasurement]'
    InspectionPlanActionQuestionAnswer:
      properties:
        type:
          const: QUESTION_ANSWER
          default: QUESTION_ANSWER
          title: Type
          type: string
        optional:
          default: false
          title: Optional
          type: boolean
        question:
          minLength: 3
          title: Question
          type: string
      required:
      - question
      title: InspectionPlanActionQuestionAnswer
      type: object
      description: '[schema: InspectionPlanActionQuestionAnswer]'
    InspectionPlanActionType:
      enum:
      - MEASUREMENT
      - IMAGE
      - VIDEO
      - QUESTION_ANSWER
      title: InspectionPlanActionType
      type: string
      description: '[schema: InspectionPlanActionType]'
    InspectionPlanActionVideo:
      properties:
        type:
          const: VIDEO
          default: VIDEO
          title: Type
          type: string
        optional:
          default: false
          title: Optional
          type: boolean
        instruction:
          anyOf:
          - minLength: 3
            type: string
          - type: 'null'
          title: Instruction
      required:
      - instruction
      title: InspectionPlanActionVideo
      type: object
      description: '[schema: InspectionPlanActionVideo]'
    InspectionPlanStep:
      properties:
        drawingQuad:
          anyOf:
          - $ref: '#/components/schemas/InspectionDrawingQuad'
          - type: 'null'
          description: If one of the actions is of type 'MEASUREMENT' this is required
        actions:
          items:
            discriminator:
              mapping:
                IMAGE: '#/components/schemas/InspectionPlanActionImage'
                MEASUREMENT: '#/components/schemas/InspectionPlanActionMeasurement'
                QUESTION_ANSWER: '#/components/schemas/InspectionPlanActionQuestionAnswer'
                VIDEO: '#/components/schemas/InspectionPlanActionVideo'
              propertyName: type
            oneOf:
            - $ref: '#/components/schemas/InspectionPlanActionMeasurement'
            - $ref: '#/components/schemas/InspectionPlanActionImage'
            - $ref: '#/components/schemas/InspectionPlanActionVideo'
            - $ref: '#/components/schemas/InspectionPlanActionQuestionAnswer'
          minItems: 1
          title: Actions
          type: array
        repeatForEachSample:
          description: If set to false the sample_index parameter should be null for
            its result and the step and its actions only need to be performed once
            during a matrix inspection (i.e. >1 sample)
          title: Repeatforeachsample
          type: boolean
      required:
      - drawingQuad
      - actions
      - repeatForEachSample
      title: InspectionPlanStep
      type: object
      description: '[schema: InspectionPlanStep]'
    InspectionPlanSummaryView:
      properties:
        id:
          title: Id
          type: integer
        owner:
          $ref: '#/components/schemas/TenantView'
        title:
          title: Title
          type: string
        isReadonly:
          title: Isreadonly
          type: boolean
      required:
      - id
      - owner
      - title
      - isReadonly
      title: InspectionPlanSummaryView
      type: object
      description: '[schema: InspectionPlanSummaryView]'
    InspectionPlanView:
      properties:
        id:
          title: Id
          type: integer
        inspectionPlan:
          $ref: '#/components/schemas/InspectionPlan'
        owner:
          $ref: '#/components/schemas/TenantView'
        isReadonly:
          title: Isreadonly
          type: boolean
        editTimestamp:
          format: date-time
          title: Edittimestamp
          type: string
      required:
      - id
      - inspectionPlan
      - owner
      - isReadonly
      - editTimestamp
      title: InspectionPlanView
      type: object
      description: '[schema: InspectionPlanView]'
    InspectionPlansMetadataView:
      properties:
        inspectionPlanId:
          title: Inspectionplanid
          type: integer
        accessRights:
          items:
            $ref: '#/components/schemas/InspectionPlanAccessRight'
          title: Accessrights
          type: array
      required:
      - inspectionPlanId
      - accessRights
      title: InspectionPlansMetadataView
      type: object
      description: '[schema: InspectionPlansMetadataView]'
    InspectionReportCustomerDetails:
      properties:
        customerName:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Customername
        deliveryLocation:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Deliverylocation
        changeDocuments:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Changedocuments
        ppaSampleOrderNumber:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Ppasampleordernumber
      title: InspectionReportCustomerDetails
      type: object
      description: '[schema: InspectionReportCustomerDetails]'
    InspectionReportDetails:
      properties:
        manufacturer:
          $ref: '#/components/schemas/InspectionReportManufacturerDetails'
        part:
          $ref: '#/components/schemas/InspectionReportPartDetails'
        inspection:
          $ref: '#/components/schemas/InspectionReportInspectionDetails'
        report:
          $ref: '#/components/schemas/InspectionReportReportDetails'
        customer:
          $ref: '#/components/schemas/InspectionReportCustomerDetails'
        user:
          $ref: '#/components/schemas/InspectionReportUserDetails'
      title: InspectionReportDetails
      type: object
      description: '[schema: InspectionReportDetails]'
    InspectionReportInspectionDetails:
      properties:
        drawingNumber:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Drawingnumber
        drawingDate:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Drawingdate
        batchOrSerialNumber:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Batchorserialnumber
        inspectionType:
          items:
            $ref: '#/components/schemas/InspectionReportInspectionType'
          title: Inspectiontype
          type: array
        inspectionFacility:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Inspectionfacility
        diagnosisStatus:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Diagnosisstatus
        hardwareVersion:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Hardwareversion
        softwareVersion:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Softwareversion
      title: InspectionReportInspectionDetails
      type: object
      description: '[schema: InspectionReportInspectionDetails]'
    InspectionReportInspectionType:
      enum:
      - GEOMETRY_AND_DIMENSIONS
      - MATERIAL
      - FUNCTION
      - HAPTICS
      - ACOUSTICS
      - ODOR
      - APPEARANCE
      - SURFACE_REQUIREMENT
      - TECHNICAL_CLEANLINESS
      - RELIABILITY
      - RESISTANCE_TO_ELECTROSTATIC_DISCHARGE
      - ELECTRICAL_SAFETY_OR_HIGH_VOLTAGE_SAFETY
      - ELECTROMAGNETIC_COMPATIBILITY
      title: InspectionReportInspectionType
      type: string
      description: '[schema: InspectionReportInspectionType]'
    InspectionReportManufacturerDetails:
      properties:
        productionSite:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Productionsite
        manufacturerId:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Manufacturerid
      title: InspectionReportManufacturerDetails
      type: object
      description: '[schema: InspectionReportManufacturerDetails]'
    InspectionReportPartDetails:
      properties:
        sampleWeight:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Sampleweight
      title: InspectionReportPartDetails
      type: object
      description: '[schema: InspectionReportPartDetails]'
    InspectionReportReportDetails:
      properties:
        userDefinedId:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Userdefinedid
        userDefinedVersion:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Userdefinedversion
        remark:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Remark
        reasons:
          items:
            $ref: '#/components/schemas/InspectionReportReportReason'
          title: Reasons
          type: array
      title: InspectionReportReportDetails
      type: object
      description: '[schema: InspectionReportReportDetails]'
    InspectionReportReportReason:
      enum:
      - PPA
      - REPORT_ON_OTHER_SAMPLES
      - REQUALIFICATION
      - SAMPLE_PRESENTATION
      - NEW_PART
      - CHANGES_TO_PRODUCT
      - CHANGES_TO_PRODUCTION_PROCESS
      - CHANGE_TO_SUPPLY_CHAIN
      - REUSE_AFTER_12_MONTH_STANDSTILL
      - UPDATED_PPA_DOCUMENTATION
      title: InspectionReportReportReason
      type: string
      description: '[schema: InspectionReportReportReason]'
    InspectionReportUserDetails:
      properties:
        department:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Department
        phone:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Phone
      title: InspectionReportUserDetails
      type: object
      description: '[schema: InspectionReportUserDetails]'
    InspectionResult:
      properties:
        availableResults:
          description: Already submitted action results
          items:
            $ref: '#/components/schemas/InspectionActionResult'
          title: Availableresults
          type: array
        missingResults:
          description: Still missing action results
          items:
            $ref: '#/components/schemas/InspectionActionMissingResult'
          title: Missingresults
          type: array
        allMandatoryResultsAreAvailable:
          description: Indicates whether the inspection can be finished, optional
            action results may still be missing
          title: Allmandatoryresultsareavailable
          type: boolean
      required:
      - availableResults
      - missingResults
      - allMandatoryResultsAreAvailable
      title: InspectionResult
      type: object
      description: '[schema: InspectionResult]'
    InspectionSummaryView:
      properties:
        id:
          title: Id
          type: integer
        creator:
          $ref: '#/components/schemas/UserView'
        creatorTenant:
          $ref: '#/components/schemas/TenantView'
        inspectorTenant:
          $ref: '#/components/schemas/TenantView'
        inspectionPlanId:
          title: Inspectionplanid
          type: integer
        sampleCount:
          title: Samplecount
          type: integer
        inspectionFinish:
          anyOf:
          - $ref: '#/components/schemas/InspectionFinishView'
          - type: 'null'
          default: null
      required:
      - id
      - creator
      - creatorTenant
      - inspectorTenant
      - inspectionPlanId
      - sampleCount
      title: InspectionSummaryView
      type: object
      description: '[schema: InspectionSummaryView]'
    InspectionView:
      properties:
        summary:
          $ref: '#/components/schemas/InspectionSummaryView'
        plan:
          $ref: '#/components/schemas/InspectionPlan'
        result:
          $ref: '#/components/schemas/InspectionResult'
      required:
      - summary
      - plan
      - result
      title: InspectionView
      type: object
      description: '[schema: InspectionView]'
    LinearDetail:
      description: '[schema: LinearDetail] Details specific to linear measurements.'
      properties:
        nominal_value:
          description: Primary measurement value - if type is chamfer, this is the
            length value
          title: Nominal Value
          type: number
        lower_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Lower tolerance value
          title: Lower Tol
        upper_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Upper tolerance value
          title: Upper Tol
        unit:
          $ref: '#/components/schemas/Unit'
          description: Unit of measurement
        raw_callout:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Raw callout text
          title: Raw Callout
        type:
          const: LINEAR
          default: LINEAR
          title: Type
          type: string
      required:
      - nominal_value
      - unit
      title: LinearDetail
      type: object
    LinkFileToMaterialInput:
      properties:
        fileResourceRevisionId:
          title: Fileresourcerevisionid
          type: integer
        materialId:
          title: Materialid
          type: integer
      required:
      - fileResourceRevisionId
      - materialId
      title: LinkFileToMaterialInput
      type: object
      description: '[schema: LinkFileToMaterialInput]'
    LinkFileToMaterialOutput:
      properties:
        success:
          title: Success
          type: boolean
      required:
      - success
      title: LinkFileToMaterialOutput
      type: object
      description: '[schema: LinkFileToMaterialOutput]'
    ListDrawingDiscussionsInput:
      properties:
        count:
          exclusiveMinimum: 0
          maximum: 100
          title: Count
          type: integer
        offset:
          minimum: 0
          title: Offset
          type: integer
        inDrawingDiscussionIds:
          default: []
          items:
            type: integer
          title: Indrawingdiscussionids
          type: array
        inFileResourceIds:
          default: []
          items:
            type: integer
          title: Infileresourceids
          type: array
        inResolvedStatus:
          default: []
          items:
            type: boolean
          title: Inresolvedstatus
          type: array
      required:
      - count
      - offset
      title: ListDrawingDiscussionsInput
      type: object
      description: '[schema: ListDrawingDiscussionsInput]'
    ListDrawingDiscussionsOutput:
      properties:
        drawingDiscussions:
          items:
            $ref: '#/components/schemas/DrawingDiscussionView'
          title: Drawingdiscussions
          type: array
        overallCount:
          title: Overallcount
          type: integer
      required:
      - drawingDiscussions
      - overallCount
      title: ListDrawingDiscussionsOutput
      type: object
      description: '[schema: ListDrawingDiscussionsOutput]'
    ListFileResourcesInput:
      properties:
        count:
          exclusiveMinimum: 0
          maximum: 100
          title: Count
          type: integer
        offset:
          minimum: 0
          title: Offset
          type: integer
        displayNameMatchesRegex:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Displaynamematchesregex
        inOwnerTenantIds:
          default: []
          items:
            type: integer
          title: Inownertenantids
          type: array
        inFileResourceIds:
          default: []
          items:
            type: integer
          title: Infileresourceids
          type: array
        inFileResourceRevisionIds:
          default: []
          items:
            type: integer
          title: Infileresourcerevisionids
          type: array
        inFileResourceLabels:
          default: []
          items:
            $ref: '#/components/schemas/FileResourceLabel'
          title: Infileresourcelabels
          type: array
        linkedToOrderLineIds:
          default: []
          items:
            type: integer
          title: Linkedtoorderlineids
          type: array
        linkedToMaterialIds:
          default: []
          items:
            type: integer
          title: Linkedtomaterialids
          type: array
        linkedToTenantIds:
          default: []
          items:
            type: integer
          title: Linkedtotenantids
          type: array
        onlyLatestRevision:
          default: true
          title: Onlylatestrevision
          type: boolean
      required:
      - count
      - offset
      title: ListFileResourcesInput
      type: object
      description: '[schema: ListFileResourcesInput]'
    ListFileResourcesMetadataInput:
      properties:
        inFileResourceIds:
          default: []
          items:
            type: integer
          title: Infileresourceids
          type: array
      title: ListFileResourcesMetadataInput
      type: object
      description: '[schema: ListFileResourcesMetadataInput]'
    ListFileResourcesMetadataOutput:
      properties:
        metadataItems:
          items:
            $ref: '#/components/schemas/FileResourceMetadataView'
          title: Metadataitems
          type: array
      required:
      - metadataItems
      title: ListFileResourcesMetadataOutput
      type: object
      description: '[schema: ListFileResourcesMetadataOutput]'
    ListFileResourcesOutput:
      properties:
        fileResources:
          items:
            $ref: '#/components/schemas/FileResourceView'
          title: Fileresources
          type: array
        overallCountFileResources:
          title: Overallcountfileresources
          type: integer
        overallCountFileResourceRevisions:
          title: Overallcountfileresourcerevisions
          type: integer
      required:
      - fileResources
      - overallCountFileResources
      - overallCountFileResourceRevisions
      title: ListFileResourcesOutput
      type: object
      description: '[schema: ListFileResourcesOutput]'
    ListInspectionPlansInput:
      properties:
        inOwnerTenantIds:
          default: []
          items:
            type: integer
          title: Inownertenantids
          type: array
        linkedToOrderLineIds:
          default: []
          items:
            type: integer
          title: Linkedtoorderlineids
          type: array
        templateDiscoveryContext:
          anyOf:
          - $ref: '#/components/schemas/TemplateDiscoveryContext'
          - type: 'null'
          default: null
        count:
          exclusiveMinimum: 0
          maximum: 100
          title: Count
          type: integer
        offset:
          minimum: 0
          title: Offset
          type: integer
      required:
      - count
      - offset
      title: ListInspectionPlansInput
      type: object
      description: '[schema: ListInspectionPlansInput]'
    ListInspectionPlansMetadataInput:
      properties:
        inInspectionPlanIds:
          default: []
          items:
            type: integer
          title: Ininspectionplanids
          type: array
      title: ListInspectionPlansMetadataInput
      type: object
      description: '[schema: ListInspectionPlansMetadataInput]'
    ListInspectionPlansMetadataOutput:
      properties:
        metadataItems:
          items:
            $ref: '#/components/schemas/InspectionPlansMetadataView'
          title: Metadataitems
          type: array
      required:
      - metadataItems
      title: ListInspectionPlansMetadataOutput
      type: object
      description: '[schema: ListInspectionPlansMetadataOutput]'
    ListInspectionPlansOutput:
      properties:
        inspectionPlans:
          items:
            $ref: '#/components/schemas/InspectionPlanSummaryView'
          title: Inspectionplans
          type: array
        countOverall:
          title: Countoverall
          type: integer
      required:
      - inspectionPlans
      - countOverall
      title: ListInspectionPlansOutput
      type: object
      description: '[schema: ListInspectionPlansOutput]'
    ListInspectionsInput:
      properties:
        inInspectorTenantIds:
          default: []
          items:
            type: integer
          title: Ininspectortenantids
          type: array
        inOrderLineIds:
          default: []
          items:
            type: integer
          title: Inorderlineids
          type: array
        inInspectionPlanIds:
          default: []
          items:
            type: integer
          title: Ininspectionplanids
          type: array
        inInspectionIds:
          default: []
          items:
            type: integer
          title: Ininspectionids
          type: array
        isFinished:
          anyOf:
          - type: boolean
          - type: 'null'
          default: null
          title: Isfinished
        count:
          exclusiveMinimum: 0
          maximum: 100
          title: Count
          type: integer
        offset:
          minimum: 0
          title: Offset
          type: integer
      required:
      - count
      - offset
      title: ListInspectionsInput
      type: object
      description: '[schema: ListInspectionsInput]'
    ListInspectionsOutput:
      properties:
        inspections:
          items:
            $ref: '#/components/schemas/InspectionSummaryView'
          title: Inspections
          type: array
        countOverall:
          title: Countoverall
          type: integer
      required:
      - inspections
      - countOverall
      title: ListInspectionsOutput
      type: object
      description: '[schema: ListInspectionsOutput]'
    ListMaterialsInput:
      properties:
        count:
          exclusiveMinimum: 0
          maximum: 100
          title: Count
          type: integer
        offset:
          minimum: 0
          title: Offset
          type: integer
        inMaterialIds:
          default: []
          items:
            type: integer
          title: Inmaterialids
          type: array
        ownerInternalRefMatchesRegex:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Ownerinternalrefmatchesregex
      required:
      - count
      - offset
      title: ListMaterialsInput
      type: object
      description: '[schema: ListMaterialsInput]'
    ListMaterialsOutput:
      properties:
        materials:
          items:
            $ref: '#/components/schemas/MaterialView'
          title: Materials
          type: array
        countOverall:
          title: Countoverall
          type: integer
      required:
      - materials
      - countOverall
      title: ListMaterialsOutput
      type: object
      description: '[schema: ListMaterialsOutput]'
    ListOrdersInput:
      properties:
        count:
          exclusiveMinimum: 0
          maximum: 100
          title: Count
          type: integer
        offset:
          minimum: 0
          title: Offset
          type: integer
        inOrderTypes:
          default: []
          items:
            $ref: '#/components/schemas/OrderType'
          title: Inordertypes
          type: array
        inCustomerTenantIds:
          default: []
          items:
            type: integer
          title: Incustomertenantids
          type: array
        inSupplierTenantIds:
          default: []
          items:
            type: integer
          title: Insuppliertenantids
          type: array
        inOrderIds:
          default: []
          items:
            type: integer
          title: Inorderids
          type: array
        inOrderLineIds:
          default: []
          items:
            type: integer
          title: Inorderlineids
          type: array
        inMaterialIds:
          default: []
          items:
            type: integer
          title: Inmaterialids
          type: array
        orderInternalReferenceMatchesRegex:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Orderinternalreferencematchesregex
        orderLineInternalReferenceMatchesRegex:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Orderlineinternalreferencematchesregex
        excludeOrderLines:
          default: false
          title: Excludeorderlines
          type: boolean
        includeOrderLineInspectionStats:
          default: true
          title: Includeorderlineinspectionstats
          type: boolean
        includeOrderLineDocumentStats:
          default: true
          title: Includeorderlinedocumentstats
          type: boolean
      required:
      - count
      - offset
      title: ListOrdersInput
      type: object
      description: '[schema: ListOrdersInput]'
    ListOrdersOutput:
      properties:
        pos:
          items:
            $ref: '#/components/schemas/OrderView'
          title: Pos
          type: array
        overallCountPos:
          title: Overallcountpos
          type: integer
        overallCountPols:
          title: Overallcountpols
          type: integer
      required:
      - pos
      - overallCountPos
      - overallCountPols
      title: ListOrdersOutput
      type: object
      description: '[schema: ListOrdersOutput]'
    ListTenantsInput:
      properties:
        inTenantIds:
          default: []
          items:
            type: integer
          title: Intenantids
          type: array
      title: ListTenantsInput
      type: object
      description: '[schema: ListTenantsInput]'
    ListTenantsOutput:
      properties:
        tenants:
          items:
            $ref: '#/components/schemas/TenantView'
          title: Tenants
          type: array
      required:
      - tenants
      title: ListTenantsOutput
      type: object
      description: '[schema: ListTenantsOutput]'
    ListUsersInput:
      properties:
        inTenantIds:
          default: []
          items:
            type: integer
          title: Intenantids
          type: array
        inUserIds:
          default: []
          items:
            type: integer
          title: Inuserids
          type: array
        inSubjects:
          default: []
          items:
            type: string
          title: Insubjects
          type: array
        inIssuers:
          default: []
          items:
            type: string
          title: Inissuers
          type: array
        emailMatchesRegex:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          title: Emailmatchesregex
      title: ListUsersInput
      type: object
      description: '[schema: ListUsersInput]'
    ListUsersOutput:
      properties:
        users:
          items:
            $ref: '#/components/schemas/UserView'
          title: Users
          type: array
      required:
      - users
      title: ListUsersOutput
      type: object
      description: '[schema: ListUsersOutput]'
    LoginRequest:
      properties:
        userAgentRedirect:
          anyOf:
          - format: uri
            minLength: 1
            type: string
          - type: 'null'
          default: null
          title: Useragentredirect
      title: LoginRequest
      type: object
      description: '[schema: LoginRequest]'
    LogoutRequest:
      properties: {}
      title: LogoutRequest
      type: object
      description: '[schema: LogoutRequest]'
    LogoutResponse:
      properties:
        success:
          title: Success
          type: boolean
      required:
      - success
      title: LogoutResponse
      type: object
      description: '[schema: LogoutResponse]'
    MaterialCertificateAnalysisResponse:
      description: '[schema: MaterialCertificateAnalysisResponse] Final response from
        the redesigned agentic analysis.

        Includes metadata context and elegantly aggregated products.'
      properties:
        metadata:
          $ref: '#/components/schemas/CertificateMetadata'
          description: Certificate metadata and analysis context
        products:
          description: List of products with aggregated data
          items:
            $ref: '#/components/schemas/Product'
          title: Products
          type: array
      required:
      - metadata
      - products
      title: MaterialCertificateAnalysisResponse
      type: object
    MaterialView:
      properties:
        id:
          title: Id
          type: integer
        owner:
          $ref: '#/components/schemas/TenantView'
        internalReference:
          title: Internalreference
          type: string
        name:
          title: Name
          type: string
        chatId:
          title: Chatid
          type: integer
      required:
      - id
      - owner
      - internalReference
      - name
      - chatId
      title: MaterialView
      type: object
      description: '[schema: MaterialView]'
    MechanicalProperties:
      description: '[schema: MechanicalProperties] Mechanical properties with foreign
        key references.

        Treated like a database table - always includes batch_number and heat_number.

        Contains tensile test results and a list of impact tests.'
      properties:
        batch_number:
          description: Batch/item identifier - must match HeaderDetails
          title: Batch Number
          type: string
        heat_number:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Heat/Smelt number - must match HeaderDetails
          title: Heat Number
        tensile_test:
          anyOf:
          - $ref: '#/components/schemas/TensileTest'
          - type: 'null'
          default: null
          description: Tensile test results
        impact_tests:
          default: []
          description: List of impact test results (can be multiple tests at different
            temperatures)
          items:
            $ref: '#/components/schemas/ImpactTest'
          title: Impact Tests
          type: array
      required:
      - batch_number
      title: MechanicalProperties
      type: object
    MultipartSchema_api_v1_file-resource_revision:
      properties:
        revision_info:
          $ref: '#/components/schemas/NewFileResourceRevisionInput'
        revision_bytes:
          format: binary
          title: Revision Bytes
          type: string
      required:
      - revision_info
      - revision_bytes
      title: MultipartSchema_api_v1_file-resource_revision
      type: object
      description: '[schema: MultipartSchema_api_v1_file-resource_revision]'
    MultipartSchema_api_v1_inspection-action-result:
      properties:
        inspection_action_result:
          $ref: '#/components/schemas/NewInspectionActionResultInput'
        image_evidence:
          items:
            format: binary
            type: string
          title: Image Evidence
          type: array
        video_evidence:
          items:
            format: binary
            type: string
          title: Video Evidence
          type: array
      required:
      - inspection_action_result
      - image_evidence
      - video_evidence
      title: MultipartSchema_api_v1_inspection-action-result
      type: object
      description: '[schema: MultipartSchema_api_v1_inspection-action-result]'
    NewDrawingDiscussionInput:
      properties:
        fileResourceId:
          title: Fileresourceid
          type: integer
        firstMessage:
          $ref: '#/components/schemas/ChatAddMessageInput'
        drawingQuad:
          $ref: '#/components/schemas/InspectionDrawingQuad'
      required:
      - fileResourceId
      - firstMessage
      - drawingQuad
      title: NewDrawingDiscussionInput
      type: object
      description: '[schema: NewDrawingDiscussionInput]'
    NewDrawingDiscussionOutput:
      properties:
        drawingDiscussionId:
          title: Drawingdiscussionid
          type: integer
      required:
      - drawingDiscussionId
      title: NewDrawingDiscussionOutput
      type: object
      description: '[schema: NewDrawingDiscussionOutput]'
    NewFileResourceRevisionInfo:
      properties:
        fileResourceId:
          title: Fileresourceid
          type: integer
        fileResourceRevisionId:
          title: Fileresourcerevisionid
          type: integer
      required:
      - fileResourceId
      - fileResourceRevisionId
      title: NewFileResourceRevisionInfo
      type: object
      description: '[schema: NewFileResourceRevisionInfo]'
    NewFileResourceRevisionInput:
      properties:
        fileResourceId:
          anyOf:
          - type: integer
          - type: 'null'
          title: Fileresourceid
        fileName:
          title: Filename
          type: string
        fileLabel:
          $ref: '#/components/schemas/FileResourceLabel'
        ignoreHashCollisions:
          default: false
          title: Ignorehashcollisions
          type: boolean
        initialLinksToOrderLines:
          default: []
          items:
            $ref: '#/components/schemas/NewFileResourceRevisionOrderLineLink'
          title: Initiallinkstoorderlines
          type: array
        initialLinksToMaterials:
          default: []
          items:
            $ref: '#/components/schemas/NewFileResourceRevisionMaterialLink'
          title: Initiallinkstomaterials
          type: array
        initialLinkToTenant:
          default: false
          title: Initiallinktotenant
          type: boolean
      required:
      - fileResourceId
      - fileName
      - fileLabel
      title: NewFileResourceRevisionInput
      type: object
      description: '[schema: NewFileResourceRevisionInput]'
    NewFileResourceRevisionMaterialLink:
      properties:
        materialId:
          title: Materialid
          type: integer
      required:
      - materialId
      title: NewFileResourceRevisionMaterialLink
      type: object
      description: '[schema: NewFileResourceRevisionMaterialLink]'
    NewFileResourceRevisionOrderLineLink:
      properties:
        orderLineId:
          title: Orderlineid
          type: integer
        shareWithOtherTenant:
          title: Sharewithothertenant
          type: boolean
      required:
      - orderLineId
      - shareWithOtherTenant
      title: NewFileResourceRevisionOrderLineLink
      type: object
      description: '[schema: NewFileResourceRevisionOrderLineLink]'
    NewFileResourceRevisionOutput:
      properties:
        info:
          anyOf:
          - $ref: '#/components/schemas/NewFileResourceRevisionInfo'
          - type: 'null'
        errorFileCollision:
          items:
            type: integer
          title: Errorfilecollision
          type: array
        errorNoFileResourceFound:
          title: Errornofileresourcefound
          type: boolean
        errorMimeTypeMismatch:
          title: Errormimetypemismatch
          type: boolean
        errorVersioningNotEnabled:
          title: Errorversioningnotenabled
          type: boolean
        errorDrawingValidation:
          anyOf:
          - $ref: '#/components/schemas/DrawingPdfValidationErrorCode'
          - type: 'null'
      required:
      - info
      - errorFileCollision
      - errorNoFileResourceFound
      - errorMimeTypeMismatch
      - errorVersioningNotEnabled
      - errorDrawingValidation
      title: NewFileResourceRevisionOutput
      type: object
      description: '[schema: NewFileResourceRevisionOutput]'
    NewInspectionActionResultInput:
      properties:
        inspectionId:
          title: Inspectionid
          type: integer
        result:
          $ref: '#/components/schemas/InspectionActionResult'
      required:
      - inspectionId
      - result
      title: NewInspectionActionResultInput
      type: object
      description: '[schema: NewInspectionActionResultInput]'
    NewInspectionActionResultOutput:
      properties:
        errorIsAlreadyFinished:
          title: Errorisalreadyfinished
          type: boolean
        errorBadInput:
          anyOf:
          - type: string
          - type: 'null'
          title: Errorbadinput
      required:
      - errorIsAlreadyFinished
      - errorBadInput
      title: NewInspectionActionResultOutput
      type: object
      description: '[schema: NewInspectionActionResultOutput]'
    NewInspectionPlanInput:
      properties:
        inspectionPlan:
          $ref: '#/components/schemas/InspectionPlan'
        orderLineId:
          title: Orderlineid
          type: integer
        shareWithOtherTenant:
          default: true
          title: Sharewithothertenant
          type: boolean
      required:
      - inspectionPlan
      - orderLineId
      title: NewInspectionPlanInput
      type: object
      description: '[schema: NewInspectionPlanInput]'
    NewInspectionPlanOutput:
      properties:
        inspectionPlanId:
          title: Inspectionplanid
          type: integer
      required:
      - inspectionPlanId
      title: NewInspectionPlanOutput
      type: object
      description: '[schema: NewInspectionPlanOutput]'
    NewOrderLineInput:
      properties:
        internalReferenceMaterial:
          minLength: 1
          title: Internalreferencematerial
          type: string
        internalReferenceOrder:
          minLength: 1
          title: Internalreferenceorder
          type: string
        internalReferenceOrderLine:
          minLength: 1
          title: Internalreferenceorderline
          type: string
        materialName:
          minLength: 1
          title: Materialname
          type: string
        count:
          exclusiveMinimum: 0
          title: Count
          type: integer
        orderDate:
          format: date
          title: Orderdate
          type: string
        expectedDeliveryDate:
          format: date
          title: Expecteddeliverydate
          type: string
        info:
          anyOf:
          - $ref: '#/components/schemas/NewPurchaseOrderLineInfo'
          - $ref: '#/components/schemas/NewProductionOrderLineInfo'
          title: Info
        requirements:
          items:
            $ref: '#/components/schemas/FileResourceLabel'
          title: Requirements
          type: array
      required:
      - internalReferenceMaterial
      - internalReferenceOrder
      - internalReferenceOrderLine
      - materialName
      - count
      - orderDate
      - expectedDeliveryDate
      - info
      title: NewOrderLineInput
      type: object
      description: '[schema: NewOrderLineInput]'
    NewOrderLineOutput:
      properties:
        materialId:
          title: Materialid
          type: integer
        materialExistedBefore:
          title: Materialexistedbefore
          type: boolean
        orderId:
          title: Orderid
          type: integer
        orderExistedBefore:
          title: Orderexistedbefore
          type: boolean
        orderLineId:
          title: Orderlineid
          type: integer
        orderLineExistedBefore:
          title: Orderlineexistedbefore
          type: boolean
      required:
      - materialId
      - materialExistedBefore
      - orderId
      - orderExistedBefore
      - orderLineId
      - orderLineExistedBefore
      title: NewOrderLineOutput
      type: object
      description: '[schema: NewOrderLineOutput]'
    NewOrderLineTaskInput:
      properties:
        newTaskForInspection:
          $ref: '#/components/schemas/NewOrderLineTaskInspectionInput'
      required:
      - newTaskForInspection
      title: NewOrderLineTaskInput
      type: object
      description: '[schema: NewOrderLineTaskInput]'
    NewOrderLineTaskInspectionInput:
      properties:
        inspectionPlanId:
          title: Inspectionplanid
          type: integer
        inspectorTenantId:
          title: Inspectortenantid
          type: integer
        sampleCount:
          default: 1
          minimum: 1
          title: Samplecount
          type: integer
        dueDate:
          anyOf:
          - format: date
            type: string
          - type: 'null'
          default: null
          title: Duedate
      required:
      - inspectionPlanId
      - inspectorTenantId
      title: NewOrderLineTaskInspectionInput
      type: object
      description: '[schema: NewOrderLineTaskInspectionInput]'
    NewOrderLineTaskInspectionOutput:
      properties:
        taskId:
          title: Taskid
          type: integer
        inspectionId:
          title: Inspectionid
          type: integer
      required:
      - taskId
      - inspectionId
      title: NewOrderLineTaskInspectionOutput
      type: object
      description: '[schema: NewOrderLineTaskInspectionOutput]'
    NewOrderLineTaskOutput:
      properties:
        newInspectionTask:
          $ref: '#/components/schemas/NewOrderLineTaskInspectionOutput'
      required:
      - newInspectionTask
      title: NewOrderLineTaskOutput
      type: object
      description: '[schema: NewOrderLineTaskOutput]'
    NewProductionOrderLineInfo:
      properties: {}
      title: NewProductionOrderLineInfo
      type: object
      description: '[schema: NewProductionOrderLineInfo]'
    NewPurchaseOrderLineInfo:
      properties:
        supplierTenantId:
          exclusiveMinimum: 0
          title: Suppliertenantid
          type: integer
      required:
      - supplierTenantId
      title: NewPurchaseOrderLineInfo
      type: object
      description: '[schema: NewPurchaseOrderLineInfo]'
    NewSession:
      properties:
        session:
          title: Session
          type: string
      required:
      - session
      title: NewSession
      type: object
      description: '[schema: NewSession]'
    NormalizedPMIBlockAnalysisResult:
      description: '[schema: NormalizedPMIBlockAnalysisResult] PMI block analysis
        result with normalized coordinates.'
      properties:
        polygon:
          $ref: '#/components/schemas/NormalizedPolygon'
        result:
          $ref: '#/components/schemas/PMIBlockAnalysisResult'
        tolerance_source:
          anyOf:
          - enum:
            - explicit
            - ISO_2768
            - ISO_286
            type: string
          - type: 'null'
          default: null
          description: Source of tolerance values (explicit, ISO_2768, or ISO_286)
          title: Tolerance Source
      required:
      - polygon
      - result
      title: NormalizedPMIBlockAnalysisResult
      type: object
    NormalizedPoint:
      description: '[schema: NormalizedPoint] Normalized (0-1) coordinate point'
      properties:
        x:
          description: Normalized X coordinate (0-1)
          maximum: 1
          minimum: 0
          title: X
          type: number
        y:
          description: Normalized Y coordinate (0-1)
          maximum: 1
          minimum: 0
          title: Y
          type: number
      required:
      - x
      - y
      title: NormalizedPoint
      type: object
    NormalizedPolygon:
      description: '[schema: NormalizedPolygon] Polygon with normalized (0-1) coordinates
        in clockwise order'
      properties:
        points:
          description: List of normalized points defining the polygon in clockwise
            order
          items:
            $ref: '#/components/schemas/NormalizedPoint'
          minItems: 4
          title: Points
          type: array
      required:
      - points
      title: NormalizedPolygon
      type: object
    OrderLineDocumentStatsView:
      properties:
        countTechnicalDrawings:
          title: Counttechnicaldrawings
          type: integer
      required:
      - countTechnicalDrawings
      title: OrderLineDocumentStatsView
      type: object
      description: '[schema: OrderLineDocumentStatsView]'
    OrderLineInspectionStatsView:
      properties:
        countIncompleteInspections:
          title: Countincompleteinspections
          type: integer
        countCompletedInspections:
          title: Countcompletedinspections
          type: integer
        countCompletedInspectionsWithNonConformity:
          title: Countcompletedinspectionswithnonconformity
          type: integer
      required:
      - countIncompleteInspections
      - countCompletedInspections
      - countCompletedInspectionsWithNonConformity
      title: OrderLineInspectionStatsView
      type: object
      description: '[schema: OrderLineInspectionStatsView]'
    OrderLineStatus:
      enum:
      - ACTIVE
      - COMPLETED
      title: OrderLineStatus
      type: string
      description: '[schema: OrderLineStatus]'
    OrderLineTaskInspectionView:
      properties:
        id:
          title: Id
          type: integer
        orderLineId:
          title: Orderlineid
          type: integer
        inspectionPlanId:
          title: Inspectionplanid
          type: integer
        inspectionId:
          title: Inspectionid
          type: integer
        inspectionIsFinished:
          title: Inspectionisfinished
          type: boolean
        taskCreator:
          $ref: '#/components/schemas/UserView'
        inspectionCreatorTenant:
          $ref: '#/components/schemas/TenantView'
        inspectorTenant:
          $ref: '#/components/schemas/TenantView'
        dueDate:
          anyOf:
          - format: date
            type: string
          - type: 'null'
          title: Duedate
      required:
      - id
      - orderLineId
      - inspectionPlanId
      - inspectionId
      - inspectionIsFinished
      - taskCreator
      - inspectionCreatorTenant
      - inspectorTenant
      - dueDate
      title: OrderLineTaskInspectionView
      type: object
      description: '[schema: OrderLineTaskInspectionView]'
    OrderLineTasksView:
      properties:
        tasksForInspection:
          items:
            $ref: '#/components/schemas/OrderLineTaskInspectionView'
          title: Tasksforinspection
          type: array
      required:
      - tasksForInspection
      title: OrderLineTasksView
      type: object
      description: '[schema: OrderLineTasksView]'
    OrderLineView:
      properties:
        id:
          title: Id
          type: integer
        orderId:
          title: Orderid
          type: integer
        materialId:
          title: Materialid
          type: integer
        internalReference:
          title: Internalreference
          type: string
        count:
          title: Count
          type: integer
        expectedDeliveryDate:
          format: date
          title: Expecteddeliverydate
          type: string
        status:
          $ref: '#/components/schemas/OrderLineStatus'
        chatId:
          title: Chatid
          type: integer
        inspectionStats:
          anyOf:
          - $ref: '#/components/schemas/OrderLineInspectionStatsView'
          - type: 'null'
        documentStats:
          anyOf:
          - $ref: '#/components/schemas/OrderLineDocumentStatsView'
          - type: 'null'
        requirements:
          items:
            $ref: '#/components/schemas/FileResourceLabel'
          title: Requirements
          type: array
      required:
      - id
      - orderId
      - materialId
      - internalReference
      - count
      - expectedDeliveryDate
      - status
      - chatId
      - inspectionStats
      - documentStats
      title: OrderLineView
      type: object
      description: '[schema: OrderLineView]'
    OrderType:
      enum:
      - PURCHASE
      - PRODUCTION
      title: OrderType
      type: string
      description: '[schema: OrderType]'
    OrderView:
      properties:
        id:
          title: Id
          type: integer
        customer:
          $ref: '#/components/schemas/TenantView'
        internalReference:
          title: Internalreference
          type: string
        orderDate:
          format: date
          title: Orderdate
          type: string
        info:
          discriminator:
            mapping:
              PRODUCTION: '#/components/schemas/ProductionOrderInfoView'
              PURCHASE: '#/components/schemas/PurchaseOrderInfoView'
            propertyName: type
          oneOf:
          - $ref: '#/components/schemas/PurchaseOrderInfoView'
          - $ref: '#/components/schemas/ProductionOrderInfoView'
          title: Info
        lineItems:
          items:
            $ref: '#/components/schemas/OrderLineView'
          title: Lineitems
          type: array
      required:
      - id
      - customer
      - internalReference
      - orderDate
      - info
      - lineItems
      title: OrderView
      type: object
      description: '[schema: OrderView]'
    PMIBlockAnalysisResult:
      description: '[schema: PMIBlockAnalysisResult] Schema of PMI block analysis
        (for API responses).'
      properties:
        importance:
          $ref: '#/components/schemas/Importance'
          description: 'Classifies the PMI based on its visual presentation and functional
            intent:


            - ''auxiliary'': The PMI is enclosed in **parentheses** directly surrounding
            the annotation.


            - ''theoretical'': The PMI is enclosed in a **rectangular box** directly
            surrounding the annotation.


            - ''special'': The PMI is highlighted with a **capsule-shaped elongated
            oval** that directly surrounds the annotation.


            - ''control'': The PMI is identified as GD&T or DIAMETER with explicit
            fits (e.g H7/g6) or THREAD or other types with extremely tight tolerances.


            - ''common'': The PMI is not any of the above.


            '
        count:
          default: 1
          description: Number of occurrences
          title: Count
          type: integer
        detail:
          description: Type-specific details
          discriminator:
            mapping:
              ANGLE: '#/components/schemas/AngleDetail'
              CHAMFER: '#/components/schemas/ChamferDetail'
              DIAMETER: '#/components/schemas/DiameterDetail'
              GDT: '#/components/schemas/GDTDetail'
              LINEAR: '#/components/schemas/LinearDetail'
              RADIUS: '#/components/schemas/RadiusDetail'
              ROUGHNESS: '#/components/schemas/RoughnessDetail'
              THREAD: '#/components/schemas/ThreadDetail'
            propertyName: type
          oneOf:
          - $ref: '#/components/schemas/DiameterDetail'
          - $ref: '#/components/schemas/ThreadDetail'
          - $ref: '#/components/schemas/GDTDetail'
          - $ref: '#/components/schemas/AngleDetail'
          - $ref: '#/components/schemas/LinearDetail'
          - $ref: '#/components/schemas/RadiusDetail'
          - $ref: '#/components/schemas/ChamferDetail'
          - $ref: '#/components/schemas/RoughnessDetail'
          title: Detail
      required:
      - importance
      - detail
      title: PMIBlockAnalysisResult
      type: object
    PMIMetadata:
      description: '[schema: PMIMetadata] Metadata for a PMI block, including standards
        and materials information.'
      properties:
        general_tolerance_info:
          anyOf:
          - $ref: '#/components/schemas/ExtractedGeneralTolerance'
          - type: 'null'
          default: null
          description: General tolerance standard information
        material_info:
          anyOf:
          - $ref: '#/components/schemas/ExtractedMaterial'
          - type: 'null'
          default: null
          description: Material information
      title: PMIMetadata
      type: object
    Product:
      description: '[schema: Product] Final aggregated product combining all data
        sources.

        Assembled by matching foreign keys from the three main data tables.'
      properties:
        batch_number:
          description: Unique batch/item identifier
          title: Batch Number
          type: string
        heat_number:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Heat/Smelt number
          title: Heat Number
        header_details:
          anyOf:
          - $ref: '#/components/schemas/HeaderDetails'
          - type: 'null'
          default: null
          description: Header/batch information
        chemical_composition:
          anyOf:
          - $ref: '#/components/schemas/ChemicalComposition'
          - type: 'null'
          default: null
          description: Chemical analysis results
        mechanical_properties:
          anyOf:
          - $ref: '#/components/schemas/MechanicalProperties'
          - type: 'null'
          default: null
          description: Mechanical test results
      required:
      - batch_number
      title: Product
      type: object
    ProductType:
      description: '[schema: ProductType] Product type classification.'
      enum:
      - Plate
      - Bar
      - Tube
      - Other
      title: ProductType
      type: string
    ProductionOrderInfoView:
      properties:
        type:
          const: PRODUCTION
          default: PRODUCTION
          title: Type
          type: string
      title: ProductionOrderInfoView
      type: object
      description: '[schema: ProductionOrderInfoView]'
    PurchaseOrderConnectionView:
      properties:
        tenant:
          $ref: '#/components/schemas/TenantView'
        countAsCustomer:
          title: Countascustomer
          type: integer
        countAsSupplier:
          title: Countassupplier
          type: integer
      required:
      - tenant
      - countAsCustomer
      - countAsSupplier
      title: PurchaseOrderConnectionView
      type: object
      description: '[schema: PurchaseOrderConnectionView]'
    PurchaseOrderConnectionsView:
      properties:
        connections:
          items:
            $ref: '#/components/schemas/PurchaseOrderConnectionView'
          title: Connections
          type: array
      required:
      - connections
      title: PurchaseOrderConnectionsView
      type: object
      description: '[schema: PurchaseOrderConnectionsView]'
    PurchaseOrderInfoView:
      properties:
        type:
          const: PURCHASE
          default: PURCHASE
          title: Type
          type: string
        supplier:
          $ref: '#/components/schemas/TenantView'
      required:
      - supplier
      title: PurchaseOrderInfoView
      type: object
      description: '[schema: PurchaseOrderInfoView]'
    RadiusDetail:
      description: '[schema: RadiusDetail] Details specific to radius measurements.'
      properties:
        nominal_value:
          description: Primary measurement value - if type is chamfer, this is the
            length value
          title: Nominal Value
          type: number
        lower_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Lower tolerance value
          title: Lower Tol
        upper_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Upper tolerance value
          title: Upper Tol
        unit:
          $ref: '#/components/schemas/Unit'
          description: Unit of measurement
        raw_callout:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Raw callout text
          title: Raw Callout
        type:
          const: RADIUS
          default: RADIUS
          title: Type
          type: string
      required:
      - nominal_value
      - unit
      title: RadiusDetail
      type: object
    ReportType:
      enum:
      - STANDARD
      - FAI
      - PPF10
      title: ReportType
      type: string
      description: '[schema: ReportType]'
    RoughnessDetail:
      description: '[schema: RoughnessDetail] Details specific to surface roughness.'
      properties:
        nominal_value:
          description: Primary measurement value - if type is chamfer, this is the
            length value
          title: Nominal Value
          type: number
        lower_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Lower tolerance value
          title: Lower Tol
        upper_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Upper tolerance value
          title: Upper Tol
        unit:
          $ref: '#/components/schemas/Unit'
          description: Unit of measurement
        raw_callout:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Raw callout text
          title: Raw Callout
        type:
          const: ROUGHNESS
          default: ROUGHNESS
          title: Type
          type: string
        roughness_category:
          anyOf:
          - $ref: '#/components/schemas/qw_drawing_toolkit_ocr__post_processing__models__RoughnessCategory'
          - type: 'null'
          default: null
          description: Roughness category (e.g., Ra, Ry, Rz, Rt, Rq)
      required:
      - nominal_value
      - unit
      title: RoughnessDetail
      type: object
    SearchMatch:
      properties:
        type:
          $ref: '#/components/schemas/SearchMatchType'
        id:
          title: Id
          type: integer
        matchedString:
          title: Matchedstring
          type: string
        score:
          maximum: 1
          minimum: 0
          title: Score
          type: number
      required:
      - type
      - id
      - matchedString
      - score
      title: SearchMatch
      type: object
      description: '[schema: SearchMatch]'
    SearchMatchType:
      enum:
      - TENANT
      - MATERIAL
      - ORDER
      - FILE_RESOURCE
      title: SearchMatchType
      type: string
      description: '[schema: SearchMatchType]'
    SessionInfo:
      properties:
        issuer:
          title: Issuer
          type: string
        subject:
          title: Subject
          type: string
        expirationAccessUtc:
          format: date-time
          title: Expirationaccessutc
          type: string
        expirationRefreshUtc:
          format: date-time
          title: Expirationrefreshutc
          type: string
      required:
      - issuer
      - subject
      - expirationAccessUtc
      - expirationRefreshUtc
      title: SessionInfo
      type: object
      description: '[schema: SessionInfo]'
    SetDrawingDiscussionStatusInput:
      properties:
        resolved:
          title: Resolved
          type: boolean
      required:
      - resolved
      title: SetDrawingDiscussionStatusInput
      type: object
      description: '[schema: SetDrawingDiscussionStatusInput]'
    SetDrawingDiscussionStatusOutput:
      properties: {}
      title: SetDrawingDiscussionStatusOutput
      type: object
      description: '[schema: SetDrawingDiscussionStatusOutput]'
    TechnicalDrawingAnalysisResult:
      description: '[schema: TechnicalDrawingAnalysisResult] Result model for technical
        drawing analysis.'
      properties:
        results:
          additionalProperties:
            items:
              $ref: '#/components/schemas/NormalizedPMIBlockAnalysisResult'
            type: array
          description: Dictionary mapping classifications to lists of normalized PMI
            results
          title: Results
          type: object
        metadata:
          anyOf:
          - $ref: '#/components/schemas/PMIMetadata'
          - type: 'null'
          default: null
          description: Metadata for the entire drawing, including standards and materials
        status:
          title: Status
          type: string
      required:
      - results
      - status
      title: TechnicalDrawingAnalysisResult
      type: object
    TechnicalDrawingAnalysisStatus:
      description: '[schema: TechnicalDrawingAnalysisStatus] Status model for technical
        drawing analysis.'
      properties:
        status:
          title: Status
          type: string
        message:
          title: Message
          type: string
      required:
      - status
      - message
      title: TechnicalDrawingAnalysisStatus
      type: object
    TemplateDiscoveryContext:
      properties:
        orderLineId:
          title: Orderlineid
          type: integer
        materialId:
          title: Materialid
          type: integer
      required:
      - orderLineId
      - materialId
      title: TemplateDiscoveryContext
      type: object
      description: '[schema: TemplateDiscoveryContext]'
    TenantConnectionView:
      properties:
        tenant:
          $ref: '#/components/schemas/TenantView'
        role:
          $ref: '#/components/schemas/TenantRole'
      required:
      - tenant
      - role
      title: TenantConnectionView
      type: object
      description: '[schema: TenantConnectionView]'
    TenantExtendedView:
      properties:
        id:
          title: Id
          type: integer
        prettyName:
          title: Prettyname
          type: string
        connections:
          items:
            $ref: '#/components/schemas/TenantConnectionView'
          title: Connections
          type: array
      required:
      - id
      - prettyName
      - connections
      title: TenantExtendedView
      type: object
      description: '[schema: TenantExtendedView]'
    TenantRole:
      enum:
      - SUPPLIER
      - CUSTOMER
      title: TenantRole
      type: string
      description: '[schema: TenantRole]'
    TenantView:
      properties:
        id:
          title: Id
          type: integer
        prettyName:
          title: Prettyname
          type: string
      required:
      - id
      - prettyName
      title: TenantView
      type: object
      description: '[schema: TenantView]'
    TensileTest:
      description: '[schema: TensileTest] Tensile test results including strength
        and elongation properties.'
      properties:
        test_direction:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: 'Direction of testing: L (Longitudinal) or T (Transverse).
            Most common is T.'
          title: Test Direction
        test_temperature_celsius:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: "Test temperature in \xB0C, commonly 20\xB0C"
          title: Test Temperature Celsius
        tensile_strength_mpa:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: 'Rm: Ultimate tensile strength in MPa'
          title: Tensile Strength Mpa
        yield_strength_mpa:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: 'ReH: Yield strength in MPa'
          title: Yield Strength Mpa
        elongation_percent:
          anyOf:
          - type: number
          - type: string
          - type: 'null'
          default: null
          description: 'A%: Elongation at break in %'
          title: Elongation Percent
      title: TensileTest
      type: object
    ThreadDetail:
      description: '[schema: ThreadDetail] Details specific to threaded features.'
      properties:
        nominal_value:
          description: Primary measurement value - if type is chamfer, this is the
            length value
          title: Nominal Value
          type: number
        lower_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Lower tolerance value
          title: Lower Tol
        upper_tol:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Upper tolerance value
          title: Upper Tol
        unit:
          $ref: '#/components/schemas/Unit'
          description: Unit of measurement
        raw_callout:
          anyOf:
          - type: string
          - type: 'null'
          default: null
          description: Raw callout text
          title: Raw Callout
        type:
          const: THREAD
          default: THREAD
          title: Type
          type: string
        pitch:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          title: Pitch
        iso965_identifier:
          anyOf:
          - pattern: ^[0-9][a-zA-Z]([0-9][a-zA-Z])?$
            type: string
          - type: 'null'
          default: null
          title: Iso965 Identifier
        thread_standard:
          anyOf:
          - $ref: '#/components/schemas/ThreadStandard'
          - type: 'null'
          default: null
          description: Thread Standard
        depth:
          anyOf:
          - type: number
          - type: 'null'
          default: null
          description: Depth value if specified
          title: Depth
        depth_unit:
          anyOf:
          - $ref: '#/components/schemas/Unit'
          - type: 'null'
          default: null
          description: Unit for depth measurement
      required:
      - nominal_value
      - unit
      title: ThreadDetail
      type: object
    ThreadStandard:
      enum:
      - M
      - UNC
      - UNF
      - BSP
      - NPT
      - TR
      title: ThreadStandard
      type: string
      description: '[schema: ThreadStandard]'
    TileInfo:
      properties:
        x:
          title: X
          type: integer
        y:
          title: Y
          type: integer
        width:
          title: Width
          type: integer
        height:
          title: Height
          type: integer
        data:
          title: Data
          type: string
      required:
      - x
      - y
      - width
      - height
      - data
      title: TileInfo
      type: object
      description: '[schema: TileInfo]'
    ToggleFileResourceRevisionAccessInput:
      properties:
        fileResourceRevisionId:
          title: Fileresourcerevisionid
          type: integer
        tenantId:
          title: Tenantid
          type: integer
        enabled:
          title: Enabled
          type: boolean
      required:
      - fileResourceRevisionId
      - tenantId
      - enabled
      title: ToggleFileResourceRevisionAccessInput
      type: object
      description: '[schema: ToggleFileResourceRevisionAccessInput]'
    ToggleFileResourceRevisionAccessOutput:
      properties:
        success:
          default: true
          title: Success
          type: boolean
      title: ToggleFileResourceRevisionAccessOutput
      type: object
      description: '[schema: ToggleFileResourceRevisionAccessOutput]'
    ToleranceLookupInput:
      properties:
        toleranceName:
          title: Tolerancename
          type: string
        value:
          title: Value
          type: number
        tableName:
          title: Tablename
          type: string
        rowIdentifier:
          items:
            type: string
          title: Rowidentifier
          type: array
      required:
      - toleranceName
      - value
      - tableName
      - rowIdentifier
      title: ToleranceLookupInput
      type: object
      description: '[schema: ToleranceLookupInput]'
    ToleranceLookupOutput:
      properties:
        tolerance:
          anyOf:
          - $ref: '#/components/schemas/ToleranceLookupResult'
          - type: 'null'
      required:
      - tolerance
      title: ToleranceLookupOutput
      type: object
      description: '[schema: ToleranceLookupOutput]'
    ToleranceLookupResult:
      properties:
        upper:
          title: Upper
          type: number
        lower:
          title: Lower
          type: number
        unit:
          $ref: '#/components/schemas/ToleranceUnit'
      required:
      - upper
      - lower
      - unit
      title: ToleranceLookupResult
      type: object
      description: '[schema: ToleranceLookupResult]'
    ToleranceSource:
      enum:
      - EXPLICIT
      - ISO_2768
      - ISO_286
      title: ToleranceSource
      type: string
      description: '[schema: ToleranceSource]'
    ToleranceStandardView:
      properties:
        name:
          title: Name
          type: string
        tables:
          items:
            $ref: '#/components/schemas/ToleranceTableView'
          title: Tables
          type: array
      required:
      - name
      - tables
      title: ToleranceStandardView
      type: object
      description: '[schema: ToleranceStandardView]'
    ToleranceTableView:
      properties:
        name:
          title: Name
          type: string
        valueUnit:
          $ref: '#/components/schemas/ToleranceUnit'
        toleranceUnit:
          $ref: '#/components/schemas/ToleranceUnit'
        rowIdentifiers:
          items:
            items:
              type: string
            type: array
          title: Rowidentifiers
          type: array
      required:
      - name
      - valueUnit
      - toleranceUnit
      - rowIdentifiers
      title: ToleranceTableView
      type: object
      description: '[schema: ToleranceTableView]'
    ToleranceUnit:
      enum:
      - mm
      - deg
      title: ToleranceUnit
      type: string
      description: '[schema: ToleranceUnit]'
    TolerancesView:
      properties:
        toleranceStandards:
          items:
            $ref: '#/components/schemas/ToleranceStandardView'
          title: Tolerancestandards
          type: array
      required:
      - toleranceStandards
      title: TolerancesView
      type: object
      description: '[schema: TolerancesView]'
    Unit:
      description: '[schema: Unit] Measurement unit for PMI values.'
      enum:
      - MILLIMETER
      - MICROMETER
      - DEGREE
      title: Unit
      type: string
    UnlinkFileFromMaterialInput:
      properties:
        fileResourceRevisionId:
          title: Fileresourcerevisionid
          type: integer
        materialId:
          title: Materialid
          type: integer
      required:
      - fileResourceRevisionId
      - materialId
      title: UnlinkFileFromMaterialInput
      type: object
      description: '[schema: UnlinkFileFromMaterialInput]'
    UnlinkFileFromMaterialOutput:
      properties:
        success:
          title: Success
          type: boolean
      required:
      - success
      title: UnlinkFileFromMaterialOutput
      type: object
      description: '[schema: UnlinkFileFromMaterialOutput]'
    UpdateInspectionPlanInput:
      properties:
        inspectionPlan:
          $ref: '#/components/schemas/InspectionPlan'
        editTimestamp:
          format: date-time
          title: Edittimestamp
          type: string
      required:
      - inspectionPlan
      - editTimestamp
      title: UpdateInspectionPlanInput
      type: object
      description: '[schema: UpdateInspectionPlanInput]'
    UpdateInspectionPlanOutput:
      properties:
        newEditTimestamp:
          format: date-time
          title: Newedittimestamp
          type: string
      required:
      - newEditTimestamp
      title: UpdateInspectionPlanOutput
      type: object
      description: '[schema: UpdateInspectionPlanOutput]'
    UpdateOrderLineRequirementsInput:
      properties:
        requirements:
          items:
            $ref: '#/components/schemas/FileResourceLabel'
          title: Requirements
          type: array
      title: UpdateOrderLineRequirementsInput
      type: object
      description: '[schema: UpdateOrderLineRequirementsInput]'
    UpdateOrderLineRequirementsOutput:
      properties: {}
      title: UpdateOrderLineRequirementsOutput
      type: object
      description: '[schema: UpdateOrderLineRequirementsOutput]'
    UpdateOrderLineTaskDueDateInput:
      properties:
        dueDate:
          anyOf:
          - format: date
            type: string
          - type: 'null'
          title: Duedate
      required:
      - dueDate
      title: UpdateOrderLineTaskDueDateInput
      type: object
      description: '[schema: UpdateOrderLineTaskDueDateInput]'
    UpdateOrderLineTaskDueDateOutput:
      properties: {}
      title: UpdateOrderLineTaskDueDateOutput
      type: object
      description: '[schema: UpdateOrderLineTaskDueDateOutput]'
    UserView:
      properties:
        id:
          title: Id
          type: integer
        tenantId:
          anyOf:
          - type: integer
          - type: 'null'
          title: Tenantid
        givenName:
          title: Givenname
          type: string
        familyName:
          title: Familyname
          type: string
        email:
          title: Email
          type: string
      required:
      - id
      - tenantId
      - givenName
      - familyName
      - email
      title: UserView
      type: object
      description: '[schema: UserView]'
    WhoamiView:
      properties:
        id:
          title: Id
          type: integer
        tenantId:
          anyOf:
          - type: integer
          - type: 'null'
          title: Tenantid
        givenName:
          title: Givenname
          type: string
        familyName:
          title: Familyname
          type: string
        email:
          title: Email
          type: string
      required:
      - id
      - tenantId
      - givenName
      - familyName
      - email
      title: WhoamiView
      type: object
      description: '[schema: WhoamiView]'
    WopiAccessTokenView:
      properties:
        accessToken:
          title: Accesstoken
          type: string
      required:
      - accessToken
      title: WopiAccessTokenView
      type: object
      description: '[schema: WopiAccessTokenView]'
    WopiActionView:
      properties:
        extension:
          title: Extension
          type: string
        appName:
          title: Appname
          type: string
        favIconUrl:
          anyOf:
          - type: string
          - type: 'null'
          title: Faviconurl
        name:
          title: Name
          type: string
        urlSrc:
          title: Urlsrc
          type: string
      required:
      - extension
      - appName
      - favIconUrl
      - name
      - urlSrc
      title: WopiActionView
      type: object
      description: '[schema: WopiActionView]'
    WopiCheckFileInfoResponse:
      properties:
        BaseFileName:
          title: Basefilename
          type: string
        OwnerId:
          pattern: ^[a-zA-Z0-9]+$
          title: Ownerid
          type: string
        UserId:
          pattern: ^[a-zA-Z0-9]+$
          title: Userid
          type: string
        Size:
          title: Size
          type: integer
        LastModifiedTime:
          format: date-time
          title: Lastmodifiedtime
          type: string
        UserFriendlyName:
          title: Userfriendlyname
          type: string
        UserCanWrite:
          default: false
          title: Usercanwrite
          type: boolean
        UserCanNotWriteRelative:
          default: true
          title: Usercannotwriterelative
          type: boolean
        HidePrintOption:
          default: false
          title: Hideprintoption
          type: boolean
        DisablePrint:
          default: false
          title: Disableprint
          type: boolean
        HideSaveOption:
          default: false
          title: Hidesaveoption
          type: boolean
        HideExportOption:
          default: false
          title: Hideexportoption
          type: boolean
        DisableExport:
          default: false
          title: Disableexport
          type: boolean
        DisableCopy:
          default: false
          title: Disablecopy
          type: boolean
        EnableOwnerTermination:
          default: false
          title: Enableownertermination
          type: boolean
        IsUserLocked:
          default: false
          title: Isuserlocked
          type: boolean
        IsUserRestricted:
          default: false
          title: Isuserrestricted
          type: boolean
      required:
      - BaseFileName
      - OwnerId
      - UserId
      - Size
      - LastModifiedTime
      - UserFriendlyName
      title: WopiCheckFileInfoResponse
      type: object
      description: '[schema: WopiCheckFileInfoResponse]'
    WopiDiscoveryView:
      properties:
        actions:
          items:
            $ref: '#/components/schemas/WopiActionView'
          title: Actions
          type: array
      required:
      - actions
      title: WopiDiscoveryView
      type: object
      description: '[schema: WopiDiscoveryView]'
    WopiPutFileResponse:
      properties: {}
      title: WopiPutFileResponse
      type: object
      description: '[schema: WopiPutFileResponse]'
    qw_drawing_toolkit_ocr__post_processing__models__GDTSymbol:
      description: '[schema: qw_drawing_toolkit_ocr__post_processing__models__GDTSymbol]
        Geometric Dimensioning and Tolerancing symbols.'
      enum:
      - FLATNESS
      - STRAIGHTNESS
      - CIRCULARITY
      - CYLINDRICITY
      - PROFILE_OF_LINE
      - PROFILE_OF_SURFACE
      - PERPENDICULARITY
      - ANGULARITY
      - PARALLELISM
      - CONCENTRICITY
      - SYMMETRY
      - POSITION
      - CIRCULAR_RUNOUT
      - TOTAL_RUNOUT
      title: GDTSymbol
      type: string
    qw_drawing_toolkit_ocr__post_processing__models__RoughnessCategory:
      description: '[schema: qw_drawing_toolkit_ocr__post_processing__models__RoughnessCategory]
        Roughness category for surface finish.'
      enum:
      - RA
      - RZ
      - RY
      - RT
      - RQ
      title: RoughnessCategory
      type: string
    qw_inspection__spec__plan__GDTSymbol:
      enum:
      - FLATNESS
      - STRAIGHTNESS
      - CIRCULARITY
      - CYLINDRICITY
      - PROFILE_OF_LINE
      - PROFILE_OF_SURFACE
      - PERPENDICULARITY
      - ANGULARITY
      - PARALLELISM
      - CONCENTRICITY
      - SYMMETRY
      - POSITION
      - CIRCULAR_RUNOUT
      - TOTAL_RUNOUT
      title: GDTSymbol
      type: string
      description: '[schema: qw_inspection__spec__plan__GDTSymbol]'
    qw_inspection__spec__plan__RoughnessCategory:
      enum:
      - RA
      - RZ
      - RY
      - RT
      - RQ
      title: RoughnessCategory
      type: string
      description: '[schema: qw_inspection__spec__plan__RoughnessCategory]'
  parameters: {}
  securitySchemes:
    session_token:
      type: apiKey
      name: session_token
      in: cookie
tags: []
