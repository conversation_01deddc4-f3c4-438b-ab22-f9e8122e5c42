from pathlib import PurePosixPath
from typing import <PERSON>ary<PERSON>, Generator, List, Literal

import minio
import minio.datatypes
import urllib3.exceptions

# from minio import Minio
# from minio.datatypes import Bucket
from pydantic import BaseModel
from urllib3 import HTTPResponse

from qw_basic_s3.impl.common import normalize_path_to_str
from qw_basic_s3.interface import S3Object, S3ObjectBytes, S3StorageError
from qw_basic_s3.schema import S3StorageSchema
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class MinioS3Storage(object):
    def __init__(
        self,
        schema: S3StorageSchema,
        host: str,
        access_key: str | None,
        secret_key: str | None,
        secure: bool,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.schema = schema
        self.host = host
        self.client = minio.Minio(host, access_key=access_key, secret_key=secret_key, secure=secure)
        self.logger = lf.get_logger(__name__)

    # --- interface implementation below ---

    def get_name(self) -> str:
        return self.host

    def get_schema(self) -> S3StorageSchema:
        return self.schema

    def create_bucket(self, name: str) -> bool:
        existed_already = False
        try:
            self.client.make_bucket(name)
        except minio.error.S3Error as e:
            if e.code != "BucketAlreadyOwnedByYou":
                raise
            existed_already = True

        self.logger.info(f"Created bucket '{name}' (existed_already={existed_already})")
        return True

    def remove_bucket(self, name: str) -> bool:
        self.client.remove_bucket(name)
        self.logger.info(f"Removed bucket '{name}'")
        return True

    def bucket_exists(self, name: str) -> bool:
        exists: bool = self.client.bucket_exists(name)
        return exists

    def list_buckets(self) -> List[str]:
        buckets: List[minio.datatypes.Bucket] = self.client.list_buckets()
        return [b.name for b in buckets]

    def put_object(
        self,
        bucket: str,
        path: PurePosixPath,
        bio: BinaryIO,
        size: int = -1,
        content_type: str = "application/octet-stream",
        part_size: int = 0,
    ) -> bool:
        path_as_str = normalize_path_to_str(path, trailing_slash="")
        self.client.put_object(bucket, path_as_str, bio, size, content_type, part_size=part_size)
        self.logger.info(f"Put object '{path_as_str}'")
        return True

    def remove_object(self, bucket: str, path: PurePosixPath) -> bool:
        path_as_str = normalize_path_to_str(path, trailing_slash="")
        self.client.remove_object(bucket, path_as_str)
        self.logger.info(f"Removed object '{path_as_str}'")
        return True

    def get_object(self, bucket: str, path: PurePosixPath, offset: int = 0, length: int = 0) -> S3ObjectBytes:
        path_as_str = normalize_path_to_str(path, trailing_slash="")
        response: HTTPResponse = self.client.get_object(bucket, path_as_str, offset, length)
        try:
            data = response.read(decode_content=False)
            content_type: str = response.getheader("content-type") or "application/octet-stream"
            return S3ObjectBytes(data, content_type)
        except urllib3.exceptions.HTTPError:
            raise S3StorageError(f"Could not retrieve bytes of {path_as_str}")
        finally:
            response.close()
            response.release_conn()

    def list_objects(
        self,
        bucket: str,
        prefix: PurePosixPath | None,
        trailing_slash: Literal["", "/", "auto"] = "auto",
        recursive: bool = False,
    ) -> Generator[S3Object, None, None]:
        prefix_as_str = None
        if prefix is not None:
            prefix_as_str = normalize_path_to_str(prefix, trailing_slash, can_be_empty=True)
        for obj in self.client.list_objects(bucket, prefix_as_str, recursive=recursive):
            obj2 = S3Object(path=PurePosixPath(obj.object_name), is_dir=obj.is_dir)
            yield obj2


class MinioS3StorageConfig(BaseModel):
    type: Literal["minio"] = "minio"
    host: str
    access_key: str | None
    secret_key: str | None
    secure: bool

    def build(self, storage_schema: S3StorageSchema, lf: LogFactory = NO_LOG_FACTORY) -> MinioS3Storage:
        return MinioS3Storage(
            schema=storage_schema,
            host=self.host,
            access_key=self.access_key,
            secret_key=self.secret_key,
            secure=self.secure,
            lf=lf,
        )
