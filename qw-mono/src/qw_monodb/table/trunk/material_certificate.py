# src/qw_monodb/table/trunk/material_cert.py
from enum import Enum, unique

from sqlalchemy import Foreign<PERSON>ey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from qw_monodb.table.base import Base


@unique
class MaterialCertificateAnalysisStatus(str, Enum):
    ANALYZING = "ANALYZING"  # Analysis in progress
    COMPLETED = "COMPLETED"  # Successfully analyzed
    FAILURE = "FAILURE"  # Analysis failed
    PENDING = "PENDING"  # Initial state, queued for analysis


class MaterialCertificateAnalysis(Base):
    """Tracks material certification analysis status and results."""

    __tablename__ = "material_certificate_analysis"

    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    analysis_obj_id: Mapped[int | None] = mapped_column(ForeignKey("s3_object_reference.id"), nullable=True)
    analysis_status: Mapped[MaterialCertificateAnalysisStatus]


class MaterialCertificatePage(Base):
    """Stores processed pages of the certificate."""

    __tablename__ = "material_certificate_page"
    __table_args__ = (
        UniqueConstraint("file_resource_revision_id", "page_index", name="unique_material_certificate_page"),
    )

    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    page_index: Mapped[int]
    image_obj_id: Mapped[int] = mapped_column(ForeignKey("s3_object_reference.id"))
