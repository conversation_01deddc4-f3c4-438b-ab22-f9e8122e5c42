from typing import Any, Dict

import falcon.testing

from qw_basic_keycloak.interface import Code<PERSON><PERSON><PERSON>uthInit
from qw_basic_keycloak.realm import Realm
from qw_basic_keycloak.testing.auth import CodeFlowAuthMock
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class CodeFlow(object):
    def __init__(
        self,
        client: falcon.testing.TestClient,
        auth_mock: CodeFlowAuthMock,
        url_login: str,
        url_redirect: str,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.client = client
        self.auth_mock = auth_mock
        self.url_login = url_login
        self.url_redirect = url_redirect
        self.logger = lf.get_logger(__name__)

    def login(self, username: str, password: str) -> Dict[str, Any]:
        resp_login = self.client.simulate_get(self.url_login)
        init_data = CodeFlowAuthInit(**resp_login.json)
        auth_data = self.auth_mock.auth(init_data, username, password)
        resp_redirect = self.client.simulate_get(self.url_redirect, params=auth_data.model_dump())
        if resp_redirect.status_code != 200:
            self.logger.warning(f"Got HTTP {resp_redirect.status_code} from redirect endpoint")
        data: Dict[str, Any] = resp_redirect.json
        return data

    def login_for(self, realm: Realm, username: str) -> Dict[str, Any]:
        secret = realm.secrets.by_mail.get(username)
        if secret is None:
            raise ValueError(f"Could not find username {username} in realm")
        return self.login(username, secret)
