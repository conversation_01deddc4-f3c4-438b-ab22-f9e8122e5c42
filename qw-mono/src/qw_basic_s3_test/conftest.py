import pytest

from qw_basic_s3.devutil import S3Mode, S3TestFactory
from qw_basic_s3.impl.minio import MinioS3StorageConfig
from qw_basic_s3.interface import S3Storage
from qw_basic_s3.schema import S3StorageSchema

OPT_S3 = "--s3"


# # TODO: add once libraries are moved into separate repos
# def pytest_addoption(parser: pytest.Parser) -> None:
#     parser.addoption(
#         OPT_S3,
#         action="store",
#         default=S3Impl.IN_MEMORY,
#         help=f"specify which s3 mode to use: {', '.join(S3Mode)}",
#     )


@pytest.fixture(scope="function")  # create for each test!
def s3(request: pytest.FixtureRequest) -> S3Storage:
    mode = S3Mode(request.config.getoption(OPT_S3))
    return S3TestFactory(
        bucket_prefix="s3-storage-test",
        minio_cfg=MinioS3StorageConfig(
            host="127.0.0.1:9000", access_key="minio_dev_user", secret_key="minio_dev_user", secure=False
        ),
        mode=mode,
    ).create(S3StorageSchema.create_simple())
