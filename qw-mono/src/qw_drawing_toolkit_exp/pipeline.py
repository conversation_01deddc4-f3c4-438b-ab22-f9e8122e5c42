import io
import time
from typing import Callable, List

from pydantic import BaseModel, Field

from qw_drawing_toolkit.engine import QwDrawingToolkit
from qw_drawing_toolkit.pdf.models.config_models import DrawingAnalyzerOpts
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysis, DrawingObjectWithBoundingBox
from qw_drawing_toolkit_exp.matcher import compute_bounding_box_matches
from qw_drawing_toolkit_exp.sink.result import ComparisonCounts, ComparisonMatch, ComparisonRates, ComparisonResult
from qw_drawing_toolkit_exp.source.interface import ExperimentSampleInterface, ExperimentSourceInterface
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class ExperimentSettings(BaseModel):
    iou_threshold: float = Field(
        default=0.5,
        description="The minimum value for intersection over union for two boxes to be considered to be a match",
    )
    analysis_opts: DrawingAnalyzerOpts = Field(default_factory=DrawingAnalyzerOpts)


PerformDrawingAnalysis = Callable[[io.BytesIO], DrawingAnalysis]


def get_results_from_analysis(analysis: DrawingAnalysis) -> List[DrawingObjectWithBoundingBox]:
    results: List[DrawingObjectWithBoundingBox] = []
    results.extend(analysis.result.lengths)
    results.extend(analysis.result.angles)
    results.extend(analysis.result.radii)
    results.extend(analysis.result.diameters)
    results.extend(analysis.result.threads)
    results.extend(analysis.result.countersinks)
    results.extend(analysis.result.counterbores)
    results.extend(analysis.result.gdt_frames)
    results.extend(analysis.result.gdt_datums)
    return results


def process_sample(
    toolkit: QwDrawingToolkit,
    sample: ExperimentSampleInterface,
    settings: ExperimentSettings,
) -> ComparisonResult:
    _, analysis = toolkit.analyse_pdf(
        sample.get_pdf_bytes(),
        opts=settings.analysis_opts,
        close_pdf_file=True,
    )

    analysis_expected = sample.get_analysis()

    results = get_results_from_analysis(analysis)
    results_expected = get_results_from_analysis(analysis_expected)
    matching_result = compute_bounding_box_matches(
        actual_bboxes=[r.bounding_box for r in results],
        expected_bboxes=[r.bounding_box for r in results_expected],
        iou_threshold=settings.iou_threshold,
    )

    counts = ComparisonCounts(actual=len(results), expected=len(results_expected))
    diffs = []

    for act_index, exp_indices in matching_result.iter_over_match_idx_for_actual_bboxes():
        mfe: List[BaseModel] = [results_expected[ei] for ei in exp_indices]
        match = ComparisonMatch(
            actual=results[act_index],
            matched_from_expected=mfe,
        )

        if match.is_correct(ignore_properties=["bounding_box"]):
            counts.match_correct += 1
        else:
            diffs.append(match)
            if match.misses_expected:
                counts.match_without_expected += 1
            else:
                counts.match_incorrect += 1

    for exp_index in matching_result.get_unmatched_expected_bboxes():
        match = ComparisonMatch(
            actual=None,
            matched_from_expected=[results_expected[exp_index]],
        )
        counts.match_without_actual += 1
        diffs.append(match)

    return ComparisonResult(
        sample_id=sample.get_id(),
        counts=counts,
        rates=ComparisonRates.from_counts(counts),
        diffs=diffs,
    )


def run_experiment(
    toolkit: QwDrawingToolkit,
    source: ExperimentSourceInterface,
    settings: ExperimentSettings,
    lf: LogFactory = NO_LOG_FACTORY,
) -> None:
    logger = lf.get_logger(__name__)

    comparisons = []
    overall_counts = ComparisonCounts()

    for i in range(source.get_count()):
        sample = source.get_sample(i)

        t0 = time.time()
        c = process_sample(toolkit, sample, settings)
        dt = time.time() - t0

        comparisons.append(c)
        overall_counts.add(c.counts)

        logger.info(
            f"sample={sample.get_id()} boxes_exp={c.counts.expected} boxes_act={c.counts.actual} "
            f"precision={c.rates.precision:.2f} recall={c.rates.recall:.2f} "
            f"misdetections={c.rates.misdetection:.2f} duration={dt:.3}s"
        )

    overall_rates = ComparisonRates.from_counts(overall_counts)
    logger.info(
        f"overall ({len(comparisons)} samples) boxes_exp={overall_counts.expected} boxes_act={overall_counts.actual} "
        f"precision={overall_rates.precision:.2f} recall={overall_rates.recall:.2f} "
        f"misdetections={overall_rates.misdetection:.2f}"
    )
