import re
from pathlib import PurePosixPath
from typing import Any, Callable, Generic, Literal, TypeVar

SEP = "/"


def check_bucket_name(name: str) -> None:
    # https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucketnamingrules.html
    pattern = r"[a-z0-9][a-z0-9-.]{1,61}[a-z0-9]"
    if re.match(pattern, name) is None:
        raise ValueError(f"Bucket name must match the pattern {pattern}")

    if ".." in name:
        raise ValueError("Bucket name must not contain '..'")

    for prefix in ["xn--", "sthree-", "sthree-configurator"]:
        if name.startswith(prefix):
            raise ValueError(f"Bucket name must not start with {prefix}")

    for suffix in ["-s3alias", "--ol-s3"]:
        if name.endswith(suffix):
            raise ValueError(f"Bucket name must not end with {suffix}")


def normalize_path(path: PurePosixPath) -> PurePosixPath:
    if path.is_absolute():
        path = path.relative_to(SEP)
    if ".." in path.parts:
        raise ValueError(f"Object name {path} must not contain ..\n{path}")
    # https://min.io/docs/minio/kubernetes/upstream/operations/checklists/thresholds.html#id1
    max_len_name = 1024
    max_len_name_segment = 255
    if len(str(path)) > max_len_name:
        raise ValueError(f"Object name {path} is longer than {max_len_name}")
    if any(len(part) > max_len_name_segment for part in path.parts):
        raise ValueError(
            f"Object name {path} has one or more parts (separated by '/') that are longer than {max_len_name_segment}"
        )
    return path


def path_to_str(path: PurePosixPath, trailing_slash: Literal["", "/", "auto"], can_be_empty: bool = False) -> str:
    prefix_as_str = str(path)
    if prefix_as_str == ".":
        if not can_be_empty:
            raise ValueError("Object name is empty")
        if trailing_slash == "" or trailing_slash == "auto":
            return ""
        else:
            return SEP
    if trailing_slash == SEP or (trailing_slash == "auto" and path.suffix == ""):
        prefix_as_str = prefix_as_str + SEP
    return prefix_as_str


def path_has_trailing_slash(
    path: PurePosixPath, trailing_slash: Literal["", "/", "auto"], can_be_empty: bool = False
) -> bool:
    return path_to_str(path, trailing_slash, can_be_empty).endswith(SEP)


def normalize_path_to_str(
    path: PurePosixPath, trailing_slash: Literal["", "/", "auto"], can_be_empty: bool = False
) -> str:
    path = normalize_path(path)
    return path_to_str(path, trailing_slash, can_be_empty)


T_OBJ = TypeVar("T_OBJ")


class SimpleContextManager(Generic[T_OBJ]):
    """https://docs.python.org/3/library/stdtypes.html#typecontextmanager"""

    def __init__(self, obj: T_OBJ, on_exit: Callable[[], None] | None = None):
        self.obj = obj
        self.on_exit = on_exit

    def __enter__(self) -> T_OBJ:
        return self.obj

    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        if self.on_exit is not None:
            self.on_exit()
        # do not suppress any exceptions that happened between __enter__ and __exit__, None will be evaluated as False
        # https://mypy.readthedocs.io/en/stable/error_code_list.html#check-the-return-type-of-exit-exit-return
