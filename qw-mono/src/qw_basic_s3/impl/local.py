import io
import json
import os
from pathlib import Path, PurePosixPath
from typing import List, Literal

from pydantic import BaseModel

from qw_basic_s3.impl.memory import MemoryS3Storage
from qw_basic_s3.schema import S3StorageSchema
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class MemoryS3ObjectSnapshot(BaseModel):
    bucket: str
    path: PurePosixPath
    id: int
    content_type: str


class MemoryS3Snapshot(BaseModel):
    objects: List[MemoryS3ObjectSnapshot]


class MemoryS3Serializer(object):
    def __init__(self, path: Path):
        self.path = path

    def get_object_path(self, object_id: int) -> Path:
        return self.path.with_name(f"{self.path.stem}-{object_id}.obj")

    def serialize(self, storage: MemoryS3Storage, only_put: int | None = None, only_remove: int | None = None) -> None:
        snapshots = []
        for bucket, mem_dir in storage.buckets.items():
            for ref, obj in mem_dir.traverse_all_objects():
                if not ref.is_dir and obj is not None:
                    obj_id = obj.id
                    snapshots.append(
                        MemoryS3ObjectSnapshot(bucket=bucket, path=ref.path, id=obj_id, content_type=obj.content_type)
                    )
                    if only_remove is None and (only_put is None or only_put == obj_id):
                        obj.write_data_to(self.get_object_path(obj_id))

        if only_remove is not None:
            p = self.get_object_path(only_remove)
            if p.exists():
                os.remove(p)

        memory_snapshot = MemoryS3Snapshot(objects=snapshots)
        with self.path.open("w") as f:
            memory_snapshot_json = memory_snapshot.model_dump_json(indent=2)
            f.write(memory_snapshot_json)

    def deserialize(self, storage: MemoryS3Storage) -> None:
        if not self.path.exists():
            return

        with self.path.open("r") as f:
            data = json.load(f)
        memory_snapshot = MemoryS3Snapshot(**data)

        for object_snapshot in sorted(memory_snapshot.objects, key=lambda obj: obj.id):
            bucket = object_snapshot.bucket
            if not storage.bucket_exists(bucket):
                storage.create_bucket(bucket)

            with self.get_object_path(object_snapshot.id).open("rb") as f:
                data = f.read()
            storage.next_obj_id = object_snapshot.id
            storage.put_object(
                bucket,
                object_snapshot.path,
                io.BytesIO(data),
                size=len(data),
                content_type=object_snapshot.content_type,
            )


class LocalS3Storage(MemoryS3Storage):
    def __init__(self, schema: S3StorageSchema, path: Path, lf: LogFactory = NO_LOG_FACTORY):
        super().__init__(schema, lf)
        self.serializer = MemoryS3Serializer(path)
        self.serializer.deserialize(self)

        def hook(only_put_id: int | None = None, only_remove_id: int | None = None) -> None:
            self.serializer.serialize(self, only_put_id, only_remove_id)

        # set hook afterward to avoid serialization during deserialization
        self.serialize_hook = hook

    def get_name(self) -> str:
        return str(self.serializer.path)


class LocalS3StorageConfig(BaseModel):
    type: Literal["local"] = "local"
    path: Path

    def build(self, storage_schema: S3StorageSchema, lf: LogFactory = NO_LOG_FACTORY) -> LocalS3Storage:
        return LocalS3Storage(storage_schema, path=self.path, lf=lf)
