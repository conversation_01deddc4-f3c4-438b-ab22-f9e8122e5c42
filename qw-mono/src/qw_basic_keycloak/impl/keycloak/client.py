import time
from typing import List

import requests
from joserfc import jwk
from pydantic import AnyHttpUrl, BaseModel

from qw_log_interface import NO_LOG_FACTORY, LogFactory


class OpenIdConfigurationMTlsAliases(BaseModel):
    token_endpoint: AnyHttpUrl
    revocation_endpoint: AnyHttpUrl
    introspection_endpoint: AnyHttpUrl
    device_authorization_endpoint: AnyHttpUrl
    registration_endpoint: AnyHttpUrl
    userinfo_endpoint: AnyHttpUrl
    pushed_authorization_request_endpoint: AnyHttpUrl
    backchannel_authentication_endpoint: AnyHttpUrl


class OpenIdConfiguration(BaseModel):
    issuer: AnyHttpUrl  # TODO: relax to string?
    authorization_endpoint: AnyHttpUrl
    token_endpoint: AnyHttpUrl
    introspection_endpoint: AnyHttpUrl
    userinfo_endpoint: AnyHttpUrl
    end_session_endpoint: AnyHttpUrl
    frontchannel_logout_session_supported: bool
    frontchannel_logout_supported: bool
    jwks_uri: AnyHttpUrl
    check_session_iframe: AnyHttpUrl
    grant_types_supported: List[str]
    acr_values_supported: List[str]
    response_types_supported: List[str]
    subject_types_supported: List[str]
    id_token_signing_alg_values_supported: List[str]
    id_token_encryption_alg_values_supported: List[str]
    id_token_encryption_enc_values_supported: List[str]
    userinfo_signing_alg_values_supported: List[str]
    userinfo_encryption_alg_values_supported: List[str]
    userinfo_encryption_enc_values_supported: List[str]
    request_object_signing_alg_values_supported: List[str]
    request_object_encryption_alg_values_supported: List[str]
    request_object_encryption_enc_values_supported: List[str]
    response_modes_supported: List[str]
    registration_endpoint: AnyHttpUrl
    token_endpoint_auth_methods_supported: List[str]
    token_endpoint_auth_signing_alg_values_supported: List[str]
    introspection_endpoint_auth_methods_supported: List[str]
    introspection_endpoint_auth_signing_alg_values_supported: List[str]
    authorization_signing_alg_values_supported: List[str]
    authorization_encryption_alg_values_supported: List[str]
    authorization_encryption_enc_values_supported: List[str]
    claims_supported: List[str]
    claim_types_supported: List[str]
    claims_parameter_supported: bool
    scopes_supported: List[str]
    request_parameter_supported: bool
    request_uri_parameter_supported: bool
    require_request_uri_registration: bool
    code_challenge_methods_supported: List[str]
    tls_client_certificate_bound_access_tokens: bool
    revocation_endpoint: AnyHttpUrl
    revocation_endpoint_auth_methods_supported: List[str]
    revocation_endpoint_auth_signing_alg_values_supported: List[str]
    backchannel_logout_supported: bool
    backchannel_logout_session_supported: bool
    device_authorization_endpoint: AnyHttpUrl
    backchannel_token_delivery_modes_supported: List[str]
    backchannel_authentication_endpoint: AnyHttpUrl
    backchannel_authentication_request_signing_alg_values_supported: List[str]
    require_pushed_authorization_requests: bool
    pushed_authorization_request_endpoint: AnyHttpUrl
    mtls_endpoint_aliases: OpenIdConfigurationMTlsAliases


class KeycloakClient(object):
    def __init__(self, host: str, lf: LogFactory = NO_LOG_FACTORY):
        self.host = host.rstrip("/")
        self.logger = lf.get_logger(__name__)

    def is_ready(self, timeout_seconds: float = 10) -> bool:
        """https://www.keycloak.org/server/health"""
        url = f"{self.host}/health/ready"
        try:
            resp = requests.get(url, timeout=timeout_seconds)
        except requests.exceptions.ConnectionError as e:
            self.logger.error(str(e))
            return False
        return resp.status_code == 200

    def is_ready_with_retry(self, timeout_seconds: float, retry_count: int) -> bool:
        retry_count_str = "infinite" if retry_count < 0 else str(retry_count)
        i = 0
        while i < retry_count or retry_count < 0:
            last_iteration = i == (retry_count - 1)
            t0 = time.time()
            if self.is_ready(timeout_seconds):
                self.logger.info("Keycloak could be reached!")
                return True
            dt = timeout_seconds - (time.time() - t0)
            self.logger.info(
                f"Keycloak could not be reached (attempt {i + 1}/{retry_count_str}, timeout={timeout_seconds:.3f}s)"
            )
            if dt > 0 and not last_iteration:
                time.sleep(dt)
            i += 1
        return False

    def get_openid_configuration(self, realm: str) -> OpenIdConfiguration:
        """https://www.keycloak.org/docs/latest/securing_apps/#endpoints"""
        url = f"{self.host}/realms/{realm}/.well-known/openid-configuration"
        resp = requests.get(url)
        if resp.status_code >= 400:
            raise RuntimeError(f"Could not get keycloak openid configuration, received http {resp.status_code}")
        data = resp.json()
        cfg = OpenIdConfiguration(**data)
        return cfg

    def get_openid_key_set(self, realm: str) -> jwk.KeySet:
        """
        https://www.keycloak.org/docs/latest/securing_apps/#endpoints
        https://datatracker.ietf.org/doc/html/rfc7517
        """
        url = f"{self.host}/realms/{realm}/protocol/openid-connect/certs"
        resp = requests.get(url)
        if resp.status_code >= 400:
            raise RuntimeError(f"Could not get keycloak openid-connect certificates, received http {resp.status_code}")
        data = resp.json()
        return jwk.KeySet.import_key_set(data)
