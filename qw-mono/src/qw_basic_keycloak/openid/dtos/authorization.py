from typing import Literal, Sequence

from pydantic import AnyHttpUrl, BaseModel, field_validator

SCOPE_OPEN_ID = "openid"


class AuthenticationRequest(BaseModel):
    """https://openid.net/specs/openid-connect-core-1_0.html#AuthRequest"""

    scope: str = SCOPE_OPEN_ID
    response_type: Literal["code", "none"] = "code"
    client_id: str
    redirect_uri: AnyHttpUrl
    state: str  # used to find the authentication request that corresponds to a redirect
    nonce: str  # used to verify that the redirect was not done by someone else

    # # Nullable breaks openapi spec generation?
    # prompt: Literal["none", "login", "consent", "select_account"] | None = None
    # id_token_hint: str | None = None
    # # there are more optional fields, check spec

    @field_validator("scope")
    @classmethod
    def scope_must_contain_openid(cls, scope_str: str) -> Sequence[str]:
        scopes = scope_str.strip().split()
        if SCOPE_OPEN_ID not in scopes:
            raise ValueError(f"{SCOPE_OPEN_ID} must be among the scope values")
        return scope_str


class AuthenticationResponse(BaseModel):
    """https://openid.net/specs/openid-connect-core-1_0.html#AuthResponse"""

    code: str
    state: str
