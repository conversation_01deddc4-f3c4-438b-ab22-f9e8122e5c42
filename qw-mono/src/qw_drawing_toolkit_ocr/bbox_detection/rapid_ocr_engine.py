"""
Main OCR engine module for processing technical drawings.
This module provides the core functionality for detecting and recognizing
text and symbols in technical drawings using RapidOCR.
"""
from typing import Any, List, cast

import cv2
import numpy as np
from numpy.typing import NDArray
from rapidocr_onnxruntime import RapidOCR

from qw_drawing_toolkit_ocr.bbox_detection.models import BoxCoords, Detection, OcrResult
from qw_log_interface import NO_LOGGER, Logger


class RapidOcrEngine:
    """
    Main OCR engine for processing technical drawings.
    Handles text detection and recognition using RapidOCR.
    """

    def __init__(
        self,
        rotations: List[int],
        logger: Logger = NO_LOGGER,
    ):
        """Initialize OCR engine"""
        self.logger = logger
        self.rotations = rotations

        try:
            self.ocr = RapidOCR(
                unclip_ratio=3,
                width_height_ratio=4,
                thresh=0.005,
                box_thresh=0.60,
                limit_side_len=1024,
                max_side_length=1024,
                min_side_length=512,
                text_score=0,
                return_word_box=True,
                score_mode="slow",
                use_cls=False,
                use_rec=False,
            )
        except Exception as e:
            self.logger.error(f"Error initializing RapidOCR: {str(e)}")
            self.ocr = None

    def process_image_array(
        self,
        image: NDArray[Any],
        image_name: str = "in-memory-image",
    ) -> OcrResult:
        """
        Process an image as numpy array with OCR

        Args:
            image: numpy array containing the image
            image_name: a name for logging purposes
        """
        image_uint8 = np.asarray(image, dtype=np.uint8)
        all_detections: List[Detection] = []
        image_height, image_width = image_uint8.shape[:2]

        for angle in self.rotations:
            # self.logger.info(f"Processing {image_name} with rotation {angle}°")

            # Rotate and process the image
            rotated_image = self._rotate_image(image_uint8, angle)

            result = self._process_image_array_internal(rotated_image, image_name=f"{image_name}_rot{angle}")

            # Transform coordinates back to original orientation
            if angle != 0 and result.detections:
                for detection in result.detections:
                    # Extract box coordinates (first element) and convert to BoxCoords
                    box_coords = cast(BoxCoords, detection[0])
                    transformed_coords = self._transform_rotated_coordinates(
                        box_coords, angle, image_width, image_height
                    )
                    detection[0] = transformed_coords

            # Collect all detections
            all_detections.extend(result.detections)

        # Create the combined result with all detections
        combined_result = OcrResult(detections=all_detections)

        return combined_result

    def _rotate_image(self, image: NDArray[np.uint8], angle: int) -> NDArray[np.uint8]:
        """
        Rotate an image by the specified angle.

        Args:
            image: Image to rotate
            angle: Rotation angle in degrees (0 or 90)
        """
        if angle == 0:
            return image
        elif angle == 90:
            # Convert the rotated image back to uint8
            return np.asarray(cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE), dtype=np.uint8)
        else:
            self.logger.warning(f"Unsupported rotation angle: {angle}, using original image")
            return image

    def _transform_rotated_coordinates(
        self, coords: BoxCoords, angle: int, image_width: int, image_height: int
    ) -> BoxCoords:
        """
        Transform coordinates from a rotated image back to the original orientation.

        Args:
            coords: List of [x, y] coordinates
            angle: Rotation angle that was applied
            image_width: Width of the original image
            image_height: Height of the original image
        """
        transformed_coords: BoxCoords = []
        for x, y in coords:
            if angle == 0:
                transformed_coords.append([x, y])
            elif angle == 90:
                transformed_coords.append([y, image_height - x])
            else:
                transformed_coords.append([x, y])

        return transformed_coords

    def _process_image_array_internal(
        self,
        image: NDArray[np.uint8],
        image_name: str = "in-memory-image",
    ) -> OcrResult:
        """
        Internal method to process an image as numpy array with OCR without rotation

        Args:
            image: numpy array containing the image
            image_name: a name for logging purposes
        """
        try:
            if image is None or image.size == 0:
                self.logger.error(f"Empty or invalid image array for {image_name}")
                return OcrResult()

            if self.ocr is None:
                self.logger.error("OCR engine not initialized")
                return OcrResult()

            # Process with RapidOCR - use the engine instance directly
            result = self.ocr(
                image,
                unclip_ratio=3,
                width_height_ratio=4,
                thresh=0.005,
                box_thresh=0.60,
                limit_side_len=1024,
                max_side_length=1024,
                min_side_length=512,
                text_score=0,
                return_word_box=True,
                score_mode="slow",
                use_cls=False,
                use_rec=False,
            )

            # Process detection results
            detections: List[Detection] = []

            if result and len(result) > 0:
                # Extract boxes - could be at result[0] or directly in result
                boxes = result[0] if isinstance(result[0], list) else result

                # Process each box - assume standard box coordinates format
                for i, box in enumerate(boxes):
                    if isinstance(box, list):
                        # Create detection with region name and default confidence
                        detection: Detection = [box, f"region_{i+1}", 0.9]
                        detections.append(detection)

            return OcrResult(detections=detections)

        except Exception as e:
            self.logger.error(f"Error processing image {image_name}: {str(e)}")
            return OcrResult()
