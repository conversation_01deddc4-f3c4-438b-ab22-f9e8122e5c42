"""Image utils"""

import cv2 as cv
import numpy as np
import numpy.typing as npt
import pymupdf

from qw_drawing_toolkit.pdf.models.base_models import T_BBOX
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysisContext


def extract_and_preprocess_image_within_bbox(
    ctx: DrawingAnalysisContext, bbox: T_BBOX, output_width: int = 256
) -> npt.NDArray[np.uint8]:
    """
    Extract, crop, and preprocess an image from a PDF page within a specified bounding box.

    This function performs the following operations:
    1. Extracts the image from the PDF page using the specified bounding box
    2. Converts the image to grayscale
    3. Resizes the image to the specified output width while maintaining aspect ratio

    Args:
        ctx: Drawing analysis context containing page and zoom information
        bbox: Bounding box as (x0, y0, x1, y1) in zoomed coordinates
        output_width: Desired width of the output image in pixels (default: 256)

    Returns:
        Preprocessed grayscale image as a numpy array with shape (height, width)
    """
    mat = pymupdf.Matrix(ctx.zoom * ctx.resolution_booster, ctx.zoom * ctx.resolution_booster)

    x0, y0, x1, y1 = bbox
    clip = pymupdf.Rect((int(x0 / ctx.zoom), int(y0 / ctx.zoom), int(x1 / ctx.zoom), int(y1 / ctx.zoom)))

    pix = ctx.page.get_pixmap(matrix=mat, clip=clip)
    crop_rgb_buffer = np.frombuffer(pix.samples, dtype=np.uint8)
    crop_rgb = crop_rgb_buffer.reshape(pix.height, pix.width, pix.n)
    crop = cv.cvtColor(crop_rgb, cv.COLOR_RGB2GRAY)

    aspect_ratio = crop.shape[0] / crop.shape[1]
    new_width = output_width
    new_height = int(new_width * aspect_ratio)
    dim = (new_width, new_height)

    scaled_img = cv.resize(crop, dim, interpolation=cv.INTER_LINEAR).astype(np.uint8)

    return scaled_img
