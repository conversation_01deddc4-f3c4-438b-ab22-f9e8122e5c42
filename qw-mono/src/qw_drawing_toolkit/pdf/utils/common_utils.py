"""Common utils for the drawing toolkit"""

from typing import List, cast

from qw_drawing_toolkit.pdf.models.analysis_models import TextBox
from qw_drawing_toolkit.pdf.models.base_models import T_BBOX, T_LINE_SEGMENT, T_POINT


def is_box_in_box(
    box_to_check: T_BBOX, outer_box: T_BBOX, point_proximity_tolerance: float, tolerance: float | None = None
) -> bool:
    """
    Determine if one bounding box is completely contained within another.

    This function checks if box_to_check is entirely contained within outer_box,
    with an optional tolerance to allow for slight overlaps or gaps. The tolerance
    expands the outer_box in all directions.

    Args:
        box_to_check: Tuple of (x1, y1, x2, y2) coordinates of the inner box
        outer_box: Tuple of (x1, y1, x2, y2) coordinates of the outer box
        point_proximity_tolerance: Default tolerance value if none is provided
        tolerance: Optional specific tolerance value to use instead of default

    Returns:
        bool: True if box_to_check is contained within outer_box (with tolerance),
              False otherwise
    """
    if tolerance is None:
        tolerance = point_proximity_tolerance

    (db_x1, db_y1, db_x2, db_y2) = box_to_check
    (rx1, ry1, rx2, ry2) = outer_box

    left = min(rx1, rx2) - tolerance
    right = max(rx1, rx2) + tolerance
    top = min(ry1, ry2) - tolerance
    bottom = max(ry1, ry2) + tolerance

    db_left = min(db_x1, db_x2)
    db_right = max(db_x1, db_x2)
    db_top = min(db_y1, db_y2)
    db_bottom = max(db_y1, db_y2)

    return (
        left <= db_left <= right
        and left <= db_right <= right
        and top <= db_top <= bottom
        and top <= db_bottom <= bottom
    )


def find_bounding_box(coordinates: List[T_LINE_SEGMENT] | List[T_POINT]) -> T_BBOX:
    """
    Calculate the minimum bounding box that contains all given coordinates.

    This function handles two types of input:
    1. A list of points [(x1, y1), (x2, y2), ...]
    2. A list of line segments [((x1, y1), (x2, y2)), ...]

    It extracts all points and finds the minimum and maximum x and y values
    to create a bounding box that encompasses all points.

    Args:
        coordinates: List of points or line segments

    Returns:
        T_BBOX: Tuple of (min_x, min_y, max_x, max_y) defining the bounding box

    Raises:
        ValueError: If the input list is empty
    """
    if len(coordinates) == 0:
        raise ValueError("Need at least one coordinate in order to find the bounding box")

    points: List[T_POINT] = []
    first_item = coordinates[0]

    if isinstance(first_item, tuple) and len(first_item) == 2 and isinstance(first_item[0], tuple):
        lines: List[T_LINE_SEGMENT] = cast(List[T_LINE_SEGMENT], coordinates)
        for line in lines:
            (x1, y1), (x2, y2) = line
            points.append((x1, y1))
            points.append((x2, y2))
    else:
        points = cast(List[T_POINT], coordinates)

    min_x = min(points, key=lambda coord: coord[0])[0]
    max_x = max(points, key=lambda coord: coord[0])[0]
    min_y = min(points, key=lambda coord: coord[1])[1]
    max_y = max(points, key=lambda coord: coord[1])[1]

    return min_x, min_y, max_x, max_y


def find_text_boxes_in_box(
    box: T_BBOX, text_boxes: List[TextBox], point_proximity_tolerance: float, tolerance: float | None = None
) -> List[TextBox]:
    """
    Find all text boxes that are contained within a specified bounding box.

    This function filters a list of text boxes to include only those that are
    completely contained within the specified box, using the is_box_in_box
    function with the given tolerance.

    Args:
        box: Tuple of (x1, y1, x2, y2) coordinates of the containing box
        text_boxes: List of TextBox objects to filter
        point_proximity_tolerance: Default tolerance value if none is provided
        tolerance: Optional specific tolerance value to use instead of default

    Returns:
        List[TextBox]: Filtered list of text boxes contained within the box
    """
    if tolerance is None:
        tolerance = point_proximity_tolerance
    return [
        text_box
        for text_box in text_boxes
        if is_box_in_box(text_box.bbox, box, point_proximity_tolerance, tolerance=tolerance)
    ]
