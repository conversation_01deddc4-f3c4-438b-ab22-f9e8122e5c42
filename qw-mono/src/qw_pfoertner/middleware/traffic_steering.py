"""Traffic steering middleware for gradual FastAPI agent service rollout."""
import os
from typing import Optional
import httpx
import falcon

from qw_log_interface import NO_LOG_FACTORY, LogFactory


class AgentTrafficSteeringMiddleware:
    """
    Middleware to route agent requests to FastAPI service based on feature flag.
    Provides seamless switching between Falcon and FastAPI implementations.
    """

    def __init__(self, lf: LogFactory = NO_LOG_FACTORY):
        """
        Initialize the traffic steering middleware.
        """
        self.logger = lf.get_logger(__name__)

        # Feature flag configuration
        self.fastapi_enabled = os.getenv("USE_FASTAPI_AGENT", "false").lower() == "true"
        self.agent_service_url = os.getenv("AGENT_SERVICE_URL", "http://qw-mono-dev-agent-service:8000")
        self.request_timeout = float(os.getenv("AGENT_REQUEST_TIMEOUT", "300.0"))  # 5 minutes

        # Log configuration
        self.logger.info(f"Agent traffic steering initialized - FastAPI enabled: {self.fastapi_enabled}")
        if self.fastapi_enabled:
            self.logger.info(f"Agent service URL: {self.agent_service_url}")

    def process_request(self, req: falcon.Request, resp: falcon.Response) -> None:
        """
        Intercept agent requests and route to FastAPI if enabled.
        """
        # Only process agent endpoints
        if not req.path.startswith("/api/v1/agent/"):
            return

        # Check if FastAPI routing is enabled
        if not self.fastapi_enabled:
            self.logger.info("FastAPI routing disabled, using Falcon")
            return

        self.logger.info(f"Routing agent request to FastAPI: {req.method} {req.path}")

        try:
            # Forward request to FastAPI service
            self._forward_to_fastapi(req, resp)

        except Exception as e:
            self.logger.error(f"FastAPI routing failed: {e}, falling back to Falcon")
            # Let Falcon handle the request on error
            return

    def _forward_to_fastapi(self, req: falcon.Request, resp: falcon.Response) -> None:
        """
        Forward the request to the FastAPI agent service.
        """
        url = f"{self.agent_service_url}{req.path}"

        # Prepare headers (exclude hop-by-hop headers)
        headers = {
            k: v for k, v in req.headers.items()
            if k.lower() not in ['host', 'connection', 'content-length', 'transfer-encoding']
        }

        # Read request body for POST requests
        body = None
        if req.method in ['POST', 'PUT', 'PATCH']:
            body = req.bounded_stream.read()

        # Prepare query parameters
        query_params = dict(req.params)

        try:
            # Make request to FastAPI service
            with httpx.Client(timeout=self.request_timeout) as client:
                fastapi_response = client.request(
                    method=req.method,
                    url=url,
                    headers=headers,
                    cookies=req.cookies,
                    content=body,
                    params=query_params
                )

            # Copy response status
            resp.status = f"{fastapi_response.status_code} {fastapi_response.reason_phrase}"

            # Copy response headers (exclude hop-by-hop headers)
            for header_name, header_value in fastapi_response.headers.items():
                if header_name.lower() not in ['connection', 'transfer-encoding', 'content-length']:
                    resp.set_header(header_name, header_value)

            # Copy response body
            if fastapi_response.content:
                resp.text = fastapi_response.text

            # Mark response as complete to skip Falcon processing
            resp.complete = True

            self.logger.info(f"Successfully routed to FastAPI: {fastapi_response.status_code}")

        except httpx.TimeoutException:
            self.logger.error(f"FastAPI request timeout for {url}")
            raise
        except httpx.RequestError as e:
            self.logger.error(f"FastAPI request error for {url}: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error routing to FastAPI: {e}")
            raise

    @classmethod
    def from_config(cls, lf: LogFactory = NO_LOG_FACTORY) -> "AgentTrafficSteeringMiddleware":
        """
        Create AgentTrafficSteeringMiddleware from configuration.
        """
        return cls(lf=lf)
