"""This service is responsible for handling tolerances."""

import re
from typing import List

from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysisContext, DrawingGeneralNormIso2768


class ToleranceService:
    def __init__(self, debug: bool):
        self.debug = debug

    def extract_general_tolerance_standards(self, ctx: DrawingAnalysisContext) -> List[DrawingGeneralNormIso2768]:
        """
        Extract general tolerance standards (ISO 2768) from drawing text.

        This method searches through all text boxes to identify references to the ISO 2768
        standard, which specifies general tolerances for linear and angular dimensions.
        It extracts the tolerance class identifiers (e.g., 'm' for medium, 'K' for coarse)
        from the standard reference.

        The method looks for patterns like:
        - ISO 2768-m K
        - ISO 2768:2004-f H
        - ISO 2768 c L

        If identifiers are not specified, defaults to 'm' for the first identifier
        and 'K' for the second identifier.

        Args:
            ctx: Drawing analysis context containing text boxes

        Returns:
            List[DrawingGeneralNormIso2768]: Identified ISO 2768 standards with their
                tolerance class identifiers
        """
        pattern_iso2768 = re.compile(
            r"\b[Ii][Ss][Oo][-\s]*2768(:\d{4})?[-\s]*(?P<RI1>[fmcv])?[-\s]*(?P<RI2>[HKLhkl])?\b"
        )
        default_norms: List[DrawingGeneralNormIso2768] = []

        # TODO: add and detect other default norm patterns
        # pattern_din1674 = re.compile(r"\b[Dd][Ii][Nn][-\s]*16742[\\.]*(?P<RI>[AaBbCcDdEeFf]?)\b")

        for box in ctx.text_boxes_unfiltered:
            m = pattern_iso2768.search(box.text.strip())
            if m is not None:
                ri1 = m.group("RI1")
                ri2 = m.group("RI2")
                default_norm = DrawingGeneralNormIso2768(
                    row_identifier_1=ri1.lower() if ri1 is not None else "m",
                    row_identifier_2=ri2.upper() if ri2 is not None else "K",  # TODO agree on fallback values
                )
                default_norms.append(default_norm)
                break

        return default_norms
