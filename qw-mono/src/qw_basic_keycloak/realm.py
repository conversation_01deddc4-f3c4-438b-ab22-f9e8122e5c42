import json
from pathlib import Path
from typing import Dict, List, Literal

from pydantic import BaseModel, Field


class Credential(BaseModel):
    id: str
    type: Literal["password"]
    user_label: str = Field(alias="userLabel")
    created_date: int = Field(alias="createdDate")
    secret_data: str = Field(alias="secretData")
    credential_data: str = Field(alias="credentialData")


class User(BaseModel):
    id: str
    created_timestamp: int = Field(alias="createdTimestamp")
    enabled: bool
    totp: bool
    username: str
    email_verified: bool = Field(alias="emailVerified")
    email: str
    first_name: str = <PERSON>(alias="firstName")
    last_name: str = Field(alias="lastName")
    credentials: List[Credential]
    # disableableCredentialTypes: ...
    # requiredActions: ...
    realm_roles: List[str] = Field(alias="realmRoles")
    client_roles: Dict[str, List[str]] = Field(alias="clientRoles", default_factory=dict)
    not_before: int = Field(alias="notBefore")
    groups: List[Path]


class ServiceUser(BaseModel):
    id: str
    created_timestamp: int = Field(alias="createdTimestamp")
    enabled: bool
    totp: bool
    username: str
    service_account_client_id: str = Field(alias="serviceAccountClientId")
    credentials: List[Credential]
    # disableableCredentialTypes: ...
    # requiredActions: ...
    realm_roles: List[str] = Field(alias="realmRoles")
    not_before: int = Field(alias="notBefore")
    groups: List[Path]


class Group(BaseModel):
    id: str
    name: str
    path: Path
    parent_id: str | None = Field(alias="parentId", default=None)
    sub_groups: List["Group"] = Field(alias="subGroups", default_factory=list)


class Role(BaseModel):
    id: str
    name: str
    description: str
    container_id: str = Field(alias="containerId")
    client_role: bool = Field(alias="clientRole")
    composite: bool
    # composites: ...
    # attributes: ...


class Roles(BaseModel):
    realm: List[Role]
    client: Dict[str, List[Role]]


class Client(BaseModel):
    id: str
    client_id: str = Field(alias="clientId")
    name: str
    root_url: str | None = Field(default=None, alias="rootUrl")
    base_url: str | None = Field(default=None, alias="baseUrl")
    surrogate_auth_required: bool = Field(alias="surrogateAuthRequired")
    enabled: bool
    always_display_in_console: bool = Field(alias="alwaysDisplayInConsole")
    client_authenticator_type: str = Field(alias="clientAuthenticatorType")
    secret: str | None = None
    redirect_uris: List[str] = Field(alias="redirectUris")
    web_origins: List[str] = Field(alias="webOrigins")
    not_before: int = Field(alias="notBefore")
    bearer_only: bool = Field(alias="bearerOnly")
    consent_required: bool = Field(alias="consentRequired")
    standard_flow_enabled: bool = Field(alias="standardFlowEnabled")
    implicit_flow_enabled: bool = Field(alias="implicitFlowEnabled")
    direct_access_grants_enabled: bool = Field(alias="directAccessGrantsEnabled")
    service_accounts_enabled: bool = Field(alias="serviceAccountsEnabled")
    public_client: bool = Field(alias="publicClient")
    frontchannel_logout: bool = Field(alias="frontchannelLogout")
    protocol: str
    # protocolMappers: ...
    attributes: Dict[str, str]
    # authenticationFlowBindingOverrides: ...
    full_scope_allowed: bool = Field(alias="fullScopeAllowed")
    node_re_registration_timeout: int = Field(alias="nodeReRegistrationTimeout")
    default_client_scopes: List[str] = Field(alias="defaultClientScopes")
    optional_client_scopes: List[str] = Field(alias="optionalClientScopes")


class RealmExport(BaseModel):
    id: str
    realm: str

    roles: Roles
    default_role: Role = Field(alias="defaultRole")
    groups: List[Group]
    users: List[User | ServiceUser]
    clients: List[Client]

    access_token_lifespan: int = Field(alias="accessTokenLifespan")
    access_token_lifespan_for_implicit_flow: int = Field(alias="accessTokenLifespanForImplicitFlow")
    sso_session_idle_timeout: int = Field(alias="ssoSessionIdleTimeout")
    sso_session_max_lifespan: int = Field(alias="ssoSessionMaxLifespan")
    required_credentials: List[str] = Field(alias="requiredCredentials")
    keycloak_version: Literal["23.0.3"] = Field(alias="keycloakVersion")

    @classmethod
    def from_json_file(cls, p: Path) -> "RealmExport":
        with p.open("r") as f:
            data = json.load(f)
        return cls(**data)


class RealmSecrets(BaseModel):
    by_mail: Dict[str, str] = Field(alias="byMail")
    app_client_id: str = Field(alias="appClientId")
    app_client_secret: str = Field(alias="appClientSecret")

    @classmethod
    def from_json_file(cls, p: Path) -> "RealmSecrets":
        with p.open("r") as f:
            data = json.load(f)
        return cls(**data)


class Realm(object):

    """
    For development purposes.
    """

    def __init__(self, export: RealmExport, secrets: RealmSecrets):
        self.export = export
        self.secrets = secrets

        realm_users = set(u.email for u in export.users if isinstance(u, User) and len(u.credentials) > 0)
        secret_users = set(secrets.by_mail.keys())
        missing_mails = realm_users - secret_users
        additional_mails = secret_users - realm_users

        if len(missing_mails) > 0:
            missing_mails_str = ", ".join(missing_mails)
            raise ValueError(f"Provided secrets do not cover all realm users, it missing for: {missing_mails_str}")
        if len(additional_mails) > 0:
            additional_mails_str = ", ".join(missing_mails)
            raise ValueError(f"Provided secrets do define other users: {additional_mails_str}")

        clients_by_id: Dict[str, Client] = {c.client_id: c for c in export.clients}
        client = clients_by_id.get(self.secrets.app_client_id)
        if client is None:
            raise ValueError(f"Client '{self.secrets.app_client_id}' could not be found in the configuration")
        if client.secret != self.secrets.app_client_secret:
            raise ValueError(f"Client secret for '{self.secrets.app_client_id}' does not match with the configuration")

    @classmethod
    def from_json_files(cls, path_export: Path, path_secrets: Path) -> "Realm":
        export = RealmExport.from_json_file(path_export)
        secrets = RealmSecrets.from_json_file(path_secrets)
        return cls(export, secrets)

    def find_user_by_mail(self, mail: str) -> User | None:
        for u in self.export.users:
            if isinstance(u, User) and u.email == mail:
                return u
        return None

    def find_client_by_client_id(self, client_id: str) -> Client | None:
        for c in self.export.clients:
            if c.client_id == client_id:
                return c
        return None

    def verify_user_password(self, mail: str, password: str) -> bool:
        if self.find_user_by_mail(mail) is None:
            return False
        return self.secrets.by_mail.get(mail) == password

    def verify_client_secret(self, client_id: str, secret: str | None) -> bool:
        c = self.find_client_by_client_id(client_id)
        if c is None:
            return False
        return c.secret == secret
