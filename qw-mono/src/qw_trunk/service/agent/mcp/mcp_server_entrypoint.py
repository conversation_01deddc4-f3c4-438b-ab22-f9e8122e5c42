#!/usr/bin/env python3
"""
MCP Server Standalone Entrypoint

This script runs the MCP server as a standalone application in a separate container.
It loads the configuration and starts the MCP server to expose internal APIs as MCP tools.
"""

import argparse
import os
import sys
import yaml
from pathlib import Path
from typing import Dict, Any

from qw_log.factory import QwLogFactory, QwLogConfig
from qw_mono.app import QwAppInfo
from qw_trunk.service.agent.mcp.fastmcp_server import FastMCPServerService


def main() -> None:
    """Main entrypoint for the MCP server."""
    parser = argparse.ArgumentParser("mcp-server")
    parser.add_argument("--qw-mono-config", metavar="FILE", type=Path, required=True)
    parser.add_argument("--qw-mono-overwrite-config", metavar="FILE", type=Path, default=None)

    args = parser.parse_args()

    try:
        # Load raw configuration directly
        if not args.qw_mono_config.exists():
            print(f"Configuration file not found: {args.qw_mono_config}")
            sys.exit(1)

        with open(args.qw_mono_config, 'r') as f:
            raw_config_data: Dict[str, Any] = yaml.safe_load(f)

        # Get version and commit from environment (same pattern as main app)
        version = os.environ.get("BUILD_VERSION", "0.0.0.0")
        commit = os.environ.get("BUILD_COMMIT", "0" * 40)

        print(f"DEBUG: Using version='{version}', commit='{commit}'")

        # Initialize logging (simplified for MCP server)
        app_info = QwAppInfo(
            name="qw-mcp-server",
            version=version,
            commit=commit,
        )
        log_factory = QwLogFactory(app_info.version)

        # Initialize logs with basic configuration - create a simple logging config
        logging_config_data = raw_config_data.get('logging', {})
        logging_config = QwLogConfig(**logging_config_data) if logging_config_data else QwLogConfig()
        log_factory.init_logs(logging_config)
        logger = log_factory.get_logger(__name__)

        logger.info("Starting MCP server standalone")

        # Load MCP configuration
        mcp_config_data = raw_config_data.get('mono_mcp_server', {})

        if not mcp_config_data.get('enabled', False):
            logger.error("MCP server is not enabled in configuration")
            sys.exit(1)

        # Create MCP server config from raw config data
        from qw_trunk.service.agent.mcp.models import MCPServerConfig
        mcp_config = MCPServerConfig.from_config(mcp_config_data)

        # Create and run FastMCP server
        mcp_server = FastMCPServerService(mcp_config, log_factory)
        logger.info(f"FastMCP server configured for {mcp_config.host}:{mcp_config.port}")

        # Run the server (this will block)
        mcp_server.run_server()

    except KeyboardInterrupt:
        print("\nMCP server stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"Failed to start MCP server: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
