import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@angular/common"
import {
  Component,
  computed,
  effect,
  inject,
  Injector,
  input,
  signal,
} from "@angular/core"
import { toObservable, toSignal } from "@angular/core/rxjs-interop"
import { FaIconComponent } from "@fortawesome/angular-fontawesome"
import {
  faChartBar,
  faChevronLeft,
  faChevronRight,
  faFilter,
  faRulerHorizontal,
} from "@fortawesome/free-solid-svg-icons"
import { API_ROUTES } from "@qw-core/constants/app-routes"
import { MaterialCertificateAnalysisService } from "@qw-shared-services/material-certificate-analysis.service"
import { MaterialCertificateResourceStateService } from "@qw-shared-services/material-certificate-resource-state.service"
import {
  evaluatePlateChemistry,
  Filter,
} from "@qw-shared-utils/qw-check-filter-value"
import { shareReplay, switchMap } from "rxjs"

import { <PERSON>er<PERSON>mageComponent } from "../viewer-image/viewer-image.component"
import { MaterialCertificateAnalysisResultsFilterComponent } from "./material-certificate-analysis-results-filter/material-certificate-analysis-results-filter.component"
import { MaterialCertificateAnalysisResultsComponent } from "./material-certificate-analysis-results/material-certificate-analysis-results.component"

import { QwFileResourceView, QwProduct } from "@qw-api/models"

interface ViewModel {
  analysis: QwProduct[]
  currentPageIndex: number
  currentPageUrl: string
  totalPages: number
  canGoNext: boolean
  canGoPrev: boolean
  isFitToHeight: boolean
  viewMode: "analysis" | "default"
  showFilters: boolean
  currentPlateIndex: number
  plateResults: {
    ok: { isOk: boolean }[]
    nok: { isOk: boolean }[]
    currentPlates: { isOk: boolean }[]
  }
  filters: Filter[]
}

@Component({
  selector: "qw-viewer-material-certificate",
  templateUrl: "./viewer-material-certificate.component.html",
  standalone: true,

  imports: [
    NgIf,
    NgClass,
    NgFor,
    AsyncPipe,
    FaIconComponent,
    MaterialCertificateAnalysisResultsComponent,
    MaterialCertificateAnalysisResultsFilterComponent,
    ViewerImageComponent,
  ],
})
export class ViewerMaterialCertificateComponent {
  // Initialization ============================================================
  private stateService = inject(MaterialCertificateResourceStateService)
  private materialCertAnalysisService = inject(
    MaterialCertificateAnalysisService,
  )
  private injector = inject(Injector)

  constructor() {
    effect(
      () => {
        const fileResource = this.selectedCertificate()
        const selectedVersion = this.selectedVersion() // Read to make effect reactive to version changes

        if (!fileResource.revisions.length) return

        // Reset page and plate index when certificate or revision changes
        this.currentPageIndex.set(0)
        this.stateService.updateState({
          viewMode: "default",
          currentPlateIndex: 0,
          filters: [],
        })
      },
      { allowSignalWrites: true },
    )
  }

  selectedCertificate = input.required<QwFileResourceView>()
  selectedVersion = input<number>(0)

  // Bindings ==================================================================
  protected readonly faChevronLeft = faChevronLeft
  protected readonly faChevronRight = faChevronRight
  protected readonly faRulerHorizontal = faRulerHorizontal
  protected readonly faChartBar = faChartBar
  protected readonly faFilter = faFilter

  // Data Streams and State ====================================================
  private currentPageIndex = signal(0)
  private isFitToHeight = signal(false)

  protected readonly currentCertificateRevision = computed(() => {
    const fileResource = this.selectedCertificate()
    return fileResource.revisions[this.selectedVersion()] || null
  })

  // Get current revision ID for analysis
  private readonly currentRevisionId = computed(() => {
    const revision = this.currentCertificateRevision()
    return revision?.id || 0
  })

  private readonly currentRevisionId$ = toObservable(this.currentRevisionId, {
    injector: this.injector,
  })

  private readonly analysisData$ = this.currentRevisionId$.pipe(
    switchMap((revisionId: number) =>
      this.materialCertAnalysisService.getMaterialCertificateAnalysis(
        revisionId,
      ),
    ),
    shareReplay(1),
  )

  // Convert analysis data to observable for use in computed
  protected readonly analysisSignal = toSignal(this.analysisData$)

  protected readonly vm = computed<ViewModel>(() => {
    const cert = this.currentCertificateRevision()
    const analysisData = this.analysisSignal()

    const products = analysisData?.products || []

    const evaluatedProducts = products.map((product) => ({
      ...product,
      isOk: evaluatePlateChemistry(
        product.chemical_composition,
        this.stateService.filters(),
      ),
    }))

    const totalPages = cert?.pageCount || 1
    const currentPageUrl = cert
      ? API_ROUTES.materialCertificateFileRevisionPageImage.url(
          cert.id,
          this.currentPageIndex(),
        )
      : ""

    return {
      analysis: products,
      currentPageIndex: this.currentPageIndex(),
      currentPageUrl,
      totalPages,
      canGoNext: this.currentPageIndex() < totalPages - 1,
      canGoPrev: this.currentPageIndex() > 0,
      isFitToHeight: this.isFitToHeight(),
      viewMode: this.stateService.viewMode(),
      showFilters: this.stateService.showFilters(),
      currentPlateIndex: this.stateService.currentPlateIndex(),
      plateResults: {
        ok: evaluatedProducts.filter((p) => p.isOk),
        nok: evaluatedProducts.filter((p) => !p.isOk),
        currentPlates: evaluatedProducts,
      },
      filters: this.stateService.filters(),
    }
  })

  // Actions ===================================================================
  protected changePageIndex(direction: "next" | "prev"): void {
    this.currentPageIndex.update((current) =>
      direction === "next" ? current + 1 : current - 1,
    )
  }

  protected toggleLayout(): void {
    this.isFitToHeight.update((current) => !current)
  }

  protected toggleViewMode(): void {
    this.stateService.updateState({
      viewMode:
        this.stateService.viewMode() === "default" ? "analysis" : "default",
    })
  }

  protected toggleFilters(): void {
    this.stateService.updateState({
      showFilters: !this.stateService.showFilters(),
    })
  }

  protected setCurrentPlateIndex(index: number): void {
    this.stateService.updateState({
      currentPlateIndex: index,
    })
  }

  // UI/View ===================================================================
  readonly filtersToggleTitle = computed(
    () =>
      (showFilters: boolean): string => {
        if (showFilters) {
          return $localize`Hide Filters`
        }
        return $localize`Show Filters`
      },
  )
}
