{"name": "NF-T-58-000", "tables": [{"name": "Tabelle 8", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.17, "lower": -0.17}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.22, "lower": -0.22}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 22, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 22, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 53, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 53, "inclusive": false}, "upperValueLimit": {"value": 70, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.38, "lower": -0.38}}, {"lowerValueLimit": {"value": 70, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.43, "lower": -0.43}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 115, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 115, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 2.9, "lower": -2.9}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1300, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 3.6, "lower": -3.6}}, {"lowerValueLimit": {"value": 1300, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 4.4, "lower": -4.4}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 5.4, "lower": -5.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.06, "lower": -0.06}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.07, "lower": -0.07}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.08, "lower": -0.08}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.09, "lower": -0.09}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 22, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 22, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 53, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.17, "lower": -0.17}}, {"lowerValueLimit": {"value": 53, "inclusive": false}, "upperValueLimit": {"value": 70, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 70, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.24, "lower": -0.24}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 115, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.29, "lower": -0.29}}, {"lowerValueLimit": {"value": 115, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.44, "lower": -0.44}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1300, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 1300, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 3.9, "lower": -3.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.04, "lower": -0.04}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.06, "lower": -0.06}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.07, "lower": -0.07}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.08, "lower": -0.08}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 22, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.09, "lower": -0.09}}, {"lowerValueLimit": {"value": 22, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 53, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 53, "inclusive": false}, "upperValueLimit": {"value": 70, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 70, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.17, "lower": -0.17}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 115, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 115, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.24, "lower": -0.24}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.36, "lower": -0.36}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.44, "lower": -0.44}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1300, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 1300, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 2.7, "lower": -2.7}}]}, {"name": "Tabelle 9", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.16, "lower": -0.16}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.23, "lower": -0.23}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 22, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.28, "lower": -0.28}}, {"lowerValueLimit": {"value": 22, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.33, "lower": -0.33}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 53, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.36, "lower": -0.36}}, {"lowerValueLimit": {"value": 53, "inclusive": false}, "upperValueLimit": {"value": 70, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.41, "lower": -0.41}}, {"lowerValueLimit": {"value": 70, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.46, "lower": -0.46}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 115, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 115, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1300, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 3.7, "lower": -3.7}}, {"lowerValueLimit": {"value": 1300, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["Normal"]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.09, "lower": -0.09}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 22, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 22, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.16, "lower": -0.16}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 53, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 53, "inclusive": false}, "upperValueLimit": {"value": 70, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.23, "lower": -0.23}}, {"lowerValueLimit": {"value": 70, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 115, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.32, "lower": -0.32}}, {"lowerValueLimit": {"value": 115, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.38, "lower": -0.38}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.47, "lower": -0.47}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1300, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 1300, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.07, "lower": -0.07}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.08, "lower": -0.08}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.09, "lower": -0.09}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 22, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 22, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 53, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 53, "inclusive": false}, "upperValueLimit": {"value": 70, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 70, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 115, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.23, "lower": -0.23}}, {"lowerValueLimit": {"value": 115, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.33, "lower": -0.33}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.39, "lower": -0.39}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.47, "lower": -0.47}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1300, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 1300, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON>"]], "tolerance": {"upper": 2.8, "lower": -2.8}}]}]}