"""
Redesigned models for elegant, reliable, and effective agentic material certificate analysis.

Key Design Principles:
1. Database-like structure with foreign key references (batch_number, heat_number)
2. Metadata-driven flow with progressive context building
3. Simplified aggregation through foreign key matching
4. Support for chat history and conversational metadata collection
"""
from decimal import Decimal
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field

# =============================================================================
# ENUMS & BASIC TYPES
# =============================================================================


class ProductType(str, Enum):
    """Product type classification."""

    PLATE = "Plate"
    BAR = "Bar"
    TUBE = "Tube"
    OTHER = "Other"


# =============================================================================
# METADATA MODELS (for progressive context building)
# =============================================================================


class CertificateMetadata(BaseModel):
    """
    Comprehensive metadata collected by the metadata agent with chat history.
    This builds context progressively and enables chitchat benefits.
    """

    # Basic certificate info
    product_type: ProductType = Field(description="Type of product this certificate covers")
    total_product_count: int = Field(description="Total number of individual products/items in the certificate")
    batch_numbers: List[str] = Field(description="List of all batch numbers found in the certificate")

    # Page location analysis
    pages_with_header_details: List[int] = Field(description="Page numbers containing header/batch details")
    pages_with_chemical_composition: List[int] = Field(description="Page numbers containing chemical composition data")
    pages_with_mechanical_properties: List[int] = Field(description="Page numbers containing mechanical properties")

    # Context for agents (chitchat style)
    analysis_context: str = Field(
        description=(
            "Conversational context about the certificate structure, e.g., "
            "'I found 2 steel plates on pages 1-2, with chemical data on page 1 and mechanical properties on page 2'"
        )
    )


# =============================================================================
# CORE DATA MODELS (with foreign key design)
# =============================================================================


class HeaderDetails(BaseModel):
    """
    Header/batch details with foreign key references.
    Treated like a database table - always includes batch_number and heat_number.
    """

    # Foreign keys (required for aggregation)
    batch_number: str = Field(description="Batch/item identifier - acts as primary key")
    heat_number: Optional[str] = Field(None, description="Heat/Smelt number - acts as secondary key")

    # Manufacturer information
    manufacturer: Optional[str] = Field(None, description="Manufacturer name (capitalize first letter only)")

    # Product specifications
    product_description: Optional[str] = Field(None, description="Product description (e.g., Hot Rolled Heavy Plate)")
    material_grade: Optional[str] = Field(None, description="Steel grade (e.g., S355J2+N)")

    # Physical dimensions
    thickness_mm: Optional[Decimal] = Field(None, description="Thickness in mm")
    width_mm: Optional[Decimal] = Field(None, description="Width in mm")
    length_mm: Optional[Decimal] = Field(None, description="Length in mm")
    weight_kg: Optional[Decimal] = Field(None, description="Weight in kg")


class ChemicalComposition(BaseModel):
    """
    Chemical composition with foreign key references.
    Treated like a database table - always includes batch_number and heat_number.
    """

    # Foreign keys (required for aggregation)
    batch_number: str = Field(description="Batch/item identifier - must match HeaderDetails")
    heat_number: Optional[str] = Field(None, description="Heat/Smelt number - must match HeaderDetails")

    # Chemical elements (all optional, comprehensive list)
    C: Optional[Decimal] = Field(None, description="Carbon in %")
    Mn: Optional[Decimal] = Field(None, description="Manganese in %")
    Si: Optional[Decimal] = Field(None, description="Silicon in %")
    P: Optional[Decimal] = Field(None, description="Phosphorus in %")
    S: Optional[Decimal] = Field(None, description="Sulfur in %")
    Cr: Optional[Decimal] = Field(None, description="Chromium in %")
    Ni: Optional[Decimal] = Field(None, description="Nickel in %")
    Mo: Optional[Decimal] = Field(None, description="Molybdenum in %")
    Cu: Optional[Decimal] = Field(None, description="Copper in %")
    Al: Optional[Decimal] = Field(None, description="Aluminum in %")
    V: Optional[Decimal] = Field(None, description="Vanadium in %")
    Ti: Optional[Decimal] = Field(None, description="Titanium in %")
    Nb: Optional[Decimal] = Field(None, description="Niobium in %")
    B: Optional[Decimal] = Field(None, description="Boron in %")
    N: Optional[Decimal] = Field(None, description="Nitrogen in %")
    Ca: Optional[Decimal] = Field(None, description="Calcium in %")
    H: Optional[Decimal] = Field(None, description="Hydrogen in %")
    Sn: Optional[Decimal] = Field(None, description="Tin in %")
    As: Optional[Decimal] = Field(None, description="Arsenic in %")

    # Calculated values
    Ceq: Optional[Decimal] = Field(None, description="Carbon equivalent in %")


class TensileTest(BaseModel):
    """
    Tensile test results including strength and elongation properties.
    """

    # Test conditions
    test_direction: Optional[str] = Field(
        None, description="Direction of testing: L (Longitudinal) or T (Transverse). Most common is T."
    )
    test_temperature_celsius: Optional[Decimal] = Field(None, description="Test temperature in °C, commonly 20°C")

    # Strength properties
    tensile_strength_mpa: Optional[Decimal] = Field(None, description="Rm: Ultimate tensile strength in MPa")
    yield_strength_mpa: Optional[Decimal] = Field(None, description="ReH: Yield strength in MPa")
    elongation_percent: Optional[Decimal] = Field(None, description="A%: Elongation at break in %")


class ImpactTest(BaseModel):
    """
    Impact test results (Charpy V-notch test).
    Multiple impact tests can be performed at different temperatures.
    """

    # Test conditions
    test_direction: Optional[str] = Field(
        None, description="Impact test direction: L (Longitudinal) or T (Transverse). Most common is L."
    )
    test_temperature_celsius: Optional[Decimal] = Field(
        None, description="Impact test temperature in °C (e.g., 20, -20, -40, -60)"
    )

    # Impact energy values
    impact_energy_1_joules: Optional[Decimal] = Field(None, description="First impact energy value in Joules")
    impact_energy_2_joules: Optional[Decimal] = Field(None, description="Second impact energy value in Joules")
    impact_energy_3_joules: Optional[Decimal] = Field(None, description="Third impact energy value in Joules")
    impact_energy_average_joules: Optional[Decimal] = Field(
        None, description="Average impact energy in Joules (calculate if not provided)"
    )


class MechanicalProperties(BaseModel):
    """
    Mechanical properties with foreign key references.
    Treated like a database table - always includes batch_number and heat_number.
    Contains tensile test results and a list of impact tests.
    """

    # Foreign keys (required for aggregation)
    batch_number: str = Field(description="Batch/item identifier - must match HeaderDetails")
    heat_number: Optional[str] = Field(None, description="Heat/Smelt number - must match HeaderDetails")

    # Test results
    tensile_test: Optional[TensileTest] = Field(None, description="Tensile test results")
    impact_tests: List[ImpactTest] = Field(
        default=[], description="List of impact test results (can be multiple tests at different temperatures)"
    )


# =============================================================================
# AGGREGATED FINAL MODELS
# =============================================================================


class AggregationInput(BaseModel):
    """Input data for the aggregation agent with foreign key design."""

    metadata: CertificateMetadata
    header_details: List[HeaderDetails]
    chemical_compositions: List[ChemicalComposition]
    mechanical_properties: List[MechanicalProperties]


class Product(BaseModel):
    """
    Final aggregated product combining all data sources.
    Assembled by matching foreign keys from the three main data tables.
    """

    # Identification
    batch_number: str = Field(description="Unique batch/item identifier")
    heat_number: Optional[str] = Field(None, description="Heat/Smelt number")

    # Aggregated data (assembled by foreign key matching)
    header_details: Optional[HeaderDetails] = Field(None, description="Header/batch information")
    chemical_composition: Optional[ChemicalComposition] = Field(None, description="Chemical analysis results")
    mechanical_properties: Optional[MechanicalProperties] = Field(None, description="Mechanical test results")


class MaterialCertificateAnalysisResponse(BaseModel):
    """
    Final response from the redesigned agentic analysis.
    Includes metadata context and elegantly aggregated products.
    """

    # Metadata and context
    metadata: CertificateMetadata = Field(description="Certificate metadata and analysis context")

    # Final results
    products: List[Product] = Field(description="List of products with aggregated data")
