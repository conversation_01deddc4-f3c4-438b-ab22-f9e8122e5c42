from typing import List, <PERSON><PERSON>

from qw_drawing_toolkit.dtl.interface import DefaultToleranceLookupInterface
from qw_drawing_toolkit.norm.model import ToleranceStandard, ToleranceValueType
from qw_drawing_toolkit.norm.query import FROM_DRAWING_UNIT, ToleranceQuery
from qw_drawing_toolkit.norm.tolerance_collection import ToleranceCollection
from qw_drawing_toolkit.pdf.models.drawing_models import (
    DrawingAnalysisResult,
    DrawingAngularValue,
    DrawingDefaultTolerance,
    DrawingDiameterAnnotation,
    DrawingExplicitIso286FitTolerance,
    DrawingGeneralNormIso2768,
    DrawingLengthValue,
)
from qw_log_interface import NO_LOGGER, Logger


class DefaultToleranceLookup(DefaultToleranceLookupInterface):
    def __init__(self, tolerance_collection: ToleranceCollection, logger: Logger = NO_LOGGER):
        super().__init__(tolerance_collection, logger)
        self.standard_iso286 = self.standard_by_name["ISO_286"]
        self.standard_iso2768 = self.standard_by_name["ISO_2768"]

    def lookup_and_update(self, result: DrawingAnalysisResult, overwrite_general: bool, overwrite_fits: bool) -> None:
        t_anno = Tuple[DrawingLengthValue | DrawingAngularValue | None, ToleranceValueType]
        annotation_data: List[t_anno] = []

        annotation_data.extend([(item.length, ToleranceValueType.LINEAR) for item in result.lengths])
        annotation_data.extend([(item.diameter, ToleranceValueType.LINEAR) for item in result.diameters])
        annotation_data.extend([(item.radius, ToleranceValueType.LINEAR) for item in result.radii])

        # TODO the input for this lookup should not be the angle itself but the shortest leg, this requires
        #  the detection of such
        # annotation_data.extend([(item.angle, ToleranceValueType.ANGULAR) for item in result.angles])

        # annotation_data.extend([(item.angle, ToleranceValueType.ANGULAR) for item in result.countersinks])
        annotation_data.extend([(item.diameter, ToleranceValueType.LINEAR) for item in result.countersinks])

        # annotation_data.extend(
        #     [
        #         (item.thread.depth.depth, ToleranceValueType.CHAMFER)
        #         for item in result.threads
        #         if isinstance(item.thread.depth, DrawingDepthValue)
        #     ]
        # )
        # annotation_data.extend([(item.thread.pitch, ToleranceValueType.ANGULAR) for item in result.threads])

        for drawing_value, value_type in annotation_data:
            if drawing_value is None:
                continue
            if drawing_value.default_tolerance is None or overwrite_general:
                drawing_value.default_tolerance = self.find_default_tolerance_for_value(
                    value=drawing_value,
                    value_type=value_type,
                    analysis_result=result,
                )

        if overwrite_fits:
            for item in result.diameters:
                self.update_explicit_iso286_tolerance(item, self.standard_iso286, logger=self.logger)

    def find_default_tolerance_for_value(
        self,
        value: DrawingLengthValue | DrawingAngularValue,
        value_type: ToleranceValueType,
        analysis_result: DrawingAnalysisResult,
    ) -> DrawingDefaultTolerance | None:
        for norm in analysis_result.norms:
            tl: DrawingDefaultTolerance | None = None
            if isinstance(norm, DrawingGeneralNormIso2768):
                tl = self.get_default_iso2768_tolerance(value, value_type, norm, self.standard_iso2768, self.logger)
            # elif ...
            if tl is not None:
                return tl
        return None

    @classmethod
    def update_explicit_iso286_tolerance(
        cls, item: DrawingDiameterAnnotation, standard_iso286: ToleranceStandard, logger: Logger = NO_LOGGER
    ) -> None:
        if isinstance(item.diameter.explicit_tolerance, DrawingExplicitIso286FitTolerance):
            unit = FROM_DRAWING_UNIT[item.diameter.unit]
            query = ToleranceQuery(
                row_identifier=(item.diameter.explicit_tolerance.identifier,),
                value_type=ToleranceValueType.LINEAR,
                value_in=item.diameter.value,
                unit_in=unit,
                unit_out=unit,
                logger=logger,
            )
            result = query.find_in(standard_iso286)
            if result is not None:
                item.diameter.explicit_tolerance.upper = result.upper
                item.diameter.explicit_tolerance.lower = result.lower

    @classmethod
    def get_default_iso2768_tolerance(
        cls,
        value: DrawingLengthValue | DrawingAngularValue,
        value_type: ToleranceValueType,
        norm: DrawingGeneralNormIso2768,
        standard_iso286: ToleranceStandard,
        logger: Logger = NO_LOGGER,
    ) -> DrawingDefaultTolerance | None:
        row_identifier = {
            ToleranceValueType.LINEAR: norm.row_identifier_1,
            ToleranceValueType.CHAMFER: norm.row_identifier_1,
            ToleranceValueType.STRAIGHTNESS: norm.row_identifier_2,
            ToleranceValueType.SYMMETRY: norm.row_identifier_2,
            ToleranceValueType.RUNOUT: norm.row_identifier_2,
        }.get(value_type)
        if row_identifier is not None:
            unit = FROM_DRAWING_UNIT[value.unit]
            query = ToleranceQuery(
                row_identifier=(row_identifier,),
                value_type=value_type,
                value_in=value.value,
                unit_in=unit,
                unit_out=unit,
                logger=logger,
            )
            result = query.find_in(standard_iso286)
            if result is not None:
                return DrawingDefaultTolerance(upper=result.upper, lower=result.lower, source=norm.name)
        return None
