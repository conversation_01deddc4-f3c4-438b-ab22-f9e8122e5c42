"""Minimal RouterAgent implementation for Phase 1 - basic text responses only."""
import asyncio
from typing import Any, Dict, Optional, Union, cast
from uuid import UUID, uuid4

from pydantic_ai import Agent as PydanticAgent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.models import AgentPromptRequest, AgentPromptResponse, AgentAction
from qw_agent_service.config import AgentServiceConfig


class MinimalAgentContext(Dict[str, Any]):
    """Minimal context for agent processing."""
    pass


class MinimalAgentResponse:
    """Minimal response structure for agent processing."""
    
    def __init__(self, message: str, actions: Optional[list] = None):
        self.message = message
        self.actions = actions or []


class MinimalRouterAgent:
    """
    Minimal RouterAgent implementation for Phase 1.
    Provides basic text responses without specialized agents or complex tools.
    """
    
    def __init__(
        self,
        api_key: str,
        model_name: str = "gpt-4o",
        temperature: float = 0.2,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the minimal router agent.
        """
        self.api_key = api_key
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout
        self.logger = lf.get_logger(__name__)
        self.lf = lf
        self.agent_available = False
        self.usage = Usage()
        
        self._configure_agent()
    
    def _configure_agent(self) -> None:
        """
        Configure the pydantic-ai agent with minimal setup.
        """
        try:
            # Prepare model settings
            model_settings = ModelSettings(
                temperature=self.temperature,
                timeout=self.timeout,
            )
            
            # Setup OpenAI model with provider
            provider = OpenAIProvider(api_key=self.api_key)
            
            # Extract model name without provider prefix if needed
            model_name = self.model_name
            if model_name.startswith("openai:"):
                model_name = model_name.split(":")[-1]
            
            model = OpenAIModel(model_name, provider=provider)
            
            # Create the agent with minimal configuration
            self.agent = PydanticAgent[MinimalAgentContext, str](
                model=model,
                model_settings=model_settings,
                retries=2,
                system_prompt=self._get_system_prompt(),
                deps_type=MinimalAgentContext,
            )
            
            self.agent_available = True
            self.logger.info("Minimal router agent configured successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to configure minimal router agent: {e}")
            self.agent_available = False
    
    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the minimal router agent.
        """
        return """
        You are a helpful AI assistant for a quality management system.
        
        You can help users with:
        - General questions about quality management
        - Information about inspection processes
        - Guidance on material certificates and technical drawings
        - Basic workflow assistance
        
        Currently, you are running in minimal mode and cannot perform complex actions
        or access specialized tools. Provide helpful text responses and guidance.
        
        Keep your responses concise and professional.
        """
    
    async def process(
        self,
        prompt: str,
        context: Optional[Dict[str, Any]] = None,
        usage: Optional[Usage] = None,
    ) -> MinimalAgentResponse:
        """
        Process a user prompt and return a minimal response.
        """
        if not self.agent_available:
            self.logger.error("Agent not available for processing")
            return MinimalAgentResponse(
                message="I'm currently unavailable. Please try again later.",
                actions=[]
            )
        
        try:
            # Prepare context
            agent_context = MinimalAgentContext(context or {})
            
            # Run the agent
            result = await self.agent.run(prompt, deps=agent_context, usage=usage)
            
            # Return minimal response
            response_message = result.output if result.output else "I'm here to help! How can I assist you?"
            
            self.logger.info(f"Agent processed prompt successfully: {len(response_message)} chars")
            
            return MinimalAgentResponse(
                message=response_message,
                actions=[]  # No actions in Phase 1
            )
            
        except Exception as e:
            self.logger.error(f"Error processing prompt with minimal agent: {e}")
            return MinimalAgentResponse(
                message="I encountered an error processing your request. Please try again.",
                actions=[]
            )
    
    @classmethod
    def from_config(cls, config: AgentServiceConfig, lf: LogFactory = NO_LOG_FACTORY) -> "MinimalRouterAgent":
        """
        Create MinimalRouterAgent from configuration.
        """
        return cls(
            api_key=config.openai_api_key,
            model_name=config.agent_model_name,
            temperature=config.agent_temperature,
            timeout=config.agent_timeout,
            lf=lf
        )
