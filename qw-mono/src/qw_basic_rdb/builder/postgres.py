from typing import Literal, Type

from pydantic import BaseModel
from sqlalchemy import URL
from sqlalchemy.orm import DeclarativeBase

from qw_basic_rdb.sqlalch import SqlAlchemyDatabase
from qw_log_interface import NO_LOGGER, Logger


class PostgreSqlConfig(BaseModel):
    type: Literal["postgres"] = "postgres"
    host: str
    port: int = 5432
    username: str
    password: str
    database_name: str
    driver: str = "psycopg2"
    pool_size: int = 5
    pool_max_overflow: int = 10
    pool_timeout_seconds: float = 30

    def create_url(self, username: str | None = None, password: str | None = None) -> URL:
        return URL.create(
            drivername=f"postgresql+{self.driver}",
            username=self.username if username is None else username,
            password=self.password if password is None else password,
            host=self.host,
            port=self.port,
            database=self.database_name,
        )

    def build(self, base: Type[DeclarativeBase], echo: bool = False, logger: Logger = NO_LOGGER) -> SqlAlchemyDatabase:
        loggable_url = self.create_url(username="***", password="***")
        logger.info(f"Setting up postgres connection to {loggable_url}")
        return SqlAlchemyDatabase.from_url(
            base,
            self.create_url(),
            echo=echo,
            extra_args={
                "pool_size": self.pool_size,
                "max_overflow": self.pool_max_overflow,
                "pool_timeout": self.pool_timeout_seconds,
            },
        )
