{"name": "ISO_2768", "tables": [{"name": "Table 1.1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": null, "upperValueLimit": {"value": 0.5, "inclusive": true}, "rowIdentifiers": [["f"], ["m"], ["c"], ["v"]], "tolerance": {"upper": 0.0, "lower": -0.0}}, {"lowerValueLimit": {"value": 0.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.0, "lower": -0.0}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 8000, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.0, "lower": -0.0}}, {"lowerValueLimit": {"value": 0.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 2.0, "lower": -2.0}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 8000, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 3.0, "lower": -3.0}}, {"lowerValueLimit": {"value": 0.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 2.0, "lower": -2.0}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 3.0, "lower": -3.0}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 4.0, "lower": -4.0}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 8000, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 5.0, "lower": -5.0}}, {"lowerValueLimit": {"value": 0.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 0.0, "lower": -0.0}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 1.0, "lower": -1.0}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 4.0, "lower": -4.0}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 6.0, "lower": -6.0}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 8.0, "lower": -8.0}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 8000, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 12.0, "lower": -12.0}}]}, {"name": "Table 1.2", "valueType": "chamfer", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"rowIdentifiers": [["f"], ["m"], ["c"], ["v"]], "lowerValueLimit": null, "upperValueLimit": {"value": 0.5, "inclusive": true}, "tolerance": {"upper": 0, "lower": 0}}, {"rowIdentifiers": [["f"]], "lowerValueLimit": {"value": 0.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "tolerance": {"upper": 0.2, "lower": -0.2}}, {"rowIdentifiers": [["f"]], "lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "tolerance": {"upper": 0.5, "lower": -0.5}}, {"rowIdentifiers": [["f"]], "lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["f"]], "lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "tolerance": {"upper": 2.0, "lower": -2.0}}, {"rowIdentifiers": [["f"]], "lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "tolerance": {"upper": 4.0, "lower": -4.0}}, {"rowIdentifiers": [["m"]], "lowerValueLimit": {"value": 0.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "tolerance": {"upper": 0.2, "lower": -0.2}}, {"rowIdentifiers": [["m"]], "lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "tolerance": {"upper": 0.5, "lower": -0.5}}, {"rowIdentifiers": [["m"]], "lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["m"]], "lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "tolerance": {"upper": 2.0, "lower": -2.0}}, {"rowIdentifiers": [["m"]], "lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "tolerance": {"upper": 4.0, "lower": -4.0}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0.5, "inclusive": true}, "tolerance": {"upper": 0.4, "lower": -0.4}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 0.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "tolerance": {"upper": 2.0, "lower": -2.0}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "tolerance": {"upper": 4.0, "lower": -4.0}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "tolerance": {"upper": 8.0, "lower": -8.0}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0.5, "inclusive": true}, "tolerance": {"upper": 0.4, "lower": -0.4}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 0.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "tolerance": {"upper": 2.0, "lower": -2.0}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "tolerance": {"upper": 4.0, "lower": -4.0}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "tolerance": {"upper": 8.0, "lower": -8.0}}]}, {"name": "Table 1.3", "valueType": "angular", "valueUnit": "mm", "toleranceUnit": "deg", "ranges": [{"rowIdentifiers": [["f"], ["m"], ["c"], ["v"]], "lowerValueLimit": null, "upperValueLimit": {"value": 10, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["f"]], "lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "tolerance": {"upper": 0.00833, "lower": -0.00833}}, {"rowIdentifiers": [["f"]], "lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "tolerance": {"upper": 0.3333, "lower": -0.3333}}, {"rowIdentifiers": [["f"]], "lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "tolerance": {"upper": 0.1667, "lower": -0.1667}}, {"rowIdentifiers": [["f"]], "lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": "inf", "inclusive": true}, "tolerance": {"upper": 0.0833, "lower": -0.0833}}, {"rowIdentifiers": [["m"]], "lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "tolerance": {"upper": 0.00833, "lower": -0.00833}}, {"rowIdentifiers": [["m"]], "lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "tolerance": {"upper": 0.3333, "lower": -0.3333}}, {"rowIdentifiers": [["m"]], "lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "tolerance": {"upper": 0.1667, "lower": -0.1667}}, {"rowIdentifiers": [["m"]], "lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": "inf", "inclusive": true}, "tolerance": {"upper": 0.0833, "lower": -0.0833}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 0, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "tolerance": {"upper": 1.5, "lower": -1.5}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "tolerance": {"upper": 0.00833, "lower": -0.00833}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "tolerance": {"upper": 0.25, "lower": -0.25}}, {"rowIdentifiers": [["c"]], "lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": "inf", "inclusive": true}, "tolerance": {"upper": 0.1667, "lower": -0.1667}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 0, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "tolerance": {"upper": 3.0, "lower": -3.0}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "tolerance": {"upper": 2.0, "lower": -2.0}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "tolerance": {"upper": 0.5, "lower": -0.5}}, {"rowIdentifiers": [["v"]], "lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": "inf", "inclusive": true}, "tolerance": {"upper": 0.3333, "lower": -0.3333}}]}, {"name": "Table 2.1", "valueType": "straightness", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"rowIdentifiers": [["H"], ["h"], ["K"], ["k"], ["L"], ["l"]], "lowerValueLimit": null, "upperValueLimit": {"value": 10, "inclusive": true}, "tolerance": {"upper": 0.02, "lower": -0.02}}, {"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "tolerance": {"upper": 0.05, "lower": -0.05}}, {"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "tolerance": {"upper": 0.1, "lower": -0.1}}, {"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "tolerance": {"upper": 0.2, "lower": -0.2}}, {"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "tolerance": {"upper": 0.3, "lower": -0.3}}, {"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "tolerance": {"upper": 0.4, "lower": -0.4}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "tolerance": {"upper": 0.1, "lower": -0.1}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "tolerance": {"upper": 0.2, "lower": -0.2}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "tolerance": {"upper": 0.4, "lower": -0.4}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "tolerance": {"upper": 0.6, "lower": -0.6}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "tolerance": {"upper": 0.8, "lower": -0.8}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "tolerance": {"upper": 0.2, "lower": -0.2}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "tolerance": {"upper": 0.4, "lower": -0.4}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "tolerance": {"upper": 0.8, "lower": -0.8}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "tolerance": {"upper": 1.2, "lower": -1.2}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "tolerance": {"upper": 1.6, "lower": -1.6}}]}, {"name": "Table 2.2", "valueType": "symmetry", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"rowIdentifiers": [["H"], ["h"], ["K"], ["k"], ["L"], ["l"]], "lowerValueLimit": null, "upperValueLimit": {"value": 100, "inclusive": true}, "tolerance": {"upper": 0.2, "lower": -0.2}}, {"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "tolerance": {"upper": 0.3, "lower": -0.3}}, {"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "tolerance": {"upper": 0.4, "lower": -0.4}}, {"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "tolerance": {"upper": 0.5, "lower": -0.5}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "tolerance": {"upper": 0.6, "lower": -0.6}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "tolerance": {"upper": 0.8, "lower": -0.8}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "tolerance": {"upper": 1.5, "lower": -1.5}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "tolerance": {"upper": 2.0, "lower": -2.0}}]}, {"name": "Table 2.3", "valueType": "perpendicularity", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"rowIdentifiers": [["H"], ["h"], ["K"], ["k"], ["L"], ["l"]], "lowerValueLimit": null, "upperValueLimit": {"value": 100, "inclusive": true}, "tolerance": {"upper": 0.5, "lower": -0.5}}, {"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "tolerance": {"upper": 0.8, "lower": -0.8}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "tolerance": {"upper": 1.0, "lower": -1.0}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "tolerance": {"upper": 1.5, "lower": -1.5}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "tolerance": {"upper": 2.0, "lower": -2.0}}]}, {"name": "Table 2.4", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"rowIdentifiers": [["H"], ["h"]], "lowerValueLimit": null, "upperValueLimit": null, "tolerance": {"upper": 0.1, "lower": -0.1}}, {"rowIdentifiers": [["K"], ["k"]], "lowerValueLimit": null, "upperValueLimit": null, "tolerance": {"upper": 0.2, "lower": -0.2}}, {"rowIdentifiers": [["L"], ["l"]], "lowerValueLimit": null, "upperValueLimit": null, "tolerance": {"upper": 0.5, "lower": -0.5}}]}]}