import uuid
from dataclasses import dataclass
from pathlib import PurePosixPath
from typing import BinaryIO, Dict, List, Literal, Sequence, Tuple, Type

import sqlalchemy.exc
from sqlalchemy import select
from sqlalchemy.orm import Session as RdbSession

import qw_inspection.spec.plan as plan_spec
import qw_inspection.spec.result as result_spec
from qw_basic_rdb.common import TransactionStrategy, begin_or_use_session
from qw_basic_rdb.interface import RelationalDatabase
from qw_inspection.spec.util import find_non_conform_measurements
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_monodb.table.base import Base
from qw_monodb.table.trunk.inspection import (
    Inspection,
    InspectionActionResult,
    InspectionFinish,
    InspectionPlanActionImage,
    InspectionPlanActionQuestionAnswer,
    InspectionPlanActionVideo,
)
from qw_monodb.table.trunk.order import TaskOrderLineInspection
from qw_tenant_config.registry import TenantConfigRegistry
from qw_trunk.service.inspection.acl import InspectionAclService
from qw_trunk.service.inspection.db import (
    InspectionDatabaseService,
    InspectionPlanActionAny,
    InspectionPlanActionMeasurementContainer,
)
from qw_trunk.service.inspection.plan import InspectionPlanService
from qw_trunk.service.resource.s3_layout import S3BucketLayout
from qw_trunk.service.resource.s3_object import S3ObjectInput, S3ObjectInputData, S3ObjectService


class NoInspectionFoundError(Exception):
    pass


class NoUnfinishedInspectionFoundError(NoInspectionFoundError):
    pass


class InspectionDeletedError(Exception):
    pass


class BadInspectionResult(Exception):
    pass


class MissingStepEvidenceError(BadInspectionResult):
    pass


class IncorrectEvidenceTypeError(BadInspectionResult):
    pass


class CannotFinishInspection(Exception):
    pass


class NoInspectionFinishFoundError(Exception):
    pass


@dataclass
class InspectionBinaryEvidence:
    bio: BinaryIO
    size: int
    content_type: Literal["image/jpeg", "image/png", "video/mp4", "video/webm"]

    @property
    def media_type(self) -> Literal["image", "video"]:
        if self.content_type in ("video/mp4", "video/webm"):
            return "video"
        return "image"

    @property
    def file_ext(self) -> str:
        extension_map: Dict[Literal["image/jpeg", "image/png", "video/mp4", "video/webm"], str] = {
            "image/jpeg": ".jpeg",
            "image/png": ".png",
            "video/mp4": ".mp4",
            "video/webm": ".webm",
        }
        return extension_map[self.content_type]

    def to_resource_input(self, bucket: str, path: PurePosixPath) -> S3ObjectInput:
        return S3ObjectInput(
            bucket=bucket,
            path=path,
            data=S3ObjectInputData(bio=self.bio, size=self.size, content_type=self.content_type),
        )


@dataclass
class InspectionActionResultContainer:
    result: result_spec.InspectionActionResult
    binary_evidence: InspectionBinaryEvidence | None

    @property
    def action_type(self) -> plan_spec.InspectionPlanActionType:
        return self.result.evidence.type

    @property
    def evidence(self) -> result_spec.InspectionActionEvidence:
        return self.result.evidence


@dataclass
class MappedInspectionActionEvidence:
    measured_value: float | None = None
    binary_value: bool | None = None
    image_resource_id: int | None = None
    video_resource_id: int | None = None
    answer_value: str | None = None


class InspectionResultService(object):
    def __init__(
        self,
        inspection_db_service: InspectionDatabaseService,
        inspection_acl_service: InspectionAclService,
        inspection_plan_service: InspectionPlanService,
        s3_obj_service: S3ObjectService,
        s3_layout: S3BucketLayout,
        tenant_registry: TenantConfigRegistry,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.inspection_db_service = inspection_db_service
        self.inspection_acl_service = inspection_acl_service
        self.inspection_plan_service = inspection_plan_service
        self.s3_obj_service = s3_obj_service
        self.s3_layout = s3_layout
        self.tenant_registry = tenant_registry
        self.logger = lf.get_logger(__name__)

    @property
    def rdb(self) -> RelationalDatabase:
        return self.inspection_db_service.rdb

    def __store_action_result_binary_evidence(
        self,
        result: InspectionActionResultContainer,
        bucket: str,
        inspection_uuid: str,
        session: RdbSession,
    ) -> int:
        if result.binary_evidence is None:
            raise ValueError(f"Inspection {inspection_uuid}: Binary evidence is unexpectedly None")
        result_id = result.result.result_id
        evidence_path = self.s3_layout.inspection_result(inspection_uuid).path_to_media(
            step_index=result_id.step_index,
            action_index=result_id.action_index,
            sample_index=result_id.sample_index,
            suffix=result.binary_evidence.file_ext,
        )
        resource_id = self.s3_obj_service.add_object(
            obj_input=result.binary_evidence.to_resource_input(bucket, evidence_path),
            session=session,
        )
        return resource_id

    def __map_evidence_from_user_input(
        self,
        container: InspectionActionResultContainer,
        plan_action: InspectionPlanActionAny,
        inspection_uuid: str,
        bucket: str,
        s: RdbSession,
    ) -> MappedInspectionActionEvidence:
        def ensure_data_consistency(
            db_action_type: Type[Base | InspectionPlanActionMeasurementContainer],
            binary_type: Literal["image", "video"] | None,
        ) -> None:
            if not isinstance(plan_action, db_action_type):
                raise BadInspectionResult("Provided evidence type is incorrect")
            act_binary_type: Literal["image", "video"] | None = None
            if container.binary_evidence is not None:
                act_binary_type = container.binary_evidence.media_type
            if binary_type != act_binary_type:
                raise BadInspectionResult(f"Expected binary evidence type '{binary_type}', got '{act_binary_type}'")

        mapped_evidence = MappedInspectionActionEvidence()

        if isinstance(container.evidence, result_spec.InspectionActionMeasurementEvidence):
            ensure_data_consistency(InspectionPlanActionMeasurementContainer, binary_type=None)

            if not isinstance(plan_action, InspectionPlanActionMeasurementContainer):
                raise BadInspectionResult("Plan action is not of type InspectionPlanActionMeasurement")

            allowed_result_types = {
                plan_spec.InspectionMeasurementResultType.BINARY: [plan_spec.InspectionMeasurementResultType.BINARY],
                plan_spec.InspectionMeasurementResultType.CONTINUOUS: [
                    plan_spec.InspectionMeasurementResultType.CONTINUOUS
                ],
                plan_spec.InspectionMeasurementResultType.CONTINUOUS_OR_BINARY: [
                    plan_spec.InspectionMeasurementResultType.BINARY,
                    plan_spec.InspectionMeasurementResultType.CONTINUOUS,
                ],
            }[plan_spec.InspectionMeasurementResultType(plan_action.measurement.result_type)]

            if container.evidence.result_type not in allowed_result_types:
                raise BadInspectionResult(
                    f"Invalid result type for measurement action: {container.evidence.result_type}"
                )

            if container.evidence.result_type == plan_spec.InspectionMeasurementResultType.BINARY:
                mapped_evidence.binary_value = container.evidence.binary_value
            else:
                mapped_evidence.measured_value = container.evidence.measured_value

        elif isinstance(container.evidence, result_spec.InspectionActionImageEvidence):
            ensure_data_consistency(InspectionPlanActionImage, binary_type="image")
            mapped_evidence.image_resource_id = self.__store_action_result_binary_evidence(
                result=container,
                bucket=bucket,
                inspection_uuid=inspection_uuid,
                session=s,
            )

        elif isinstance(container.evidence, result_spec.InspectionActionVideoEvidence):
            ensure_data_consistency(InspectionPlanActionVideo, binary_type="video")
            mapped_evidence.video_resource_id = self.__store_action_result_binary_evidence(
                result=container,
                bucket=bucket,
                inspection_uuid=inspection_uuid,
                session=s,
            )

        elif isinstance(container.evidence, result_spec.InspectionActionQuestionAnswerEvidence):
            ensure_data_consistency(InspectionPlanActionQuestionAnswer, binary_type=None)
            mapped_evidence.answer_value = container.evidence.answer

        else:
            raise BadInspectionResult(f"Cannot store an action with evidence type of {type(container.evidence)}")

        return mapped_evidence

    def __get_inspection(
        self,
        inspection_id: int,
        s: RdbSession,
        require_not_finished: bool = False,
        require_tenant_id: int | None = None,
        include_deleted: bool = False,
    ) -> Inspection:
        inspection = self.inspection_db_service.inspection.find_by_id(idx=inspection_id, session=s)
        if inspection is None:
            raise NoInspectionFoundError()

        if not include_deleted and inspection.deleted:
            # We may enable access to deleted inspections later.
            # Thus, overwriting include_deleted is made possible.
            raise InspectionDeletedError(f"Inspection {inspection_id} has been deleted")

        if require_not_finished and inspection.finish_id is not None:
            raise NoUnfinishedInspectionFoundError()

        if require_tenant_id is not None and inspection.inspector_tenant_id != require_tenant_id:
            raise NoInspectionFoundError(
                f"Tenant of the inspection {inspection_id} is not {require_tenant_id}, "
                f"but {inspection.inspector_tenant_id}"
            )

        return inspection

    def add_inspection(
        self,
        inspection_plan_id: int,
        sample_count: int,
        creator_user_id: int,
        creator_tenant_id: int,
        inspector_tenant_id: int,
        session: RdbSession | None = None,
    ) -> int:
        with begin_or_use_session(self.rdb, session, strategy=TransactionStrategy.REUSE_OR_NEW) as s:
            # making sure the plan exists
            self.inspection_plan_service.get_inspection_plan(inspection_plan_id, session=s)

            inspection_uuid = uuid.uuid4().hex
            inspection_id = self.inspection_db_service.inspection.add(
                obj=Inspection(
                    uuid=inspection_uuid,
                    inspection_plan_id=inspection_plan_id,
                    sample_count=sample_count,
                    finish_id=None,
                    creator_user_id=creator_user_id,
                    creator_tenant_id=creator_tenant_id,
                    inspector_tenant_id=inspector_tenant_id,
                    deleted=False,
                ),
                session=s,
            )

            for tenant_id in {creator_tenant_id, inspector_tenant_id}:
                self.inspection_acl_service.acl_inspection.insert_if_not_exists(
                    item_id=inspection_id,
                    tenant_id=tenant_id,
                    session=s,
                )
                self.inspection_acl_service.acl_inspection_plan.insert_if_not_exists(
                    item_id=inspection_plan_id,
                    tenant_id=tenant_id,
                    session=s,
                )

        return inspection_id

    def add_inspection_action_result(
        self,
        container: InspectionActionResultContainer,
        inspection_id: int,
        user_id: int,
        tenant_id: int,
    ) -> int:
        result_id = container.result.result_id

        with begin_or_use_session(self.rdb, strategy=TransactionStrategy.NEW) as s:
            inspection = self.__get_inspection(
                inspection_id,
                s=s,
                require_not_finished=True,
                require_tenant_id=tenant_id,
            )
            inspection_plan_id = inspection.inspection_plan_id
            action_data = self.inspection_db_service.get_inspection_plan_action(
                inspection_plan_id=inspection_plan_id,
                step_index=result_id.step_index,
                action_index=result_id.action_index,
                session=s,
            )
            bucket = self.tenant_registry.get_tenant_by_id(inspection.inspector_tenant_id).bucket

            if action_data is None:
                raise BadInspectionResult(
                    f"Action {result_id.action_index} of step {result_id.step_index} "
                    f"could not be found for inspection plan {inspection_plan_id}"
                )
            if action_data.step.repeat_for_each_sample:
                if result_id.sample_index is None:
                    raise BadInspectionResult("Step actions are required for each sample, but no sample_index provided")
                if not 0 <= result_id.sample_index < inspection.sample_count:
                    raise BadInspectionResult(
                        f"sample_index is out of bounds: {result_id.sample_index} "
                        f"is not in 0...{inspection.sample_count - 1}"
                    )
            if not action_data.step.repeat_for_each_sample and result_id.sample_index is not None:
                raise BadInspectionResult(
                    "Step actions are not required for each sample, but a sample_index was provided"
                )

            existing_error_msg = f"There exists already a result for action {result_id} of inspection {inspection_id}"
            existing_result = self.inspection_db_service.find_inspection_action_result_by_index(
                inspection_id=inspection_id,
                step_index=result_id.step_index,
                action_index=result_id.action_index,
                sample_index=result_id.sample_index,
                session=s,
            )
            if existing_result is not None:
                raise BadInspectionResult(existing_error_msg)

            mapped_evidence = self.__map_evidence_from_user_input(
                container=container,
                plan_action=action_data.action,
                inspection_uuid=inspection.uuid,
                bucket=bucket,
                s=s,
            )

            # The check for an existing result above is not 100% reliable, therefore the UNIQUE constraint on
            # inspection_id, plan_id and sample_index ensures no duplicated results. In that context also remember that:
            # - s3 uploads via the s3 service are ensured to not overwrite (unique DB entry will be written first)
            # - the session context manager handles SQL transaction management, so the DB will be cleaned up
            try:
                inspection_action_result_id = self.inspection_db_service.inspection_action_result.add(
                    obj=InspectionActionResult(
                        inspection_id=inspection_id,
                        inspection_plan_action_id=action_data.base_action.id,
                        sample_index=result_id.sample_index,
                        measured_value=mapped_evidence.measured_value,
                        image_resource_id=mapped_evidence.image_resource_id,
                        video_resource_id=mapped_evidence.video_resource_id,
                        answer_value=mapped_evidence.answer_value,
                        binary_value=mapped_evidence.binary_value,
                        creator_user_id=user_id,
                    ),
                    session=s,
                )
            except sqlalchemy.exc.IntegrityError:
                self.logger.error("Could not enter inspection action result row")
                raise BadInspectionResult(existing_error_msg)

        return inspection_action_result_id

    def delete_inspections(
        self,
        inspection_ids: Sequence[int],
        tenant_id: int,
        session: RdbSession | None = None,
    ) -> None:
        """Delete inspections (both finished and unfinished).

        Authorization Rules:
        - Plan owner can delete any inspection
        - Creator can delete their own inspections

        Args:
            inspection_ids: Sequence of inspection IDs to delete
            tenant_id: ID of tenant requesting deletion
            session: Optional database session for transaction management

        Raises:
            NoInspectionFoundError: If inspection not found
            InspectionDeletedError: If inspection already deleted
            PermissionError: If tenant is not authorized to delete the inspection
        """
        with begin_or_use_session(self.rdb, session, strategy=TransactionStrategy.REUSE_OR_NEW) as s:
            affected_plan_ids = set()
            for inspection_id in inspection_ids:
                inspection = self.__get_inspection(
                    inspection_id,
                    s=s,
                    require_not_finished=False,
                    include_deleted=False,
                )
                inspection_plan = self.inspection_db_service.inspection_plan.find_by_id(
                    inspection.inspection_plan_id, session=s
                )
                if inspection_plan is None:
                    raise NoInspectionFoundError(f"Inspection plan not found for inspection {inspection_id}")

                is_plan_owner = inspection_plan.owner_tenant_id == tenant_id
                is_creator = inspection.creator_tenant_id == tenant_id
                if not (is_plan_owner or is_creator):
                    raise PermissionError(f"Tenant {tenant_id} is not authorized to delete inspection {inspection_id}")

                inspection.deleted = True
                s.add(inspection)
                task = self.find_task_by_inspection_id(inspection_id, session=s)

                if task:
                    task.deleted = True
                    s.add(task)

                affected_plan_ids.add(inspection.inspection_plan_id)

            s.flush()

            for plan_id in affected_plan_ids:
                plan = self.inspection_db_service.get_inspection_plan_by_id_locked(plan_id, session=s)
                if plan is None:
                    continue

                active_count = self.inspection_db_service.get_active_inspection_count_by_plan_id(plan_id, session=s)

                if active_count == 0 and plan.is_readonly:
                    plan.is_readonly = False
                    s.add(plan)

    def finish_inspection(self, inspection_id: int, tenant_id: int, user_id: int) -> None:
        with begin_or_use_session(self.rdb, strategy=TransactionStrategy.NEW) as s:
            inspection = self.__get_inspection(
                inspection_id,
                s=s,
                require_not_finished=True,
                require_tenant_id=tenant_id,
            )
            result, plan = self.get_inspection_result(inspection_id, session=s)

            if not result.all_mandatory_results_are_available:
                raise CannotFinishInspection()

            nc_rids = find_non_conform_measurements(result, plan, self.logger)
            inspection_finish = InspectionFinish(
                count_non_conform_measurements=len(nc_rids),
                creator_user_id=user_id,
            )
            s.add(inspection_finish)
            s.flush()
            inspection.finish_id = inspection_finish.id
            self.logger.info(f"Finished inspection {inspection.id} at {inspection_finish.ts_created}")

    def __map_evidence_from_db(
        self,
        action_result: InspectionActionResult,
        result_id: result_spec.InspectionActionResultIdentifier,
        expected_type: plan_spec.InspectionPlanActionType,
        inspection_id: int,
    ) -> result_spec.InspectionActionEvidence:
        error_prefix = f"For inspection {inspection_id} result {result_id}"
        if expected_type == plan_spec.InspectionPlanActionType.MEASUREMENT:
            measured_value: float = 0
            binary_value: bool = False
            result_type: Literal[
                plan_spec.InspectionMeasurementResultType.CONTINUOUS, plan_spec.InspectionMeasurementResultType.BINARY
            ]
            if action_result.measured_value is not None:
                result_type = plan_spec.InspectionMeasurementResultType.CONTINUOUS
                measured_value = action_result.measured_value
            elif action_result.binary_value is not None:
                result_type = plan_spec.InspectionMeasurementResultType.BINARY
                binary_value = action_result.binary_value
            else:
                raise ValueError(f"{error_prefix} there is neither measured value nor binary value available")

            return result_spec.InspectionActionMeasurementEvidence(
                measured_value=measured_value, binary_value=binary_value, result_type=result_type
            )

        if expected_type == plan_spec.InspectionPlanActionType.IMAGE:
            if action_result.image_resource_id is None:
                raise ValueError(f"{error_prefix} the image resource is missing")
            return result_spec.InspectionActionImageEvidence()

        if expected_type == plan_spec.InspectionPlanActionType.VIDEO:
            if action_result.video_resource_id is None:
                raise ValueError(f"{error_prefix} the video resource is missing")
            return result_spec.InspectionActionVideoEvidence()

        if expected_type == plan_spec.InspectionPlanActionType.QUESTION_ANSWER:
            if action_result.answer_value is None:
                raise ValueError(f"{error_prefix} the answer value is unexpectedly None")
            return result_spec.InspectionActionQuestionAnswerEvidence(answer=action_result.answer_value)

        raise ValueError(f"Unsupported inspection action result type {expected_type}")

    def find_task_by_inspection_id(
        self, inspection_id: int, session: RdbSession | None = None
    ) -> TaskOrderLineInspection | None:
        with begin_or_use_session(self.rdb, session) as s:
            stmt = select(TaskOrderLineInspection).where(TaskOrderLineInspection.inspection_id == inspection_id)
            return s.execute(stmt).scalar_one_or_none()

    def get_inspection_result(
        self, inspection_id: int, session: RdbSession | None = None
    ) -> Tuple[result_spec.InspectionResult, plan_spec.InspectionPlan]:
        results: Dict[Tuple[int, int, int | None], result_spec.InspectionActionResult] = {}
        missing: Dict[Tuple[int, int, int | None], result_spec.InspectionActionMissingResult] = {}

        with begin_or_use_session(self.rdb, session) as s:
            inspection = self.__get_inspection(inspection_id, s=s, require_not_finished=False)
            plan = self.inspection_plan_service.get_inspection_plan(inspection.inspection_plan_id, session=s)
            action_results = self.inspection_db_service.find_inspection_action_results(inspection_id, session=s)

        for step_index, plan_step in enumerate(plan.steps):
            sample_idx: List[int | None] = (
                [None] if not plan_step.repeat_for_each_sample else list(range(inspection.sample_count))
            )
            for action_index, plan_action in enumerate(plan_step.actions):
                for sample_index in sample_idx:
                    result_id = result_spec.InspectionActionResultIdentifier(
                        step_index=step_index, action_index=action_index, sample_index=sample_index
                    )
                    missing[result_id.as_tuple()] = result_spec.InspectionActionMissingResult(
                        result_id=result_id,
                        type=plan_action.type,
                        optional=plan_action.optional,
                    )

        for step, action, action_result in action_results:
            result_id = result_spec.InspectionActionResultIdentifier(
                step_index=step.step_index,
                action_index=action.action_index,
                sample_index=action_result.sample_index,
            )
            result_id_tuple = result_id.as_tuple()

            # _if_ submitting of results is working properly these cases should not happen
            if result_id_tuple in results:
                self.logger.error(f"For inspection {inspection_id} action result {result_id} exists at least twice")
                continue
            if result_id_tuple not in missing:
                self.logger.error(f"For inspection {inspection_id} action result {result_id} is not expected")
                continue

            missing_result = missing.pop(result_id_tuple)
            evidence = self.__map_evidence_from_db(
                action_result=action_result,
                result_id=missing_result.result_id,
                expected_type=missing_result.type,
                inspection_id=inspection_id,
            )
            results[result_id_tuple] = result_spec.InspectionActionResult(
                result_id=result_id,
                evidence=evidence,
            )

        sorted_results = sorted(results.values(), key=lambda r: r.result_id.as_tuple())
        sorted_missing_results = sorted(missing.values(), key=lambda r: r.result_id.as_tuple())
        result = result_spec.InspectionResult.from_results(
            available_results=sorted_results,
            missing_results=sorted_missing_results,
        )
        return result, plan

    def get_inspection_finish(self, inspection_finish_id: int) -> InspectionFinish:
        inspection_finish = self.inspection_db_service.inspection_finish.find_by_id(inspection_finish_id)

        if not inspection_finish:
            raise NoInspectionFinishFoundError()

        return inspection_finish
