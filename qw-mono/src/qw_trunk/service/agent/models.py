"""
Agent models for processing user prompts and generating structured responses.
"""
from typing import Any, Dict, List, Optional, TypedDict, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field
from pydantic_ai.messages import ModelMessage  # Used in ChatSession


class ChatSession(BaseModel):
    """Model for tracking a chat session."""

    session_id: UUID = Field(default_factory=uuid4, description="Unique identifier for the session")
    messages: List[ModelMessage] = Field(default_factory=list, description="Message history for this session")
    created_at: float = Field(
        default_factory=lambda: __import__("time").time(), description="Timestamp when the session was created"
    )
    last_activity: float = Field(
        default_factory=lambda: __import__("time").time(), description="Timestamp of the last activity in this session"
    )


class AgentAction(BaseModel):
    """Model for an action to be executed by the frontend."""

    action_type: str = Field(description="Type of action to perform")
    component: str = Field(description="Component that should handle the action")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Parameters for the action")


class AgentPromptRequest(BaseModel):
    """Request model for agent prompt processing."""

    prompt: str = Field(description="User prompt text")
    session_id: Optional[UUID] = Field(
        default=None,
        description="Session ID to continue an existing conversation. If None, a new session will be created.",
    )
    context: Optional[Dict[str, Any]] = Field(default=None, description="Additional context (e.g., UI state)")


class AgentPromptResponse(BaseModel):
    """Response model for agent prompt processing."""

    message: str = Field(description="Text response to display to the user")
    session_id: UUID = Field(description="Session ID for this conversation")
    actions: Optional[List[AgentAction]] = Field(default=None, description="Actions to be executed by the frontend")


# Structured output models for pydantic-ai
class AgentContext(TypedDict, total=False):
    """TypedDict for agent context that can be passed to tools."""

    session_id: str
    user_context: Dict[str, Any]
    available_actions: List[str]


class ActionParameters(TypedDict, total=False):
    """Base TypedDict for action parameters."""

    pass


class AgentToolResponse(BaseModel):
    """Base model for tool responses."""

    action_result: Optional[AgentAction] = Field(default=None, description="Action result from the tool")
    info_result: Optional[Any] = Field(default=None, description="Information result from the tool")


class AgentActionResponse(BaseModel):
    """Model for agent responses that include actions."""

    message: str = Field(description="Text response to display to the user")
    actions: List[AgentAction] = Field(default_factory=list, description="Actions to be executed by the frontend")


class AgentTextResponse(BaseModel):
    """Model for simple text responses from the agent."""

    message: str = Field(description="Text response to display to the user")


class AgentErrorResponse(BaseModel):
    """Model for error responses from the agent."""

    error: str = Field(description="Error message")
    details: Optional[str] = Field(default=None, description="Additional error details")


# Union type for agent responses
AgentResponse = Union[AgentActionResponse, AgentTextResponse, AgentErrorResponse]
