{"name": "DIN_ISO_2768-1:1991-06_R_F", "tables": [{"name": "Tabelle 1", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0.499, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0.499, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0.499, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0.499, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["v"]], "tolerance": {"upper": 2, "lower": -2}}]}]}