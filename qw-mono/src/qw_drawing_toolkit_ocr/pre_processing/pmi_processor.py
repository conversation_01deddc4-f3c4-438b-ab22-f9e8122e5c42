"""
PMI (Product Manufacturing Information) processor module.
This module provides functionality to process Google Vision results
for technical drawings, classifying text blocks and elements as PMI.
"""
from pathlib import Path
from typing import Any, Dict, List, Literal, Optional

import cv2
import numpy as np
import numpy.typing as npt

from qw_drawing_toolkit_ocr.pre_processing.models import (
    BoundingPoly,
    ClassifiedDocumentText,
    ClassifiedTextBlock,
    ClassifiedTextElement,
    DocumentText,
    EdgeConfig,
    PmiClassification,
    TextBlock,
    TextElement,
)
from qw_drawing_toolkit_ocr.pre_processing.pmi_classification import (
    calculate_height_proxy,
    combine_similar_blocks,
    combine_similar_classified_blocks,
    is_element_out_of_scale,
    is_explicit_standard,
    is_grid_notation,
    is_no_value,
    is_text_only,
)
from qw_drawing_toolkit_ocr.pre_processing.pmi_geometry import expand_bounding_box
from qw_drawing_toolkit_ocr.pre_processing.pmi_grid_detection import find_aligned_elements
from qw_drawing_toolkit_ocr.pre_processing.pmi_tolerance_merging import process_tolerance_sign_blocks
from qw_log_interface import NO_LOGGER, Logger


class PmiProcessor:
    """
    Processor for PMI (Product Manufacturing Information) in technical drawings.
    Analyzes Google Vision results to classify text blocks and elements.
    """

    # Class constants for thresholds
    ALIGNMENT_TOLERANCE = 10.0  # Pixel tolerance for alignment
    SCALE_UPPER_THRESHOLD = 1.7  # Upper threshold for out-of-scale elements
    SCALE_LOWER_THRESHOLD = 0.5  # Lower threshold for out-of-scale elements
    IOU_THRESHOLD_FOR_DUPLICATE_BBOX = (
        0.85  # Intersection over Union threshold for similar blocks that google vision api returns
    )
    IOU_THRESHOLD_FOR_TOUCHING_BLOCKS = 0.01  # IoU threshold for merging touching blocks
    EDGE_ZONE_PERCENTAGE = 0.05  # Percentage of document edge zones
    TEXT_ONLY_PERCENTAGE = 75.0  # Percentage of non-digits to classify as text-only

    # Edge configuration for alignment lines
    EDGE_CONFIGS: Dict[Literal["top", "bottom", "left", "right"], EdgeConfig] = {
        "top": EdgeConfig(
            coord_key="center_y", sort_key=lambda line: line.coordinate, description="topmost"  # Ascending (minimum y)
        ),
        "bottom": EdgeConfig(
            coord_key="center_y",
            sort_key=lambda line: -line.coordinate,  # Descending (maximum y)
            description="bottommost",
        ),
        "left": EdgeConfig(
            coord_key="center_x", sort_key=lambda line: line.coordinate, description="leftmost"  # Ascending (minimum x)
        ),
        "right": EdgeConfig(
            coord_key="center_x",
            sort_key=lambda line: -line.coordinate,  # Descending (maximum x)
            description="rightmost",
        ),
    }

    def __init__(self, logger: Logger = NO_LOGGER):
        """
        Initialize the PMI processor.

        Args:
            logger: Logger instance
        """
        self.logger = logger
        self._grid_blocks: List[TextBlock] = []

    def process_document_text(self, document_text: DocumentText) -> ClassifiedDocumentText:
        """
        Process Google Vision document text to classify text blocks and elements.

        Args:
            document_text: DocumentText from Google Vision

        Returns:
            ClassifiedDocumentText with PMI classifications
        """

        # Combine similar blocks with different element text
        processed_text_blocks = combine_similar_blocks(
            document_text.text_blocks, iou_threshold=self.IOU_THRESHOLD_FOR_DUPLICATE_BBOX, logger=self.logger
        )

        # Create a new DocumentText with the combined blocks
        combined_document_text = DocumentText(text=document_text.text, text_blocks=processed_text_blocks)

        # Initialize ClassifiedDocumentText
        classified_doc = ClassifiedDocumentText(text=combined_document_text.text)

        # Calculate height proxy using rotation-based true height
        height_proxy = calculate_height_proxy(combined_document_text, logger=self.logger)

        # Find grid elements
        horizontal_grid_blocks, vertical_grid_blocks = find_aligned_elements(
            combined_document_text,
            self.EDGE_CONFIGS,
            edge_zone_percentage=self.EDGE_ZONE_PERCENTAGE,
            alignment_tolerance=self.ALIGNMENT_TOLERANCE,
            logger=self.logger,
        )

        self._grid_blocks = []
        for block in horizontal_grid_blocks + vertical_grid_blocks:
            if block not in self._grid_blocks:
                self._grid_blocks.append(block)

        # Process each text block according to priority order
        for block in combined_document_text.text_blocks:
            # Convert to classified block using our helper function
            classified_block = self._convert_to_classified_block(block)

            # First check which elements are out of scale
            out_of_scale_elements_count = 0

            for element in classified_block.elements:
                # Check if element is out of scale using rotation-aware true height
                # Create a TextElement for compatibility with is_element_out_of_scale
                text_element = TextElement(text=element.text, bounding_poly=element.bounding_poly)

                if is_element_out_of_scale(
                    text_element,
                    height_proxy,
                    upper_threshold=self.SCALE_UPPER_THRESHOLD,
                    lower_threshold=self.SCALE_LOWER_THRESHOLD,
                    logger=self.logger,
                ):
                    element.classification = PmiClassification.PMI_OUT_OF_SCALE
                    out_of_scale_elements_count += 1

            # Apply block classification in priority order
            # Step 1: Check if block is a grid notation
            if is_grid_notation(block, self._grid_blocks):
                classified_block.classification = PmiClassification.PMI_GRID_NOTATIONS
                # Cascade classification to elements
                for element in classified_block.elements:
                    element.classification = PmiClassification.PMI_GRID_NOTATIONS

            # Step 2: Check if block contains explicit standards
            elif is_explicit_standard(block):
                classified_block.classification = PmiClassification.PMI_EXPLICIT_STANDARDS
                # Cascade classification to elements
                for element in classified_block.elements:
                    element.classification = PmiClassification.PMI_EXPLICIT_STANDARDS

            # Step 3: Check if no-value pattern
            elif is_no_value(block, logger=self.logger):
                classified_block.classification = PmiClassification.PMI_NO_VALUE
                # Cascade classification to elements
                for element in classified_block.elements:
                    element.classification = PmiClassification.PMI_NO_VALUE

            # Step 4: Check if text-only
            elif is_text_only(block, self.TEXT_ONLY_PERCENTAGE, logger=self.logger):
                classified_block.classification = PmiClassification.PMI_TEXT_ONLY
                # Cascade classification to elements
                for element in classified_block.elements:
                    element.classification = PmiClassification.PMI_TEXT_ONLY

            # Step 5: Check if all elements are out of scale
            # Only mark the block as PMI_OUT_OF_SCALE if its elements are out of scale
            elif classified_block.elements and out_of_scale_elements_count == len(classified_block.elements):
                classified_block.classification = PmiClassification.PMI_OUT_OF_SCALE

            # Add to classified document
            classified_doc.text_blocks.append(classified_block)

        # Process tolerance sign blocks (merge +/- signs with nearby numeric blocks)
        classified_doc = process_tolerance_sign_blocks(
            document_text=classified_doc, height_proxy=height_proxy, logger=self.logger
        )

        # Merge touching unclassified blocks using a very low IoU threshold
        # Split unclassified blocks from other blocks
        unclassified_blocks = [
            block for block in classified_doc.text_blocks if block.classification == PmiClassification.PMI_CANDIDATE
        ]
        other_blocks = [
            block for block in classified_doc.text_blocks if block.classification != PmiClassification.PMI_CANDIDATE
        ]

        # Apply our specialized function to merge similar classified blocks
        merged_unclassified_blocks = combine_similar_classified_blocks(
            unclassified_blocks, iou_threshold=self.IOU_THRESHOLD_FOR_TOUCHING_BLOCKS, logger=self.logger
        )

        # Create updated document with merged blocks
        result_doc = ClassifiedDocumentText(text=classified_doc.text)
        result_doc.text_blocks = other_blocks.copy()  # Start with a copy of other blocks
        result_doc.text_blocks.extend(merged_unclassified_blocks)  # Extend with merged blocks
        classified_doc = result_doc

        # Process and expand PMI_UNCLASSIFIED blocks
        classified_doc = self.process_unclassified_blocks(classified_doc, height_proxy)

        return classified_doc

    def process_unclassified_blocks(
        self,
        classified_doc: ClassifiedDocumentText,
        height_proxy: float,
    ) -> ClassifiedDocumentText:
        """
        Process PMI_UNCLASSIFIED blocks to expand their bounding boxes.

        For blocks with a single element containing at most 2 characters,
        expansion is applied equally on all sides.
        For other blocks, expansion is larger on the "left" side of the text.

        Args:
            classified_doc: ClassifiedDocumentText with PMI classifications
            height_proxy: Calculated height proxy for text elements

        Returns:
            Updated ClassifiedDocumentText with expanded bounding boxes
        """

        self.logger.info("Processing unclassified blocks for bounding box expansion")

        # Filter unclassified blocks
        unclassified_blocks = [
            block for block in classified_doc.text_blocks if block.classification == PmiClassification.PMI_CANDIDATE
        ]

        # Process each unclassified block
        for block in unclassified_blocks:
            try:
                # Use the pre-calculated height proxy instead of calculating height for each block
                if height_proxy <= 0:
                    # self.logger.warning(f"Skipping block '{block.text}' due to invalid height proxy")
                    continue

                # Determine expansion strategy:
                # If block has exactly one element AND text has at most 2 characters,
                # then apply equal expansion on all sides
                is_single_element = len(block.elements) == 1
                text_length = len(block.text.strip())
                use_equal_expansion = is_single_element and text_length <= 2

                # if use_equal_expansion:
                #     self.logger.info(f"Using equal expansion for single element with short text: '{block.text}'")

                # Expand bounding box using the pre-calculated height proxy
                expanded_vertices = expand_bounding_box(
                    vertices=block.bounding_poly.vertices,
                    height_value=height_proxy,
                    logger=self.logger,
                    use_equal_expansion=use_equal_expansion,
                )

                # Store expanded bounding box
                block.expanded_bounding_poly = BoundingPoly(vertices=expanded_vertices)

            except Exception as e:
                self.logger.error(f"Error processing unclassified block '{block.text}': {str(e)}")

        return classified_doc

    def save_classified_document(
        self,
        classified_doc: ClassifiedDocumentText,
        output_dir: Optional[Path],
    ) -> Optional[Path]:
        """
        Save the classified document text to a JSON file.

        Args:
            classified_doc: ClassifiedDocumentText with PMI classifications
            output_dir: Directory to save the JSON file

        Returns:
            Path to the saved JSON file or None if output_dir is None
        """
        if output_dir is None:
            self.logger.warning("Cannot save classified document: output_dir is None")
            return None
        import json

        # Convert to dictionary
        classified_data: Dict[str, Any] = {
            "text": classified_doc.text,
            "text_blocks": [
                {
                    "text": block.text,
                    "classification": block.classification,
                    "bounding_poly": {"vertices": block.bounding_poly.vertices},
                    # Include expanded bounding polygon if available
                    "expanded_bounding_poly": (
                        {"vertices": block.expanded_bounding_poly.vertices} if block.expanded_bounding_poly else None
                    ),
                    "elements": [
                        {
                            "text": element.text,
                            "classification": element.classification,
                            "bounding_poly": {"vertices": element.bounding_poly.vertices},
                        }
                        for element in block.elements
                    ],
                }
                for block in classified_doc.text_blocks
            ],
        }

        # Save to file
        output_path = output_dir / "3_classified_pmi_boxes.json"
        with open(output_path, "w") as f:
            json.dump(classified_data, f, indent=2)

        return output_path

    def visualize_pmi_classifications(
        self,
        image: npt.NDArray[np.uint8],
        classified_doc: ClassifiedDocumentText,
        output_dir: Optional[Path],
    ) -> Optional[Path]:
        """
        Create visualization image for PMI classifications with different colors.
        For PMI_UNCLASSIFIED blocks with expanded bounding boxes, shows both
        the original (gray) and expanded (purple) boxes.
        For PMI_UNCLASSIFIED blocks, also displays the text content.

        Args:
            image: Original image
            classified_doc: ClassifiedDocumentText with PMI classifications
            output_dir: Directory to save visualization image

        Returns:
            Path to the saved visualization image or None if output_dir is None
        """
        if output_dir is None:
            self.logger.warning("Cannot create PMI visualizations: output_dir is None")
            return None

        # Create a copy of the original image
        visualization = image.copy()

        # Define colors for different classifications (BGR format)
        colors = {
            PmiClassification.PMI_GRID_NOTATIONS: (0, 255, 0),  # Green
            PmiClassification.PMI_EXPLICIT_STANDARDS: (0, 0, 255),  # Red
            PmiClassification.PMI_OUT_OF_SCALE: (255, 0, 0),  # Blue
            PmiClassification.PMI_TEXT_ONLY: (255, 255, 0),  # Cyan
            PmiClassification.PMI_NO_VALUE: (0, 255, 255),  # Yellow
            PmiClassification.PMI_CANDIDATE: (128, 128, 128),  # Gray
        }

        # Special color for expanded bounding boxes
        expanded_box_color = (128, 0, 128)  # Purple

        # Draw rectangles for each classification type
        for block in classified_doc.text_blocks:
            try:
                # Draw original bounding box
                vertices = block.bounding_poly.vertices
                points: List[List[int]] = []
                for vertex in vertices:
                    if "x" in vertex and "y" in vertex:
                        x_val = int(vertex["x"])
                        y_val = int(vertex["y"])
                        points.append([x_val, y_val])
                pts = np.array(points, dtype=np.int32)

                if pts.size > 0:
                    # Get color based on classification
                    color = colors.get(block.classification, (128, 128, 128))

                    # Create transparent overlay
                    overlay = visualization.copy()
                    cv2.fillPoly(overlay, [pts], color)
                    alpha = 0.3
                    cv2.addWeighted(overlay, alpha, visualization, 1 - alpha, 0, visualization)

                    # Draw outline with the same color
                    cv2.polylines(visualization, [pts], True, color, 2)

                    # For PMI_UNCLASSIFIED blocks, display the text content
                    if block.classification == PmiClassification.PMI_CANDIDATE:
                        # Calculate the centroid position for text placement
                        # Initialize default values
                        centroid_x = 0
                        centroid_y = 0

                        # Type-safe handling of points
                        if points:
                            x_sum = 0
                            y_sum = 0
                            valid_points = 0

                            # Safely extract coordinates using explicit typing
                            points_with_types: List[List[int]] = []
                            for p in points:
                                if len(p) >= 2:
                                    try:
                                        # Convert to explicitly typed point
                                        point_x = int(p[0])
                                        point_y = int(p[1])
                                        points_with_types.append([point_x, point_y])
                                    except (IndexError, ValueError, TypeError):
                                        pass

                            # Calculate centroid from the typed points
                            for point in points_with_types:
                                x_sum += point[0]
                                y_sum += point[1]
                                valid_points += 1

                            # Calculate centroid if we have valid points
                            if valid_points > 0:
                                centroid_x = x_sum // valid_points
                                centroid_y = y_sum // valid_points

                        # Put the text content at the centroid
                        # Use black text with white outline for better visibility
                        text = block.text
                        # Limit text length to avoid overcrowding
                        if len(text) > 20:
                            text = text[:17] + "..."

                        # Draw text with white outline for better visibility
                        font = cv2.FONT_HERSHEY_SIMPLEX
                        font_scale = 0.5
                        thickness = 1
                        text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]

                        # Position text to not obscure the content
                        text_x = centroid_x - text_size[0] // 2
                        text_y = centroid_y + text_size[1] // 2

                        # White outline
                        cv2.putText(
                            visualization, text, (text_x, text_y), font, font_scale, (255, 255, 255), thickness + 2
                        )
                        # Black text
                        cv2.putText(visualization, text, (text_x, text_y), font, font_scale, (0, 0, 0), thickness)

                # Draw expanded bounding box if available
                if block.expanded_bounding_poly:
                    exp_vertices = block.expanded_bounding_poly.vertices
                    exp_points = []
                    for vertex in exp_vertices:
                        if "x" in vertex and "y" in vertex:
                            exp_points.append([vertex["x"], vertex["y"]])
                    exp_pts = np.array(exp_points, dtype=np.int32)

                    if exp_pts.size > 0:
                        # Draw dashed outline with purple color for expanded box
                        # Create a dash pattern
                        for i in range(len(exp_pts)):
                            pt1 = tuple(exp_pts[i])
                            pt2 = tuple(exp_pts[(i + 1) % len(exp_pts)])

                            # Draw dashed line (using multiple small lines)
                            dash_length = 10
                            gap_length = 5
                            d = np.sqrt((pt2[0] - pt1[0]) ** 2 + (pt2[1] - pt1[1]) ** 2)

                            if d > 0:
                                num_segments = int(d / (dash_length + gap_length)) + 1

                                for j in range(num_segments):
                                    start_ratio = j * (dash_length + gap_length) / d
                                    end_ratio = min((j * (dash_length + gap_length) + dash_length) / d, 1.0)

                                    if start_ratio < 1.0:
                                        start_pt = (
                                            int(pt1[0] + (pt2[0] - pt1[0]) * start_ratio),
                                            int(pt1[1] + (pt2[1] - pt1[1]) * start_ratio),
                                        )
                                        end_pt = (
                                            int(pt1[0] + (pt2[0] - pt1[0]) * end_ratio),
                                            int(pt1[1] + (pt2[1] - pt1[1]) * end_ratio),
                                        )

                                        cv2.line(visualization, start_pt, end_pt, expanded_box_color, 2)

            except Exception as e:
                self.logger.warning(f"Error visualizing block: {str(e)}")

        # Save visualization
        output_path = output_dir / "3_pmi_classified_blocks.png"
        cv2.imwrite(str(output_path), visualization)

        return output_path

    def _convert_to_classified_block(self, block: TextBlock) -> ClassifiedTextBlock:
        """
        Convert a regular TextBlock to a ClassifiedTextBlock.

        Args:
            block: Regular TextBlock from the OCR result

        Returns:
            ClassifiedTextBlock with default classification
        """
        # Create classified elements from regular elements
        classified_elements: List[ClassifiedTextElement] = []
        for element in block.elements:
            classified_element = ClassifiedTextElement(
                text=element.text,
                bounding_poly=element.bounding_poly,
                classification=PmiClassification.PMI_CANDIDATE,
            )
            classified_elements.append(classified_element)

        # Create new classified block with the same text and bounding polygon
        classified_block = ClassifiedTextBlock(
            text=block.text,
            bounding_poly=block.bounding_poly,
            elements=classified_elements,
            classification=PmiClassification.PMI_CANDIDATE,
        )

        return classified_block
