import io
import json
import re
from dataclasses import dataclass, field
from datetime import datetime
from typing import BinaryIO, Dict, Generic, List, Literal, Sequence, Tuple, Type, TypeVar

import urllib3
from pydantic import BaseModel, ValidationError

from qw_basic_keycloak.interface import CodeFlowAuthInit
from qw_basic_keycloak.openid.dtos.authorization import AuthenticationResponse
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysis
from qw_falcon_openapi.spec import OpenApiDocument
from qw_material_certificate_analysis.agents.models import MaterialCertificateAnalysisResponse
from qw_monodb.table.trunk.resource import FileResourceLabel
from qw_pfoertner.api.auth import COOKIE_SESSION
from qw_pfoertner.api.internal.get_health import HealthOutput
from qw_pfoertner.api.v1.chat.add_chat_message import ChatAddMessageInput, ChatAddMessageOutput
from qw_pfoertner.api.v1.chat.edit_chat_message import ChatEditMessageInput, ChatEditMessageOutput
from qw_pfoertner.api.v1.chat.get_chat_messages import ChatGetMessagesInput, ChatGetMessagesOutput
from qw_pfoertner.api.v1.file.add_file_resource_revision import (
    NewFileResourceRevisionInput,
    NewFileResourceRevisionMpBodyDef,
    NewFileResourceRevisionOutput,
)
from qw_pfoertner.api.v1.file.delete_file_resources import DeleteFileResourcesInput, DeleteFileResourcesOutput
from qw_pfoertner.api.v1.file.drawing.add_drawing_discussion import (
    NewDrawingDiscussionInput,
    NewDrawingDiscussionOutput,
)
from qw_pfoertner.api.v1.file.drawing.get_drawing_discussion import DrawingDiscussionView
from qw_pfoertner.api.v1.file.drawing.list_drawing_discussions import (
    ListDrawingDiscussionsInput,
    ListDrawingDiscussionsOutput,
)
from qw_pfoertner.api.v1.file.drawing.set_drawing_discussion_status import (
    SetDrawingDiscussionStatusInput,
    SetDrawingDiscussionStatusOutput,
)
from qw_pfoertner.api.v1.file.list_file_resources import ListFileResourcesInput, ListFileResourcesOutput
from qw_pfoertner.api.v1.file.list_file_resources_metadata import (
    ListFileResourcesMetadataInput,
    ListFileResourcesMetadataOutput,
)
from qw_pfoertner.api.v1.file.material_certificate.get_material_certificate_analysis import FileResourceRevisionIdParam
from qw_pfoertner.api.v1.file.rename_file_resource import FileResourceRenameInput, FileResourceRenameOutput
from qw_pfoertner.api.v1.file.toggle_file_resource_revision_access import (
    ToggleFileResourceRevisionAccessInput,
    ToggleFileResourceRevisionAccessOutput,
)
from qw_pfoertner.api.v1.file.views import FileResourceView
from qw_pfoertner.api.v1.inspection.add_inspection_action_result import (
    NewInspectionActionResultInput,
    NewInspectionActionResultMpBodyDef,
    NewInspectionActionResultOutput,
)
from qw_pfoertner.api.v1.inspection.add_inspection_plan import NewInspectionPlanInput, NewInspectionPlanOutput
from qw_pfoertner.api.v1.inspection.delete_inspection_plans import (
    DeleteInspectionPlansInput,
    DeleteInspectionPlansOutput,
)
from qw_pfoertner.api.v1.inspection.delete_inspections import DeleteInspectionsInput, DeleteInspectionsOutput
from qw_pfoertner.api.v1.inspection.finish_inspection import FinishInspectionOutput
from qw_pfoertner.api.v1.inspection.get_inspection import InspectionView
from qw_pfoertner.api.v1.inspection.get_inspection_plan import InspectionPlanIdParam, InspectionPlanView
from qw_pfoertner.api.v1.inspection.get_inspection_result_as_pdf import GenerateInspectionPdfInput
from qw_pfoertner.api.v1.inspection.get_inspection_result_image import InspectionEvidenceQueryParams
from qw_pfoertner.api.v1.inspection.list_inspection_plans import ListInspectionPlansInput, ListInspectionPlansOutput
from qw_pfoertner.api.v1.inspection.list_inspection_plans_metadata import (
    ListInspectionPlansMetadataInput,
    ListInspectionPlansMetadataOutput,
)
from qw_pfoertner.api.v1.inspection.list_inspections import ListInspectionsInput, ListInspectionsOutput
from qw_pfoertner.api.v1.inspection.update_inspection_plan import UpdateInspectionPlanInput, UpdateInspectionPlanOutput
from qw_pfoertner.api.v1.material.views import MaterialView
from qw_pfoertner.api.v1.order.add_order_line import NewOrderLineInput, NewOrderLineOutput
from qw_pfoertner.api.v1.order.add_order_line_task import NewOrderLineTaskInput, NewOrderLineTaskOutput
from qw_pfoertner.api.v1.order.delete_order_lines import DeleteOrderLinesInput, DeleteOrderLinesOutput
from qw_pfoertner.api.v1.order.delete_orders import DeleteOrdersInput, DeleteOrdersOutput
from qw_pfoertner.api.v1.order.get_order_line_tasks import OrderLineTasksView
from qw_pfoertner.api.v1.order.list_orders import ListOrdersInput, ListOrdersOutput
from qw_pfoertner.api.v1.order.update_order_line_inspection_due_date import (
    UpdateOrderLineTaskDueDateInput,
    UpdateOrderLineTaskDueDateOutput,
)
from qw_pfoertner.api.v1.order.update_order_line_requirements import (
    UpdateOrderLineRequirementsInput,
    UpdateOrderLineRequirementsOutput,
)
from qw_pfoertner.api.v1.order.views import OrderLineView, OrderView
from qw_pfoertner.api.v1.session.conclude_login import NewSession
from qw_pfoertner.api.v1.session.get_session import SessionInfo
from qw_pfoertner.api.v1.session.initiate_login import LoginRequest
from qw_pfoertner.api.v1.session.initiate_logout import LogoutRequest, LogoutResponse
from qw_pfoertner.api.v1.tenant.list_tenants import ListTenantsInput, ListTenantsOutput
from qw_pfoertner.api.v1.tenant.views import TenantExtendedView
from qw_pfoertner.api.v1.tolerance.get_tolerance_range import ToleranceLookupInput, ToleranceLookupOutput
from qw_pfoertner.api.v1.tolerance.list_tolerance_standards import TolerancesView
from qw_pfoertner.api.v1.user.get_user import UserView
from qw_pfoertner.api.v1.user.list_users import ListUsersInput, ListUsersOutput
from qw_pfoertner.api.v1.wopi.internal.wopi_token import WopiAccessTokenView
from qw_pfoertner.api.v1.wopi.wopi_file import WopiCheckFileInfoResponse
from qw_pfoertner.api.v1.wopi.wopi_file_contents import WopiPutFileResponse

T = TypeVar("T")
RQ = TypeVar("RQ", bound=BaseModel)
RP = TypeVar("RP", bound=BaseModel)

CT_JSON = "application/json"
CT_PDF = "application/pdf"


@dataclass
class ClientRequest:
    method: Literal["GET", "POST"]
    url: str
    url_params: Dict[str, str] = field(default_factory=dict)
    headers: Dict[str, str] = field(default_factory=dict)
    body: bytes = b""


@dataclass
class ClientResponse:
    status_code: int
    headers: Dict[str, str] = field(default_factory=dict)
    body: bytes = b""


class ApiResponse(Generic[T]):
    def __init__(self, obj: T | None, http: ClientResponse):
        self.obj = obj
        self.http = http

    def get(self) -> T:
        if self.obj is None:
            raise ValueError(f"Response is missing (status {self.status_code})")
        return self.obj

    @property
    def status_code(self) -> int:
        return self.http.status_code

    @property
    def is_ok(self) -> bool:
        return self.obj is not None and 200 <= self.status_code < 400


class RawApiResponse(object):
    def __init__(self, http: ClientResponse):
        self.http = http

    @property
    def status_code(self) -> int:
        return self.http.status_code


class QwPfoertnerClient(object):
    def copy(self) -> "QwPfoertnerClient":
        raise NotImplementedError()

    def do_request(self, req: ClientRequest) -> ClientResponse:
        raise NotImplementedError()

    def set_default_header(self, name: str, value: str) -> None:
        raise NotImplementedError()

    def unset_default_header(self, name: str) -> None:
        raise NotImplementedError()

    def set_cookie(self, name: str, value: str) -> None:
        raise NotImplementedError()

    def unset_cookie(self, name: str) -> None:
        raise NotImplementedError()

    # ---

    def unset_session_cookie(self) -> None:
        self.unset_cookie(COOKIE_SESSION)

    @classmethod
    def try_json_deserialization(cls, t: Type[RP], body: bytes) -> RP | None:
        try:
            data = json.loads(body)
            return t(**data)
        except (ValidationError, json.JSONDecodeError):
            return None

    def get(
        self,
        url: str,
        url_params: Dict[str, str] | None = None,
        headers: Dict[str, str] | None = None,
    ) -> RawApiResponse:
        req = ClientRequest("GET", url=url, url_params=url_params or {}, headers=headers or {})
        resp = self.do_request(req)
        return RawApiResponse(resp)

    def get_for_json(
        self,
        t_resp: Type[RP],
        url: str,
        url_params: Dict[str, str] | None = None,
        headers: Dict[str, str] | None = None,
    ) -> ApiResponse[RP]:
        resp = self.get(url, url_params, headers)
        return ApiResponse(self.try_json_deserialization(t_resp, resp.http.body), resp.http)

    def post(
        self,
        binary: BinaryIO,
        content_type: str,
        url: str,
        url_params: Dict[str, str] | None = None,
        headers: Dict[str, str] | None = None,
    ) -> RawApiResponse:
        if headers is None:
            headers = {}
        headers["content-type"] = content_type
        req = ClientRequest("POST", body=binary.read(), url=url, url_params=url_params or {}, headers=headers)
        resp = self.do_request(req)
        return RawApiResponse(resp)

    def post_for_json(
        self,
        binary: BinaryIO,
        content_type: str,
        t_resp: Type[RP],
        url: str,
        url_params: Dict[str, str] | None = None,
        headers: Dict[str, str] | None = None,
    ) -> ApiResponse[RP]:
        resp = self.post(binary, content_type, url, url_params, headers)
        return ApiResponse(self.try_json_deserialization(t_resp, resp.http.body), resp.http)

    def post_json(
        self,
        obj: BaseModel,
        url: str,
        url_params: Dict[str, str] | None = None,
        headers: Dict[str, str] | None = None,
    ) -> RawApiResponse:
        data = io.BytesIO(obj.model_dump_json(by_alias=True).encode())
        return self.post(data, CT_JSON, url, url_params, headers)

    def post_json_for_json(
        self,
        obj: BaseModel,
        t_resp: Type[RP],
        url: str,
        url_params: Dict[str, str] | None = None,
        headers: Dict[str, str] | None = None,
    ) -> ApiResponse[RP]:
        data = io.BytesIO(obj.model_dump_json(by_alias=True).encode())
        return self.post_for_json(data, CT_JSON, t_resp, url, url_params, headers)

    def post_multipart_for_json(
        self,
        parts: Sequence[Tuple[str, BaseModel | bytes, str]],
        t_resp: Type[RP],
        url: str,
        url_params: Dict[str, str] | None = None,
        headers: Dict[str, str] | None = None,
    ) -> ApiResponse[RP]:
        fields = []
        for name, part_data, content_type in parts:
            if isinstance(part_data, BaseModel):
                part_data = part_data.model_dump_json(by_alias=True).encode()
            fields.append(
                urllib3.filepost.RequestField(
                    name=name,
                    data=part_data,
                    headers={
                        "content-disposition": f'form-data; name="{name}"',
                        "content-type": content_type,
                    },
                )
            )
        payload, multipart_ct = urllib3.encode_multipart_formdata(fields)
        return self.post_for_json(io.BytesIO(payload), multipart_ct, t_resp, url, url_params, headers)

    # ---

    def get_openapi_spec(self) -> ApiResponse[OpenApiDocument]:
        return self.get_for_json(OpenApiDocument, "/api/openapi.json")

    def get_openapi_spec_yaml(self) -> ApiResponse[str]:
        resp = self.get("/api/openapi.yaml")
        return ApiResponse(resp.http.body.decode(), resp.http)

    def get_health(self) -> ApiResponse[HealthOutput]:
        return self.get_for_json(HealthOutput, "/api/health")

    # --------------------------------------------------------------------------------------
    #                              Session and User
    # --------------------------------------------------------------------------------------

    def login_init(self) -> ApiResponse[CodeFlowAuthInit]:
        return self.post_json_for_json(LoginRequest(), CodeFlowAuthInit, "/api/v1/session/login/init")

    def login_conclude(self, auth_resp: AuthenticationResponse, set_session: bool = True) -> ApiResponse[NewSession]:
        resp = self.get_for_json(NewSession, "/api/v1/session/login/conclude", url_params=auth_resp.model_dump())
        if set_session and resp.status_code == 200 and resp.obj is not None:
            set_cookie_value = resp.http.headers.get("set-cookie")
            if not set_cookie_value:
                raise RuntimeError("No cookie after login conclusion")
            m = re.match(rf"\s*{COOKIE_SESSION}=(?P<SESSION>.*?)\s*;.*", set_cookie_value)
            if m is None:
                raise RuntimeError("Could not parse cookie after login conclusion")
            self.set_cookie(COOKIE_SESSION, m.group("SESSION"))
        return resp

    def logout_init(self, clear_cookie: bool = True) -> ApiResponse[LogoutResponse]:
        resp = self.post_json_for_json(LogoutRequest(), LogoutResponse, "/api/v1/session/logout/init")
        if resp.is_ok and clear_cookie:
            self.unset_cookie(COOKIE_SESSION)
        return resp

    def get_session_info(self) -> ApiResponse[SessionInfo]:
        return self.get_for_json(SessionInfo, "/api/v1/session")

    def get_session_user(self) -> UserView:
        resp = self.get_session_info()
        if not resp.is_ok:
            raise RuntimeError("Could not retrieve session info")
        session_info = resp.get()
        resp2 = self.list_users(ListUsersInput(in_issuers=[session_info.issuer], in_subjects=[session_info.subject]))
        if not resp2.is_ok:
            raise RuntimeError("Could not retrieve users")
        list_output = resp2.get()
        if len(list_output.users) != 1:
            raise RuntimeError(f"Got {len(list_output.users)} users, expected 1")
        return list_output.users[0]

    def get_user(self, user_id: int) -> ApiResponse[UserView]:
        return self.get_for_json(UserView, f"/api/v1/user/{user_id}")

    def list_users(self, params: ListUsersInput) -> ApiResponse[ListUsersOutput]:
        return self.post_json_for_json(params, ListUsersOutput, "/api/v1/users")

    # --------------------------------------------------------------------------------------
    #                                  Chat related
    # --------------------------------------------------------------------------------------

    def add_chat_message(self, chat_id: int, content: str, ts_sent: datetime) -> ApiResponse[ChatAddMessageOutput]:
        params = ChatAddMessageInput(content=content, ts_sent=ts_sent)
        return self.post_json_for_json(params, ChatAddMessageOutput, f"/api/v1/chat/{chat_id}/messages/add")

    def edit_chat_message(
        self, message_id: int | None, content: str, ts_sent: datetime | None
    ) -> ApiResponse[ChatEditMessageOutput]:
        params = ChatEditMessageInput(content=content, ts_sent=ts_sent)
        return self.post_json_for_json(params, ChatEditMessageOutput, f"/api/v1/chat/messages/{message_id}/edit")

    def get_chat_messages(
        self,
        chat_id: int,
        count: int,
        offset: int = 0,
        ts_after: datetime | None = None,
        ts_before: datetime | None = None,
        newest_first: bool = True,
    ) -> ApiResponse[ChatGetMessagesOutput]:
        params = ChatGetMessagesInput(
            chat_id=chat_id,
            count=count,
            offset=offset,
            ts_after=ts_after,
            ts_before=ts_before,
            newest_first=newest_first,
        )
        return self.post_json_for_json(params, ChatGetMessagesOutput, f"/api/v1/chat/{chat_id}/messages")

    # --------------------------------------------------------------------------------------
    #                                  File Resources
    # --------------------------------------------------------------------------------------

    def add_file_resource_revision(
        self, data: NewFileResourceRevisionInput, file_bytes: bytes, file_content_type: str
    ) -> ApiResponse[NewFileResourceRevisionOutput]:
        parts: List[Tuple[str, BaseModel | bytes, str]] = [
            (NewFileResourceRevisionMpBodyDef.NAME_REV_INFO, data, CT_JSON),
            (NewFileResourceRevisionMpBodyDef.NAME_REV_BYTES, file_bytes, file_content_type),
        ]
        return self.post_multipart_for_json(parts, NewFileResourceRevisionOutput, "/api/v1/file-resource/revision")

    def get_file_resource(self, file_resource_id: int) -> ApiResponse[FileResourceView]:
        return self.get_for_json(FileResourceView, f"/api/v1/file-resource/{file_resource_id}")

    def get_file_resource_download(self, file_resource_revision_id: int) -> RawApiResponse:
        return self.get(f"/api/v1/file-resource/revision/{file_resource_revision_id}/download")

    def get_file_resource_revision(self, file_resource_revision_id: int) -> RawApiResponse:
        return self.get(f"/api/v1/file-resource/revision/{file_resource_revision_id}")

    def get_drawing_analysis(self, file_resource_revision_id: int) -> ApiResponse[DrawingAnalysis]:
        return self.get_for_json(
            DrawingAnalysis, f"/api/v1/file-resource/drawing/revision/{file_resource_revision_id}/analysis"
        )

    def list_file_resources(self, params: ListFileResourcesInput) -> ApiResponse[ListFileResourcesOutput]:
        return self.post_json_for_json(params, ListFileResourcesOutput, "/api/v1/file-resources")

    def list_file_resources_metadata(
        self, params: ListFileResourcesMetadataInput
    ) -> ApiResponse[ListFileResourcesMetadataOutput]:
        return self.post_json_for_json(params, ListFileResourcesMetadataOutput, "/api/v1/file-resources-metadata")

    def add_drawing_discussion(self, params: NewDrawingDiscussionInput) -> ApiResponse[NewDrawingDiscussionOutput]:
        return self.post_json_for_json(params, NewDrawingDiscussionOutput, "/api/v1/file-resource/drawing-discussion")

    def get_drawing_discussion(self, drawing_discussion_id: int) -> ApiResponse[DrawingDiscussionView]:
        return self.get_for_json(
            DrawingDiscussionView, f"/api/v1/file-resource/drawing-discussion/{drawing_discussion_id}"
        )

    def list_drawing_discussions(
        self, params: ListDrawingDiscussionsInput
    ) -> ApiResponse[ListDrawingDiscussionsOutput]:
        return self.post_json_for_json(
            params, ListDrawingDiscussionsOutput, "/api/v1/file-resource/drawing-discussions"
        )

    def set_drawing_discussion_status(
        self, drawing_discussion_id: int, resolved: bool
    ) -> ApiResponse[SetDrawingDiscussionStatusOutput]:
        params = SetDrawingDiscussionStatusInput(resolved=resolved)
        return self.post_json_for_json(
            params,
            SetDrawingDiscussionStatusOutput,
            f"/api/v1/file-resource/drawing-discussion/{drawing_discussion_id}/status",
        )

    def rename_file_resource(
        self, file_resource_id: int, new_display_name: str
    ) -> ApiResponse[FileResourceRenameOutput]:
        params = FileResourceRenameInput(new_display_name=new_display_name)
        return self.post_json_for_json(
            params,
            FileResourceRenameOutput,
            f"/api/v1/file-resource/{file_resource_id}/rename",
        )

    def delete_file_resources(self, file_resource_ids: List[int]) -> ApiResponse[DeleteFileResourcesOutput]:
        params = DeleteFileResourcesInput(file_resource_ids=file_resource_ids)
        return self.post_json_for_json(
            params,
            DeleteFileResourcesOutput,
            "/api/v1/file-resources/delete",
        )

    def get_material_certification_page_image(self, file_resource_revision_id: int, page_index: int) -> RawApiResponse:
        return self.get(
            f"/api/v1/file-resource/material-certificate/revision/{file_resource_revision_id}/page/{page_index}/image"
        )

    def get_material_certification_analysis(
        self, file_resource_revision_id: int
    ) -> ApiResponse[MaterialCertificateAnalysisResponse]:
        params = FileResourceRevisionIdParam(file_resource_revision_id=file_resource_revision_id)
        return self.get_for_json(
            MaterialCertificateAnalysisResponse,
            f"/api/v1/file-resource/material-certificate/revision/{file_resource_revision_id}/analysis",
            params.model_dump(by_alias=True),
        )

    def toggle_file_resource_revision_share(
        self, file_resource_revision_id: int, tenant_id: int, enabled: bool
    ) -> ApiResponse[ToggleFileResourceRevisionAccessOutput]:
        params = ToggleFileResourceRevisionAccessInput(
            file_resource_revision_id=file_resource_revision_id, tenant_id=tenant_id, enabled=enabled
        )
        return self.post_json_for_json(
            params,
            ToggleFileResourceRevisionAccessOutput,
            "/api/v1/file-resource/revision/share",
        )

    # --------------------------------------------------------------------------------------
    #                                     Orders
    # --------------------------------------------------------------------------------------

    def add_order_line(self, params: NewOrderLineInput) -> ApiResponse[NewOrderLineOutput]:
        return self.post_json_for_json(params, NewOrderLineOutput, "/api/v1/order-line")

    def get_order(self, order_id: int) -> ApiResponse[OrderView]:
        return self.get_for_json(OrderView, f"/api/v1/order/{order_id}")

    def get_order_line(self, order_line_id: int) -> ApiResponse[OrderLineView]:
        return self.get_for_json(OrderLineView, f"/api/v1/order-line/{order_line_id}")

    def get_order_line_tasks(self, order_line_id: int) -> ApiResponse[OrderLineTasksView]:
        return self.get_for_json(OrderLineTasksView, f"/api/v1/order-line/{order_line_id}/tasks")

    def add_order_line_task(
        self, order_line_id: int, params: NewOrderLineTaskInput
    ) -> ApiResponse[NewOrderLineTaskOutput]:
        return self.post_json_for_json(params, NewOrderLineTaskOutput, f"/api/v1/order-line/{order_line_id}/task")

    def update_order_line_inspection_due_date(
        self, order_line_task_id: int, params: UpdateOrderLineTaskDueDateInput
    ) -> ApiResponse[UpdateOrderLineTaskDueDateOutput]:
        return self.post_json_for_json(
            params,
            UpdateOrderLineTaskDueDateOutput,
            f"/api/v1/order-line-task/inspection/{order_line_task_id}/due-date",
        )

    def list_orders(self, params: ListOrdersInput) -> ApiResponse[ListOrdersOutput]:
        return self.post_json_for_json(params, ListOrdersOutput, "/api/v1/orders")

    def delete_orders(self, order_ids: Sequence[int]) -> ApiResponse[DeleteOrdersOutput]:
        return self.post_json_for_json(
            DeleteOrdersInput(order_ids=order_ids), DeleteOrdersOutput, "/api/v1/orders/delete"
        )

    def delete_order_lines(self, order_line_ids: Sequence[int]) -> ApiResponse[DeleteOrderLinesOutput]:
        return self.post_json_for_json(
            DeleteOrderLinesInput(order_line_ids=order_line_ids), DeleteOrderLinesOutput, "/api/v1/order-lines/delete"
        )

    def update_order_line_requirements(
        self, order_line_id: int, requirements: List[FileResourceLabel]
    ) -> ApiResponse[UpdateOrderLineRequirementsOutput]:
        return self.post_json_for_json(
            UpdateOrderLineRequirementsInput(requirements=requirements),
            UpdateOrderLineRequirementsOutput,
            f"/api/v1/order-line/{order_line_id}/requirements/update",
        )

    # --------------------------------------------------------------------------------------
    #                                  Materials
    # --------------------------------------------------------------------------------------

    def get_material(self, material_id: int) -> ApiResponse[MaterialView]:
        return self.get_for_json(MaterialView, f"/api/v1/material/{material_id}")

    # --------------------------------------------------------------------------------------
    #                                  Inspections
    # --------------------------------------------------------------------------------------

    def add_inspection_plan(self, params: NewInspectionPlanInput) -> ApiResponse[NewInspectionPlanOutput]:
        return self.post_json_for_json(params, NewInspectionPlanOutput, "/api/v1/inspection-plan")

    def delete_inspections(self, inspection_ids: Sequence[int]) -> ApiResponse[DeleteInspectionsOutput]:
        return self.post_json_for_json(
            DeleteInspectionsInput(inspection_ids=inspection_ids), DeleteInspectionsOutput, "/api/v1/inspections/delete"
        )

    def list_inspection_plans(self, params: ListInspectionPlansInput) -> ApiResponse[ListInspectionPlansOutput]:
        return self.post_json_for_json(params, ListInspectionPlansOutput, "/api/v1/inspection-plans")

    def list_inspection_plans_metadata(
        self, params: ListInspectionPlansMetadataInput
    ) -> ApiResponse[ListInspectionPlansMetadataOutput]:
        return self.post_json_for_json(params, ListInspectionPlansMetadataOutput, "/api/v1/inspection-plans-metadata")

    def add_inspection_action_result(
        self, params: NewInspectionActionResultInput, image_bytes: bytes | None = None, video_bytes: bytes | None = None
    ) -> ApiResponse[NewInspectionActionResultOutput]:
        parts: List[Tuple[str, BaseModel | bytes, str]] = [
            (NewInspectionActionResultMpBodyDef.NAME_INSPECTION_RESULT, params, CT_JSON),
        ]
        if image_bytes is not None:
            parts.append((NewInspectionActionResultMpBodyDef.NAME_IMAGE_EVIDENCE, image_bytes, "image/jpeg"))
        if video_bytes is not None:
            parts.append((NewInspectionActionResultMpBodyDef.NAME_VIDEO_EVIDENCE, video_bytes, "video/mp4"))
        return self.post_multipart_for_json(
            parts=parts,
            t_resp=NewInspectionActionResultOutput,
            url="/api/v1/inspection-action-result",
        )

    def finish_inspection(self, inspection_id: int) -> ApiResponse[FinishInspectionOutput]:
        return self.get_for_json(FinishInspectionOutput, f"/api/v1/inspection/{inspection_id}/finish")

    def get_inspection(self, inspection_id: int) -> ApiResponse[InspectionView]:
        return self.get_for_json(InspectionView, f"/api/v1/inspection/{inspection_id}")

    def get_inspection_result_image(
        self, inspection_id: int, step_index: int, action_index: int, sample_index: int | None
    ) -> RawApiResponse:
        return self.get(
            f"/api/v1/inspection/{inspection_id}/result/image",
            url_params=InspectionEvidenceQueryParams(
                step_index=step_index,
                action_index=action_index,
                sample_index=sample_index,
            ).model_dump(by_alias=True),
        )

    def get_inspection_result_video(
        self, inspection_id: int, step_index: int, action_index: int, sample_index: int | None
    ) -> RawApiResponse:
        return self.get(
            f"/api/v1/inspection/{inspection_id}/result/video",
            url_params=InspectionEvidenceQueryParams(
                step_index=step_index,
                action_index=action_index,
                sample_index=sample_index,
            ).model_dump(by_alias=True),
        )

    def get_inspection_result_pdf(self, inspection_id: int, params: GenerateInspectionPdfInput) -> RawApiResponse:
        url = f"/api/v1/inspection/{inspection_id}/result/pdf"
        return self.post_json(params, url, headers={"Accept": "application/pdf"})

    def list_inspections(self, params: ListInspectionsInput) -> ApiResponse[ListInspectionsOutput]:
        return self.post_json_for_json(params, ListInspectionsOutput, "/api/v1/inspections")

    def get_inspection_plan(self, params: InspectionPlanIdParam) -> ApiResponse[InspectionPlanView]:
        return self.get_for_json(InspectionPlanView, f"/api/v1/inspection-plan/{params.inspection_plan_id}")

    def update_inspection_plan(
        self, inspection_plan_id: int, params: UpdateInspectionPlanInput
    ) -> ApiResponse[UpdateInspectionPlanOutput]:
        return self.post_json_for_json(
            params, UpdateInspectionPlanOutput, f"/api/v1/inspection-plan/{inspection_plan_id}/overwrite"
        )

    def delete_inspection_plans(self, inspection_plan_ids: Sequence[int]) -> ApiResponse[DeleteInspectionPlansOutput]:
        return self.post_json_for_json(
            DeleteInspectionPlansInput(inspection_plan_ids=inspection_plan_ids),
            DeleteInspectionPlansOutput,
            "/api/v1/inspection-plans/delete",
        )

    # --------------------------------------------------------------------------------------
    #                  Web Application Open Platform Interface Protocol (WOPI)
    # --------------------------------------------------------------------------------------

    def wopi_get_token(self, file_resource_revision_id: int) -> ApiResponse[WopiAccessTokenView]:
        return self.get_for_json(
            WopiAccessTokenView, f"/api/v1/wopi/internal/files/{file_resource_revision_id}/access-token"
        )

    def wopi_check_file_info(
        self, file_resource_revision_id: int, wopi_access_token: str
    ) -> ApiResponse[WopiCheckFileInfoResponse]:
        return self.get_for_json(
            WopiCheckFileInfoResponse,
            f"/api/v1/wopi/files/{file_resource_revision_id}",
            {"access_token": wopi_access_token},
        )

    def wopi_get_file_contents(self, file_resource_revision_id: int, wopi_access_token: str) -> RawApiResponse:
        return self.get(f"/api/v1/wopi/files/{file_resource_revision_id}/contents", {"access_token": wopi_access_token})

    def wopi_put_file_contents(
        self,
        file_resource_revision_id: int,
        wopi_access_token: str,
        new_file_contents: bytes,
        is_modified: bool = True,
    ) -> ApiResponse[WopiPutFileResponse]:
        return self.post_for_json(
            io.BytesIO(new_file_contents),
            "application/octet-stream",
            WopiPutFileResponse,
            f"/api/v1/wopi/files/{file_resource_revision_id}/contents",
            url_params={"access_token": wopi_access_token},
            headers={"x-cool-wopi-ismodifiedbyuser": str(is_modified)},
        )

    # --------------------------------------------------------------------------------------
    #                                      Tenants
    # --------------------------------------------------------------------------------------

    def get_tenant_details(self) -> ApiResponse[TenantExtendedView]:
        return self.get_for_json(TenantExtendedView, "/api/v1/tenant-details")

    def list_tenants(
        self,
        params: ListTenantsInput,
    ) -> ApiResponse[ListTenantsOutput]:
        return self.post_json_for_json(params, ListTenantsOutput, "/api/v1/tenants")

    # --------------------------------------------------------------------------------------
    #                                     Tolerances
    # --------------------------------------------------------------------------------------

    def list_tolerance_standards(self) -> ApiResponse[TolerancesView]:
        return self.get_for_json(TolerancesView, "/api/v1/tolerances")

    def get_tolerance_range(
        self, tolerance_name: str, value: float, table_name: str, row_identifier: Tuple[str, ...]
    ) -> ApiResponse[ToleranceLookupOutput]:
        params = ToleranceLookupInput(
            tolerance_name=tolerance_name,
            value=value,
            table_name=table_name,
            row_identifier=row_identifier,
        )
        return self.post_json_for_json(params, ToleranceLookupOutput, "/api/v1/tolerance")

    # --------------------------------------------------------------------------------------
    #                                     Drawings
    # --------------------------------------------------------------------------------------

    def get_drawing_revision_page_image(self, frr_id: int, page_index: int) -> RawApiResponse:
        return self.get(f"/api/v1/file-resource/drawing/revision/{frr_id}/page/{page_index}/image")

    def get_drawing_revision_page_thumbnail(self, frr_id: int, page_index: int) -> RawApiResponse:
        return self.get(f"/api/v1/file-resource/drawing/revision/{frr_id}/page/{page_index}/thumbnail")
