{"name": "DIN_ISO_3302-1:1999-10", "tables": [{"name": "Tabelle 1", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["M1 F"]], "tolerance": {"upper": 0.08, "lower": -0.08}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["M1 F"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["M1 F"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["M1 F"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["M1 F"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["M1 F"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["M1 F"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["M1 F"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["M1 F"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["M1 C"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["M1 C"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["M1 C"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["M1 C"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["M1 C"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["M1 C"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["M1 C"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["M1 C"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["M1 C"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["M2 F"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["M2 F"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["M2 F"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["M2 F"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["M2 F"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["M2 F"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["M2 F"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["M2 F"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["M2 F"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["M2 C"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["M2 C"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["M2 C"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["M2 C"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["M2 C"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["M2 C"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["M2 C"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["M2 C"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["M2 C"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["M3 F"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["M3 F"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["M3 F"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["M3 F"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["M3 F"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["M3 F"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["M3 F"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["M3 F"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["M3 F"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["M3 C"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["M3 C"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["M3 C"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["M3 C"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["M3 C"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["M3 C"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["M3 C"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["M3 C"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["M3 C"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["M4 F C"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["M4 F C"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["M4 F C"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["M4 F C"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["M4 F C"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["M4 F C"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["M4 F C"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["M4 F C"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["M4 F C"]], "tolerance": {"upper": 2.5, "lower": -2.5}}]}, {"name": "Tabelle 2", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 2.5, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 2.5, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E1"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 2.5, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 2.5, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E2"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 2.5, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 2.5, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E3"]], "tolerance": {"upper": 3.2, "lower": -3.2}}]}, {"name": "Tabelle 3", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["EN1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["EN1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EN1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EN1"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EN1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["EN1"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["EN1"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["EN1"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["EN1"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["EN2"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["EN2"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EN2"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EN2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EN2"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["EN2"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["EN2"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["EN2"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["EN2"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["EN3"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["EN3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EN3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EN3"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EN3"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["EN3"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["EN3"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["EN3"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["EN3"]], "tolerance": {"upper": 2, "lower": -2}}]}, {"name": "Tabelle 4", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EG1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EG1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EG1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["EG1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["EG1"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["EG1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["EG1"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EG2"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EG2"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EG2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["EG2"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["EG2"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["EG2"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["EG2"]], "tolerance": {"upper": 1, "lower": -1}}]}, {"name": "Tabelle 5", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["EW1"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["EW1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EW1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EW1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EW1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["EW2"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["EW2"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EW2"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EW2"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EW2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}]}, {"name": "Tabelle 6", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["L1"]], "tolerance": {"upper": 6.3, "lower": -6.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 6.3, "lower": -6.3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["L2"]], "tolerance": {"upper": 12.5, "lower": -12.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 6.3, "lower": -6.3}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 12.5, "lower": -12.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 16, "lower": -16}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["L3"]], "tolerance": {"upper": 20, "lower": -20}}]}, {"name": "Tabelle 7", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["EC1"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1.6, "inclusive": true}, "rowIdentifiers": [["EC1"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 1.6, "inclusive": false}, "upperValueLimit": {"value": 2.5, "inclusive": true}, "rowIdentifiers": [["EC1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 2.5, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["EC1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["EC1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EC1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EC1"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EC1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["EC2"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1.6, "inclusive": true}, "rowIdentifiers": [["EC2"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 1.6, "inclusive": false}, "upperValueLimit": {"value": 2.5, "inclusive": true}, "rowIdentifiers": [["EC2"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 2.5, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["EC2"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["EC2"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EC2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EC2"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EC2"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["EC3"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1.6, "inclusive": true}, "rowIdentifiers": [["EC3"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 1.6, "inclusive": false}, "upperValueLimit": {"value": 2.5, "inclusive": true}, "rowIdentifiers": [["EC3"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 2.5, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["EC3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["EC3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["EC3"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["EC3"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["EC3"]], "tolerance": {"upper": 1, "lower": -1}}]}, {"name": "Tabelle 8", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["ST1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1.6, "inclusive": true}, "rowIdentifiers": [["ST1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 1.6, "inclusive": false}, "upperValueLimit": {"value": 2.5, "inclusive": true}, "rowIdentifiers": [["ST1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 2.5, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["ST1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["ST1"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["ST1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["ST1"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["ST2"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1.6, "inclusive": true}, "rowIdentifiers": [["ST2"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 1.6, "inclusive": false}, "upperValueLimit": {"value": 2.5, "inclusive": true}, "rowIdentifiers": [["ST2"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 2.5, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["ST2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["ST2"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["ST2"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["ST2"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["ST3"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1.6, "inclusive": true}, "rowIdentifiers": [["ST3"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 1.6, "inclusive": false}, "upperValueLimit": {"value": 2.5, "inclusive": true}, "rowIdentifiers": [["ST3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 2.5, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["ST3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 6.3, "inclusive": true}, "rowIdentifiers": [["ST3"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 6.3, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["ST3"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["ST3"]], "tolerance": {"upper": 1, "lower": -1}}]}, {"name": "Tabelle 9", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 629.999, "inclusive": true}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["SW1"]], "tolerance": {"upper": 20, "lower": -20}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["SW1"]], "tolerance": {"upper": 30, "lower": -30}}, {"lowerValueLimit": {"value": 629.999, "inclusive": true}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["SW2"]], "tolerance": {"upper": 25, "lower": -25}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["SW2"]], "tolerance": {"upper": 40, "lower": -40}}, {"lowerValueLimit": {"value": 629.999, "inclusive": true}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["SW3"]], "tolerance": {"upper": 30, "lower": -30}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["SW3"]], "tolerance": {"upper": 50, "lower": -50}}]}]}