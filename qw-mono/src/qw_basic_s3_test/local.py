import io
import os
import tempfile
from pathlib import Path, PurePosixPath

from qw_basic_s3.impl.local import LocalS3Storage
from qw_basic_s3.schema import S3StorageSchema


class TestLocalS3Storage(object):
    def test_local_storage_serialization_and_deserialization(self) -> None:
        data1 = b"123"
        data2 = b"abc"
        data3 = b"._!"

        tmp_path = Path(tempfile.mktemp(suffix=".json"))

        empty_schema = S3StorageSchema.create_simple()
        storage = LocalS3Storage(path=tmp_path, schema=empty_schema)

        storage.create_bucket("bucket")
        storage.put_object("bucket", PurePosixPath("abc.txt"), io.BytesIO(data1), len(data1))
        storage.remove_object("bucket", PurePosixPath("abc.txt"))
        storage.put_object("bucket", PurePosixPath("test/another/file.txt"), io.BytesIO(data2), len(data2))

        storage.create_bucket("bucket2")
        storage.put_object("bucket2", PurePosixPath("hello/1,2,3/file.txt"), io.BytesIO(data3), len(data3))

        other = LocalS3Storage(path=tmp_path, schema=empty_schema)

        assert set(storage.buckets.keys()) == set(other.buckets.keys())
        for bucket in storage.buckets.keys():
            for ref, obj in storage.buckets[bucket].traverse_all_objects():
                if not ref.is_dir and obj is not None:
                    with other.get_object(bucket, ref.path) as other_obj:
                        assert obj.id == other_obj.object_id
                        assert obj.content_type == other_obj.content_type
                        assert obj.data == other_obj.read()

        for p in tmp_path.parent.glob(f"{tmp_path.stem}*"):
            os.remove(p)
