import { inject, Injectable } from "@angular/core"
import { AgentActionDispatcherService } from "@qw-shared-services/agent-action-dispatcher.service"
import { BehaviorSubject, catchError, finalize, Observable, of } from "rxjs"

import { QwAgentPromptRequest, QwAgentPromptResponse } from "@qw-api/models"
import { QwAgentService } from "@qw-api/services"

export enum MessageRole {
  User = "user",
  Assistant = "assistant",
  System = "system",
}

export interface AgentMessage {
  id: string
  role: MessageRole
  content: string
  timestamp: Date
  isLoading?: boolean
}

@Injectable({
  providedIn: "root",
})
export class AgentService {
  private agentServiceAPIWrapper = inject(QwAgentService)
  private actionDispatcher = inject(AgentActionDispatcherService)

  // Default welcome message
  private readonly WELCOME_MESSAGE: AgentMessage = {
    id: "welcome",
    role: MessageRole.System,
    content: "Hi there! I'm your AI assistant.",
    timestamp: new Date(),
  }

  // State management
  private isVisibleSubject = new BehaviorSubject<boolean>(false)
  private messagesSubject = new BehaviorSubject<AgentMessage[]>([
    this.WELCOME_MESSAGE,
  ])
  private isLoadingSubject = new BehaviorSubject<boolean>(false)
  private currentContextSubject = new BehaviorSubject<any>(null)
  private sessionId: string | null = null

  // Public observables
  isVisible$: Observable<boolean> = this.isVisibleSubject.asObservable()
  messages$: Observable<AgentMessage[]> = this.messagesSubject.asObservable()
  isLoading$: Observable<boolean> = this.isLoadingSubject.asObservable()

  // Helper method to check current visibility state
  isVisible(): boolean {
    return this.isVisibleSubject.value
  }

  // Helper method to create a message with unique ID
  private createMessage(
    role: MessageRole,
    content: string,
    isLoading = false,
  ): AgentMessage {
    return {
      id: `msg-${Date.now()}-${Math.floor(Math.random() * 1000)}`, // More unique ID
      role,
      content,
      timestamp: new Date(),
      isLoading,
    }
  }

  // Reset to initial state
  private resetState(): void {
    this.sessionId = null
    this.messagesSubject.next([this.WELCOME_MESSAGE])
    this.currentContextSubject.next(null)
  }

  show(): void {
    this.isVisibleSubject.next(true)
  }

  hide(): void {
    this.isVisibleSubject.next(false)
    this.resetState()
  }

  toggle(): void {
    const newVisibility = !this.isVisibleSubject.value
    this.isVisibleSubject.next(newVisibility)

    // If we're closing the window, reset the state
    if (!newVisibility) {
      this.hide()
    }
  }

  setContext(context: any): void {
    // Called by component
    this.currentContextSubject.next(context)
    logger.log(
      "Current context after update:",
      this.currentContextSubject.value,
    )
  }

  sendMessage(content: string): void {
    if (!content.trim()) return

    // Create user and assistant messages
    const userMessage = this.createMessage(MessageRole.User, content)
    const assistantMessage = this.createMessage(MessageRole.Assistant, "", true)

    // Update messages
    const currentMessages = this.messagesSubject.value
    this.messagesSubject.next([
      ...currentMessages,
      userMessage,
      assistantMessage,
    ])

    // Set loading state
    this.isLoadingSubject.next(true)

    // Prepare context
    const context = {
      ...this.currentContextSubject.value,
    }

    // Log the context being sent
    logger.log("Sending context to agent:", context)

    // Check if context is empty and log a warning
    if (!context || Object.keys(context).length === 0) {
      logger.warn(
        "WARNING: Empty context being sent to agent. This may indicate a problem with context setting.",
      )
    }

    // Create the request object
    const request: QwAgentPromptRequest = {
      prompt: content,
      context: context,
      session_id: this.sessionId,
    }

    // Call the API service
    this.agentServiceAPIWrapper
      .processAgentPrompt({ body: request })
      .pipe(
        catchError((error) => {
          console.error("Error calling agent API:", error)
          return of({
            message: "Oops... Something went wrong. Please try again.",
            session_id: this.sessionId,
            actions: null,
          } as QwAgentPromptResponse)
        }),
        finalize(() => {
          this.isLoadingSubject.next(false)
        }),
      )
      .subscribe((response: QwAgentPromptResponse) => {
        // Store the session ID for future requests
        if (response.session_id) {
          this.sessionId = response.session_id
        }

        // Update assistant message with response
        const updatedMessages = this.messagesSubject.value.map((m) =>
          m.id === assistantMessage.id
            ? {
                ...m,
                content: response.message,
                isLoading: false,
              }
            : m,
        )

        // Update messages
        this.messagesSubject.next(updatedMessages)

        // Process any actions
        if (response.actions && response.actions.length > 0) {
          logger.log("Processing actions:", response.actions)

          // Use the AgentActionDispatcherService to handle actions
          this.actionDispatcher.dispatchActions(response.actions)
        }
      })
  }
}
