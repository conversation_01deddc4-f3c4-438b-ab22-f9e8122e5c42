import time
from collections import defaultdict
from typing import Dict, List, Set, Tuple

from qw_drawing_toolkit.pdf.models.analysis_models import SymbolBox, TextBox
from qw_drawing_toolkit.pdf.models.base_models import T_BBOX, T_LINE_SEGMENT
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysisContext
from qw_drawing_toolkit.pdf.services.geometry_service import GeometryService
from qw_drawing_toolkit.pdf.utils.common_utils import find_bounding_box, find_text_boxes_in_box, is_box_in_box
from qw_drawing_toolkit.pdf.utils.text_utils import (
    is_only_letters,
    is_only_numbers,
    remove_custom_filter_text_boxes,
    remove_single_letter_or_single_number_text_boxes,
    remove_text_text_boxes,
)


class TextService:
    def __init__(self, debug: bool, geometry_service: GeometryService | None = None):
        """Initialize the TextService"""
        self.debug = debug
        self.geometry_service = geometry_service

    def extract_text_boxes(
        self, ctx: DrawingAnalysisContext
    ) -> tuple[List[TextBox], Dict[Tuple[float, float], Set[TextBox]], List[TextBox]]:
        """
        Extract text boxes from a PDF page.

        Args:
            ctx: Drawing analysis context

        Returns:
            tuple: (text_boxes, direction_to_text_boxes_map, text_boxes_unfiltered)
        """
        start_time = time.time()
        text_boxes: List[TextBox] = []
        direction_to_text_boxes_map: Dict[Tuple[float, float], Set[TextBox]] = defaultdict(set)
        text_boxes_unfiltered: List[TextBox] = []

        for text in ctx.page.get_text("dict")["blocks"]:
            if "lines" in text:
                for text_line in text["lines"]:
                    for span in text_line["spans"]:
                        bbox = span["bbox"]
                        bbox_zoomed = (
                            ctx.zoom * bbox[0],
                            ctx.zoom * bbox[1],
                            ctx.zoom * bbox[2],
                            ctx.zoom * bbox[3],
                        )
                        text_box = TextBox(
                            span["text"], span["font"], span["size"] * ctx.zoom, bbox_zoomed, text_line["dir"]
                        )
                        text_boxes.append(text_box)
                        direction_to_text_boxes_map[text_line["dir"]].add(text_box)

        text_boxes_unfiltered.extend(text_boxes)

        if self.debug:
            print(f"TextService.extract_text_boxes: {time.time() - start_time} Seconds")

        return text_boxes, direction_to_text_boxes_map, text_boxes_unfiltered

    def remove_section_coordinate_text_boxes(self, ctx: DrawingAnalysisContext) -> List[TextBox]:
        """
        Filter out section coordinate markers while keeping relevant text boxes.

        This method identifies and removes text boxes that likely represent section
        coordinate markers (typically single letters or numbers at the drawing edges)
        based on their content, size, and proximity to drawing boundaries.

        A text box is considered a section coordinate marker if:
        1. It's inside the drawing box
        2. Contains only 1-2 letters or numbers
        3. Is positioned close to the drawing box edge (distance < 1/3 of text height)

        Args:
            ctx: Drawing analysis context containing text boxes and drawing boundaries

        Returns:
            List[TextBox]: Filtered text boxes with section coordinates removed
        """
        start_time = time.time()
        result = []
        drawing_box = ctx.drawing_box

        if not drawing_box:
            print("TextService.remove_section_coordinate_text_boxes: Drawing box is not set")
            return []

        for text_box in ctx.text_boxes:
            bbox = text_box.bbox
            text = text_box.text.strip()

            if (
                is_box_in_box(bbox, drawing_box, ctx.point_proximity_tolerance)
                and len(text) <= 2
                and (is_only_letters(text) or is_only_numbers(text))
            ):
                text_box_height = abs(bbox[3] - bbox[1])

                distances = [
                    abs(bbox[0] - drawing_box[0]),
                    abs(bbox[2] - drawing_box[2]),
                    abs(bbox[1] - drawing_box[1]),
                    abs(bbox[3] - drawing_box[3]),
                ]

                if min(distances) * 3 >= text_box_height:
                    result.append(text_box)

            elif is_box_in_box(bbox, drawing_box, ctx.point_proximity_tolerance):
                result.append(text_box)

        if self.debug:
            print(f"TextService.remove_section_coordinate_text_boxes: {time.time() - start_time} seconds")

        return result

    def find_title_block_text_boxes(self, ctx: DrawingAnalysisContext) -> tuple[List[TextBox], List[T_BBOX]]:
        """
        Find title block text boxes.
        """
        start_time = time.time()
        title_block_sections: List[T_BBOX] = []

        text_boxes_title_block = [
            text_box
            for text_box in ctx.text_boxes
            if any(
                is_box_in_box(text_box.bbox, section, ctx.point_proximity_tolerance)
                for section in ctx.title_block_sections
            )
        ]

        if len(text_boxes_title_block) == 0:
            if self.geometry_service:
                title_block_sections = self.geometry_service.find_title_block_sections(ctx)
                text_boxes_title_block = [
                    text_box
                    for text_box in ctx.text_boxes
                    if any(
                        is_box_in_box(text_box.bbox, section, ctx.point_proximity_tolerance)
                        for section in title_block_sections
                    )
                ]
            else:
                print("TextService.find_title_block_text_boxes: GeometryService is not set")
                title_block_sections = []

        if self.debug:
            print("TextService.find_title_block_text_boxes: " + str(time.time() - start_time) + " Seconds")

        return text_boxes_title_block, title_block_sections

    def find_measurement_text_boxes(self, ctx: DrawingAnalysisContext) -> List[TextBox]:
        """
        Find measurement text boxes without symbols.

        Args:
            ctx: Drawing analysis context

        Returns:
            List[TextBox]: Filtered measurement text boxes without symbols
        """
        start_time = time.time()

        text_boxes_measurements_no_symbol = [
            text_box for text_box in ctx.text_boxes if text_box not in ctx.text_boxes_title_block
        ]

        text_boxes_measurements_no_symbol = remove_text_text_boxes(text_boxes_measurements_no_symbol)
        text_boxes_measurements_no_symbol = remove_single_letter_or_single_number_text_boxes(
            text_boxes_measurements_no_symbol
        )
        text_boxes_measurements_no_symbol = remove_custom_filter_text_boxes(text_boxes_measurements_no_symbol)

        if self.debug:
            print(f"TextService.find_measurement_text_boxes: {time.time() - start_time} seconds")

        return text_boxes_measurements_no_symbol

    def handle_table_structures(
        self, ctx: DrawingAnalysisContext
    ) -> tuple[List[TextBox | SymbolBox], List[TextBox], List[T_LINE_SEGMENT]]:
        """
        Process and filter out table structures from the drawing analysis.

        This method identifies tables in the drawing and removes any text boxes,
        symbol boxes, and lines that are part of these tables. This prevents table
        content from being incorrectly interpreted as measurement annotations or
        drawing elements.

        Args:
            ctx: Drawing analysis context containing text boxes, symbol boxes, and lines

        Returns:
            tuple: (
                text_boxes_measurement_symbols: Symbol boxes excluding those in tables,
                text_boxes_measurements_no_symbol: Text boxes excluding those in tables,
                lines_drawing: Drawing lines excluding those in tables
            )
        """
        start_time = time.time()
        text_boxes_measurement_symbols = ctx.text_boxes_measurement_symbols.copy()
        text_boxes_measurements_no_symbol = ctx.text_boxes_measurements_no_symbol.copy()
        lines_drawing = ctx.lines_drawing.copy()

        if not ctx.line_collection:
            print("TextService.handle_table_structures: Line collection is not set")
            return text_boxes_measurement_symbols, text_boxes_measurements_no_symbol, lines_drawing

        # for now we are ignoring the tables and the content of the tables
        for table in ctx.line_collection.find_table_structures():
            bbox = find_bounding_box(table)
            table_text_boxes = find_text_boxes_in_box(bbox, ctx.text_boxes, ctx.point_proximity_tolerance)
            table_symbol_boxes = find_text_boxes_in_box(
                bbox, ctx.text_boxes_measurement_symbols, ctx.point_proximity_tolerance
            )
            text_boxes_measurement_symbols = [
                box
                for box in text_boxes_measurement_symbols
                if box not in table_text_boxes and box not in table_symbol_boxes
            ]
            text_boxes_measurements_no_symbol = [
                box
                for box in text_boxes_measurements_no_symbol
                if box not in table_text_boxes and box not in table_symbol_boxes
            ]
            lines_drawing = [line for line in lines_drawing if line not in table]

        if self.debug:
            print("TextService.handle_table_structures: " + str(time.time() - start_time) + " Seconds")

        return text_boxes_measurement_symbols, text_boxes_measurements_no_symbol, lines_drawing
