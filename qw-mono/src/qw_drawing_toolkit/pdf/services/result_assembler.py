"""This service is responsible for assembling the results of the drawing analysis."""

import re
import time
from dataclasses import dataclass, field
from typing import List

from qw_drawing_toolkit.pdf.models.analysis_models import DimensionType
from qw_drawing_toolkit.pdf.models.base_models import DrawingUnit
from qw_drawing_toolkit.pdf.models.drawing_models import (
    DrawingAnalysisContext,
    DrawingAngularAnnotation,
    DrawingAngularValue,
    DrawingCountersinkAnnotation,
    DrawingDiameterAnnotation,
    DrawingExplicitIso286FitTolerance,
    DrawingExplicitTolerance,
    DrawingLengthAnnotation,
    DrawingLengthValue,
    DrawingRadiusAnnotation,
    DrawingRoughnessAnnotation,
    DrawingRoughnessCategory,
    DrawingThread,
    DrawingThreadAnnotation,
)
from qw_drawing_toolkit.pdf.models.gdt_models import (
    DrawingGdtDatumAnnotation,
    DrawingGdtDatumName,
    DrawingGdtFrameAnnotation,
    DrawingGdtSymbol,
)
from qw_drawing_toolkit.pdf.utils.assembler_utils import (
    prepare_drawing_depth,
    prepare_drawing_explicit_iso286_tolerance,
    prepare_drawing_explicit_tolerance,
    prepare_normalized_drawing_bounding_box,
)


@dataclass
class AssembledDrawingAnalysis:
    """Container for all assembled drawing analysis results."""

    diameters: List[DrawingDiameterAnnotation] = field(default_factory=list)
    countersinks: List[DrawingCountersinkAnnotation] = field(default_factory=list)
    radii: List[DrawingRadiusAnnotation] = field(default_factory=list)
    angles: List[DrawingAngularAnnotation] = field(default_factory=list)
    threads: List[DrawingThreadAnnotation] = field(default_factory=list)
    lengths: List[DrawingLengthAnnotation] = field(default_factory=list)
    roughness: List[DrawingRoughnessAnnotation] = field(default_factory=list)
    gdt_frames: List[DrawingGdtFrameAnnotation] = field(default_factory=list)
    gdt_datums: List[DrawingGdtDatumAnnotation] = field(default_factory=list)


class ResultAssembler:
    def __init__(self, debug: bool):
        self.debug = debug

    def assemble_drawing_analysis_result(self, ctx: DrawingAnalysisContext) -> AssembledDrawingAnalysis:
        """Assemble the drawing analysis result into a container of annotated drawing elements."""
        start_time = time.time()

        # Create the result container
        result = AssembledDrawingAnalysis()

        # Process dimension boxes
        for box in ctx.dimension_boxes:
            if box.dimension_type == DimensionType.DIAMETER and box.diameter is not None:
                explicit_tolerance: DrawingExplicitTolerance | DrawingExplicitIso286FitTolerance | None = (
                    prepare_drawing_explicit_iso286_tolerance(box)
                )
                if explicit_tolerance is None:
                    explicit_tolerance = prepare_drawing_explicit_tolerance(box)

                diameter_annotation = DrawingDiameterAnnotation(
                    bounding_box=prepare_normalized_drawing_bounding_box(
                        ctx.page_index, box.bounding_box, ctx.max_width, ctx.max_height
                    ),
                    diameter=DrawingLengthValue(
                        value=box.diameter,
                        explicit_tolerance=explicit_tolerance,
                    ),
                    depth=prepare_drawing_depth(box),
                )
                result.diameters.append(diameter_annotation)

            if box.dimension_type == DimensionType.COUNTERSINK and box.diameter is not None and box.angle is not None:
                cs_annotation = DrawingCountersinkAnnotation(
                    bounding_box=prepare_normalized_drawing_bounding_box(
                        ctx.page_index, box.bounding_box, ctx.max_width, ctx.max_height
                    ),
                    diameter=DrawingLengthValue(value=box.diameter),
                    angle=DrawingAngularValue(value=box.angle),
                )
                result.countersinks.append(cs_annotation)

            if box.dimension_type == DimensionType.RADIUS and box.radius is not None:
                radius_annotation = DrawingRadiusAnnotation(
                    bounding_box=prepare_normalized_drawing_bounding_box(
                        ctx.page_index, box.bounding_box, ctx.max_width, ctx.max_height
                    ),
                    radius=DrawingLengthValue(
                        value=box.radius,
                        explicit_tolerance=prepare_drawing_explicit_tolerance(box),
                    ),
                )
                result.radii.append(radius_annotation)

            if box.dimension_type == DimensionType.ANGLE and box.angle is not None:
                angle_annotation = DrawingAngularAnnotation(
                    bounding_box=prepare_normalized_drawing_bounding_box(
                        ctx.page_index, box.bounding_box, ctx.max_width, ctx.max_height
                    ),
                    angle=DrawingAngularValue(
                        value=box.angle,
                        explicit_tolerance=prepare_drawing_explicit_tolerance(box),
                    ),
                )
                result.angles.append(angle_annotation)

            if (
                box.dimension_type == DimensionType.THREAD
                and box.thread is not None
                # and box.tolerance_grade is not None
            ):
                # Validate tolerance_identifier against the pattern before using it
                thread_tolerance_id = None
                if box.tolerance_grade and re.match(r"^[0-9][a-zA-Z]([0-9][a-zA-Z])?$", box.tolerance_grade):
                    thread_tolerance_id = box.tolerance_grade

                thread_annotation = DrawingThreadAnnotation(
                    bounding_box=prepare_normalized_drawing_bounding_box(
                        ctx.page_index, box.bounding_box, ctx.max_width, ctx.max_height
                    ),
                    thread=DrawingThread(
                        identifier=re.sub(r"\s+", "", box.thread),
                        tolerance_identifier=thread_tolerance_id,  # Use validated value
                        depth=prepare_drawing_depth(box),
                        pitch=None if not box.pitch else DrawingLengthValue(value=box.pitch),
                    ),
                )
                result.threads.append(thread_annotation)

            if box.dimension_type == DimensionType.LENGTH and box.length is not None:
                length_annotation = DrawingLengthAnnotation(
                    bounding_box=prepare_normalized_drawing_bounding_box(
                        ctx.page_index, box.bounding_box, ctx.max_width, ctx.max_height
                    ),
                    length=DrawingLengthValue(
                        value=box.length,
                        explicit_tolerance=prepare_drawing_explicit_tolerance(box),
                    ),
                )
                result.lengths.append(length_annotation)

            if (
                box.dimension_type == DimensionType.ROUGHNESS
                and box.value is not None
                and box.roughness_category is not None
            ):
                roughness_annotation = DrawingRoughnessAnnotation(
                    bounding_box=prepare_normalized_drawing_bounding_box(
                        ctx.page_index, box.bounding_box, ctx.max_width, ctx.max_height
                    ),
                    roughness=DrawingLengthValue(
                        value=box.value,
                        unit=DrawingUnit.MICROMETER,
                        explicit_tolerance=prepare_drawing_explicit_tolerance(box),
                    ),
                    category=DrawingRoughnessCategory(identifier=box.roughness_category),
                )
                result.roughness.append(roughness_annotation)

        # Process GDT frames
        for frame_box in ctx.gd_t_boxes:
            tolerance_value = 0.0
            tolerance_unit = DrawingUnit.MILLIMETER  # TODO: depends on symbol
            try:
                tolerance_value = float(
                    re.sub("[A-Z]", "", frame_box.measurement_text_box.text).strip().replace(",", ".")
                )
            except ValueError:
                pass

            datum_references = []

            for datum_reference_box in frame_box.datum_reference_boxes:
                datum_references.append(datum_reference_box.datum)

            gdt_frame_annotation = DrawingGdtFrameAnnotation(
                bounding_box=prepare_normalized_drawing_bounding_box(
                    ctx.page_index, frame_box.bbox, ctx.max_width, ctx.max_height
                ),
                symbol=DrawingGdtSymbol.from_string(frame_box.symbol_mapper_box.symbol.name),
                tolerance_value=tolerance_value,
                tolerance_unit=tolerance_unit,
                datum_references=datum_references,
            )

            result.gdt_frames.append(gdt_frame_annotation)

        # Process datums
        for datum_box in ctx.datum_boxes:
            if re.match(r"[A-Z]", datum_box.datum):
                gdt_box_annotation = DrawingGdtDatumAnnotation(
                    bounding_box=prepare_normalized_drawing_bounding_box(
                        ctx.page_index, datum_box.bbox, ctx.max_width, ctx.max_height
                    ),
                    name=DrawingGdtDatumName(datum_box.datum),
                )

                result.gdt_datums.append(gdt_box_annotation)

        if self.debug:
            print("ResultAssembler.assemble_drawing_analysis_result: " + str(time.time() - start_time) + " Seconds")

        return result
