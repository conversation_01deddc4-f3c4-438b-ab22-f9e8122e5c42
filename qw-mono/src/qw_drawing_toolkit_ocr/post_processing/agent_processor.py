"""
PMI Agent Processor
This module provides an LLM-based agent for processing PMI blocks
from OCR results, using pydanticAI framework with async processing.
"""
import asyncio
import base64
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import cv2
import numpy as np
from numpy.typing import NDArray
from pydantic_ai import Agent, BinaryContent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import UsageLimits

from qw_drawing_toolkit_ocr.post_processing.config import PMIAnalysisSettings
from qw_drawing_toolkit_ocr.post_processing.models import PMIBlockAnalysisResultWithNonNormalizedBoundingPoly
from qw_drawing_toolkit_ocr.post_processing.utils import encode_image_to_base64
from qw_drawing_toolkit_ocr.pre_processing.models import (
    BoundingPoly,
    ClassifiedDocumentText,
    ClassifiedTextBlock,
    PmiClassification,
)
from qw_log_interface import NO_LOGGER, Logger


class PmiAgentProcessor:
    """
    Agent-based processor for PMI blocks using pydanticAI.
    Uses a GPT model to analyze and enhance PMI blocks from OCR results.
    All methods are async-first for optimal performance.
    """

    def __init__(
        self,
        settings: PMIAnalysisSettings,
        save_images_for_debugging: bool,
        save_json_for_debugging: bool,
        output_dir: Optional[Path] = None,
        logger: Logger = NO_LOGGER,
    ):
        """
        Initialize the PMI agent processor.

        Args:
            settings: Configuration settings for the agent
            save_images_for_debugging: Whether to save images for debugging
            save_json_for_debugging: Whether to save JSON for debugging
            output_dir: Optional directory to save results to
            logger: Logger instance
        """
        self.logger = logger
        self.settings = settings
        self.output_dir = output_dir
        self.save_images_for_debugging = save_images_for_debugging
        self.save_json_for_debugging = save_json_for_debugging

        # Configure the agent model based on settings
        self._configure_agent()

    async def process_blocks(
        self,
        classified_doc: ClassifiedDocumentText,
        image: NDArray[np.uint8],
        classification_filter: Optional[List[PmiClassification]] = None,
        max_concurrent: int = 5,
    ) -> Dict[str, List[PMIBlockAnalysisResultWithNonNormalizedBoundingPoly]]:
        """
        Process all blocks in parallel using async methods.

        Args:
            classified_doc: ClassifiedDocumentText containing blocks to process
            image: Background image for cropping PMI blocks
            classification_filter: Optional list of classifications to process (defaults to
                                  PMI_CANDIDATE if None)
            max_concurrent: Maximum number of concurrent API calls to make

        Returns:
            Dictionary mapping classifications to lists of PMI results (not normalized)
        """
        if classification_filter is None:
            classification_filter = [PmiClassification.PMI_CANDIDATE]

        results: Dict[str, List[PMIBlockAnalysisResultWithNonNormalizedBoundingPoly]] = {}
        empty_results: Dict[str, List[PMIBlockAnalysisResultWithNonNormalizedBoundingPoly]] = {}

        # Initialize results dict with empty lists
        for classification in classification_filter:
            results[classification] = []
            empty_results[classification] = []

        if not self.agent_available:
            self.logger.error("Agent not available. Cannot process blocks.")
            return empty_results

        try:
            # Collect all blocks that match the filter
            blocks_to_process: List[ClassifiedTextBlock] = []
            for block in classified_doc.text_blocks:
                if block.classification in classification_filter:
                    blocks_to_process.append(block)

            if not blocks_to_process:
                self.logger.info("No blocks found matching the filter criteria")
                return empty_results

            # Process blocks with true overlapping batches using queue-based approach
            self.logger.info(
                f"Processing {len(blocks_to_process)} blocks with overlapping batches (max_concurrent={max_concurrent})"
            )

            # Create a queue for blocks to process
            block_queue: asyncio.Queue[Optional[ClassifiedTextBlock]] = asyncio.Queue()
            for block in blocks_to_process:
                await block_queue.put(block)

            # Add sentinel values to signal workers to stop
            for _ in range(max_concurrent):
                await block_queue.put(None)

            async def worker(worker_id: int) -> None:
                """Worker coroutine that processes blocks from the queue."""
                worker_completed = 0
                while True:
                    block = await block_queue.get()
                    if block is None:  # Sentinel value - worker should stop
                        break

                    try:
                        # self.logger.info(f"Worker {worker_id} processing block {worker_completed + 1}")
                        result = await self._process_block(block, image=image)

                        # Handle the result
                        for pmi_result in result:
                            results[block.classification].append(pmi_result)

                        worker_completed += 1

                    except Exception as e:
                        self.logger.error(f"Worker {worker_id} error processing block: {str(e)}")
                    finally:
                        block_queue.task_done()

                self.logger.info(f"Worker {worker_id} completed {worker_completed} blocks")

            # Start worker tasks
            workers = [asyncio.create_task(worker(i)) for i in range(max_concurrent)]

            # Wait for all workers to complete
            await asyncio.gather(*workers)

            self.logger.info(f"All workers completed. Processed {len(blocks_to_process)} blocks total")

            # Return the raw results
            return results

        except Exception as e:
            self.logger.error(f"Error processing blocks with agent: {str(e)}")
            return empty_results

    def _configure_agent(self) -> None:
        """Configure the pydanticAI agent with proper settings."""
        try:
            # Prepare model settings
            model_settings = ModelSettings(
                temperature=self.settings.temperature,
                timeout=self.settings.timeout,
            )

            # Setup OpenAI model with provider
            provider = OpenAIProvider(api_key=self.settings.api_key)

            # Check if we need to extract the model name without provider prefix
            model_name = self.settings.model_name
            if model_name.startswith("openai:"):
                model_name = model_name.split(":")[-1]

            model = OpenAIModel(model_name, provider=provider)

            # Create the agent
            self.agent = Agent(
                model=model,
                model_settings=model_settings,
                retries=2,
                output_type=List[PMIBlockAnalysisResultWithNonNormalizedBoundingPoly],
                system_prompt=self._get_system_prompt(),
            )
            self.agent_available = True

        except Exception as e:
            self.logger.error(f"Error initializing PmiAgentProcessor: {str(e)}")
            self.agent_available = False

    def _get_system_prompt(self) -> str:
        # For gpt-4o, and gpt-4.1 this system prompt cause wrong results
        # 3. Skip if the PMI block in the image
        #    - interprets as zero
        #    - appears to be a view callout, (e.g. detail view callout or section callout)
        #    - shows partial PMI (e.g. "the core piece is missing but it shows (2x) to indicate count", \
        #      "lonely tolerance such as +0.06")
        """
        Generate the system prompt for the agent.

        Returns:
            String containing the system prompt
        """
        return """
        Context:
        You are a PMI (Product Manufacturing Information) extraction agent for technical drawings.
        Your task is to analyze the provided cropped image and associated text block, and extract all \
        valid PMI objects present.
        Instructions:
        1. Focus only on fully visible, main PMI(s) in the crop. Ignore partial or background blocks.
        2. Use your own analysis; the provided text may be incomplete or incorrect.
        3. If the block contains multiple child elements that represent separate PMIs:
           - Extract each PMI as a separate PMIBlockAnalysisResult object
           - Set the bounding_poly field using the element's bounding polygon vertices
           - If an extracted PMI that previously mistaken as element and if it consists
           of multiple elements, recalculate the bounding polygon to encompass all elements.
        4. If the block represents a single PMI:
           - Create a PMIBlockAnalysisResult for the PMI
           - Set the bounding_poly field using the block's bounding polygon vertices
        5. For each PMI:
           - Accurately identify and set all PMI-specific fields (importance, value, tolerances, etc.)
           - Always include the corresponding bounding_poly to indicate the PMI's location in the image
        6. If no valid PMI is present, return an empty list.
        """

    def _crop_image_from_polygon(
        self,
        image: NDArray[np.uint8],
        bounding_poly: BoundingPoly,
    ) -> Optional[NDArray[np.uint8]]:
        """
        Extract a cropped image based on polygon boundaries, masking out everything outside the polygon.

        Args:
            image: Original image as numpy array
            bounding_poly: Bounding polygon with coordinates
        Returns:
            Cropped image as numpy array or None if extraction fails
        """
        try:
            if not hasattr(bounding_poly, "vertices") or not bounding_poly.vertices:
                self.logger.error("Invalid bounding polygon: missing vertices")
                return None

            # Get image dimensions
            h, w = image.shape[:2]

            # Extract x,y coordinates from vertices
            coords: List[List[int]] = []
            for vertex in bounding_poly.vertices:
                if "x" in vertex and "y" in vertex:
                    coords.append([int(vertex["x"]), int(vertex["y"])])

            if len(coords) < 3:  # Need at least 3 points for a polygon
                self.logger.error("Invalid bounding polygon: insufficient vertices")
                return None

            # Convert to numpy array for polygon operations
            coords_array = np.array(coords, dtype=np.int32)

            # Calculate bounding box for the polygon
            x_min = max(0, np.min(coords_array[:, 0]))
            y_min = max(0, np.min(coords_array[:, 1]))
            x_max = min(w, np.max(coords_array[:, 0]))
            y_max = min(h, np.max(coords_array[:, 1]))

            # Ensure valid dimensions
            if x_min >= x_max or y_min >= y_max:
                self.logger.error("Invalid bounding box dimensions")
                return None

            # Create a mask using the polygon - fix: use tuple for color parameter
            mask = np.zeros((h, w), dtype=np.uint8)
            cv2.fillPoly(mask, [coords_array], (255,))

            # Apply the mask to the image - specify dtype explicitly to fix type error
            masked_image = np.zeros_like(image, dtype=np.uint8)
            for c in range(0, 3 if len(image.shape) > 2 else 1):
                if len(image.shape) > 2:
                    masked_image[:, :, c] = cv2.bitwise_and(image[:, :, c], mask).astype(np.uint8)
                else:
                    masked_image = cv2.bitwise_and(image, mask).astype(np.uint8)

            # Crop to the bounding rectangle to remove extra space
            cropped_image = masked_image[y_min:y_max, x_min:x_max].copy()

            # Save cropped image if output directory is provided and debugging is enabled
            if self.save_images_for_debugging and self.output_dir is not None:
                try:
                    # Create crops subdirectory if it doesn't exist
                    crops_dir = self.output_dir / "crops"
                    crops_dir.mkdir(exist_ok=True, parents=True)

                    # Generate a unique filename using coordinates
                    timestamp = datetime.now().strftime("%H%M%S")
                    crop_filename = f"crop_{x_min}_{y_min}_{x_max}_{y_max}_{timestamp}.png"
                    crop_path = crops_dir / crop_filename

                    # Save the image
                    cv2.imwrite(str(crop_path), cropped_image)
                except Exception as e:
                    self.logger.error(f"Error saving cropped image: {str(e)}")
            elif self.save_images_for_debugging and self.output_dir is None:
                self.logger.info("Cannot save cropped image: output_dir is None")

            return cropped_image

        except Exception as e:
            self.logger.error(f"Error cropping image from polygon: {str(e)}")
            return None

    async def _process_block(
        self,
        block: ClassifiedTextBlock,
        image: Optional[NDArray[np.uint8]] = None,
    ) -> List[PMIBlockAnalysisResultWithNonNormalizedBoundingPoly]:
        """
        Process a single PMI block with the agent asynchronously.

        Args:
            block: ClassifiedTextBlock to process
            image: Optional background image for cropping
            output_dir: Optional directory to save cropped images and results

        Returns:
            List of PMIBlockAnalysisResult (empty if none found)
        """
        if not self.agent_available:
            self.logger.error("Agent not available. Cannot process block.")
            return []
        if image is None or block.expanded_bounding_poly is None:
            self.logger.warning("No image or expanded bounding poly provided; skipping block.")
            return []
        try:
            block_for_prompt = block.model_dump()
            # Remove expanded_bounding_poly from prompt to reduce tokens
            block_for_prompt = block.model_dump(exclude={"expanded_bounding_poly"})
            if len(block.elements) == 1 and block.elements[0].text == block.text:
                block_for_prompt["elements"] = []

            text_prompt = f"""
            Please analyze this PMI block extracted from a technical drawing.

            {json.dumps(block_for_prompt, indent=2)}
            """
            cropped_image = self._crop_image_from_polygon(image, block.expanded_bounding_poly)
            if cropped_image is not None:
                image_base64 = encode_image_to_base64(cropped_image, self.logger)
                if image_base64:
                    img_bytes = base64.b64decode(image_base64)
                    # Create usage limits with no request limit to prevent UsageLimitExceeded errors
                    usage_limits = UsageLimits(request_limit=None)
                    result = await self.agent.run(
                        [text_prompt, BinaryContent(data=img_bytes, media_type="image/png")], usage_limits=usage_limits
                    )
                    return result.output or []
            return []
        except Exception as e:
            self.logger.error(f"Error processing block with agent: {str(e)}")
            return []
