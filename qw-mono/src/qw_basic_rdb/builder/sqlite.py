from pathlib import Path
from sqlite3 import Connection as SqliteConnection
from typing import Any, Literal, Type

from pydantic import BaseModel
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.pool import ConnectionPoolEntry

from qw_basic_rdb.sqlalch import SqlAlchemyDatabase
from qw_log_interface import NO_LOGGER, Logger


class SqliteConfig(BaseModel):
    type: Literal["sqlite"] = "sqlite"
    path: Path | None
    enforce_foreign_key_constraints: bool = True

    @classmethod
    def in_memory(cls) -> "SqliteConfig":
        return cls(path=None)

    @property
    def url(self) -> str:
        if self.path is None:
            return "sqlite://"
        else:
            return f"sqlite:///{self.path.absolute()}"

    def build(self, base: Type[DeclarativeBase], echo: bool = False, logger: Logger = NO_LOGGER) -> SqlAlchemyDatabase:
        def on_connect(dbapi_connection: Any, connection_record: ConnectionPoolEntry) -> None:
            if self.enforce_foreign_key_constraints:
                if not isinstance(dbapi_connection, SqliteConnection):
                    raise ValueError(
                        f"Expected connection of type {SqliteConnection.__name__}, " f"but got {type(dbapi_connection)}"
                    )
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON;")
                cursor.close()

        url = self.url
        logger.info(f"Setting up sqlite connection to {url}")
        return SqlAlchemyDatabase.from_url(base, url, echo=echo, on_connect=on_connect)
