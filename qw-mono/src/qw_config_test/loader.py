from pathlib import Path
from typing import List

from pydantic import BaseModel

from qw_config.loader import CONFIG_KEY_DIR, CONFIG_KEY_OVERWRITE, load_config_from_data


class DemoSubConfig(BaseModel):
    a: str
    b: List[str]


class DemoConfig(BaseModel):
    x: str
    y: List[str]
    z: DemoSubConfig


class TestConfigLoader(object):
    def test_path_substitution(self) -> None:
        data = {
            "x": CONFIG_KEY_DIR,
            "y": ["image.png", f"{CONFIG_KEY_DIR}/test.txt"],
            "z": {
                "a": f"{CONFIG_KEY_DIR}/test.txt",
                "b": ["image.png", CONFIG_KEY_DIR],
            },
        }

        cfg = load_config_from_data(DemoConfig, data, Path("/a/b/c/my_config_file.yaml"))
        assert cfg.x == "/a/b/c"
        assert cfg.y == ["image.png", "/a/b/c/test.txt"]
        assert cfg.z.a == "/a/b/c/test.txt"
        assert cfg.z.b == ["image.png", "/a/b/c"]

    def test_find_overwrite_paths(self) -> None:
        data = {
            "x": CONFIG_KEY_OVERWRITE,
            "y": ["2", CONFIG_KEY_OVERWRITE],
            "z": {
                "a": CONFIG_KEY_OVERWRITE,
                "b": [CONFIG_KEY_OVERWRITE, "6"],
            },
        }
        data_overwrite = {"x": "1", "y": [None, "3"], "z": {"a": "4", "b": ["5"]}}

        cfg = load_config_from_data(DemoConfig, data, Path("/a/b/c"), data_overwrite)
        assert cfg.x == "1"
        assert cfg.y == ["2", "3"]
        assert cfg.z.a == "4"
        assert cfg.z.b == ["5", "6"]
