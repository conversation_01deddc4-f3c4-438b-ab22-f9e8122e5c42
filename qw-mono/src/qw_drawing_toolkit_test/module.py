import io

from qw_drawing_toolkit.engine import QwDrawingToolkit
from qw_drawing_toolkit.pdf.models.config_models import DrawingAnalyzerOpts
from qw_drawing_toolkit_test.conftest import TEST_RESOURCES


class TestDrawingToolkit(object):
    def test_simple_lengths_with_explicit_tolerances(self) -> None:
        path = TEST_RESOURCES / "demo_drawing.pdf"
        with path.open("rb") as f:
            bio = io.BytesIO(f.read())

        toolkit = QwDrawingToolkit()
        doc, analysis = toolkit.analyse_pdf(bio, DrawingAnalyzerOpts())

        lengths_with_explicit_tol = [
            item for item in analysis.result.lengths if item.length.explicit_tolerance is not None
        ]
        assert len(lengths_with_explicit_tol) > 0
        for item in lengths_with_explicit_tol:
            assert item.length.explicit_tolerance is not None
            assert item.length.explicit_tolerance.upper > 0
            assert item.length.explicit_tolerance.lower < 0
