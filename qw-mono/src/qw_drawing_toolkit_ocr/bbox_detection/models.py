from typing import Any, Dict, List, Optional, cast

import numpy as np
from pydantic import BaseModel, Field

# Core type definitions
BoxCoords = List[List[float]]  # [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
Detection = List[Any]  # [box_coords, text, confidence]


class DrawingPoint(BaseModel):
    """Normalized (0-1) drawing coordinate in image style, i.e. origin is top left, x to the right, y to the bottom"""

    x: float = Field(ge=0, le=1)
    y: float = Field(ge=0, le=1)


class BoundingBox(BaseModel):
    """Bounding box with p1 (top-left) and p2 (bottom-right) coordinates"""

    p1: DrawingPoint
    p2: DrawingPoint

    @classmethod
    def from_box_coords(cls, box_coords: BoxCoords) -> "BoundingBox":
        """Create a BoundingBox from a list of coordinates"""
        if not box_coords or len(box_coords) < 4:
            # Default to zero-sized box if invalid input
            return cls(p1=DrawingPoint(x=0.0, y=0.0), p2=DrawingPoint(x=0.0, y=0.0))

        # Extract min and max coordinates to form a rectangular box
        points = np.array(box_coords)

        x_values = points[:, 0]
        y_values = points[:, 1]

        min_x = float(np.min(x_values))
        min_y = float(np.min(y_values))
        max_x = float(np.max(x_values))
        max_y = float(np.max(y_values))

        return cls(p1=DrawingPoint(x=min_x, y=min_y), p2=DrawingPoint(x=max_x, y=max_y))

    def to_box_coords(self) -> BoxCoords:
        """Convert to list-based box coordinates format (four corners clockwise)"""
        return [
            [self.p1.x, self.p1.y],  # top-left
            [self.p2.x, self.p1.y],  # top-right
            [self.p2.x, self.p2.y],  # bottom-right
            [self.p1.x, self.p2.y],  # bottom-left
        ]


class DetectionItem(BaseModel):
    """Structured representation of an OCR detection"""

    box_coords: BoxCoords
    text: str
    confidence: float

    @classmethod
    def from_detection(cls, detection: Detection) -> "DetectionItem":
        """Convert from list-based Detection to structured DetectionItem"""
        # Safe extraction with explicit casts
        box = cast(BoxCoords, detection[0])
        text = str(detection[1]) if len(detection) > 1 else ""
        confidence = float(detection[2]) if len(detection) > 2 else 0.0
        return cls(box_coords=box, text=text, confidence=confidence)

    def to_detection(self) -> Detection:
        """Convert to list-based Detection format"""
        return [self.box_coords, self.text, self.confidence]


class OcrResult:
    """Class to store OCR detection results"""

    def __init__(self, detections: Optional[List[Detection]] = None):
        """Initialize OCR result object"""
        self.detections = detections if detections is not None else []

    def __len__(self) -> int:
        """Return the number of detected text regions"""
        return len(self.detections)

    def to_dict(self) -> Dict[str, List[Detection]]:
        """Convert OCR result to dictionary using original format"""
        return {"detections": self.detections}

    def to_p1p2_dict(self) -> Dict[str, List[Dict[str, Any]]]:
        """Convert OCR result to dictionary using p1/p2 format for bounding boxes only"""
        formatted_detections = []

        for detection in self.detections:
            try:
                # Extract box coordinates (first element)
                box_coords = cast(BoxCoords, detection[0])

                # Convert box coordinates to BoundingBox format
                bbox = BoundingBox.from_box_coords(box_coords)

                # Create formatted detection with only bounding box information
                formatted_detection = {
                    "boundingBox": {"p1": {"x": bbox.p1.x, "y": bbox.p1.y}, "p2": {"x": bbox.p2.x, "y": bbox.p2.y}}
                }

                formatted_detections.append(formatted_detection)
            except Exception:
                # Skip this detection if there's an error
                continue

        return {"detections": formatted_detections}
