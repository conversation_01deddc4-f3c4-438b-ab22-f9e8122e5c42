"""Models for Google Cloud Vision API processing in material certificate analysis."""
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List

from pydantic import BaseModel


class PDFType(str, Enum):
    """PDF type classification."""

    NATIVE = "native"
    RASTER = "raster"


class BoundingPoly(BaseModel):
    """Bounding polygon for text elements."""

    vertices: List[Dict[str, Any]] = []


class TextElement(BaseModel):
    """Individual text element with bounding box."""

    text: str
    bounding_poly: BoundingPoly


class TextBlock(BaseModel):
    """Text block containing multiple elements."""

    text: str
    bounding_poly: BoundingPoly
    elements: List[TextElement] = []


class DocumentText(BaseModel):
    """Complete document text with structured blocks."""

    text: str
    text_blocks: List[TextBlock] = []


@dataclass
class VisionProcessingResult:
    """Result of vision processing for material certificates."""

    pdf_type: PDFType
    total_pages: int
    page_images: List[bytes]
    vision_results: List[DocumentText]
    processed_images: List[bytes]
