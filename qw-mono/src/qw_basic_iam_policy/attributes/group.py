from pathlib import Path, PurePosixPath
from typing import List, Protocol, Sequence, Set

from pydantic import BaseModel

from qw_tenant_config.model.config import TenantConfig
from qw_tenant_config.registry import TenantConfigRegistry

ROOT_TENANTS = PurePosixPath("/tenants")


def belongs_to_tenants(groups: Sequence[PurePosixPath]) -> List[str]:
    """Syntax for tenants is ``tenants/<tenant_name>/and/potentially/subgroups``"""
    tenants: Set[str] = set()
    for g in groups:
        g_converted = Path(g)  # <- this is done to fix the local testing on Windows machines
        # first path part is "/" (subsequent slashes are no parts, though)
        if g_converted.is_relative_to(ROOT_TENANTS) and len(g_converted.parts) >= 3:
            tenants.add(g_converted.parts[2])
    return list(sorted(tenants))


class MultipleMembershipError(ValueError):
    pass


class Membership(BaseModel):
    tenant_id: int | None
    tenant_groups: Sequence[PurePosixPath]
    other_groups: Sequence[PurePosixPath]

    @property
    def tenant_id_(self) -> int:
        if self.tenant_id is None:
            raise ValueError("TenantId is non null")
        return self.tenant_id


class MembershipParser(Protocol):
    def __call__(self, groups_raw: Sequence[str]) -> Membership:
        """Can raise MultipleMembershipError"""
        ...


def parse_membership(groups_raw: Sequence[str], tenant_registry: TenantConfigRegistry) -> Membership:
    """Use `functools.partial` to make it compliant with MembershipParser"""
    groups: List[PurePosixPath] = []
    for gr in groups_raw:
        groups.append(PurePosixPath(gr))

    tenant_group_names = belongs_to_tenants(groups)
    tenants = tenant_registry.find_tenants_from_iam_groups(tenant_group_names)
    if len(tenants) > 1:
        tenant_names = ", ".join(t.code for t in tenants)
        raise MultipleMembershipError(f"More than one tenant membership found: {tenant_names}")

    tenant: TenantConfig | None = None if len(tenants) == 0 else tenants[0]
    if tenant is None:
        return Membership(tenant_id=None, tenant_groups=[], other_groups=groups)

    tenant_group_base = ROOT_TENANTS / tenant.iam_group_name
    tenant_groups = []
    other_groups = []
    for g in groups:
        if g.is_relative_to(tenant_group_base):
            if g != tenant_group_base:
                tenant_groups.append(g)
        else:
            other_groups.append(g)

    return Membership(tenant_id=tenant.id, tenant_groups=tenant_groups, other_groups=other_groups)


def parse_membership_no_op(groups_raw: Sequence[str]) -> Membership:
    """Compliant with MembershipParser"""
    groups: List[PurePosixPath] = []
    for gr in groups_raw:
        groups.append(PurePosixPath(gr))
    return Membership(tenant_id=None, tenant_groups=[], other_groups=groups)
