

/**
 * @typedef {Object} BoundingBox
 * @property {number} pageIndex
 * @property {number} p1.x
 * @property {number} p1.y
 * @property {number} p2.x
 * @property {number} p2.y
 */

/**
 * @typedef {Object} DataWithBoundingBox
 * @property {'length' | 'angle' | 'radius' | 'diameter' | 'diameter_thread' | 'diameter_countersink' | 'diameter_counterbore' | 'roughness' | 'gdt_frame' | 'gdt_datum'} annotationType
 * @property {BoundingBox} boundingBox
 */

/**
 * @typedef {Object} DrawingAnalysisResult
 * @property {DataWithBoundingBox[]} lengths
 * @property {DataWithBoundingBox[]} angles
 * @property {DataWithBoundingBox[]} radii
 * @property {DataWithBoundingBox[]} diameters
 * @property {DataWithBoundingBox[]} threads
 * @property {DataWithBoundingBox[]} counterbores
 * @property {DataWithBoundingBox[]} countersinks
 * @property {DataWithBoundingBox[]} roughness
 * @property {DataWithBoundingBox[]} gdtFrames
 * @property {DataWithBoundingBox[]} gdtDatums
 * @property {Object} norms
 */

/**
 * @typedef {Object} DrawingAnalysis
 * @property {DrawingAnalysisResult} result
 */

/**
 * @typedef {Object} DrawingInfo
 * @property {string} id
 * @property {string} path
 * @property {number} pageCount
 */


class EditorApiClient {

    /**
     * @param {string} host
     */
    constructor(host) {
        this.host = host;
    }

    /**
     * @returns {Promise<DrawingInfo[]>}
     */
    async getDrawingInfos() {
        const response = await fetch(`${this.host}/api/drawings`);
        if (!response.ok) {
            throw new Error(`Could not retrieve infos for all drawing ids: ${response.status}`);
        }
        const jsonData = await response.json();
        return jsonData["drawingInfos"];
    }

    /**
     * @param {string} drawingId
     * @returns {Promise<DrawingInfo>}
     */
    async getDrawingInfo(drawingId) {
        const response = await fetch(`${this.host}/api/drawing/${drawingId}`);
        if (!response.ok) {
            throw new Error(`Could not retrieve info for drawing id ${drawingId}: ${response.status}`);
        }
        return await response.json();
    }

    /**
     * @param {string} drawingId
     * @returns {Promise<DrawingAnalysis>}
     */
    async getDrawingAnalysis(drawingId) {
        const response = await fetch(`${this.host}/api/drawing/${drawingId}/analysis`);
        if (!response.ok) {
            throw new Error(`Could not retrieve drawing analysis: ${response.status}`);
        }
        return await response.json();
    }

    /**
     * @param {string} drawingId
     * @param {DrawingAnalysis} newAnalysis
     */
    async putDrawingAnalysis(drawingId, newAnalysis) {
        const jsonBody = JSON.stringify(newAnalysis);
        const response = await fetch(
            `${this.host}/api/drawing/${drawingId}/analysis`,
            {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: jsonBody,
            });
        if (!response.ok) {
            throw new Error(`Could not validate box config: ${jsonBody}`);
        }
    }

    /**
     * @param {Object} boxConfig
     * @returns {Promise<string | null>}
     */
    async validateItemConfiguration(boxConfig){
        const jsonBody = JSON.stringify(boxConfig);
        const response = await fetch(
            `${this.host}/api/validate`,
            {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: jsonBody,
            });
        if (!response.ok) {
            throw new Error(`Could not validate box config: ${jsonBody}`);
        }
        const jsonData = await response.json();
        return jsonData["errorMessage"];
    }

    /**
     * @param {string} drawingId
     * @param {number} pageIndex
     * @returns {string}
     */
    buildDrawingPageImageUrl(drawingId, pageIndex) {
        return `${this.host}/api/drawing/${drawingId}/image/${pageIndex}`
    }
}
