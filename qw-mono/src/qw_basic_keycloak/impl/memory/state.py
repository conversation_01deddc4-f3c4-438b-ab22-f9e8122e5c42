import uuid
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Tuple

from joserfc import jwk, jwt

from qw_basic_keycloak.openid.dtos.token import TokenResponse
from qw_basic_keycloak.realm import Realm, User


@dataclass
class Key:
    header: Dict[str, str]
    key: jwk.KeyFlexible

    def encode(self, claims: Dict[str, Any]) -> str:
        return jwt.encode(self.header, claims, self.key)


@dataclass
class MemoryIamSession(object):
    auth_code: str
    nonce: str

    user: User
    token_resp: TokenResponse | None = None
    ts_refresh_expiration: datetime | None = None

    @property
    def active(self) -> bool:
        return self.token_resp is not None


class MemoryIamState(object):
    def __init__(self, realm: Realm, issuer: str):
        self.realm = realm
        self.issuer = issuer
        self.__sessions: List[MemoryIamSession] = []

        # mimicking keycloak (i.e. using a symmetric key for signing of refresh tokens)
        self.__jwk_tokens = Key(header={"alg": "RS256"}, key=jwk.RSAKey.generate_key())
        self.__jwk_refresh_tokens = Key(header={"alg": "HS256"}, key=jwk.OctKey.generate_key())

    @property
    def key_data(self) -> jwk.KeyFlexible:
        # only expose main key
        return self.__jwk_tokens.key

    def __get_session_by_auth_code(self, auth_code: str) -> MemoryIamSession:
        for sess in self.__sessions:
            if sess.auth_code == auth_code:
                return sess
        raise ValueError(f"No session found for auth code {auth_code}")

    def __get_session_by_refresh_token(self, refresh_token: str) -> MemoryIamSession:
        for sess in self.__sessions:
            if sess.token_resp is not None and sess.token_resp.refresh_token == refresh_token:
                return sess
        raise ValueError(f"No session found for refresh token {refresh_token}")

    def __get_session_by_id_token(self, id_token: str) -> MemoryIamSession:
        for sess in self.__sessions:
            if sess.token_resp is not None and sess.token_resp.id_token == id_token:
                return sess
        raise ValueError(f"No session found for id token {id_token}")

    def __remove_session(self, session: MemoryIamSession) -> None:
        self.__sessions = [sess for sess in self.__sessions if sess != session]

    def generate_auth_code(self, username: str, nonce: str) -> str:
        user = self.realm.find_user_by_mail(username)
        if user is None:
            raise ValueError(f"Could not find user for username {username}")

        new_session = MemoryIamSession(
            auth_code=str(uuid.uuid4()),
            nonce=nonce,
            user=user,
        )
        self.__sessions.append(new_session)
        return new_session.auth_code

    def generate_tokens(self, client_id: str, user: User, nonce: str) -> Tuple[TokenResponse, datetime]:
        claim_groups = "qw_groups"
        claim_roles = "qw_roles"
        expires_in_secs = self.realm.export.access_token_lifespan
        refresh_expires_in_secs = self.realm.export.sso_session_idle_timeout
        ts_issuing = datetime.now(timezone.utc)
        ts_expiration = ts_issuing + timedelta(seconds=expires_in_secs)
        ts_refresh_expiration = ts_issuing + timedelta(seconds=refresh_expires_in_secs)
        groups = [str(g) for g in user.groups]

        claims_default = {
            "iss": self.issuer,
            "sub": user.id,
            "exp": int(ts_expiration.timestamp()),
            "iat": int(ts_issuing.timestamp()),
            "nonce": nonce,
        }
        claims_id = {
            "typ": "ID",
            "given_name": user.first_name,
            "family_name": user.last_name,
            "email": user.email,
            claim_groups: groups,
        }
        claims_access = {
            "typ": "Bearer",
            claim_roles: user.client_roles.get(client_id, []),
            claim_groups: groups,
        }
        claims_refresh = {"typ": "Refresh", "exp": int(ts_refresh_expiration.timestamp())}

        id_token = self.__jwk_tokens.encode(claims_default | claims_id)
        access_token = self.__jwk_tokens.encode(claims_default | claims_access)
        refresh_token = self.__jwk_tokens.encode(claims_default | claims_refresh)

        return (
            TokenResponse(
                token_type="Bearer",
                id_token=id_token,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_in=expires_in_secs,
                refresh_expires_in=refresh_expires_in_secs,
            ),
            ts_refresh_expiration,
        )

    def generate_tokens_and_update_session(self, session: MemoryIamSession, client_id: str) -> TokenResponse:
        resp, ts_refresh_expiration = self.generate_tokens(client_id, session.user, session.nonce)
        session.token_resp = resp
        session.ts_refresh_expiration = ts_refresh_expiration
        return resp

    def convert_auth_code(self, client_id: str, auth_code: str) -> TokenResponse:
        session = self.__get_session_by_auth_code(auth_code)
        return self.generate_tokens_and_update_session(session, client_id)

    def convert_refresh_token(self, client_id: str, signed_refresh_token: str) -> TokenResponse:
        session = self.__get_session_by_refresh_token(signed_refresh_token)
        ts_now = datetime.now(timezone.utc)
        if session.ts_refresh_expiration is None or ts_now > session.ts_refresh_expiration:
            raise ValueError("Cannot refresh, token is expired")
        return self.generate_tokens_and_update_session(session, client_id)

    def end_sessions(self, id_token: str) -> None:
        session = self.__get_session_by_id_token(id_token)
        self.__remove_session(session)
