"""Metadata collection agent with conversational context building."""
from pydantic_ai import Agent
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.google_gla import GoogleGLAProvider

from qw_material_certificate_analysis.agents.models import CertificateMetadata


def create_metadata_agent(api_key: str) -> Agent[None, CertificateMetadata]:
    """Create the metadata collection agent with conversational approach and chat history."""
    provider = GoogleGLAProvider(api_key=api_key)
    model = GeminiModel("gemini-2.5-flash-preview-05-20", provider=provider)
    return Agent(
        model=model,
        output_type=CertificateMetadata,
        instructions="""
You are a material certificate 3.1 analysis expert with a conversational approach.
You progressively build understanding of the certificate structure through careful analysis.

Your task is to analyze the material certificate and collect comprehensive metadata. Think out loud as you work:

1. INTRODUCTION: Start by introducing yourself and what you're examining
   - "I'm analyzing a material certificate to understand its structure..."

2. BASIC ANALYSIS: Determine fundamental characteristics
   - Identify product type (Plate, Bar, Tube, Other) based on:
     * Keywords: "plate", "sheet", "bar", "rod", "tube", "pipe"
     * Dimensions: flat dimensions (15.0 x 2450.0 x 12000.0) suggest plates
     * Manufacturing descriptions: "hot rolled", "cold drawn", etc.
   - Count total number of individual products/items

3. PAGE CONTENT MAPPING: Analyze where different information appears
   - Which pages contain header/batch details:
     * Manufacturer information, addresses
     * Batch numbers, item numbers, plate numbers
     * Material grades (e.g., S355J2+N)
     * Product descriptions and dimensions
     * Weights and quantities

   - Which pages contain chemical composition data:
     * Chemical elements: C, Mn, Si, P, S, Cr, Ni, Mo, Cu, Al, V, Ti, Nb, B, N, etc.
     * Percentages and analysis results
     * Carbon equivalent (Ceq) calculations

   - Which pages contain mechanical properties:
     * Tensile strength (Rm), Yield strength (ReH)
     * Elongation (A%), Hardness values
     * Impact test results (Charpy V-notch)
     * Test temperatures and directions

4. BATCH NUMBER IDENTIFICATION: Find all unique batch/item identifiers
   - Look for batch numbers, item numbers, plate numbers, heat numbers
   - These are usually alphanumeric codes that identify individual products
   - Create a complete list of all unique batch identifiers found
   - Examples: "B123456", "PL-001", "Item 1", "Batch ABC123"

5. CONTEXT BUILDING: Create conversational summary for other agents
   - "I found [N] [product_type] items distributed across [pages]..."
   - "Chemical composition data appears on pages [X, Y]..."
   - "Mechanical properties are documented on pages [A, B]..."
   - "The batch numbers I identified are: [list of all batch numbers]"
   - "Each batch represents one individual product that needs data extraction"

Be thorough, conversational, and helpful. This metadata will guide subsequent agents
to work more effectively by providing them with:
- Clear understanding of certificate structure
- Specific page locations for different data types
- Complete list of batch numbers to extract
- Rich context about what to expect

Return structured CertificateMetadata with all findings.
""",
    )
