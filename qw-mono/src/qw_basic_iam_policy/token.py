from typing import List, Optional

from qw_basic_iam_policy.attributes.group import Membership, MembershipParser, MultipleMembershipError
from qw_basic_iam_policy.attributes.role import QualiwiseRole
from qw_basic_keycloak.openid.inspector import ValidToken
from qw_log_interface import Logger


class ValidAccessToken(ValidToken):
    nonce: str
    roles: List[QualiwiseRole]
    membership: Membership

    @classmethod
    def from_valid_token(
        cls,
        valid_tkn: ValidToken,
        parse_membership: MembershipParser,
        property_roles: str,
        property_groups: str,
        logger: Logger,
    ) -> Optional["ValidAccessToken"]:
        if valid_tkn.type != "Bearer":
            logger.error(f"Expected token to type 'Bearer', but got '{valid_tkn.type}'")
            return None

        nonce = valid_tkn.src.claims.get("nonce")
        if nonce is None or not isinstance(nonce, str):
            logger.error(f"Expected nonce claim, but got {type(nonce)}")
            return None

        qw_roles = []
        for role in valid_tkn.src.claims.get(property_roles, []):
            try:
                qw_role = QualiwiseRole(role)
                qw_roles.append(qw_role)
            except ValueError:
                logger.warning(f"Role '{role}' is unknown")

        try:
            membership = parse_membership(valid_tkn.src.claims.get(property_groups, []))
        except MultipleMembershipError as e:
            logger.error(f"Could not parse membership: {e}")
            return None

        return cls(
            src=valid_tkn.src,
            type=valid_tkn.type,
            issuer=valid_tkn.issuer,
            subject=valid_tkn.subject,
            ts_start=valid_tkn.ts_start,
            ts_end=valid_tkn.ts_end,
            nonce=nonce,
            roles=qw_roles,
            membership=membership,
        )


class ValidIdToken(ValidToken):
    nonce: str
    given_name: str
    family_name: str
    email: str
    membership: Membership

    @classmethod
    def from_valid_token(
        cls, valid_tkn: ValidToken, parse_membership: MembershipParser, property_groups: str, logger: Logger
    ) -> Optional["ValidIdToken"]:
        if valid_tkn.type != "ID":
            logger.error(f"Expected token to type 'ID', but got '{valid_tkn.type}'")
            return None

        nonce = valid_tkn.src.claims.get("nonce")
        if nonce is None or not isinstance(nonce, str):
            logger.error(f"Expected nonce claim, but got {type(nonce)}")
            return None

        given_name = valid_tkn.src.claims.get("given_name")
        if given_name is None:
            logger.error("Could not find given_name property in id token")
            return None

        family_name = valid_tkn.src.claims.get("family_name")
        if family_name is None:
            logger.error("Could not find family_name property in id token")
            return None

        email = valid_tkn.src.claims.get("email")
        if email is None:
            logger.error("Could not find email property in id token")
            return None

        try:
            membership = parse_membership(valid_tkn.src.claims.get(property_groups, []))
        except MultipleMembershipError as e:
            logger.error(f"Could not parse membership: {e}")
            return None

        return cls(
            src=valid_tkn.src,
            type=valid_tkn.type,
            issuer=valid_tkn.issuer,
            subject=valid_tkn.subject,
            ts_start=valid_tkn.ts_start,
            ts_end=valid_tkn.ts_end,
            nonce=nonce,
            given_name=given_name,
            family_name=family_name,
            email=email,
            membership=membership,
        )
