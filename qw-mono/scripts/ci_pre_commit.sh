#!/usr/bin/env sh

FILE=$(readlink -f "$0")
PROJECT_DIR=$(realpath $(dirname "$FILE")/..)

CHECK_ONLY=0

while getopts 'c' opt; do
  case "$opt" in
    c)
      CHECK_ONLY=1
      ;;

    ?|h)
      echo "Usage: $(basename $0) [-c]"
      echo "-c   only check, don't apply changes"
      exit 1
      ;;
  esac
done
shift "$(($OPTIND -1))"


# see also pyproject.toml for configurations
cd ${PROJECT_DIR}
SRC_FOLDERS="src"


echo "running black"
black $SRC_FOLDERS --check
CHECK_RESULT_BLACK=$?
if [ "$CHECK_RESULT_BLACK" -ne 0 ]; then
  if [ "$CHECK_ONLY" -eq 0 ]; then
    black $SRC_FOLDERS
  else
    exit $CHECK_RESULT_BLACK
  fi
fi


echo "running isort"
isort $SRC_FOLDERS --check
CHECK_RESULT_ISORT=$?
if [ "$CHECK_RESULT_ISORT" -ne 0 ]; then
  if [ "$CHECK_ONLY" -eq 0 ]; then
    isort $SRC_FOLDERS
  else
    exit $CHECK_RESULT_ISORT
  fi
fi


echo "running flake8"
flake8 $SRC_FOLDERS --max-line-length=120 --extend-ignore=E203 --per-file-ignores=./scripts/*.py:E402 || exit 1


echo "running mypy"
mypy $SRC_FOLDERS --junit-xml=./junit_reports/mypy.xml || exit 1
