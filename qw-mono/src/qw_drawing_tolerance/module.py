"""Module interface for qw_drawing_tolerance."""

from qw_drawing_tolerance.tolerance_service import ToleranceService
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class QwDrawingToleranceModule:
    """Module interface for qw_drawing_tolerance."""

    def __init__(self, tolerance_service: ToleranceService, lf: LogFactory = NO_LOG_FACTORY):
        """Initialize the module.

        Args:
            tolerance_service: The tolerance service
            lf: Log factory
        """
        self.tolerance_service = tolerance_service
        self.lf = lf

    @classmethod
    def from_config(cls, lf: LogFactory = NO_LOG_FACTORY) -> "QwDrawingToleranceModule":
        """Create a module from configuration.

        Args:
            lf: Log factory

        Returns:
            Initialized module
        """
        tolerance_service = ToleranceService(lf)
        return cls(tolerance_service, lf)
