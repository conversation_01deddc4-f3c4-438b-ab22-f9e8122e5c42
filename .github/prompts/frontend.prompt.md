# QualiWise Frontend Coding Assistant

## Project Overview
This is the frontend part of the QualiWise project, an Angular application located in the `qw-webui` directory.

## Coding Conventions

### HTML Structure & Styling
- Use `<div>`, `<span>`, `<p>` as primary elements
- Avoid hierarchical elements like `<li>`, `<ul>`, `<h1>`, etc.
- Use `<button>` only when `qw-btn-classic` is not suitable
- All styles must be handled within HTML using Tailwind CSS

### Component Architecture
- Use self-contained standalone components
- Each component should manage its own state
- Avoid tight coupling between components

### Method Guidelines
- Use descriptive method names
- Follow single responsibility principle
- Mark methods as private if not accessed outside component
- Avoid injection of components (99.99% of cases)

### Observables & State Management
- Suffix observables with `$` (e.g., `userData$`)
- Use `takeUntilDestroyed` for subscription management
- Avoid nested subscriptions
- Use signals only inside components
- Use stateful services for state across components
- Create `vm$` observable and subscribe to it in template
- Prefer template-driven forms over reactive forms

### Localization
- Use Angular's i18n for all user-facing text

## Form Implementation Guidance
- Use template-driven forms with signals and models
- Child input components handle field-level validation
- Parent form components maintain form state using signals
- Use computed signals for form-level validation
- Keep form logic at appropriate levels

## Development Tasks
- Run frontend in dev mode: `scripts/dev_enter.sh`, `cd qw-webui`, `npm install`, `ng serve --host 0.0.0.0`
- Update localization: `ng extract-i18n --output-path=src/locale`
- Access the application at http://app.docker.localhost
