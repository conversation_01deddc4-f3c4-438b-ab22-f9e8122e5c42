from typing import Protocol, Type

from sqlalchemy.orm import DeclarativeBase, Session


class RelationalDatabase(Protocol):
    base: Type[DeclarativeBase]

    def has_table(self, table_name: str) -> bool:
        ...

    def create_or_update_tables(self) -> None:
        ...

    def create_and_get_session(self) -> Session:
        """
        For now the purpose of this setup is to only have 1 active session at any given time. Of course, it is still
        possible to have multiple consequent sessions within a request, but no overlapping ones! Anyway, one session for
        the entire request is the cleanest way to ensure proper transactions.
        """
        ...

    def close_session(self) -> None:
        ...
