"""Geometry Utils"""

import math
from collections import defaultdict
from typing import List, <PERSON><PERSON>, cast

import numpy as np
import numpy.typing as npt

from qw_drawing_toolkit.pdf.models.analysis_models import TextBox
from qw_drawing_toolkit.pdf.models.base_models import T_BBOX, T_LINE_SEGMENT, T_POINT
from qw_drawing_toolkit.pdf.models.geometry_models import LineDirection


def rotate_point(
    point: T_POINT,
    max_width: float,
    max_height: float,
    angle_in_deg: float,
) -> T_POINT:
    """
    Rotate a point around the center of a page.

    Args:
        point: The point to rotate as (x, y) coordinates
        max_width: Width of the page in points
        max_height: Height of the page in points
        angle_in_deg: Rotation angle in degrees (must be 90, 180, or 270)

    Returns:
        Rotated point as (x, y) coordinates

    Raises:
        ValueError: If angle is not 90, 180, or 270 degrees
    """
    x, y = point
    center_x = max_width / 2
    center_y = max_height / 2

    translated_x = x - center_x
    translated_y = y - center_y

    if angle_in_deg == 90:
        rotated_x = max_height - y - center_x
        rotated_y = x - center_y
    elif angle_in_deg == 180:
        rotated_x = -translated_x
        rotated_y = -translated_y
    elif angle_in_deg == 270:
        rotated_x = translated_y + center_x
        rotated_y = max_width - x - center_y
    else:
        raise ValueError("Angle must be 90, 180, or 270 degrees")

    final_x = rotated_x + center_x
    final_y = rotated_y + center_y

    return final_x, final_y


def rotate_line(
    line: T_LINE_SEGMENT,
    max_width: float,
    max_height: float,
    angle: float,
) -> T_LINE_SEGMENT:
    """
    Rotate a line segment around the center of a page.

    Args:
        line: Line segment to rotate as ((x1, y1), (x2, y2))
        max_width: Width of the page in points
        max_height: Height of the page in points
        angle: Rotation angle in degrees (must be 90, 180, or 270)

    Returns:
        Rotated line segment as ((x1', y1'), (x2', y2'))
    """
    (x1, y1), (x2, y2) = line
    rotated_p1 = rotate_point((x1, y1), max_width, max_height, angle)
    rotated_p2 = rotate_point((x2, y2), max_width, max_height, angle)
    return rotated_p1, rotated_p2


def rotate_lines(
    lines: List[T_LINE_SEGMENT],
    max_width: float,
    max_height: float,
    angle: float,
) -> List[T_LINE_SEGMENT]:
    """
    Rotate multiple line segments around the center of a page.

    Args:
        lines: List of line segments to rotate
        max_width: Width of the page in points
        max_height: Height of the page in points
        angle: Rotation angle in degrees (must be 90, 180, or 270)

    Returns:
        List of rotated line segments
    """
    return [rotate_line(line, max_width, max_height, angle) for line in lines]


def rotate_bbox(bbox: T_BBOX, max_width: float, max_height: float, angle: float) -> T_BBOX:
    """
    Rotate a bounding box around the center of a page.

    The function rotates all four corners of the bounding box and then
    creates a new axis-aligned bounding box that contains all rotated corners.

    Args:
        bbox: Bounding box as (x1, y1, x2, y2) where (x1, y1) is the top-left corner
              and (x2, y2) is the bottom-right corner
        max_width: Width of the page in points
        max_height: Height of the page in points
        angle: Rotation angle in degrees (must be 90, 180, or 270)

    Returns:
        Rotated bounding box as (x1', y1', x2', y2')
    """
    x1, y1, x2, y2 = bbox
    corners = [(x1, y1), (x1, y2), (x2, y1), (x2, y2)]
    rotated_corners = [rotate_point((x, y), max_width, max_height, angle) for x, y in corners]

    min_x = min(corner[0] for corner in rotated_corners)
    min_y = min(corner[1] for corner in rotated_corners)
    max_x = max(corner[0] for corner in rotated_corners)
    max_y = max(corner[1] for corner in rotated_corners)

    return min_x, min_y, max_x, max_y


def rotate_textbox(textbox: TextBox, max_width: float, max_height: float, angle: float) -> TextBox:
    """
    Rotate a TextBox object around the center of a page.

    This rotates the bounding box and direction vector of the TextBox
    without modifying any text content or other mappings.

    Args:
        textbox: The TextBox object to rotate
        max_width: Width of the page in points
        max_height: Height of the page in points
        angle: Rotation angle in degrees (must be 90, 180, or 270)

    Returns:
        New TextBox with rotated parameters

    Raises:
        ValueError: If angle is not 90, 180, or 270 degrees
    """
    rotated_bbox = rotate_bbox(textbox.bbox, max_width, max_height, angle)

    x, y = textbox.direction

    if angle == 90:
        transformed_vector = (-y, x)
    elif angle == 180:
        transformed_vector = (-x, -y)  # Corrected from original
    elif angle == 270:
        transformed_vector = (y, -x)
    else:
        raise ValueError("Rotation must be 90, 180, or 270 degrees")

    # Create a new TextBox with the rotated parameters
    return TextBox(
        textbox.text,
        textbox.font,
        textbox.font_size,
        rotated_bbox,
        transformed_vector,
        textbox.is_gd_t,
        textbox.is_symbol,
    )


def rotate_text_boxes(text_boxes: List[TextBox], max_width: float, max_height: float, angle: float) -> List[TextBox]:
    """
    Rotate multiple TextBox objects around the center of a page.

    Args:
        text_boxes: List of TextBox objects to rotate
        max_width: Width of the page in points
        max_height: Height of the page in points
        angle: Rotation angle in degrees (must be 90, 180, or 270)

    Returns:
        List of new TextBox objects with rotated parameters
    """
    return [rotate_textbox(box, max_width, max_height, angle) for box in text_boxes]


def transform_direction_vector(direction: Tuple[float, float], angle: float) -> Tuple[float, float]:
    """
    Transform a direction vector based on rotation angle.

    Args:
        direction: Direction vector as (x, y) coordinates
        angle: Rotation angle in degrees (90, 180, 270, or other values)

    Returns:
        Transformed direction vector as (x', y') coordinates.
        Returns the original vector if angle is not 90, 180, or 270.
    """
    x, y = direction

    if angle == 90:
        return (-y, x)
    elif angle == 180:
        return (-x, -y)
    elif angle == 270:
        return (y, -x)
    else:
        return (x, y)  # No rotation


def length_of_line(line: T_LINE_SEGMENT) -> float:
    """
    Calculate the Euclidean length of a line segment.

    Args:
        line: Line segment as ((x1, y1), (x2, y2))

    Returns:
        Length of the line segment
    """
    (lx1, ly1), (lx2, ly2) = line
    return math.sqrt(math.pow(lx2 - lx1, 2) + math.pow(ly2 - ly1, 2))


def bezier_cubic(
    p0: npt.NDArray[np.float64],
    p1: npt.NDArray[np.float64],
    p2: npt.NDArray[np.float64],
    p3: npt.NDArray[np.float64],
    t: float,
) -> npt.NDArray[np.float64]:
    """
    Calculate a point on a cubic Bezier curve.

    Args:
        p0: Start point of the curve
        p1: First control point
        p2: Second control point
        p3: End point of the curve
        t: Parameter value between 0 and 1

    Returns:
        Point on the Bezier curve at parameter t
    """
    result = (1 - t) ** 3 * p0 + 3 * (1 - t) ** 2 * t * p1 + 3 * (1 - t) * t**2 * p2 + t**3 * p3
    return cast(npt.NDArray[np.float64], result)


def sort_lines_by_y(lines: List[T_LINE_SEGMENT], reverse: bool = False) -> List[T_LINE_SEGMENT]:
    """
    Sort line segments by their minimum y-coordinate.

    Args:
        lines: List of line segments to sort
        reverse: If True, sort in descending order (top to bottom)
                 If False, sort in ascending order (bottom to top)

    Returns:
        Sorted list of line segments
    """
    return sorted(lines, key=lambda line: min(line[0][1], line[1][1]), reverse=reverse)


def sort_lines_by_x(lines: List[T_LINE_SEGMENT], reverse: bool = False) -> List[T_LINE_SEGMENT]:
    """
    Sort line segments by their minimum x-coordinate.

    Args:
        lines: List of line segments to sort
        reverse: If True, sort in descending order (right to left)
                 If False, sort in ascending order (left to right)

    Returns:
        Sorted list of line segments
    """
    return sorted(lines, key=lambda line: min(line[0][0], line[1][0]), reverse=reverse)


def is_line_in_box(
    line: T_LINE_SEGMENT,
    rectangle: T_BBOX,
    tolerance: float,
) -> bool:
    """
    Check if a line segment is completely contained within a rectangle.

    Args:
        line: Line segment as ((x1, y1), (x2, y2))
        rectangle: Bounding box as (x1, y1, x2, y2)
        tolerance: Additional margin to extend the rectangle by

    Returns:
        True if both endpoints of the line are within the rectangle (with tolerance),
        False otherwise
    """
    (lx1, ly1), (lx2, ly2) = line
    (rx1, ry1, rx2, ry2) = rectangle

    left = min(rx1, rx2) - tolerance
    right = max(rx1, rx2) + tolerance
    top = min(ry1, ry2) - tolerance
    bottom = max(ry1, ry2) + tolerance

    return (left <= lx1 <= right and top <= ly1 <= bottom) and (left <= lx2 <= right and top <= ly2 <= bottom)


def is_line_in_direction(line: T_LINE_SEGMENT, direction: LineDirection, point_proximity_tolerance: float) -> bool:
    """
    Check if a line segment is oriented in the specified direction.

    Args:
        line: Line segment as ((x1, y1), (x2, y2))
        direction: Expected orientation (HORIZONTAL or VERTICAL)
        point_proximity_tolerance: Maximum allowed deviation for considering points aligned

    Returns:
        True if the line is oriented in the specified direction, False otherwise
    """
    (lx1, ly1), (lx2, ly2) = line

    if direction == LineDirection.HORIZONTAL:
        return abs(ly1 - ly2) <= point_proximity_tolerance
    if direction == LineDirection.VERTICAL:
        return abs(lx1 - lx2) <= point_proximity_tolerance
    return False


def find_lines_in_box(
    box: T_BBOX,
    lines: List[T_LINE_SEGMENT],
    point_proximity_tolerance: float,
    direction: LineDirection | None = None,
    tolerance: float | None = None,
) -> List[T_LINE_SEGMENT]:
    """
    Find all lines that are contained within a bounding box.

    Args:
        box: Bounding box as (x1, y1, x2, y2)
        lines: List of line segments to search
        point_proximity_tolerance: Tolerance for point proximity checks
        direction: Optional filter for line direction (HORIZONTAL or VERTICAL)
        tolerance: Optional custom tolerance for box containment check.
                  Defaults to point_proximity_tolerance if not specified.

    Returns:
        List of line segments that are contained within the box and match the direction filter (if specified)
    """
    if tolerance is None:
        tolerance = point_proximity_tolerance
    if direction is None:
        return [line for line in lines if is_line_in_box(line, box, tolerance=tolerance)]
    else:
        return [
            line
            for line in lines
            if is_line_in_box(line, box, tolerance=tolerance)
            and is_line_in_direction(line, direction, point_proximity_tolerance)
        ]


def normalize_by_tolerance(value: float, point_proximity_tolerance: float) -> float:
    """
    Normalize a value to the nearest multiple of the tolerance.

    Args:
        value: The value to normalize
        point_proximity_tolerance: The tolerance value to normalize by

    Returns:
        Normalized value rounded to the nearest multiple of the tolerance
    """
    return round(value / point_proximity_tolerance) * point_proximity_tolerance


def find_adjacent_lines(
    target_line: T_LINE_SEGMENT,
    lines: List[T_LINE_SEGMENT],
    direction: LineDirection,
    max_width: float,
    max_height: float,
    point_proximity_tolerance: float,
    min_width: float = 0,
    min_height: float = 0,
) -> List[T_LINE_SEGMENT]:
    """
    Find all lines that are adjacent to the target line in the specified direction.

    Args:
        target_line: The reference line segment
        lines: List of line segments to search
        direction: Direction to search for adjacent lines (HORIZONTAL or VERTICAL)
        max_width: Maximum width constraint for the search area
        max_height: Maximum height constraint for the search area
        point_proximity_tolerance: Tolerance for considering points as adjacent
        min_width: Minimum width constraint for the search area (default: 0)
        min_height: Minimum height constraint for the search area (default: 0)

    Returns:
        List of line segments that are adjacent to the target line
    """
    adjacent_lines = []

    for line in lines:
        if is_line_adjacent(
            line,
            target_line,
            direction,
            point_proximity_tolerance,
            max_width,
            max_height,
            min_width,
            min_height,
        ):
            adjacent_lines.append(line)

    return adjacent_lines


def is_line_adjacent(
    line_to_check: T_LINE_SEGMENT,
    target_line: T_LINE_SEGMENT,
    direction: LineDirection,
    point_proximity_tolerance: float,
    max_width: float = float("inf"),
    max_height: float = float("inf"),
    min_width: float = 0,
    min_height: float = 0,
) -> bool:
    """
    Check if a line is adjacent to the target line in the specified direction.

    Args:
        line_to_check: The line to check for adjacency
        target_line: The reference line
        direction: Direction to check for adjacency (HORIZONTAL or VERTICAL)
        point_proximity_tolerance: Tolerance for considering points as adjacent
        max_width: Maximum width constraint for the search area (default: infinity)
        max_height: Maximum height constraint for the search area (default: infinity)
        min_width: Minimum width constraint for the search area (default: 0)
        min_height: Minimum height constraint for the search area (default: 0)

    Returns:
        True if the line is adjacent to the target line, False otherwise
    """
    (lx1, ly1), (lx2, ly2) = line_to_check
    (x1, y1), (x2, y2) = target_line

    ly_max = max(ly1, ly2) + point_proximity_tolerance
    ly_min = min(ly1, ly2) - point_proximity_tolerance
    lx_max = max(lx1, lx2) + point_proximity_tolerance
    lx_min = min(lx1, lx2) - point_proximity_tolerance

    max_width -= point_proximity_tolerance
    max_height -= point_proximity_tolerance
    min_width += point_proximity_tolerance
    min_height += point_proximity_tolerance

    if (
        lx1 <= min_width
        or lx1 >= max_width
        or lx2 <= min_width
        or lx2 >= max_width
        or ly1 <= min_height
        or ly1 >= max_height
        or ly2 <= min_height
        or ly2 >= max_height
    ):
        return False

    if direction == LineDirection.VERTICAL:
        if is_line_in_direction(line_to_check, LineDirection.VERTICAL, point_proximity_tolerance):
            y_max = max(y1, y2) + point_proximity_tolerance
            y_min = min(y1, y2) - point_proximity_tolerance

            if is_line_in_direction(target_line, LineDirection.VERTICAL, point_proximity_tolerance):
                return (y_min <= ly1 <= y_max or y_min <= ly2 <= y_max) and (
                    (abs(x1 - lx1) <= point_proximity_tolerance or abs(x1 - lx2) <= point_proximity_tolerance)
                    and (abs(x2 - lx1) <= point_proximity_tolerance or abs(x2 - lx2) <= point_proximity_tolerance)
                )
            else:
                x_max = max(x1, x2) + point_proximity_tolerance
                x_min = min(x1, x2) - point_proximity_tolerance

                if lx1 < x_min or lx1 > x_max or lx2 < x_min or lx2 > x_max:
                    return False

                return ly_min <= y_min <= ly_max or ly_min <= y_max <= ly_max

    elif direction == LineDirection.HORIZONTAL:
        if is_line_in_direction(line_to_check, LineDirection.HORIZONTAL, point_proximity_tolerance):
            x_max = max(x1, x2) + point_proximity_tolerance
            x_min = min(x1, x2) - point_proximity_tolerance

            if is_line_in_direction(target_line, LineDirection.HORIZONTAL, point_proximity_tolerance):
                return (x_min <= lx1 <= x_max or x_min <= lx2 <= x_max) and (
                    (abs(y1 - ly1) <= point_proximity_tolerance or abs(y1 - ly2) <= point_proximity_tolerance)
                    and (abs(y2 - ly1) <= point_proximity_tolerance or abs(y2 - ly2) <= point_proximity_tolerance)
                )
            else:
                y_max = max(y1, y2) + point_proximity_tolerance
                y_min = min(y1, y2) - point_proximity_tolerance

                if (
                    ly1 < y_min - point_proximity_tolerance
                    or ly1 > y_max + point_proximity_tolerance
                    or ly2 < y_min - point_proximity_tolerance
                    or ly2 > y_max + point_proximity_tolerance
                ):
                    return False

                return (
                    lx_min - point_proximity_tolerance <= x_min <= lx_max + point_proximity_tolerance
                    or lx_min - point_proximity_tolerance <= x_max <= lx_max + point_proximity_tolerance
                )

    return False


def find_lowest_line_with_adjacent_verticals(
    lines: List[T_LINE_SEGMENT], max_width: float, max_height: float, point_proximity_tolerance: float
) -> T_LINE_SEGMENT | None:
    """
    Find the lowest horizontal line that has adjacent vertical lines.

    This function is typically used to identify the bottom boundary of a title block.

    Args:
        lines: List of line segments to search
        max_width: Maximum width constraint for the search area
        max_height: Maximum height constraint for the search area
        point_proximity_tolerance: Tolerance for considering points as adjacent

    Returns:
        The lowest horizontal line with adjacent vertical lines, or None if not found
    """
    lowest_line = None

    lines = sort_lines_by_y(lines, reverse=True)

    for line_index in range(len(lines)):
        line = lines[line_index]
        if (
            is_line_in_direction(line, LineDirection.HORIZONTAL, point_proximity_tolerance)
            and len(
                find_adjacent_lines(
                    line, lines, LineDirection.VERTICAL, max_width, max_height, point_proximity_tolerance
                )
            )
            > 0
        ):
            if line_index + 1 < len(lines):
                next_option = None
                lno = 0.0
                next_option_index = 1

                # TODO adjust this so it works for the new pdfs.
                #  Might have to come up with a totally different title block detection

                # while (line_index + next_option_index < len(lines) and not (
                #         is_line_in_direction(
                #             lines[line_index + next_option_index], DrawingAnalyzerLineDirection.HORIZONTAL
                #         ) and not
                #         abs(self._extend_line_to_max(
                #             lines[line_index + next_option_index])[0][1] - line[0][1]
                #             ) < self.point_proximity_tolerance and
                #         self._is_line_in_box(
                #             self._extend_line_to_max(lines[line_index + next_option_index]),
                #             self.drawing_box)
                #          # and length_of_line(self._extend_line_to_max(lines[line_index + next_option_index]
                #          )) > (self.drawing_box[2] - self.drawing_box[0]) * 0.8
                # )):
                #     next_option_index += 1

                if line_index + next_option_index < len(lines):
                    next_option = lines[line_index + next_option_index]
                    lno = length_of_line(next_option)

                lnl = length_of_line(line)
                if (
                    # (lnl > (self.drawing_box[2] - self.drawing_box[0]) * 0.8 and
                    next_option is not None
                    and not (
                        lno <= lnl
                        and abs(lnl - lno) < 0.2 * lnl
                        and len(
                            find_adjacent_lines(
                                next_option,
                                lines,
                                LineDirection.VERTICAL,
                                max_width,
                                max_height,
                                point_proximity_tolerance,
                            )
                        )
                        > 0
                    )
                    and not lnl <= max_width / 10
                ):
                    return line
            else:
                return line

    return lowest_line


def find_next_connected_vertical(
    vertical: T_LINE_SEGMENT,
    lines: List[T_LINE_SEGMENT],
    drawing_box: T_BBOX,
    point_proximity_tolerance: float,
) -> T_LINE_SEGMENT | None:
    """
    Find the next connected vertical line in a sequence of connected lines.

    This function is typically used to identify vertical dividers in a title block.

    Args:
        vertical: Current vertical line to find the next connection for
        lines: List of line segments to search
        drawing_box: Bounding box of the drawing area
        point_proximity_tolerance: Tolerance for considering points as connected

    Returns:
        The next connected vertical line, or None if none found
    """
    max_width = float("inf")
    max_height = float("inf")

    upper_horizontal = find_upper_horizontal(
        vertical, lines, point_proximity_tolerance, max_width, max_height, drawing_box
    )

    if not upper_horizontal:
        return None

    verticals = find_adjacent_lines(
        upper_horizontal, lines, LineDirection.VERTICAL, max_width, max_height, point_proximity_tolerance
    )

    if not verticals:
        return None

    verticals = sort_lines_by_x(verticals)

    vertical_index = 0
    while vertical_index < len(verticals) and verticals[vertical_index] == vertical:
        vertical_index += 1

    vertical_index += 1

    if vertical_index >= len(verticals):
        return None

    next_vertical = verticals[vertical_index]

    (uhx1, uhy1), (uhx2, uhy2) = upper_horizontal
    uh_min_y = min(uhy1, uhy2)
    # vertical_index = 1

    if drawing_box:
        db_min_x = min(drawing_box[0], drawing_box[2])
        db_max_x = max(drawing_box[0], drawing_box[2]) - point_proximity_tolerance
        db_min_y = min(drawing_box[1], drawing_box[3]) + point_proximity_tolerance
        db_max_y = max(drawing_box[1], drawing_box[3]) - point_proximity_tolerance
        nv_min_x = min(next_vertical[0][0], next_vertical[1][0])
        nv_max_x = max(next_vertical[0][0], next_vertical[1][0])
        nv_min_y = min(next_vertical[0][1], next_vertical[1][1])
        # nv_max_y = max(next_vertical[0][1], next_vertical[1][1])
        cv_min_x = min(vertical[0][0], vertical[1][0])
        cv_max_x = max(vertical[0][0], vertical[1][0])
        # cv_min_y = min(vertical[0][1], vertical[1][1])
        # cv_max_y = max(vertical[0][1], vertical[1][1])

        while vertical_index < len(verticals) and (
            (
                nv_min_x < db_min_x
                or nv_max_x >= db_max_x
                or nv_min_y >= uh_min_y
                # or nv_min_y <= db_min_y or nv_max_y >= db_max_y
                or abs(nv_min_y - db_min_y) < 0.1 * abs(db_max_y - db_min_y)
                or (
                    abs(cv_min_x - nv_min_x) <= point_proximity_tolerance
                    and abs(cv_max_x - nv_max_x) <= point_proximity_tolerance
                )
            )
            or find_upper_horizontal(
                next_vertical, lines, point_proximity_tolerance, max_width, max_height, drawing_box
            )
            is None
        ):
            next_vertical = verticals[vertical_index]
            nv_min_x = min(next_vertical[0][0], next_vertical[1][0])
            nv_max_x = max(next_vertical[0][0], next_vertical[1][0])
            nv_min_y = min(next_vertical[0][1], next_vertical[1][1])
            # nv_max_y = max(next_vertical[0][1], next_vertical[1][1])
            vertical_index += 1

        if nv_min_x < db_min_x or nv_max_x >= db_max_x or nv_min_y >= uh_min_y:
            # or nv_min_y <= db_min_y or nv_max_y >= db_max_y):
            return None
    else:
        nv_min_y = min(next_vertical[0][1], next_vertical[1][1])
        while nv_min_y >= uh_min_y and vertical_index < len(verticals):
            next_vertical = verticals[vertical_index]
            nv_min_y = min(next_vertical[0][1], next_vertical[1][1])
            vertical_index += 1

    return next_vertical


def find_upper_horizontal(
    line: T_LINE_SEGMENT,
    lines: List[T_LINE_SEGMENT],
    point_proximity_tolerance: float,
    max_width: float = float("inf"),
    max_height: float = float("inf"),
    drawing_box: T_BBOX | None = None,
) -> T_LINE_SEGMENT | None:
    """
    Find the upper horizontal line connected to a vertical line.

    This function is typically used to identify horizontal dividers in a title block.

    Args:
        line: Line to find the upper horizontal connection for
        lines: List of line segments to search
        point_proximity_tolerance: Tolerance for considering points as connected
        max_width: Maximum width constraint for the search area (default: infinity)
        max_height: Maximum height constraint for the search area (default: infinity)
        drawing_box: Optional bounding box of the drawing area for additional constraints

    Returns:
        The upper horizontal line connected to the input line, or None if none found
    """
    horizontals = find_adjacent_lines(
        line, lines, LineDirection.HORIZONTAL, max_width, max_height, point_proximity_tolerance
    )

    if not horizontals:
        return None

    horizontals = sort_lines_by_y(horizontals)
    upper_horizontal = horizontals[0]

    if is_line_in_direction(line, LineDirection.HORIZONTAL, point_proximity_tolerance):
        upper_horizontal = line
    elif drawing_box:
        db_min_x = min(drawing_box[0], drawing_box[2]) + point_proximity_tolerance
        db_max_x = max(drawing_box[0], drawing_box[2]) - point_proximity_tolerance

        db_width = db_max_x - db_min_x + 2 * point_proximity_tolerance
        min_diff = 0.1 * db_width
        uh_width = length_of_line(upper_horizontal)
        diff = db_width - uh_width
        horizontal_index = 1

        while horizontal_index < len(horizontals) and diff < min_diff:
            upper_horizontal = horizontals[horizontal_index]
            uh_width = length_of_line(upper_horizontal)
            diff = db_width - uh_width
            horizontal_index += 1

        if diff < min_diff:
            return None

    return upper_horizontal


def find_title_block_lines(
    lines: List[T_LINE_SEGMENT], title_block_sections: List[T_BBOX], point_proximity_tolerance: float
) -> List[T_LINE_SEGMENT]:
    """
    Find all lines that are contained within any of the title block sections.

    Args:
        lines: List of line segments to search
        title_block_sections: List of bounding boxes representing title block sections
        point_proximity_tolerance: Tolerance for considering lines as contained

    Returns:
        List of line segments that are contained within any title block section
    """
    result = []

    for line in lines:
        for section in title_block_sections:
            if is_line_in_box(line, section, point_proximity_tolerance):
                result.append(line)
                break

    return result


def scale_box_from_center(box: T_BBOX, factor: float, scale_x: bool = True, scale_y: bool = True) -> T_BBOX:
    """
    Scale a bounding box from its center by the specified factor.

    Args:
        box: Bounding box as (x1, y1, x2, y2)
        factor: Scaling factor (>1 to enlarge, <1 to shrink)
        scale_x: Whether to scale in the x-direction (default: True)
        scale_y: Whether to scale in the y-direction (default: True)

    Returns:
        Scaled bounding box as (x1', y1', x2', y2')
    """
    center_x = (box[0] + box[2]) / 2
    center_y = (box[1] + box[3]) / 2

    width = box[2] - box[0]
    height = box[3] - box[1]

    new_width = width * (factor if scale_x else 1)
    new_height = height * (factor if scale_y else 1)

    new_min_x = center_x - new_width / 2
    new_max_x = center_x + new_width / 2
    new_min_y = center_y - new_height / 2
    new_max_y = center_y + new_height / 2

    return new_min_x, new_min_y, new_max_x, new_max_y


def get_center_of_box(box: T_BBOX) -> tuple[float, float]:
    """
    Calculate the center point of a bounding box.

    Args:
        box: Bounding box as (x1, y1, x2, y2)

    Returns:
        Center point as (center_x, center_y)
    """
    return (box[0] + box[2]) / 2, (box[1] + box[3]) / 2


def find_vertical_clusters(vertical_lines: List[T_LINE_SEGMENT], y_tolerance: float) -> List[List[T_LINE_SEGMENT]]:
    """
    Group vertical lines into clusters based on their y-coordinates.

    Lines are grouped together if their start and end y-coordinates are within
    the specified tolerance of each other. This helps identify sets of vertical
    lines that are aligned horizontally.

    Args:
        vertical_lines: List of vertical line segments to cluster
        y_tolerance: Maximum allowed difference in y-coordinates for lines to be considered in the same cluster

    Returns:
        List of clusters, where each cluster is a list of line segments
    """
    clusters = defaultdict(list)
    unique_lines = set()

    normalized_lines = [normalize_line(line) for line in vertical_lines]

    lines_array = np.array(normalized_lines, dtype=np.float32)

    y1_vals = lines_array[:, 0, 1]
    y2_vals = lines_array[:, 1, 1]

    sorted_indices = np.lexsort((y2_vals, y1_vals))
    sorted_lines = lines_array[sorted_indices]

    current_cluster = []
    current_y1 = sorted_lines[0][0][1]
    current_y2 = sorted_lines[0][1][1]

    for line in sorted_lines:
        x1, y1 = line[0]
        x2, y2 = line[1]
        normalized_line = normalize_line(((x1, y1), (x2, y2)))

        if normalized_line in unique_lines:
            continue

        if abs(current_y1 - y1) <= y_tolerance and abs(current_y2 - y2) <= y_tolerance:
            current_cluster.append(normalized_line)
            unique_lines.add(normalized_line)
        else:
            clusters[(current_y1, current_y2)].extend(current_cluster)
            current_cluster = [normalized_line]
            unique_lines.add(normalized_line)
            current_y1, current_y2 = y1, y2

    clusters[(current_y1, current_y2)].extend(current_cluster)

    return list(clusters.values())


def find_horizontals_for_vertical_cluster(
    vertical_cluster: List[T_LINE_SEGMENT], horizontal_lines: List[T_LINE_SEGMENT], x_tolerance: float
) -> List[T_LINE_SEGMENT]:
    """
    Find horizontal lines that connect to a cluster of vertical lines.

    This function identifies horizontal lines that span across a cluster of vertical lines,
    with endpoints that are within the specified tolerance of the leftmost and rightmost
    vertical lines in the cluster.

    Args:
        vertical_cluster: Cluster of vertical line segments
        horizontal_lines: List of horizontal line segments to search
        x_tolerance: Maximum allowed difference in x-coordinates for considering a horizontal line as connected

    Returns:
        List of horizontal line segments that connect to the vertical cluster
    """
    if len(vertical_cluster) == 0:
        return []

    unique_lines = set()

    cluster_array = np.array(vertical_cluster, dtype=np.float32).reshape(-1, 2, 2)

    min_x = np.min(cluster_array[:, :, 0])
    max_x = np.max(cluster_array[:, :, 0])

    min_y = np.min(cluster_array[:, :, 1])
    max_y = np.max(cluster_array[:, :, 1])

    horizontal_array = np.array(horizontal_lines, dtype=np.float32).reshape(-1, 2, 2)

    x1_vals = horizontal_array[:, 0, 0]
    x2_vals = horizontal_array[:, 1, 0]
    y_vals = horizontal_array[:, 0, 1]

    mask = (
        (min_y <= y_vals)
        & (y_vals <= max_y)
        & (np.abs(x1_vals - min_x) <= x_tolerance)
        & (np.abs(x2_vals - max_x) <= x_tolerance)
    )
    valid_horizontals = horizontal_array[mask]

    valid_horizontals_list = []
    for h in valid_horizontals:
        normalized_line = normalize_line((tuple(h[0]), tuple(h[1])))
        if normalized_line not in unique_lines:
            valid_horizontals_list.append(normalized_line)
            unique_lines.add(normalized_line)

    return valid_horizontals_list


def normalize_line(line: T_LINE_SEGMENT) -> T_LINE_SEGMENT:
    """
    Normalize a line segment by ensuring its endpoints are in a consistent order.

    This function ensures that the first endpoint is always the one with the smaller
    coordinates (comparing x first, then y), which helps with line comparison and deduplication.

    Args:
        line: Line segment as ((x1, y1), (x2, y2))

    Returns:
        Normalized line segment with endpoints in a consistent order
    """
    (x1, y1), (x2, y2) = line
    if (x1, y1) > (x2, y2):
        return (x2, y2), (x1, y1)
    return (x1, y1), (x2, y2)


def extend_lines(
    lines: List[T_LINE_SEGMENT], direction: LineDirection, proximity_tolerance: float
) -> List[T_LINE_SEGMENT]:
    """
    Extend and merge line segments that are collinear and close to each other.

    This function identifies line segments that are aligned and close to each other,
    and merges them into longer line segments. This helps simplify the representation
    of lines that may have been fragmented during extraction.

    Args:
        lines: List of line segments to extend and merge
        direction: Direction of the lines (HORIZONTAL or VERTICAL)
        proximity_tolerance: Maximum allowed distance between line segments for merging

    Returns:
        List of extended line segments

    Raises:
        ValueError: If the direction is not HORIZONTAL or VERTICAL
    """
    lines_array = np.array(lines, dtype=np.float32)

    if direction == LineDirection.HORIZONTAL:
        sorted_indices = np.lexsort((lines_array[:, 0, 0], lines_array[:, 0, 1]))
        sorted_lines = lines_array[sorted_indices]
    elif direction == LineDirection.VERTICAL:
        sorted_indices = np.lexsort((lines_array[:, 0, 1], lines_array[:, 0, 0]))
        sorted_lines = lines_array[sorted_indices]
    else:
        raise ValueError(f"Invalid direction: {direction}")

    extended_lines = []
    current_line = None

    for line in sorted_lines:
        x1, y1 = line[0]
        x2, y2 = line[1]

        if current_line is None:
            if direction == LineDirection.HORIZONTAL:
                current_line = np.array([x1, x2, y1])
            elif direction == LineDirection.VERTICAL:
                current_line = np.array([y1, y2, x1])

        else:
            if direction == LineDirection.HORIZONTAL:
                if (
                    abs(y1 - current_line[2]) <= proximity_tolerance
                    and abs(current_line[1] - x1) <= proximity_tolerance
                ):
                    current_line[1] = max(current_line[1], x2)
                else:
                    extended_lines.append(((current_line[0], current_line[2]), (current_line[1], current_line[2])))
                    current_line = np.array([x1, x2, y1])

            elif direction == LineDirection.VERTICAL:
                if (
                    abs(x1 - current_line[2]) <= proximity_tolerance
                    and abs(current_line[1] - y1) <= proximity_tolerance
                ):
                    current_line[1] = max(current_line[1], y2)
                else:
                    extended_lines.append(((current_line[2], current_line[0]), (current_line[2], current_line[1])))
                    current_line = np.array([y1, y2, x1])

    if current_line is not None:
        if direction == LineDirection.HORIZONTAL:
            extended_lines.append(((current_line[0], current_line[2]), (current_line[1], current_line[2])))
        elif direction == LineDirection.VERTICAL:
            extended_lines.append(((current_line[2], current_line[0]), (current_line[2], current_line[1])))

    return extended_lines
