"""Tests for FastAPI Agent Service authentication."""
import pytest
from unittest.mock import AsyncMock, patch
from fastapi import HTT<PERSON>Ex<PERSON>
from fastapi.testclient import TestClient

from qw_agent_service.auth import AuthenticationService
from qw_agent_service.config import AgentServiceConfig
from qw_shared_auth.models import AuthContext, AuthConfig, SessionInfo
from qw_log_interface import NO_LOG_FACTORY


@pytest.fixture
def mock_auth_config():
    """Create a mock auth configuration."""
    return AuthConfig(
        runtime_service_url="http://test-runtime:8000",
        session_validation_timeout=10.0,
        session_cache_ttl=300
    )


@pytest.fixture
def mock_agent_config(mock_auth_config):
    """Create a mock agent configuration."""
    return AgentServiceConfig(
        openai_api_key="test-key",
        runtime_service_url="http://test-runtime:8000",
        auth_config=mock_auth_config,
        use_fastapi_agent=True
    )


@pytest.fixture
def auth_service(mock_agent_config):
    """Create an authentication service for testing."""
    return AuthenticationService.from_config(mock_agent_config, NO_LOG_FACTORY)


@pytest.mark.asyncio
async def test_validate_request_success(auth_service):
    """Test successful request validation."""
    # Mock the session validator
    mock_auth_context = AuthContext(
        session_uuid="test-session",
        subject="test-user",
        issuer="test-issuer",
        access_token="test-token",
        session_info=SessionInfo(
            issuer="test-issuer",
            subject="test-user",
            expiration_access_utc="2024-12-31T23:59:59Z",
            expiration_refresh_utc="2024-12-31T23:59:59Z"
        )
    )
    
    auth_service.session_validator.validate_session = AsyncMock(return_value=mock_auth_context)
    
    # Mock request with session cookie
    mock_request = AsyncMock()
    mock_request.cookies = {"session": "test-session-token"}
    
    result = await auth_service.validate_request(mock_request)
    
    assert result == mock_auth_context
    auth_service.session_validator.validate_session.assert_called_once_with("test-session-token")


@pytest.mark.asyncio
async def test_validate_request_missing_cookie(auth_service):
    """Test request validation with missing session cookie."""
    mock_request = AsyncMock()
    mock_request.cookies = {}
    
    with pytest.raises(HTTPException) as exc_info:
        await auth_service.validate_request(mock_request)
    
    assert exc_info.value.status_code == 401
    assert "Session cookie required" in exc_info.value.detail


@pytest.mark.asyncio
async def test_validate_request_invalid_session(auth_service):
    """Test request validation with invalid session."""
    auth_service.session_validator.validate_session = AsyncMock(return_value=None)
    
    mock_request = AsyncMock()
    mock_request.cookies = {"session": "invalid-session-token"}
    
    with pytest.raises(HTTPException) as exc_info:
        await auth_service.validate_request(mock_request)
    
    assert exc_info.value.status_code == 401
    assert "Invalid or expired session" in exc_info.value.detail


def test_auth_service_from_config(mock_agent_config):
    """Test creating auth service from configuration."""
    service = AuthenticationService.from_config(mock_agent_config, NO_LOG_FACTORY)
    
    assert service.config == mock_agent_config
    assert service.session_validator is not None
