"""MCP-enhanced RouterAgent implementation with FastMCP client integration."""
import asyncio
from typing import Any, Dict, Optional, Union, cast, List
from uuid import UUID, uuid4

from pydantic_ai import Agent as PydanticAgent, RunContext
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage
from fastmcp import Client

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.models import AgentPromptRequest, AgentPromptResponse, AgentAction
from qw_agent_service.config import AgentServiceConfig


class MinimalAgentContext:
    """Context for the MCP-enhanced agent."""

    def __init__(self, session_id: str, user_context: Dict[str, Any]):
        self.session_id = session_id
        self.user_context = user_context
from qw_agent_service.minimal_agent import MinimalAgentContext, MinimalAgentResponse


class MCPEnhancedAgent:
    """
    MCP-enhanced RouterAgent that can use internal API tools via FastMCP client.
    """

    def __init__(
        self,
        api_key: str,
        mcp_server_url: str,
        model_name: str = "gpt-4o",
        temperature: float = 0.2,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the MCP-enhanced router agent.
        """
        self.api_key = api_key
        self.mcp_server_url = mcp_server_url
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout
        self.logger = lf.get_logger(__name__)
        self.lf = lf
        self.agent_available = False
        self.usage = Usage()

        # Session management
        self.session_token: Optional[str] = None

        self._configure_agent()

    async def _setup_mcp_client(self) -> None:
        """Setup MCP client connection (simplified for now)."""
        try:
            self.logger.info(f"MCP server URL configured: {self.mcp_server_url}")
            # For now, just log that MCP is configured
            # The actual MCP integration will be handled by the MCP server

        except Exception as e:
            self.logger.error(f"Failed to setup MCP client: {e}")

    def _configure_agent(self) -> None:
        """
        Configure the pydantic-ai agent with MCP tools.
        """
        try:
            # Prepare model settings
            model_settings = ModelSettings(
                temperature=self.temperature,
                timeout=self.timeout,
            )

            # Setup OpenAI model with provider
            provider = OpenAIProvider(api_key=self.api_key)

            # Extract model name without provider prefix if needed
            model_name = self.model_name
            if model_name.startswith("openai:"):
                model_name = model_name.split(":")[-1]

            model = OpenAIModel(model_name, provider=provider)

            # Create the agent with MCP tools
            self.agent = PydanticAgent[MinimalAgentContext, str](
                model=model,
                model_settings=model_settings,
                retries=2,
                system_prompt=self._get_system_prompt(),
                deps_type=MinimalAgentContext,
            )

            # Add MCP tools to the agent
            self._register_mcp_tools()

            self.agent_available = True
            self.logger.info("MCP-enhanced router agent configured successfully")

        except Exception as e:
            self.logger.error(f"Failed to configure MCP-enhanced router agent: {e}")
            self.agent_available = False

    def _register_mcp_tools(self) -> None:
        """Register basic tools with the pydantic-ai agent."""

        @self.agent.tool
        async def set_session_token(ctx: RunContext[MinimalAgentContext], token: str) -> str:
            """Set the session token for API authentication."""
            self.session_token = token
            self.logger.info(f"Session token set: {token[:10]}...")
            return f"Session token configured successfully"

        @self.agent.tool
        async def get_system_status(ctx: RunContext[MinimalAgentContext]) -> str:
            """Get the current system status."""
            return "System is operational. MCP integration available."

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the MCP-enhanced router agent.
        """
        return """
        You are a helpful AI assistant for a quality management system with access to internal APIs.

        You can help users with:
        - General questions about quality management
        - Information about inspection processes
        - Guidance on material certificates and technical drawings
        - Accessing and searching materials in the system
        - Workflow assistance

        You have access to the following tools:
        - set_session_token: Set authentication token for API access
        - get_api_health: Check system health
        - list_materials: List materials for a tenant
        - search_materials: Search materials by query

        Before accessing any data, you may need to set a session token for authentication.
        Always provide helpful, accurate responses and use the available tools when appropriate.

        Keep your responses concise and professional.
        """

    async def set_session_token(self, token: str) -> None:
        """Set session token for API authentication."""
        self.session_token = token
        self.logger.info(f"Session token set for agent: {token[:10]}...")

    async def process(
        self,
        prompt: str,
        context: Optional[Dict[str, Any]] = None,
        usage: Optional[Usage] = None,
    ) -> MinimalAgentResponse:
        """
        Process a user prompt and return a response with MCP tool access.
        """
        if not self.agent_available:
            self.logger.error("Agent not available for processing")
            return MinimalAgentResponse(
                message="I'm currently unavailable. Please try again later.",
                actions=[]
            )

        # Setup MCP client if not already done
        await self._setup_mcp_client()

        try:
            # Prepare context
            session_id = context.get("session_id", "unknown") if context else "unknown"
            user_context = context.get("user_context", {}) if context else {}
            agent_context = MinimalAgentContext(session_id, user_context)

            # Run the agent with MCP tools available
            result = await self.agent.run(prompt, deps=agent_context, usage=usage)

            # Return response
            response_message = result.output if result.output else "I'm here to help! How can I assist you?"

            self.logger.info(f"MCP-enhanced agent processed prompt successfully: {len(response_message)} chars")

            return MinimalAgentResponse(
                message=response_message,
                actions=[]  # Actions could be extracted from tool calls in the future
            )

        except Exception as e:
            self.logger.error(f"Error processing prompt with MCP-enhanced agent: {e}")
            return MinimalAgentResponse(
                message="I encountered an error processing your request. Please try again.",
                actions=[]
            )

    async def cleanup(self) -> None:
        """Cleanup resources."""
        self.logger.info("Cleaning up MCP-enhanced agent resources")

    @classmethod
    def from_config(cls, config: AgentServiceConfig, mcp_server_url: str, lf: LogFactory = NO_LOG_FACTORY) -> "MCPEnhancedAgent":
        """
        Create MCPEnhancedAgent from configuration.
        """
        return cls(
            api_key=config.openai_api_key,
            mcp_server_url=mcp_server_url,
            model_name=config.agent_model_name,
            temperature=config.agent_temperature,
            timeout=config.agent_timeout,
            lf=lf
        )
