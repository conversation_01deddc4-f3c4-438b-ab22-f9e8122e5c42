"""
PMI (Product Manufacturing Information) data models.
This module provides data classes and enums for PMI classification.
"""
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from pydantic import BaseModel, Field
from pydantic.dataclasses import dataclass

# Type alias for vertex coordinates
Vertex = Dict[str, int]
Vertices = List[Vertex]


class BoundingPoly(BaseModel):
    """Model for bounding polygon of detected text"""

    vertices: List[Dict[str, int]] = Field(description="List of vertices defining the bounding polygon")


class TextElement(BaseModel):
    """Model for individual text element with bounding polygon"""

    text: str = Field(description="Detected text")
    bounding_poly: BoundingPoly = Field(description="Bounding polygon for the text")


class TextBlock(BaseModel):
    """Model for text block containing multiple text elements"""

    text: str = Field(description="Text in this block")
    bounding_poly: BoundingPoly = Field(description="Bounding polygon for the block")
    elements: List[TextElement] = Field(default_factory=list, description="Individual text elements in this block")


class DocumentText(BaseModel):
    """Model for document text detected from Google Vision API"""

    text: str = Field(description="Detected document text from Google Vision API")
    text_blocks: List[TextBlock] = Field(default_factory=list, description="List of text blocks with bounding boxes")


class PmiClassification(str, Enum):
    """Enum for PMI classification types"""

    PMI_GRID_NOTATIONS = "pmi_grid_notations"  # Drawing grid notations
    PMI_EXPLICIT_STANDARDS = "pmi_explicit_standards"  # ISO, DIN, EN standards
    PMI_OUT_OF_SCALE = "pmi_out_of_scale"  # Text out of scale
    PMI_TEXT_ONLY = "pmi_text_only"  # Pure text blocks
    PMI_NO_VALUE = "pmi_no_value"  # Patterns like "10:1"
    PMI_CANDIDATE = "pmi_candidate"  # PMI candidates for further analysis


@dataclass
class EdgeConfig:
    """Configuration for edge alignment detection"""

    coord_key: str = Field(description="Coordinate key to use for alignment (center_x or center_y)")
    sort_key: Callable[[Any], float] = Field(description="Function to sort alignment lines")
    description: str = Field(description="Description of the edge (e.g., 'topmost', 'leftmost')")


@dataclass
class ClassifiedTextElement:
    """Model for text element with PMI classification"""

    text: str = Field(description="Detected text")
    bounding_poly: BoundingPoly = Field(description="Bounding polygon for the text")
    classification: PmiClassification = Field(
        default=PmiClassification.PMI_CANDIDATE, description="PMI classification for this text element"
    )


class ClassifiedTextBlock(BaseModel):
    """Model for text block with PMI classification"""

    text: str = Field(description="Text in this block")
    bounding_poly: BoundingPoly = Field(description="Bounding polygon for the block")
    elements: List[ClassifiedTextElement] = Field(
        default_factory=list, description="Individual text elements in this block with classification"
    )
    classification: PmiClassification = Field(
        default=PmiClassification.PMI_CANDIDATE, description="PMI classification for this text block"
    )
    expanded_bounding_poly: Optional[BoundingPoly] = Field(
        default=None, description="Expanded bounding polygon for PMI_CANDIDATE blocks"
    )


class ClassifiedDocumentText(BaseModel):
    """Model for document text with PMI classification"""

    text: str = Field(description="Detected document text from Google Vision API")
    text_blocks: List[ClassifiedTextBlock] = Field(
        default_factory=list, description="List of text blocks with PMI classification"
    )


@dataclass
class BlockInfo:
    """Information about a text block with its center position and location in the document"""

    block: TextBlock
    center_x: float
    center_y: float
    in_top: bool
    in_bottom: bool
    in_left: bool
    in_right: bool
    is_grid_pattern: bool


@dataclass
class AlignmentLine:
    """Represents an alignment line with its coordinate and associated blocks"""

    coordinate: float
    blocks: List[TextBlock]
