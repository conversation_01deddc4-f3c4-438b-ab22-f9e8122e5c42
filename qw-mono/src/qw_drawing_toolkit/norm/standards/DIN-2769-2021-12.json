{"name": "DIN_2769:2021-12", "tables": [{"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["a"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["a"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["a"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["a"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["a"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["a"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["a"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["a"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["b"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["b"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["b"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["b"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["b"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["b"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["b"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["b"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["c"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["d"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["d"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["d"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["d"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["d"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["d"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2000, "inclusive": true}, "rowIdentifiers": [["d"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 2000, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["d"]], "tolerance": {"upper": 8, "lower": -8}}]}, {"name": "Tabelle 3", "valueType": "angular", "valueUnit": "deg", "toleranceUnit": "deg", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["1 <10"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["2 <10"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["3 <10"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["1 <50"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["2 <50"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["3 <50"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["1 <120"]], "tolerance": {"upper": 0.333333333, "lower": -0.333333333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["2 <120"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["3 <120"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["1 <400"]], "tolerance": {"upper": 0.166666667, "lower": -0.166666667}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["2 <400"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["3 <400"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["1 >400"]], "tolerance": {"upper": 0.083333333, "lower": -0.083333333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["2 >400"]], "tolerance": {"upper": 0.166666667, "lower": -0.166666667}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["3 >400"]], "tolerance": {"upper": 0.333333333, "lower": -0.333333333}}]}]}