import collections
import io
import json
import re
from typing import Any, BinaryIO, Callable, Dict, Generic, List, Sequence, Type, TypeVar

import falcon
import falcon.media
from pydantic import BaseModel

from qw_falcon_openapi.common import map_model_or_raise_http_error
from qw_falcon_openapi.error import FalconOpenApiBadConfig
from qw_falcon_openapi.operation import (
    T_HEADERS,
    T_OUT,
    T_PATH_PARAMS,
    T_QUERY_PARAMS,
    OpenApiOpOutput,
    OpenApiOpParams,
    OpenApiOpSpecOptions,
    check_header_properties_are_lowercase,
)
from qw_falcon_openapi.spec import OpenApiMimeType
from qw_log_interface import NO_LOGGER, Logger


class MultipartBodypart(object):
    def __init__(self, data: bytes, content_type: str, json_loader: Callable[[BinaryIO], Dict[str, Any]] = json.load):
        self.data = data
        self.__content_type = content_type
        self.json_loader = json_loader

    @classmethod
    def read_and_wrap(cls, bp: falcon.media.multipart.BodyPart) -> "MultipartBodypart":
        return cls(data=bp.data, content_type=bp.content_type)

    @property
    def content_type(self) -> str:
        return self.__content_type.split(";")[0]

    @property
    def charset(self) -> str | None:
        m = re.match(r".*charset=(?P<CHARSET>[a-zA-Z0-9_-]+).*", self.__content_type)
        if m is None:
            return None
        return m.group("CHARSET")

    @property
    def text(self) -> str:
        encoding = self.charset or "utf-8"
        return self.data.decode(encoding)

    @property
    def json(self) -> Dict[str, Any]:
        return self.json_loader(io.BytesIO(self.data))


BodyParts = Dict[str, List[MultipartBodypart]]


class MultipartBinaryPartDef(object):
    def __init__(
        self,
        name: str,
        mime_type: OpenApiMimeType = OpenApiMimeType.OCTET_STREAM,
        expect_array: bool = False,
        expect_array_min_length: int = 1,
        logger: Logger = NO_LOGGER,
    ):
        self.name = name
        self.mime_type = mime_type
        self.expect_array = expect_array
        self.expect_array_min_length = expect_array_min_length
        self.logger = logger

    def get_parts(self, parts: BodyParts) -> List[MultipartBodypart]:
        return parts.get(self.name, [])

    def get_part(self, parts: BodyParts) -> MultipartBodypart:
        selected_parts = self.get_parts(parts)
        if len(selected_parts) == 0:
            self.logger.error(f"Did not receive a part for name {self.name}")
            raise falcon.HTTPBadRequest()
        if len(selected_parts) > 1:
            self.logger.warning(f"Only one instance of part {self.name} is used, but there are {len(selected_parts)}")
        return selected_parts[0]

    def get_binary(self, parts: BodyParts) -> bytes:
        return self.get_part(parts).data

    def get_binaries(self, parts: BodyParts) -> List[bytes]:
        selected_parts = self.get_parts(parts)
        return [sp.data for sp in selected_parts]

    def get_type(self) -> Type[Any]:
        if self.expect_array:
            return List[bytes]
        else:
            return bytes


class MultipartTextPartDef(MultipartBinaryPartDef):
    def __init__(
        self,
        name: str,
        mime_type: OpenApiMimeType,
        expect_array: bool = False,
        expect_array_min_length: int = 1,
        logger: Logger = NO_LOGGER,
    ):
        super().__init__(name, mime_type, expect_array, expect_array_min_length, logger)

    def get_texts(self, parts: BodyParts) -> List[str]:
        relevant_parts = self.get_parts(parts)
        return [p.text for p in relevant_parts]

    def get_text(self, parts: BodyParts) -> str:
        part = self.get_part(parts)
        return part.text

    def get_type(self) -> Type[Any]:
        if self.expect_array:
            return List[str]
        else:
            return str


T_MODEL = TypeVar("T_MODEL", bound=BaseModel)


class MultipartJsonPartDef(MultipartBinaryPartDef, Generic[T_MODEL]):
    def __init__(self, name: str, model_type: Type[T_MODEL], logger: Logger = NO_LOGGER):
        super().__init__(name, OpenApiMimeType.JSON, expect_array=False, logger=logger)
        self.model_type = model_type

    def get_model(self, parts: BodyParts) -> T_MODEL:
        part = self.get_part(parts)
        data: Dict[str, Any] = part.json
        return map_model_or_raise_http_error(self.model_type, data, error_place_hint=self.name)

    def get_type(self) -> Type[Any]:
        return self.model_type


class MultipartBodyDefinition(object):
    """https://www.rfc-editor.org/rfc/rfc7578"""

    def __init__(self, part_defs: Sequence[MultipartBinaryPartDef]):
        counter = collections.Counter([pd.name for pd in part_defs])
        for name, count in counter.items():
            if count > 1:
                raise FalconOpenApiBadConfig(f"Part name '{name}' is used {count} times")
        self.part_defs: Dict[str, MultipartBinaryPartDef] = {pd.name: pd for pd in part_defs}


class OpenApiOpMultipartInJsonOut(Generic[T_OUT, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        def_in: MultipartBodyDefinition,
        t_out: Type[T_OUT],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
    ):
        self.def_in = def_in
        self.t_out = t_out
        self.t_params_path = t_params_path
        self.t_params_query = t_params_query
        self.t_params_headers = t_params_headers
        self.spec_options = OpenApiOpSpecOptions()
        check_header_properties_are_lowercase(t_params_headers)

    def on_request(
        self, body_parts: BodyParts, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]
    ) -> OpenApiOpOutput[T_OUT]:
        raise NotImplementedError()
