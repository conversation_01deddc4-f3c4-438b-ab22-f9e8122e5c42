{"name": "DIN_EN_12020-2:2023-02", "tables": [{"name": "Tabelle 1", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 45, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 45, "inclusive": false}, "upperValueLimit": {"value": 60, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 60, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 240, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 240, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 350, "inclusive": true}, "rowIdentifiers": [["ausg. offene <PERSON>"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 45, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 45, "inclusive": false}, "upperValueLimit": {"value": 60, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 60, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 240, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 240, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 350, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 30, "inclusive": true}, "upperValueLimit": {"value": 45, "inclusive": true}, "rowIdentifiers": [["E <= 120"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 45, "inclusive": false}, "upperValueLimit": {"value": 60, "inclusive": true}, "rowIdentifiers": [["E <= 120"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 60, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["E <= 120"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["E <= 120"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 120"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["E <= 120"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 240, "inclusive": true}, "rowIdentifiers": [["E <= 120"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 240, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["E <= 120"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 350, "inclusive": true}, "rowIdentifiers": [["E <= 120"]], "tolerance": {"upper": 2.1, "lower": -2.1}}]}, {"name": "Tabelle 10", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["<PERSON>bwei<PERSON><PERSON>"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 5, "inclusive": true}, "rowIdentifiers": [["<PERSON>bwei<PERSON><PERSON>"]], "tolerance": {"upper": 0.5, "lower": -0.5}}]}, {"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["A CD <= 350"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A CD <= 350"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A CD <= 350"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A CD <= 350"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A CD <= 350"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A CD <= 350"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A CD <= 350"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A CD <= 350"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["B CD <= 350"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B CD <= 350"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B CD <= 350"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B CD <= 350"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B CD <= 350"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B CD <= 350"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["C CD <= 350"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C CD <= 350"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C CD <= 350"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C CD <= 350"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C CD <= 350"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C CD <= 350"]], "tolerance": {"upper": 1.5, "lower": -1.5}}]}, {"name": "Tabelle 3", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L <= 2000"]], "tolerance": {"upper": 5, "lower": 0}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L <= 2000"]], "tolerance": {"upper": 7, "lower": 0}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 350, "inclusive": true}, "rowIdentifiers": [["L <= 2000"]], "tolerance": {"upper": 8, "lower": 0}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L <= 5000"]], "tolerance": {"upper": 7, "lower": 0}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L <= 5000"]], "tolerance": {"upper": 9, "lower": 0}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 350, "inclusive": true}, "rowIdentifiers": [["L <= 5000"]], "tolerance": {"upper": 11, "lower": 0}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L <= 10000"]], "tolerance": {"upper": 10, "lower": 0}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L <= 10000"]], "tolerance": {"upper": 12, "lower": 0}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 350, "inclusive": true}, "rowIdentifiers": [["L <= 10000"]], "tolerance": {"upper": 14, "lower": 0}}]}, {"name": "Tabelle 8", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": []}]}