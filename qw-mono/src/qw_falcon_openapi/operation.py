import io
from datetime import datetime
from typing import BinaryIO, Dict, Generic, List, Literal, Protocol, Sequence, Tuple, Type, TypeVar

import falcon
from pydantic import BaseModel, Field

from qw_falcon_openapi.spec import OpenApiApiKeySecurityScheme, OpenApiMimeType

T_IN = TypeVar("T_IN", bound=BaseModel)
T_OUT = TypeVar("T_OUT", bound=BaseModel)
T_PATH_PARAMS = TypeVar("T_PATH_PARAMS", bound=BaseModel)  # https://datatracker.ietf.org/doc/html/rfc3986#section-3
T_QUERY_PARAMS = TypeVar("T_QUERY_PARAMS", bound=BaseModel)  # https://datatracker.ietf.org/doc/html/rfc3986#section-3
T_HEADERS = TypeVar("T_HEADERS", bound=BaseModel)


class Cookie(BaseModel):
    name: str
    value: str
    expires: datetime | None = None
    max_age_seconds: int | None = None
    domain: str | None = None
    path: str | None = None
    secure: bool = True
    http_only: bool = True
    same_site: Literal["Strict", "Lax", "None"] = "None"


class ReadableStream(Protocol):
    content_length: int = 0

    def read(self, size: int = -1) -> bytes:
        ...

    def tell(self) -> int:
        ...


class ReadableStreamFromBytes(object):
    def __init__(self, payload: bytes):
        self.content_length = len(payload)
        self.bio = io.BytesIO(payload)

    def read(self, size: int = -1) -> bytes:
        return self.bio.read(size)

    def tell(self) -> int:
        return self.bio.tell()


class OpenApiOpBinaryOutput(object):
    def __init__(
        self,
        data: ReadableStream | str,
        status_code: int = 200,
        headers: Dict[str, str] | None = None,
        cookies: Sequence[Cookie] = (),
        content_type: str | None = None,
    ):
        self.status_code = status_code
        self.data = data
        self.headers: Dict[str, str] = headers or {}
        self.content_type = content_type
        self.cookies = cookies

    @classmethod
    def apply_headers(cls, resp: falcon.Response, headers: Dict[str, str]) -> None:
        for h_name, h_val in headers.items():
            resp.set_header(h_name, h_val)

    @classmethod
    def apply_cookies(cls, resp: falcon.Response, cookies: Sequence[Cookie]) -> None:
        for c in cookies:
            resp.set_cookie(
                name=c.name,
                value=c.value,
                expires=c.expires,
                max_age=c.max_age_seconds,
                domain=c.domain,
                path=c.path,
                secure=c.secure,
                http_only=c.http_only,
                same_site=c.same_site,
            )

    def apply(self, resp: falcon.Response) -> None:
        if isinstance(self.data, str):
            resp.text = self.data
        else:
            if self.data.tell() != 0:
                raise ValueError(
                    f"Stream for content_type={self.content_type} size={self.data.content_length} is not set to 0"
                )
            resp.set_stream(self.data, self.data.content_length)

        self.apply_headers(resp, self.headers)
        self.apply_cookies(resp, self.cookies)
        resp.content_type = self.content_type
        resp.status = falcon.code_to_http_status(self.status_code)


class OpenApiOpOutput(Generic[T_OUT]):
    def __init__(
        self,
        data: T_OUT,
        status_code: int = 200,
        headers: Dict[str, str] | None = None,
        cookies: Sequence[Cookie] = (),
    ):
        self.status_code = status_code
        self.data = data
        self.headers: Dict[str, str] = headers or {}
        self.cookies = cookies

    def apply(self, resp: falcon.Response) -> None:
        resp.data = self.data.model_dump_json(by_alias=True).encode("utf-8")
        OpenApiOpBinaryOutput.apply_headers(resp, self.headers)
        OpenApiOpBinaryOutput.apply_cookies(resp, self.cookies)
        resp.content_type = falcon.MEDIA_JSON
        resp.status = falcon.code_to_http_status(self.status_code)


class OpenApiOpParams(Generic[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    path: T_PATH_PARAMS
    query: T_QUERY_PARAMS
    headers: T_HEADERS
    cookies: Dict[str, str]

    def __init__(self, path: T_PATH_PARAMS, query: T_QUERY_PARAMS, headers: T_HEADERS, cookies: Dict[str, str]):
        self.path = path
        self.query = query
        self.headers = headers
        self.cookies = cookies


class NoParams(BaseModel):
    pass


NoOpenApiParams = OpenApiOpParams[NoParams, NoParams, NoParams]


class OpenApiOpSpecOptions(BaseModel):
    op_id: str | None = None
    share_params_query: List[str] = Field(default_factory=list)
    share_params_headers: List[str] = Field(default_factory=list)
    security_schemes: List[OpenApiApiKeySecurityScheme] | None = None
    responses_error: Dict[int, str] = Field(default_factory=dict)

    def add_security_scheme(self, scheme: OpenApiApiKeySecurityScheme) -> None:
        if self.security_schemes is None:
            self.security_schemes = []
        self.security_schemes.append(scheme)


class OpenApiOpSpecOptionsBinaryOut(OpenApiOpSpecOptions):
    responses: Dict[int, Tuple[str, Sequence[OpenApiMimeType]]] = Field(default_factory=dict)


def check_header_properties_are_lowercase(headers_type: Type[BaseModel]) -> None:
    for name, mf in headers_type.__pydantic_fields__.items():
        name_type = "name"
        if mf.alias is not None:
            name = mf.alias
            name_type = "alias"
        if not name.islower():
            raise ValueError(f"Property {name_type} of headers model {headers_type.__name__} is not lowercase")


class OpenApiOpJsonOut(Generic[T_OUT, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        t_out: Type[T_OUT],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
    ):
        self.t_out = t_out
        self.t_params_path = t_params_path
        self.t_params_query = t_params_query
        self.t_params_headers = t_params_headers
        self.spec_options = OpenApiOpSpecOptions()
        check_header_properties_are_lowercase(t_params_headers)

    def on_request(self, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]) -> OpenApiOpOutput[T_OUT]:
        raise NotImplementedError()


class OpenApiOpJsonInJsonOut(Generic[T_IN, T_OUT, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        t_in: Type[T_IN],
        t_out: Type[T_OUT],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
    ):
        self.t_in = t_in
        self.t_out = t_out
        self.t_params_path = t_params_path
        self.t_params_query = t_params_query
        self.t_params_headers = t_params_headers
        self.spec_options = OpenApiOpSpecOptions()
        check_header_properties_are_lowercase(t_params_headers)

    def on_request(
        self, body: T_IN, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]
    ) -> OpenApiOpOutput[T_OUT]:
        raise NotImplementedError()


class OpenApiOpBinaryInJsonOut(Generic[T_OUT, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        mime_types_in: Sequence[OpenApiMimeType],
        t_out: Type[T_OUT],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
    ):
        self.mime_types_in = mime_types_in
        self.t_out = t_out
        self.t_params_path = t_params_path
        self.t_params_query = t_params_query
        self.t_params_headers = t_params_headers
        self.spec_options = OpenApiOpSpecOptions()
        check_header_properties_are_lowercase(t_params_headers)

    def on_request(
        self, body: BinaryIO, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]
    ) -> OpenApiOpOutput[T_OUT]:
        raise NotImplementedError()


class OpenApiOpJsonInBinaryOut(Generic[T_IN, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        t_in: Type[T_IN],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
    ):
        self.t_in = t_in
        self.t_params_path = t_params_path
        self.t_params_query = t_params_query
        self.t_params_headers = t_params_headers
        self.spec_options = OpenApiOpSpecOptionsBinaryOut()
        check_header_properties_are_lowercase(t_params_headers)

    def on_request(
        self, body: T_IN, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]
    ) -> OpenApiOpBinaryOutput:
        raise NotImplementedError()


class OpenApiOpBinaryOut(Generic[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
    ):
        self.t_params_path = t_params_path
        self.t_params_query = t_params_query
        self.t_params_headers = t_params_headers
        self.spec_options = OpenApiOpSpecOptionsBinaryOut()
        check_header_properties_are_lowercase(t_params_headers)

    def on_request(self, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]) -> OpenApiOpBinaryOutput:
        raise NotImplementedError()
