class BoundingBoxItem {

    /**
     * @param {number} index
     * @param {DataWithBoundingBox} data
     * @param {boolean} isNew
     * @param {function(config: Object): Promise<string|null>} validateItemConfig
     */
    constructor(index, data, isNew, validateItemConfig) {
        this.index = index;
        this.data = data;
        this.validateItemConfig = validateItemConfig;
        this.resetJsonStrings();

        /**
         * @type {{msg: string, multiline: boolean} | null}
         * @public
         */
        this.jsonValidationError = null;

        /**
         * @type {boolean}
         * @private
         */
        this.isNew = isNew;
        /**
         * @type {boolean}
         * @private
         */
        this.isInSync = !isNew;
        /**
         * @type {boolean}
         * @private
         */
        this.isMarkedForDeletion = false;
    }

    resetJsonStrings() {
        this.jsonString = JSON.stringify(this.data, null, 2);
        this.jsonStringOriginalUnsorted = this.jsonString.slice(0);
        this.jsonStringOriginalSorted = BoundingBoxItem.sortedStringify(this.data, 0);
    }

    /**
     * @param {number} index
     * @return {BoundingBoxItem}
     */
    createCopy(index) {
        if (!this.canBeCopied) {
            throw new Error('Item can not be copied');
        }

        const newBbox = new BoundingBoxItem(index, JSON.parse(this.jsonString), true, this.validateItemConfig);
        newBbox.setBoundingBoxLocation(newBbox.x2, newBbox.y1);
        return newBbox;
    }

    /**
     * @param {Object} obj
     * @param {number} indent
     * @return {string}
     */
    static sortedStringify(obj, indent) {
        const sortKeys = (key, value) => {
            if (value instanceof Object && !(value instanceof Array)) {
                return Object.keys(value)
                    .sort()
                    .reduce((sorted, key) => {
                        sorted[key] = value[key];
                        return sorted
                    }, {})
            } else {
                return value;
            }
        };
        return JSON.stringify(obj, sortKeys, indent)
    }

    /**
     * @param {number} alpha
     * @return {string}
     */
    getCssColorString(alpha) {
        let r = 100;
        let g = 100;
        let b = 100;
        switch (this.data.annotationType) {
            case 'length':
                r = 255;
                g = 69;
                b = 0;
                break;
            case 'diameter':
            case 'diameter_thread':
            case 'diameter_countersink':
            case 'diameter_counterbore':
                r = 95;
                g = 158;
                b = 160;
                break;
            case 'radius':
                r = 72;
                g = 61;
                b = 139;
                break;
            case 'angle':
                r = 139;
                g = 0;
                b = 139;
                break;
            case 'roughness':
                r = 155;
                g = 155;
                b = 20;
                break;
            case 'gdt_frame':
            case 'gdt_datum':
                r = 0;
                g = 100;
                b = 0;
                break;
        }
        if (this.status === 'deleted') {
            r = 0;
            g = 0;
            b = 0;
        }
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }


    get canBeCopied() {
        return !this.isMarkedForDeletion;
    }

    /**
     * @return {'bad config' | 'deleted' | 'new' | 'synced' | 'changed'}
     */
    get status() {
        if (this.jsonValidationError !== null) {
            return 'bad config';
        }
        if (this.isMarkedForDeletion) {
            return 'deleted';
        }
        if (this.isNew) {
            return 'new';
        }
        return this.isInSync ? 'synced' : 'changed';
    }

    get x1() {
        return this.data.boundingBox.p1.x;
    }

    get y1() {
        return this.data.boundingBox.p1.y;
    }

    get x2() {
        return this.data.boundingBox.p2.x;
    }

    get y2() {
        return this.data.boundingBox.p2.y;
    }

    /**
     * @param {boolean} synced
     * @param {boolean} updateJson
     */
    setSyncStatus(synced, updateJson) {
        this.isInSync = synced;
        if (updateJson) {
            this.jsonString = JSON.stringify(this.data, null, 2);
        }
    }

    /**
     * @param {number} newX1
     * @param {number} newY1
     */
    setBoundingBoxLocation(newX1, newY1) {
        newX1 = Math.max(0, Math.min(1 - (this.x2 - this.x1), newX1));
        newY1 = Math.max(0, Math.min(1 - (this.y2 - this.y1), newY1));
        const deltaX = newX1 - this.x1;
        const deltaY = newY1 - this.y1;
        if (deltaX === 0 && deltaY === 0) {
            return;
        }
        this.data.boundingBox.p1.x += deltaX;
        this.data.boundingBox.p1.y += deltaY;
        this.data.boundingBox.p2.x += deltaX;
        this.data.boundingBox.p2.y += deltaY;
        this.setSyncStatus(false, true);
    }

    /**
     * @param {number} newX2
     * @param {number} newY2
     */
    setPoint2Location(newX2, newY2) {
        const minDelta = 0.005;
        newX2 = Math.max(this.data.boundingBox.p1.x + minDelta, Math.min(1, newX2));
        newY2 = Math.max(this.data.boundingBox.p1.y + minDelta, Math.min(1, newY2));
        if (this.data.boundingBox.p2.x === newX2 && this.data.boundingBox.p2.y === newY2) {
            return;
        }
        this.data.boundingBox.p2.x = newX2;
        this.data.boundingBox.p2.y = newY2;
        this.setSyncStatus(false, true);
    }

    /**
     * @param {string} newJsonString
     */
    async updateFromJsonString(newJsonString) {
        let newData = this.data;
        let newJsonValidationError = null;

        try {
            newData = JSON.parse(newJsonString);
        } catch (e) {
            newJsonValidationError = {
                msg: e.toString(),
                multiline: false,
            };
        }

        if (newJsonValidationError === null) {
            const backendValidationMsg = await this.validateItemConfig(newData);
            if (backendValidationMsg !== null) {
                newJsonValidationError = {
                    msg: backendValidationMsg,
                    multiline: true,
                };
            }
        }

        if (newJsonValidationError === null) {
            this.setSyncStatus(BoundingBoxItem.sortedStringify(newData, 0) === this.jsonStringOriginalSorted, false);
            this.data = newData;
        }
        this.jsonString = newJsonString;
        this.jsonValidationError = newJsonValidationError;
    }

    resetJsonStringToLastValid() {
        if (this.jsonValidationError !== null) {
            this.updateFromJsonString(JSON.stringify(this.data, null, 2)).then();
        }
    }

    resetJsonStringToOriginal() {
        this.isMarkedForDeletion = false;
        this.updateFromJsonString(this.jsonStringOriginalUnsorted.slice(0)).then();
    }

    resetAfterSync() {
        this.isNew = false;
        this.resetJsonStrings();
        this.resetJsonStringToOriginal();
    }

    markForDeletion() {
        this.resetJsonStringToOriginal();
        this.isMarkedForDeletion = true;
    }
}

class BoundingBoxDrag {

    /**
     * @param {MouseEvent} origin
     * @param {BoundingBoxItem} bboxItem
     * @param {HTMLElement} container
     * @param {boolean} onlyPoint2
     */
    constructor(origin, bboxItem, container, onlyPoint2) {
        this.container = container;
        this.bboxItem = bboxItem;
        this.originBboxLocation = onlyPoint2 ? {x: bboxItem.x2, y: bboxItem.y2} : {x: bboxItem.x1, y: bboxItem.y1};
        this.originMouseLocation = this.getNormalizedMouseLocation(origin);
        this.onlyPoint2 = onlyPoint2;
    }

    /**
     * @param {MouseEvent} event
     * @return {{x: number, y: number}}
     */
    getNormalizedMouseLocation(event) {
        const rect = this.container.getBoundingClientRect();
        // const x = ((event.clientX - rect.left) * this.container.width.baseVal.value) / rect.width;
        // const y = ((event.clientY - rect.top) * this.container.height.baseVal.value) / rect.height;
        const x = (event.clientX - rect.left) / rect.width;
        const y = (event.clientY - rect.top) / rect.height;
        return {x: x, y: y};
    }

    /**
     * @param {MouseEvent} event
     * @return {{x: number, y: number}}
     */
    getLocation(event) {
        const eventMouseLocation = this.getNormalizedMouseLocation(event);
        return {
            x: this.originBboxLocation.x + (eventMouseLocation.x - this.originMouseLocation.x),
            y: this.originBboxLocation.y + (eventMouseLocation.y - this.originMouseLocation.y),
        };
    }

    /**
     * @param {MouseEvent} event
     */
    updateBoundingBox(event) {
        const loc = this.getLocation(event);
        if (this.onlyPoint2) {
            this.bboxItem.setPoint2Location(loc.x, loc.y);
        } else {
            this.bboxItem.setBoundingBoxLocation(loc.x, loc.y);
        }
    }
}


class IdCounter {

    constructor() {
        this.count = 0;
    }

    nextId() {
        this.count += 1;
        return this.count;
    }
}


class DrawingState {

    /**
     * @param {string} drawingId
     * @param {string[]} pageImageUrls
     * @param {DrawingAnalysis} analysis
     * @param {function(image:HTMLImageElement, pageIndex:number)} updateImage
     * @param {function(config: Object): Promise<string|null>} validateItemConfig
     */
    constructor(drawingId, pageImageUrls, analysis, updateImage, validateItemConfig) {
        this.drawingId = drawingId;
        this.pageImageUrls = pageImageUrls;
        this.analysis = analysis;
        this.updateImage = updateImage;
        this.idCounter = new IdCounter();
        this.validateItemConfig = validateItemConfig;

        /**
         * @type {BoundingBoxItem[]}
         * @public
         */
        const items = [];
        items.push(...this.analysis.result.lengths.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        items.push(...this.analysis.result.angles.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        items.push(...this.analysis.result.radii.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        items.push(...this.analysis.result.diameters.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        items.push(...this.analysis.result.threads.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        items.push(...this.analysis.result.countersinks.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        items.push(...this.analysis.result.counterbores.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        items.push(...this.analysis.result.roughness.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        items.push(...this.analysis.result.gdtDatums.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        items.push(...this.analysis.result.gdtFrames.map(data => new BoundingBoxItem(this.idCounter.nextId(), data, false, validateItemConfig)));
        this.items = items;

        /**
         * @type {BoundingBoxItem | null}
         * @public
         */
        this.selectedBoxItem = null;

        /**
         * @type {BoundingBoxDrag | null}
         * @public
         */
        this.drag = null;

        /**
         * @type {number}
         * @public
         */
        this.pageIndex = 0;

        /**
         * @type {HTMLImageElement | null}
         * @public
         */
        this.pageImage = null;
        this.loadImage(this.pageIndex);
    }

    /**
     * @param {number} pageIndex
     */
    loadImage(pageIndex) {
        if (this.pageIndex === pageIndex && this.pageImage !== null) {
            return;
        }

        const image = new Image();
        image.onload = () => this.updateImage(image, pageIndex);
        image.src = this.pageImageUrls[pageIndex];
    }

    get pageCount() {
        return this.pageImageUrls.length;
    }

    previousPage() {
        this.loadImage(Math.max(this.pageIndex - 1, 0));
    }

    nextPage() {
        this.loadImage(Math.min(this.pageIndex + 1, this.pageCount - 1));
    }

    /**
     * @return {number}
     */
    getSelectedBoxIndex() {
        if (this.selectedBoxItem === null) {
            return -1;
        }
        return this.selectedBoxItem.index;
    }

    createNewItem() {
        /**
         * @type {DataWithBoundingBox}
         */
        const initData = {
            annotationType: 'length',
            boundingBox: {
                pageIndex: this.pageIndex,
                p1: {
                    x: 0.1,
                    y: 0.1,
                },
                p2: {
                    x: 0.3,
                    y: 0.3,
                }
            }
        };
        const newItem = new BoundingBoxItem(this.idCounter.nextId(), initData, true, this.validateItemConfig);
        this.items.push(newItem);
        this.selectedBoxItem = newItem;
    }

    copySelectedItem() {
        if (this.selectedBoxItem === null) {
            return;
        }
        const newItem = this.selectedBoxItem.createCopy(this.idCounter.nextId());
        this.items.push(newItem);
        this.selectedBoxItem = newItem;
    }

    deleteSelectedItem() {
        if (this.selectedBoxItem === null) {
            return;
        }
        const item = this.selectedBoxItem;
        this.selectedBoxItem = null;
        if (item.status === 'new') {
            const arrayIndex = this.items.map(item => item.index).indexOf(item.index);
            this.items.splice(arrayIndex, 1);
        } else {
            item.markForDeletion();
        }
    }

    /**
     * @param {number | null} pageIndex
     * @param {boolean} ignoreDeleted
     * @return {BoundingBoxItem[]}
     */
    getItems(pageIndex, ignoreDeleted) {
        if (pageIndex === null) {
            return this.items;
        }

        return this.items.filter(item => {
            if (ignoreDeleted && item.status === 'deleted') {
                return false;
            }
            return item.data.boundingBox.pageIndex === pageIndex
        });
    }

    /**
     * @return {number}
     */
    getItemChangeCount() {
        return this.items.filter(item => item.status !== 'synced').length || 0;
    }

    /**
     * @return {DrawingAnalysis}
     */
    getUpdatedAnalysis() {
        const data = this.items
            .filter(item => item.status !== 'deleted')
            .map(item => item.data)
            .sort((a, b) => ((a.index < b.index) ? -1 : ((a.index > b.index) ? 1 : 0)));

        /**
         * @type {DrawingAnalysis}
         */
        const analysis = {...this.analysis};
        analysis.result.lengths = data.filter(d => d.annotationType === 'length');
        analysis.result.radii = data.filter(d => d.annotationType === 'radius');
        analysis.result.diameters = data.filter(d => d.annotationType === 'diameter');
        analysis.result.threads = data.filter(d => d.annotationType === 'diameter_thread');
        analysis.result.countersinks = data.filter(d => d.annotationType === 'diameter_countersink');
        analysis.result.counterbores = data.filter(d => d.annotationType === 'diameter_counterbore');
        analysis.result.roughness = data.filter(d => d.annotationType === 'roughness');
        analysis.result.angles = data.filter(d => d.annotationType === 'angle');
        analysis.result.gdtFrames = data.filter(d => d.annotationType === 'gdt_frame');
        analysis.result.gdtDatums = data.filter(d => d.annotationType === 'gdt_datum');
        return analysis;
    }

    resetItemsAfterSync() {
        this.items = this.items.filter(item => item.status !== 'deleted');
        this.items.forEach(item => item.resetAfterSync());
    }

    /**
     * @param {MouseEvent} event
     * @param {BoundingBoxItem | null} boundingBoxItem
     * @param {string} dragAreaDomId
     * @param {boolean} onlyPoint2
     */
    startDrag(event, boundingBoxItem, dragAreaDomId, onlyPoint2) {
        if (boundingBoxItem !== null) {
            this.selectedBoxItem = boundingBoxItem;
        }
        if (this.selectedBoxItem === null || this.selectedBoxItem.status === 'deleted' || this.drag !== null || event.button !== 0) {
            return;
        }
        this.drag = new BoundingBoxDrag(event, this.selectedBoxItem, document.getElementById(dragAreaDomId), onlyPoint2);
    }

    /**
     * @param {MouseEvent} event
     */
    updateDrag(event) {
        if (this.selectedBoxItem === null || this.drag === null) {
            return;
        }
        this.drag.updateBoundingBox(event);
    }

    /**
     * @param {MouseEvent} event
     */
    endDrag(event) {
        this.updateDrag(event);
        this.drag = null;
    }
}

class EditorState {

    /**
     * @param {EditorApiClient} client
     */
    constructor(client) {
        this.client = client;
        /**
         * @type {DrawingState | null}
         * @public
         */
        this.drawing = null;
    }

    /**
     * @param {string} drawingId
     */
    async selectDrawing(drawingId) {
        if (this.drawing !== null && this.drawing.drawingId === drawingId) {
            return;
        }

        if (this.drawing !== null) {
            this.drawing.selectedBoxItem = null;
        }

        const info = await this.client.getDrawingInfo(drawingId);
        const analysis = await this.client.getDrawingAnalysis(drawingId);
        const pageImageUrls = [...Array(info.pageCount).keys()].map(pageIndex => this.client.buildDrawingPageImageUrl(drawingId, pageIndex));


        const validationCallback = async (boxConfig) => {
            return await this.client.validateItemConfiguration(boxConfig);
        };

        const updateImageCallback = (image, pageIndex) => {
            const state = Alpine.store('state');
            state.drawing.pageImage = image;
            state.drawing.pageIndex = pageIndex;
            state.drawing.selectedBoxItem = null;
        };

        this.drawing = new DrawingState(drawingId, pageImageUrls, analysis, updateImageCallback, validationCallback);
    }

    /**
     * @return {number}
     */
    getChangeCount() {
        if (this.drawing === null) {
            return 0;
        }
        return this.drawing.getItemChangeCount();
    }

    async syncChanges() {
        if (this.drawing === null || this.getChangeCount() === 0) {
            return;
        }

        const newAnalysis = this.drawing.getUpdatedAnalysis();
        await this.client.putDrawingAnalysis(this.drawing.drawingId, newAnalysis);
        this.drawing.resetItemsAfterSync();
    }

    /**
     * @param {MouseEvent} event
     */
    onMouseMove(event) {
        if (this.drawing === null) {
            return;
        }
        this.drawing.updateDrag(event);
    }

    /**
     * @param {MouseEvent} event
     */
    onMouseUp(event) {
        if (this.drawing === null) {
            return;
        }
        this.drawing.endDrag(event);
    }
}

const client = new EditorApiClient(document.location.origin);
const state = new EditorState(client)
document.addEventListener('mousemove', event => state.onMouseMove(event));
document.addEventListener('mouseup', event => state.onMouseUp(event));
document.addEventListener('alpine:init', () => Alpine.store('state', state));
