"""Integration tests for FastAPI Agent Service."""
import asyncio
import pytest
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from qw_agent_service.service import AgentService
from qw_agent_service.config import AgentServiceConfig
from qw_agent_service.models import AgentPromptRequest, AgentPromptResponse
from qw_log_interface import NO_LOG_FACTORY


@pytest.fixture
def mock_config():
    """Create a mock configuration for testing."""
    from qw_shared_auth.models import AuthConfig

    auth_config = AuthConfig(
        runtime_service_url="http://test-runtime:8000",
        session_validation_timeout=10.0,
        session_cache_ttl=300
    )

    return AgentServiceConfig(
        openai_api_key="test-key",
        runtime_service_url="http://test-runtime:8000",
        auth_config=auth_config,
        use_fastapi_agent=True,
        enable_specialized_agents=False,
        agent_model_name="gpt-4o",
        agent_temperature=0.2,
        agent_timeout=30.0,
        max_concurrent_requests=10,
        request_timeout=300.0
    )


@pytest.fixture
async def agent_service(mock_config):
    """Create an agent service instance for testing."""
    service = AgentService.from_config(mock_config, NO_LOG_FACTORY)

    # Mock the router agent initialization
    with patch('qw_agent_service.service.MinimalRouterAgent') as mock_agent_class:
        mock_agent = AsyncMock()
        mock_agent.agent_available = True
        mock_agent_class.from_config.return_value = mock_agent

        await service.initialize()
        yield service


@pytest.mark.asyncio
async def test_agent_service_initialization(mock_config):
    """Test agent service initialization."""
    service = AgentService.from_config(mock_config, NO_LOG_FACTORY)

    with patch('qw_agent_service.service.MinimalRouterAgent') as mock_agent_class:
        mock_agent = AsyncMock()
        mock_agent.agent_available = True
        mock_agent_class.from_config.return_value = mock_agent

        await service.initialize()

        assert service.agent_available is True
        assert service.router_agent is not None


@pytest.mark.asyncio
async def test_process_prompt_success(agent_service):
    """Test successful prompt processing."""
    # Mock the router agent response
    mock_response = AsyncMock()
    mock_response.message = "Test response"
    mock_response.actions = []

    agent_service.router_agent.process = AsyncMock(return_value=mock_response)

    # Create test request
    request = AgentPromptRequest(
        prompt="Test prompt",
        session_id=uuid4(),
        context={"test": "context"}
    )

    # Process the request
    response = await agent_service.process_prompt_async(request)

    # Verify response
    assert isinstance(response, AgentPromptResponse)
    assert response.message == "Test response"
    assert response.session_id == request.session_id
    assert response.actions == []


@pytest.mark.asyncio
async def test_process_prompt_agent_unavailable(mock_config):
    """Test prompt processing when agent is unavailable."""
    service = AgentService.from_config(mock_config, NO_LOG_FACTORY)

    # Don't initialize the service (agent_available = False)

    request = AgentPromptRequest(
        prompt="Test prompt",
        session_id=uuid4()
    )

    response = await service.process_prompt_async(request)

    assert isinstance(response, AgentPromptResponse)
    assert "unavailable" in response.message.lower()
    assert response.session_id == request.session_id


@pytest.mark.asyncio
async def test_process_prompt_error_handling(agent_service):
    """Test error handling during prompt processing."""
    # Mock the router agent to raise an exception
    agent_service.router_agent.process = AsyncMock(side_effect=Exception("Test error"))

    request = AgentPromptRequest(
        prompt="Test prompt",
        session_id=uuid4()
    )

    response = await agent_service.process_prompt_async(request)

    assert isinstance(response, AgentPromptResponse)
    assert "error" in response.message.lower()
    assert response.session_id == request.session_id


@pytest.mark.asyncio
async def test_cleanup(agent_service):
    """Test service cleanup."""
    await agent_service.cleanup()
    assert agent_service.agent_available is False
