import { Injectable } from "@angular/core"
import { catchError, Observable } from "rxjs"

import { QwMaterialCertificateAnalysisResponse } from "@qw-api/models"
import { QwFileResourceService } from "@qw-api/services/qw-file-resource.service"

@Injectable({
  providedIn: "root",
})
export class MaterialCertificateAnalysisService {
  constructor(private fileResourceWrapperAPIService: QwFileResourceService) {}

  getMaterialCertificateAnalysis(
    fileResourceRevisionId: number,
  ): Observable<QwMaterialCertificateAnalysisResponse> {
    return this.fileResourceWrapperAPIService
      .getMaterialCertificateAnalysis({ fileResourceRevisionId })
      .pipe(
        catchError((error) => {
          logger.error(
            `Error fetching material certificate analysis for revision ${fileResourceRevisionId}:`,
            error,
          )
          throw error
        }),
      )
  }
}
