"""
Material Extractor
This module provides functionality to extract material information from drawing text.
Uses spatial analysis and LLM processing to identify material specifications.
"""
from dataclasses import dataclass
from typing import Any, List

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings

from qw_drawing_toolkit_ocr.post_processing.config import PMIAnalysisSettings
from qw_drawing_toolkit_ocr.post_processing.models import ExtractedMaterial
from qw_drawing_toolkit_ocr.pre_processing.models import ClassifiedDocumentText, PmiClassification
from qw_log_interface import Logger


@dataclass
class Point:
    """A 2D point with x and y coordinates."""

    x: float
    y: float


@dataclass
class Rectangle:
    """Rectangle defined by two points (min_x, min_y) and (max_x, max_y)."""

    min_x: float
    min_y: float
    max_x: float
    max_y: float

    @property
    def width(self) -> float:
        """Width of the rectangle."""
        return self.max_x - self.min_x

    @property
    def height(self) -> float:
        """Height of the rectangle."""
        return self.max_y - self.min_y

    @property
    def area(self) -> float:
        """Area of the rectangle."""
        return self.width * self.height

    @property
    def center(self) -> Point:
        """Center point of the rectangle."""
        return Point(
            x=(self.min_x + self.max_x) / 2,
            y=(self.min_y + self.max_y) / 2,
        )

    def contains_point(self, point: Point) -> bool:
        """Check if the rectangle contains a point."""
        return self.min_x <= point.x <= self.max_x and self.min_y <= point.y <= self.max_y

    def intersects(self, other: "Rectangle") -> bool:
        """Check if this rectangle intersects with another rectangle."""
        return not (
            self.max_x < other.min_x or self.min_x > other.max_x or self.max_y < other.min_y or self.min_y > other.max_y
        )

    def expand(self, factor: float) -> "Rectangle":
        """Expand the rectangle by a factor from its center."""
        center = self.center
        half_width = self.width / 2
        half_height = self.height / 2

        new_half_width = half_width * factor
        new_half_height = half_height * factor

        return Rectangle(
            min_x=center.x - new_half_width,
            min_y=center.y - new_half_height,
            max_x=center.x + new_half_width,
            max_y=center.y + new_half_height,
        )


class MaterialExtractorAgent:
    """
    Helper class to extract material information using LLM.
    Uses pydantic_ai to process text and extract structured material information.
    """

    def __init__(self, settings: PMIAnalysisSettings, logger: Logger):
        """
        Initialize the material extractor agent.

        Args:
            settings: Configuration settings
            logger: Logger instance
        """
        self.logger = logger
        self.settings = settings

        self.logger.info("Material extractor agent initialized")

        # Configure the agent
        self._configure_agent()

    async def extract_material_info_with_llm(self, text: str) -> ExtractedMaterial:
        """
        Extract material information from the provided text using LLM.

        Args:
            text: Text containing material information from a technical drawing

        Returns:
            ExtractedMaterial object with the extracted information
        """
        try:
            # Run the agent to extract material information
            self.logger.info(f"Extracting material info from: {text}")

            # Use the run method (async) with the text parameter
            # This is preferred over run_sync which internally just calls loop.run_until_complete(self.run())
            result = await self.agent.run(text)

            # Log the result with standard logging
            self.logger.info(f"Extracted material info: {result.output}")

            return result.output
        except Exception as e:
            self.logger.error(f"Error extracting material info: {str(e)}")
            # Return a default ExtractedMaterial object if extraction fails
            return ExtractedMaterial(raw_material_name=None, material_standard=None)

    def _configure_agent(self) -> None:
        """Configure the pydanticAI agent with proper settings."""
        try:
            # Log with standard logging
            self.logger.info("Configuring material extractor agent")

            # Extract model name without provider prefix if needed
            model_name = self.settings.model_name
            if model_name.startswith("openai:"):
                model_name = model_name.split(":")[-1]

            # Setup model and provider
            provider = OpenAIProvider(api_key=self.settings.api_key)
            model = OpenAIModel(model_name, provider=provider)

            # Configure model settings
            model_settings = ModelSettings(
                temperature=self.settings.temperature,
                timeout=self.settings.timeout,
            )

            # Initialize the agent with proper configuration
            self._create_agent(model, model_settings)

            # Store the model name for logging
            self.model_name = model_name

            if self.agent:
                self.logger.info("Material extractor agent configured successfully")
        except Exception as e:
            self.logger.error(f"Failed to configure material extractor agent: {str(e)}")

    def _create_agent(self, model: OpenAIModel, model_settings: ModelSettings) -> None:
        """Create the pydanticAI agent with the given model and settings."""
        # Create the agent with the appropriate configuration
        # Note: Agent.instrument_all() should have been called during logfire initialization
        # so we don't need to explicitly instrument this agent
        self.agent = Agent(
            model=model,
            output_type=ExtractedMaterial,
            retries=3,
            model_settings=model_settings,
            system_prompt="""
            Extract material information from technical drawings.

            For the input text, identify:
            1. raw_material_name: The exact material specification as it appears in the drawing
            2. material_standard: Any explicitly mentioned standard references

            CRITICAL: ALWAYS treat material codes as strings, NEVER as numbers, regardless of their format.
            Material codes like "1.4301", "1.0570", etc. MUST be treated as string literals.
            DO NOT parse them as floating-point numbers or perform any numeric operations on them.

            Only include material_standard if it's explicitly mentioned in the text.
            If no standard is mentioned, set material_standard to null.

            Examples:
            - Input: "Material: S355J2+N according to EN 10025"
            Output: raw_material_name="S355J2+N", material_standard=["EN 10025"]

            - Input: "Material: 1.4301"
            Output: raw_material_name="1.4301", material_standard=null

            - Input: "Material: 1.0570"
            Output: raw_material_name="1.0570", material_standard=null

            - Input: "Material: AlMgSi1"
            Output: raw_material_name="AlMgSi1", material_standard=null
            """,
        )


class MaterialExtractor:
    """
    Extractor for material information from drawing text.
    Uses spatial analysis and LLM processing to identify material specifications.
    """

    # Material keywords in different languages
    MATERIAL_KEYWORDS = {
        "material",
        "werkstoff",
        "matériau",
        "materiale",
        "material",
        "materiaal",
        "materiale",
        "materiał",
        "materiál",
        "materijal",
        "материал",
        "υλικό",
    }

    # Factor to expand search area around material keyword blocks
    SEARCH_RADIUS_FACTOR = 5.0

    def __init__(self, settings: PMIAnalysisSettings, logger: Logger):
        """
        Initialize the material extractor.

        Args:
            settings: Configuration settings
            logger: Logger instance
        """
        self.logger = logger
        self.settings = settings

        # Log initialization with standard logging
        self.logger.info("Material extractor initialized")

        # Initialize the LLM agent for material extraction
        self.material_agent = MaterialExtractorAgent(logger=self.logger, settings=self.settings)

    async def extract_materials(self, classified_doc: ClassifiedDocumentText) -> List[ExtractedMaterial]:
        """
        Extract material information from text blocks.

        Uses spatial analysis to find material information blocks, then processes them with an LLM.

        Args:
            classified_doc: ClassifiedDocumentText containing blocks to process

        Returns:
            List of ExtractedMaterial objects
        """
        materials: List[ExtractedMaterial] = []
        material_blocks: List[str] = []

        # First, find blocks that contain material keywords
        material_keyword_blocks = self._find_material_keyword_blocks(classified_doc)

        # If no material keyword blocks found, return empty list
        if not material_keyword_blocks:
            self.logger.info("No material keyword blocks found")
            return materials

        # For each material keyword block, find nearby blocks that might contain material information
        material_blocks = self._find_nearby_blocks(classified_doc, material_keyword_blocks)

        # Process each material block with the LLM
        materials = await self._process_material_blocks(material_blocks)

        return materials

    def _find_material_keyword_blocks(self, classified_doc: ClassifiedDocumentText) -> List[Any]:
        """Find blocks that contain material keywords."""
        material_keyword_blocks: List[Any] = []

        for block in classified_doc.text_blocks:
            if block.classification in [
                PmiClassification.PMI_TEXT_ONLY,
                PmiClassification.PMI_EXPLICIT_STANDARDS,
            ]:
                block_text_lower = block.text.lower()

                # Check if any material keyword is in the block text
                if any(keyword in block_text_lower for keyword in self.MATERIAL_KEYWORDS):
                    self.logger.info(f"Found material keyword in block: {block.text}")
                    material_keyword_blocks.append(block)

        return material_keyword_blocks

    def _find_nearby_blocks(
        self, classified_doc: ClassifiedDocumentText, material_keyword_blocks: List[Any]
    ) -> List[str]:
        """Find nearby blocks that might contain material information."""
        material_blocks: List[str] = []

        for keyword_block in material_keyword_blocks:
            # Convert bounding poly to rectangle
            keyword_rect = self._get_block_rectangle(keyword_block)

            # Expand the rectangle to search for nearby blocks
            expanded_rect = keyword_rect.expand(self.SEARCH_RADIUS_FACTOR)

            # Find all blocks that intersect with the expanded rectangle
            nearby_blocks: List[Any] = []
            for block in classified_doc.text_blocks:
                if block == keyword_block:
                    continue

                block_rect = self._get_block_rectangle(block)
                if expanded_rect.intersects(block_rect):
                    nearby_blocks.append(block)

            # Combine the keyword block with nearby blocks
            combined_text = str(keyword_block.text)
            for block in nearby_blocks:
                combined_text += " " + str(block.text)

            material_blocks.append(combined_text)

        return material_blocks

    async def _process_material_blocks(self, material_blocks: List[str]) -> List[ExtractedMaterial]:
        """Process each material block with the LLM."""
        materials: List[ExtractedMaterial] = []

        for block_text in material_blocks:
            self.logger.info(f"Processing material block with LLM: {block_text}")

            # Use the LLM agent to extract material information
            material_info = await self.material_agent.extract_material_info_with_llm(text=block_text)
            materials.append(material_info)

            # Log the result with standard logging
            self.logger.info(f"Added material info: {material_info}")

        return materials

    def _get_block_rectangle(self, block: Any) -> Rectangle:
        """
        Convert a block's bounding poly to a Rectangle.

        Args:
            block: Text block with bounding_poly attribute

        Returns:
            Rectangle representation of the block's bounds
        """
        vertices = block.bounding_poly.vertices
        x_coords = [float(vertex["x"]) for vertex in vertices]
        y_coords = [float(vertex["y"]) for vertex in vertices]

        return Rectangle(
            min_x=min(x_coords),
            min_y=min(y_coords),
            max_x=max(x_coords),
            max_y=max(y_coords),
        )
