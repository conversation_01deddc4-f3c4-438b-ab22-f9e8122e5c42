"""
PMI tolerance sign merging utilities.
This module provides functionality to merge plus/minus tolerance indicators
with nearby numeric blocks in technical drawings.
"""
import math
import re
from typing import List, Tu<PERSON>, cast

from qw_drawing_toolkit_ocr.pre_processing.models import (
    BoundingPoly,
    ClassifiedDocumentText,
    ClassifiedTextBlock,
    PmiClassification,
    TextBlock,
    Vertices,
)
from qw_drawing_toolkit_ocr.pre_processing.pmi_geometry import extract_coordinates
from qw_log_interface import NO_LOGGER, Logger


def is_tolerance_sign_block(block: TextBlock) -> bool:
    """
    Determine if a text block contains tolerance indicators (plus/minus signs).

    Args:
        block: Text block to check

    Returns:
        True if the block contains a tolerance indicator, False otherwise
    """
    # Patterns to match: standalone +/- signs, +/- followed by numbers
    tolerance_patterns = [
        r"^\s*[+\-]\s*\d",  # +/- followed by digits
        r"^\s*[+\-]\s*$",  # Standalone +/- sign
    ]

    # Check if block text matches any tolerance pattern
    for pattern in tolerance_patterns:
        if re.search(pattern, block.text):
            return True

    return False


def is_numeric_block(block: TextBlock) -> bool:
    """
    Determine if a text block likely contains numeric content.

    Args:
        block: Text block to check

    Returns:
        True if the block is mostly numeric, False otherwise
    """
    # Remove common separators and whitespace
    text = re.sub(r"[.,\s]", "", block.text)

    # If it's empty after removing separators, it's not numeric
    if not text:
        return False

    # Count digits and total characters
    digit_count = sum(c.isdigit() for c in text)
    total_count = len(text)

    # Block is considered numeric if at least 50% of characters are digits
    return digit_count / total_count >= 0.5


def calculate_distance(x1: float, y1: float, x2: float, y2: float) -> float:
    """
    Calculate Euclidean distance between two points.

    Args:
        x1: X-coordinate of first point
        y1: Y-coordinate of first point
        x2: X-coordinate of second point
        y2: Y-coordinate of second point

    Returns:
        Distance between the two points
    """
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)


def expand_box(vertices: Vertices, expansion: float) -> Vertices:
    """
    Expand a bounding box by a fixed amount in all directions.

    Args:
        vertices: Original vertices
        expansion: Amount to expand in all directions

    Returns:
        Expanded vertices
    """
    # Extract coordinates
    x_coords, y_coords = extract_coordinates(vertices)
    min_x = min(x_coords)
    max_x = max(x_coords)
    min_y = min(y_coords)
    max_y = max(y_coords)

    # Expand in all directions
    expanded_vertices: Vertices = [
        {"x": int(min_x - expansion), "y": int(min_y - expansion)},  # Top-left
        {"x": int(max_x + expansion), "y": int(min_y - expansion)},  # Top-right
        {"x": int(max_x + expansion), "y": int(max_y + expansion)},  # Bottom-right
        {"x": int(min_x - expansion), "y": int(max_y + expansion)},  # Bottom-left
    ]

    return expanded_vertices


def calculate_overlap_percentage(box1: Vertices, box2: Vertices) -> float:
    """
    Calculate the overlap percentage between two boxes.

    Args:
        box1: First box vertices
        box2: Second box vertices

    Returns:
        Overlap percentage (0.0 to 1.0)
    """
    # Extract coordinates
    x1_coords, y1_coords = extract_coordinates(box1)
    x2_coords, y2_coords = extract_coordinates(box2)

    # Find bounds
    x1_min, x1_max = min(x1_coords), max(x1_coords)
    y1_min, y1_max = min(y1_coords), max(y1_coords)
    x2_min, x2_max = min(x2_coords), max(x2_coords)
    y2_min, y2_max = min(y2_coords), max(y2_coords)

    # Calculate intersection area
    x_overlap = max(0, min(x1_max, x2_max) - max(x1_min, x2_min))
    y_overlap = max(0, min(y1_max, y2_max) - max(y1_min, y2_min))
    intersection_area = x_overlap * y_overlap

    # Calculate the smallest box area (for percentage calculation)
    box1_area = (x1_max - x1_min) * (y1_max - y1_min)
    box2_area = (x2_max - x2_min) * (y2_max - y2_min)
    min_area = min(box1_area, box2_area)

    # Avoid division by zero
    if min_area == 0:
        return 0.0

    # Calculate overlap percentage relative to the smaller box
    overlap_percentage = intersection_area / min_area

    return overlap_percentage


def merge_blocks(
    tolerance_block: ClassifiedTextBlock,
    related_block: ClassifiedTextBlock,
    logger: Logger = NO_LOGGER,
) -> ClassifiedTextBlock:
    """
    Merge a tolerance block with a related block.

    Args:
        tolerance_block: Block containing tolerance sign
        related_block: Block to merge with
        logger: Logger instance

    Returns:
        Merged ClassifiedTextBlock
    """
    # Combine text (related block text first, then tolerance block)
    combined_text = f"{related_block.text} {tolerance_block.text}".strip()

    # Combine elements from both blocks
    combined_elements = related_block.elements + tolerance_block.elements

    # Extract coordinates from both blocks
    tol_x_coords, tol_y_coords = extract_coordinates(tolerance_block.bounding_poly.vertices)
    rel_x_coords, rel_y_coords = extract_coordinates(related_block.bounding_poly.vertices)

    # Create combined bounding box
    x_min = min(min(tol_x_coords), min(rel_x_coords))
    y_min = min(min(tol_y_coords), min(rel_y_coords))
    x_max = max(max(tol_x_coords), max(rel_x_coords))
    y_max = max(max(tol_y_coords), max(rel_y_coords))

    combined_vertices: Vertices = [
        {"x": int(x_min), "y": int(y_min)},  # Top-left
        {"x": int(x_max), "y": int(y_min)},  # Top-right
        {"x": int(x_max), "y": int(y_max)},  # Bottom-right
        {"x": int(x_min), "y": int(y_max)},  # Bottom-left
    ]

    # Create merged block
    merged_block = ClassifiedTextBlock(
        text=combined_text,
        bounding_poly=BoundingPoly(vertices=combined_vertices),
        elements=combined_elements,
        classification=PmiClassification.PMI_CANDIDATE,
    )

    logger.info(f"Merged blocks: '{related_block.text}' + '{tolerance_block.text}' → '{combined_text}'")

    return merged_block


def process_tolerance_sign_blocks(
    document_text: ClassifiedDocumentText,
    height_proxy: float,
    logger: Logger = NO_LOGGER,
) -> ClassifiedDocumentText:
    """
    Process document text to merge tolerance sign blocks with nearby numeric blocks.
    Uses a simplified approach with proximity search and overlap detection.

    Args:
        document_text: ClassifiedDocumentText with PMI classifications
        height_proxy: Calculated height proxy for text elements
        logger: Logger instance

    Returns:
        Updated ClassifiedDocumentText with merged blocks
    """

    # Create output document
    merged_doc = ClassifiedDocumentText(text=document_text.text)

    # Identify tolerance sign blocks and normal blocks
    tolerance_blocks: List[ClassifiedTextBlock] = []
    normal_blocks: List[ClassifiedTextBlock] = []

    for block in document_text.text_blocks:
        # Skip blocks that are already classified as something other than CANDIDATE
        if block.classification != PmiClassification.PMI_CANDIDATE:
            normal_blocks.append(block)
            continue

        # We need to cast ClassifiedTextBlock to TextBlock since they have compatible interfaces
        # but the type checker doesn't know about this compatibility
        if is_tolerance_sign_block(cast(TextBlock, block)):
            tolerance_blocks.append(block)
            # logger.info(f"Identified tolerance sign block: '{block.text}'")
        else:
            normal_blocks.append(block)

    # Filter for numeric blocks - cast ClassifiedTextBlock to TextBlock for type compatibility
    numeric_blocks = [block for block in normal_blocks if is_numeric_block(cast(TextBlock, block))]

    # Keep track of blocks we've already processed - using list instead of set since ClassifiedTextBlock is not hashable
    processed_normal_blocks = list(normal_blocks)
    merged_blocks: List[ClassifiedTextBlock] = []

    # Process each tolerance block
    for tolerance_block in tolerance_blocks:
        candidates: List[Tuple[ClassifiedTextBlock, float, float]] = []  # (block, final_score, overlap_score)

        try:
            # Get tolerance block center
            tol_x_coords, tol_y_coords = extract_coordinates(tolerance_block.bounding_poly.vertices)
            tol_center_x = sum(tol_x_coords) / len(tol_x_coords)
            tol_center_y = sum(tol_y_coords) / len(tol_y_coords)

            # Get tolerance block bounds
            tol_min_x = min(tol_x_coords)

            # Create expanded box for overlap checking
            expanded_box = expand_box(tolerance_block.bounding_poly.vertices, height_proxy)

            # Search radius (5x height)
            search_radius = height_proxy * 5.0
            logger.info(
                f"Using search radius of {search_radius:.2f} pixels for tolerance block '{tolerance_block.text}'"
            )

            # Check each numeric block
            for numeric_block in numeric_blocks:
                # Skip if we've already processed this block
                if numeric_block not in processed_normal_blocks:
                    continue

                # Get numeric block center
                num_x_coords, num_y_coords = extract_coordinates(numeric_block.bounding_poly.vertices)
                num_center_x = sum(num_x_coords) / len(num_x_coords)
                num_center_y = sum(num_y_coords) / len(num_y_coords)

                # Get numeric block bounds
                num_max_x = max(num_x_coords)

                # Calculate center-to-center distance
                distance = calculate_distance(tol_center_x, tol_center_y, num_center_x, num_center_y)

                # Only consider blocks within search radius
                if distance <= search_radius:
                    # Calculate proximity score (1.0 for closest, 0.0 for farthest)
                    proximity_score = 1.0 - (distance / search_radius)

                    # Calculate position score (1.0 if numeric block is to the left of tolerance)
                    position_score = 1.0 if num_max_x <= tol_min_x else 0.5

                    # Calculate overlap with expanded box
                    overlap_percentage = calculate_overlap_percentage(
                        expanded_box, numeric_block.bounding_poly.vertices
                    )

                    # Combined score - weight proximity (40%), position (30%), and overlap (30%)
                    final_score = (0.4 * proximity_score) + (0.3 * position_score) + (0.3 * overlap_percentage)

                    logger.info(
                        f"Candidate for '{tolerance_block.text}': '{numeric_block.text}', "
                        f"distance: {distance:.2f}, proximity: {proximity_score:.2f}, "
                        f"position: {position_score:.2f}, overlap: {overlap_percentage:.2f}, "
                        f"final score: {final_score:.2f}"
                    )

                    candidates.append((numeric_block, final_score, overlap_percentage))

            # Sort candidates by final score (higher is better)
            candidates.sort(key=lambda x: x[1], reverse=True)

            if candidates:
                best_candidate, best_score, best_overlap = candidates[0]

                logger.info(
                    f"Best candidate for '{tolerance_block.text}': '{best_candidate.text}', "
                    f"score: {best_score:.2f}, overlap: {best_overlap:.2f}"
                )

                # Only merge if score is good enough
                if best_score >= 0.3 or best_overlap >= 0.1:  # Lower threshold for matches with good overlap
                    # Remove from processed blocks
                    if best_candidate in processed_normal_blocks:
                        processed_normal_blocks.remove(best_candidate)

                    # Merge blocks
                    merged_block = merge_blocks(tolerance_block, best_candidate, logger)
                    merged_blocks.append(merged_block)
                    logger.info(f"Merged tolerance block '{tolerance_block.text}' with '{best_candidate.text}'")
                else:
                    # Keep tolerance block as is
                    merged_blocks.append(tolerance_block)
                    logger.info(
                        f"No good match for tolerance block '{tolerance_block.text}' (best score: {best_score:.2f})"
                    )
            else:
                # Keep tolerance block as is
                merged_blocks.append(tolerance_block)
                logger.info(f"No candidates found for tolerance block '{tolerance_block.text}'")

        except Exception as e:
            logger.error(f"Error processing tolerance block '{tolerance_block.text}': {str(e)}")
            # Keep tolerance block as is
            merged_blocks.append(tolerance_block)

    # Add remaining blocks to result
    for block in normal_blocks:
        if block in processed_normal_blocks:
            merged_doc.text_blocks.append(block)

    # Add merged blocks
    merged_doc.text_blocks.extend(merged_blocks)

    logger.info(f"Processed {len(tolerance_blocks)} tolerance blocks, created {len(merged_blocks)} merged blocks")

    return merged_doc
