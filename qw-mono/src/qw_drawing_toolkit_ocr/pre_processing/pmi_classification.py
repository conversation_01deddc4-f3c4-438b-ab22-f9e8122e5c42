"""
Classification utilities for PMI processing.
This module provides functions for classifying text blocks in technical drawings.
"""
import re
from typing import List

from qw_drawing_toolkit_ocr.pre_processing.models import (
    BoundingPoly,
    ClassifiedTextBlock,
    ClassifiedTextElement,
    DocumentText,
    TextBlock,
    TextElement,
)
from qw_drawing_toolkit_ocr.pre_processing.pmi_geometry import calculate_iou, calculate_true_height
from qw_log_interface import NO_LOGGER, Logger


def _is_diameter(text: str) -> bool:
    """
    Check if a text contains a diameter symbol.

    Args:
        text: The text to check

    Returns:
        True if the text contains a diameter symbol and fewer than 3 lowercase letters,
        False otherwise
    """
    lower_case_letter_count = len(re.findall(r"[a-z]", text))
    valid = (
        "ø" in text or "⌀" in text or "∅" in text or "⊘" in text or "\u008E" in text
    ) and lower_case_letter_count < 3
    return valid


def _is_degrees(text: str) -> bool:
    """
    Check if a text contains a degree symbol.

    Args:
        text: The text to check

    Returns:
        True if the text contains a degree symbol, False otherwise
    """
    return "°" in text


def _is_plus_minus(text: str) -> bool:
    """
    Check if a text contains a plus-minus symbol.

    Args:
        text: The text to check

    Returns:
        True if the text contains a plus-minus symbol, False otherwise
    """
    return "±" in text


def _is_radius(text: str) -> bool:
    """
    Check if a text contains a radius specification (e.g., R32, R 3,4, R 3.21).

    Args:
        text: The text to check

    Returns:
        True if the text contains a radius specification, False otherwise
    """
    # Look for "R" followed by optional space and then a number (with optional decimal/comma)
    return bool(re.search(r"\bR\s*\d+(?:[.,]\d+)?", text))


def _is_numeric_dimension(text: str) -> bool:
    """
    Check if a text appears to be a numeric dimension (e.g., "49.07", "12,5", "100.0").

    Requirements:
    - Must have exactly one decimal separator (dot or comma)
    - Must have at least 3 digits in total

    Args:
        text: The text to check

    Returns:
        True if the text appears to be a numeric dimension, False otherwise
    """
    # Clean the text of whitespace
    text = text.strip()

    # Check if it has exactly one decimal separator (dot or comma)
    dot_count = text.count(".")
    comma_count = text.count(",")

    # Must have exactly one separator (either dot or comma, not both)
    if (dot_count + comma_count) != 1:
        return False

    # Check that we have a valid numeric format and count digits
    parts = text.replace(",", ".").split(".")

    # Should have exactly two parts (before and after decimal)
    if len(parts) != 2:
        return False

    # Both parts should be digits
    if not (parts[0].isdigit() and parts[1].isdigit()):
        return False

    # Count total digits
    total_digits = len(parts[0]) + len(parts[1])

    # Require at least 3 digits in total
    return total_digits >= 3


def is_pmi_pattern(text: str) -> bool:
    """
    Check if a text contains any PMI pattern (diameter, degrees, plus-minus, radius, numeric dimension).

    Args:
        text: The text to check

    Returns:
        True if the text contains any PMI pattern, False otherwise
    """
    return (
        _is_diameter(text)
        or _is_degrees(text)
        or _is_plus_minus(text)
        or _is_radius(text)
        or _is_numeric_dimension(text)
    )


def is_element_out_of_scale(
    element: TextElement,
    height_proxy: float,
    upper_threshold: float,
    lower_threshold: float,
    logger: Logger = NO_LOGGER,
) -> bool:
    """
    Check if an element is out of scale based on the height proxy.
    Uses rotation-based true height calculation for accurate comparison.

    Args:
        element: Text element to analyze
        height_proxy: Calculated height proxy for comparison
        upper_threshold: Upper threshold for out-of-scale detection
        lower_threshold: Lower threshold for out-of-scale detection
        logger: Logger instance

    Returns:
        True if element is out of scale, False otherwise
    """

    if height_proxy <= 0:
        return False

    # Extract element vertices
    vertices = element.bounding_poly.vertices
    if not vertices:
        return False

    # Calculate true height using rotation transformation
    true_height = calculate_true_height(vertices, logger)
    if true_height <= 0:
        return False

    # Check if height significantly deviates from proxy
    is_out_of_scale = true_height > height_proxy * upper_threshold or true_height < height_proxy * lower_threshold

    return is_out_of_scale


def is_grid_notation(block: TextBlock, grid_blocks: List[TextBlock]) -> bool:
    """
    Check if a block contains drawing grid notations.

    Args:
        block: Text block to analyze
        grid_blocks: List of identified grid blocks

    Returns:
        True if block contains grid notation, False otherwise
    """
    # Check if the block is a grid element
    for grid_block in grid_blocks:
        if (
            block.text.strip() == grid_block.text.strip()
            and block.bounding_poly.vertices == grid_block.bounding_poly.vertices
        ):
            return True
    return False


def is_explicit_standard(block: TextBlock) -> bool:
    """
    Check if a block contains ISO, DIN, or EN standards.

    Args:
        block: Text block to analyze

    Returns:
        True if block contains explicit standards, False otherwise
    """
    return bool(re.search(r"\b(ISO|DIN|EN)\b", block.text))


def is_text_only(block: "TextBlock", text_only_percentage: float, logger: Logger = NO_LOGGER) -> bool:
    """
    Check if a block contains mostly text (e.g. 85% or more letters vs. digits).
    Also considers dates in any format as "text only".
    The words 'thru' and 'durch' (case-insensitive) are ignored.
    Blocks containing only '+', '-', or '±' symbols are not considered text-only.
    Blocks containing 3 or more consecutive letters are considered text-only.

    Args:
        block: Text block to analyze
        text_only_percentage: Percentage of letters (vs. digits) to classify as text-only
        logger: Logger instance

    Returns:
        True if block contains mostly text or dates, False otherwise
    """

    text = block.text.strip()

    # Check if text is empty
    if not text:
        return False

    # Check for single symbol blocks ('+', '-', '±')
    if text in ["+", "-", "±"] or text.strip() in ["+", "-", "±"]:
        logger.info(f"Block containing only symbol '{text}' is not classified as text-only")
        return False

    # Check for date patterns (various formats)
    date_patterns = [
        r"\d{4}[-/]\d{1,2}[-/]\d{1,2}",  # YYYY-MM-DD, YYYY/MM/DD
        r"\d{1,2}[-.]\d{1,2}[-.]\d{4}",  # DD.MM.YYYY, DD-MM-YYYY
        r"\d{1,2}/\d{1,2}/\d{4}",  # MM/DD/YYYY
        r"\d{1,2}[-.]\d{1,2}[-.]\d{2}",  # Short dates with 2-digit years
        r"\d{1,2}\s+[A-Za-z]{3,}\s+\d{4}",  # 10 Jan 2025
        r"[A-Za-z]{3,}\s+\d{1,2}\s*,?\s*\d{4}",  # January 10, 2025
        r"\d{1,2}[-\s]+[A-Za-z]{3,}[-\s]+\d{4}",  # 10-Jan-2025
        r"\d{1,2}[-\s]+[A-Za-z]{3,}[-\s]+\d{2}",  # 10-Jan-25
    ]

    for pattern in date_patterns:
        if re.search(pattern, text):
            return True

    # Remove case-insensitive instances of "thru" and "durch"
    text = re.sub(r"\b(thru|durch)\b", "", text, flags=re.IGNORECASE).strip()

    # Check for 3 or more consecutive letters (after removing ignored words)
    if re.search(r"[a-zA-Z]{3,}", text):
        return True

    # Count only alphanumeric characters
    chars = [char for char in text if char.isdigit() or char.isalpha()]
    total_count = len(chars)
    digit_count = sum(1 for char in chars if char.isdigit())

    if total_count == 0:
        return False

    non_digit_percentage = (total_count - digit_count) / total_count * 100
    return non_digit_percentage >= text_only_percentage


def is_no_value(block: TextBlock, logger: Logger = NO_LOGGER) -> bool:
    """
    Check if a block contains patterns like:
    - Ratio notations: "10:1", "10 : 2", "1:    3"
    - Page references: "1 of 2", "2of 3"
    - Paper sizes: "A4", "A3", "A2", "A1", "A0"
    - Section view references: "A-A", "B - A"
    - Page reference patterns: "1/3", "1 / 2", "21 / 10"
    - Pure zeros: "0", "00", "000", "0 0", "00  0"

    Args:
        block: Text block to analyze
        logger: Logger instance

    Returns:
        True if block contains these patterns, False otherwise
    """
    text = block.text.strip()

    # Ratio notation (e.g., "1:2", "2: 3")
    if bool(re.search(r"\d+\s*:\s*\d+", text)):
        return True

    # Page reference pattern matching (e.g., "1 of 2", "2of 3")
    if bool(re.search(r"\d+\s*of\s*\d+", text, re.IGNORECASE)):
        return True

    # Page reference pattern (e.g., "1/3", "1 / 2", "21 / 10")
    if re.fullmatch(r"\d+\s*/\s*\d+", text):
        return True

    # Section view references (e.g., "A-A", "B - A")
    if re.search(r"\b[A-Z]\s*-\s*[A-Z]\b", text, re.IGNORECASE):
        return True

    # Paper size formats (A0-A4)
    if bool(re.search(r"\b(A[0-4])\b", text)):
        return True

    # Check for pure zeros pattern - strings containing only zeros and whitespace
    if len(block.elements) <= 1 and re.fullmatch(r"[0\s]+", text) and "0" in text:
        return True

    return False


def calculate_height_proxy(document_text: DocumentText, logger: Logger = NO_LOGGER) -> float:
    """
    Calculate height proxy from text blocks with PMI patterns.
    Uses rotation-based true height calculation for accurate height measurement.
    Aims to collect up to 6 PMI patterns from single-element blocks for consistent results.
    Will use fewer patterns if not enough qualifying blocks are found.

    If not enough single-element blocks are found, the function will progressively
    relax the constraint to include blocks with more elements.

    Args:
        document_text: Document text from Google Vision
        logger: Logger instance

    Returns:
        Average height of text blocks with PMI patterns (up to max 6 samples)
    """
    # Maximum PMI patterns to use for calculation (aim for this number)
    max_pmi_patterns = 6
    # Minimum blocks needed to calculate a reliable height
    min_blocks_needed = 1

    # Progressive relaxation of element count constraint
    for max_elements in range(1, 4):  # Try with 1, 2, then 3 elements max
        # Store heights and texts for this iteration
        true_heights: List[float] = []
        pmi_texts: List[str] = []

        # Collect all qualifying blocks first
        qualifying_blocks: List[tuple[TextBlock, float]] = []
        for block in document_text.text_blocks:
            # Check if block has PMI pattern and has acceptable number of elements
            if is_pmi_pattern(block.text) and len(block.elements) <= max_elements:
                vertices = block.bounding_poly.vertices
                if vertices:
                    true_height = calculate_true_height(vertices, logger)
                    if true_height > 0:
                        qualifying_blocks.append((block, true_height))

        # Use up to max_pmi_patterns of the qualifying blocks
        for block_tuple in qualifying_blocks[:max_pmi_patterns]:
            current_block: TextBlock = block_tuple[0]
            current_height: float = block_tuple[1]
            true_heights.append(current_height)
            pmi_texts.append(current_block.text)

        # If we have enough blocks, calculate and return the average
        if len(true_heights) >= min_blocks_needed:
            average = sum(true_heights) / len(true_heights)

            # Log detailed information about which texts were used
            logger.info(f"PMI patterns used for height calculation: {pmi_texts}")
            logger.info(
                f"Calculated height from PMI patterns: {average:.2f} pixels from {len(true_heights)} blocks "
                f"with {max_elements} or fewer elements (limited to max {max_pmi_patterns})"
            )

            return average

    # If we get here, we couldn't find enough blocks even with relaxed constraints
    logger.warning("Could not find enough PMI patterns for height calculation even with relaxed constraints")
    return 0.0


def combine_similar_blocks(
    blocks: List[TextBlock], iou_threshold: float, logger: Logger = NO_LOGGER
) -> List[TextBlock]:
    """
    Combine almost identical blocks whose element text is different into one block.
    This helps to address the common OCR issue where a single text entity is
    incorrectly separated into multiple overlapping blocks.

    Args:
        blocks: List of text blocks to process
        iou_threshold: IoU threshold to consider blocks as similar
        logger: Logger instance

    Returns:
        List of text blocks with similar blocks combined
    """

    if not blocks or len(blocks) < 2:
        return blocks

    # Track which blocks have been combined
    processed_blocks: set[int] = set()
    combined_blocks: List[TextBlock] = []

    for i, block1 in enumerate(blocks):
        # Skip already processed blocks
        if i in processed_blocks:
            continue

        similar_blocks: List[TextBlock] = [block1]
        processed_blocks.add(i)

        # Find similar blocks
        for j, block2 in enumerate(blocks):
            if j in processed_blocks or i == j:
                continue

            # Calculate IoU to determine if blocks are similar
            iou = calculate_iou(block1.bounding_poly.vertices, block2.bounding_poly.vertices)

            if iou >= iou_threshold:
                similar_blocks.append(block2)
                processed_blocks.add(j)

        # If we found similar blocks, combine them
        if len(similar_blocks) > 1:
            # logger.info(
            #     f"Found {len(similar_blocks)} similar blocks to combine: "
            #     + f"[{', '.join(b.text for b in similar_blocks[:3])}{'...' if len(similar_blocks) > 3 else ''}]"
            # )

            # Merge their texts with a space separator
            combined_text = " ".join(block.text for block in similar_blocks)

            # Collect all elements from all similar blocks
            combined_elements: List[TextElement] = []
            for block in similar_blocks:
                combined_elements.extend(block.elements)

            # Create a new block with combined text and elements
            # Use the bounding polygon of the first block for simplicity
            combined_block = TextBlock(
                text=combined_text, bounding_poly=similar_blocks[0].bounding_poly, elements=combined_elements
            )
            combined_blocks.append(combined_block)
        else:
            combined_blocks.append(block1)

    return combined_blocks


def combine_similar_classified_blocks(
    blocks: List[ClassifiedTextBlock], iou_threshold: float, logger: Logger = NO_LOGGER
) -> List[ClassifiedTextBlock]:
    """
    Combine similar classified blocks that have overlapping bounding boxes.
    This function is similar to combine_similar_blocks but works with ClassifiedTextBlock objects.
    It maintains the classification information when combining blocks.

    Args:
        blocks: List of classified text blocks to process
        iou_threshold: IoU threshold to consider blocks as similar
        logger: Logger instance

    Returns:
        List of classified text blocks with similar blocks combined
    """

    if not blocks or len(blocks) < 2:
        return blocks

    # Track which blocks have been combined
    processed_blocks: set[int] = set()
    combined_blocks: List[ClassifiedTextBlock] = []

    for i, block1 in enumerate(blocks):
        # Skip already processed blocks
        if i in processed_blocks:
            continue

        similar_blocks: List[ClassifiedTextBlock] = [block1]
        processed_blocks.add(i)

        # Find similar blocks
        for j, block2 in enumerate(blocks):
            if j in processed_blocks or i == j:
                continue

            # Calculate IoU to determine if blocks are similar
            iou = calculate_iou(block1.bounding_poly.vertices, block2.bounding_poly.vertices)

            if iou >= iou_threshold:
                similar_blocks.append(block2)
                processed_blocks.add(j)

        # If we found similar blocks, combine them
        if len(similar_blocks) > 1:
            logger.info(
                f"Found {len(similar_blocks)} similar classified blocks to combine: "
                + f"[{', '.join(b.text for b in similar_blocks[:3])}{'...' if len(similar_blocks) > 3 else ''}]"
            )

            # Merge their texts with a space separator
            combined_text = " ".join(block.text for block in similar_blocks)

            # Collect all elements from all similar blocks
            combined_elements: List[ClassifiedTextElement] = []
            for block in similar_blocks:
                combined_elements.extend(block.elements)

            # Create a new bounding polygon that encompasses all the merged blocks
            min_x = float("inf")
            min_y = float("inf")
            max_x = float("-inf")
            max_y = float("-inf")

            # Find the minimum and maximum coordinates from all blocks
            for block in similar_blocks:
                for vertex in block.bounding_poly.vertices:
                    x = vertex.get("x", 0)
                    y = vertex.get("y", 0)
                    min_x = min(min_x, x)
                    min_y = min(min_y, y)
                    max_x = max(max_x, x)
                    max_y = max(max_y, y)

            # Create a new bounding polygon using the calculated bounds
            combined_bounding_poly = BoundingPoly(
                vertices=[
                    {"x": int(min_x), "y": int(min_y)},  # top-left
                    {"x": int(max_x), "y": int(min_y)},  # top-right
                    {"x": int(max_x), "y": int(max_y)},  # bottom-right
                    {"x": int(min_x), "y": int(max_y)},  # bottom-left
                ]
            )

            # Create a new classified block with combined text and elements
            # Using the newly calculated bounding polygon that encompasses all merged blocks
            combined_block = ClassifiedTextBlock(
                text=combined_text,
                bounding_poly=combined_bounding_poly,
                elements=combined_elements,
                classification=similar_blocks[0].classification,
            )
            combined_blocks.append(combined_block)
        else:
            combined_blocks.append(block1)

    logger.info(
        f"Combined {len(blocks) - len(combined_blocks)} classified blocks, resulting in {len(combined_blocks)} blocks"
    )
    return combined_blocks
