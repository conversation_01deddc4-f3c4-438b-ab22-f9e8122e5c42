import hashlib
import io
from pathlib import Path
from typing import Callable, List, Sequence
from wsgiref import simple_server

import falcon
import pymupdf
from pydantic import BaseModel, Field, ValidationError
from typing_extensions import Self

from qw_drawing_toolkit.dtl.simple import DefaultToleranceLookup
from qw_drawing_toolkit.norm.tolerance_collection import ToleranceCollection
from qw_drawing_toolkit.pdf.models.drawing_models import (
    DrawingAnalysis,
    DrawingObjectWithBoundingBox,
    DrawingReportStatus,
)
from qw_drawing_toolkit.pdf.services.pdf_validator import open_and_validate

Resource = Callable[[falcon.Request, falcon.Response], None]


class ResourceCallback(object):
    def __init__(self, on_get: Resource | None = None, on_post: Resource | None = None, on_put: Resource | None = None):
        self.on_get = on_get
        self.on_post = on_post
        self.on_put = on_put


class DrawingInfo(BaseModel):
    id: str
    path: str
    page_count: int = Field(alias="pageCount")


class EditorDrawingItem(object):
    def __init__(self, drawing_path: Path, analysis_path: Path):
        self.drawing_path = drawing_path
        self.analysis_path = analysis_path
        with drawing_path.open("rb") as f:
            drawing_bytes = f.read()
            self.drawing_id = hashlib.blake2b(drawing_bytes, digest_size=16).hexdigest()
            self.drawing_pdf, _, _ = open_and_validate(io.BytesIO(drawing_bytes))
        self.analysis = DrawingAnalysis.from_yaml_file(analysis_path)

    @classmethod
    def from_paths(cls, paths: Sequence[Path]) -> Sequence[Self]:
        file_paths = []

        for p in paths:
            if p.is_file():
                file_paths.append(p)
            else:
                file_paths.extend(p.rglob("**/*"))

        items = []
        for drawing_path in sorted(file_paths):
            if not (drawing_path.is_file() and drawing_path.suffix.lower() == ".pdf"):
                continue
            analysis_path = drawing_path.with_suffix(".yaml")
            if not analysis_path.exists():
                continue
            print(f"Loading drawing {drawing_path}")
            items.append(cls(drawing_path, analysis_path))
        return items

    def get_info(self) -> DrawingInfo:
        page_count = len(list(self.drawing_pdf.pages()))
        return DrawingInfo(id=self.drawing_id, path=str(self.drawing_path), pageCount=page_count)

    def overwrite_analysis(self, new_analysis: DrawingAnalysis) -> None:
        new_analysis.to_yaml_file(self.analysis_path)
        self.analysis = new_analysis


class EditorServerApp(object):
    ROUTE_FRONTEND = "/frontend"
    ROUTE_DRAWINGS = "/api/drawings"
    ROUTE_DRAWING = "/api/drawing/{drawing_id}"
    ROUTE_DRAWING_ANALYSIS = "/api/drawing/{drawing_id}/analysis"
    ROUTE_DRAWING_IMAGE = "/api/drawing/{drawing_id}/image/{page_index}"
    ROUTE_VALIDATE = "/api/validate"

    DEFAULT_FRONTEND_PATH = Path(__file__).parent / "frontend"

    def __init__(self, items: Sequence[EditorDrawingItem], frontend_base_path: Path | None = None, quiet: bool = False):
        self.items_by_id = {item.drawing_id: item for item in items}
        self.frontend_base_path = frontend_base_path or self.DEFAULT_FRONTEND_PATH
        self.quiet = quiet
        self.dtl = DefaultToleranceLookup(ToleranceCollection.from_default_directory())

    def log(self, msg: str) -> None:
        if not self.quiet:
            print(msg)

    def get_item_or_abort(self, drawing_id: str) -> EditorDrawingItem:
        item = self.items_by_id.get(drawing_id)
        if item is None:
            raise falcon.HTTPNotFound()
        return item

    def get_drawings(self, req: falcon.Request, resp: falcon.Response) -> None:
        drawing_infos = [item.get_info().model_dump(by_alias=True) for item in self.items_by_id.values()]
        data = {"drawingInfos": drawing_infos}
        resp.media = data
        resp.status = falcon.HTTP_200

    def get_drawing(self, req: falcon.Request, resp: falcon.Response, drawing_id: str) -> None:
        item = self.get_item_or_abort(drawing_id)
        resp.media = item.get_info().model_dump(by_alias=True)
        resp.status = falcon.HTTP_200

    def get_drawing_analysis(self, req: falcon.Request, resp: falcon.Response, drawing_id: str) -> None:
        item = self.get_item_or_abort(drawing_id)
        resp.media = item.analysis.model_dump(by_alias=True)
        resp.status = falcon.HTTP_200

    def put_drawing_analysis(self, req: falcon.Request, resp: falcon.Response, drawing_id: str) -> None:
        item = self.get_item_or_abort(drawing_id)
        try:
            new_analysis = DrawingAnalysis(**req.media)
        except ValidationError as e:
            self.log(f"Could not validate new analysis for {drawing_id}\n{e}")
            raise falcon.HTTP_400

        self.dtl.lookup_and_update(new_analysis.result, overwrite_general=True, overwrite_fits=True)
        new_analysis.report.analysis_status = DrawingReportStatus.COMPLETED
        new_analysis.report.default_tolerance_lookup_status = DrawingReportStatus.COMPLETED
        item.overwrite_analysis(new_analysis)
        self.log(f"Updated analysis for drawing {drawing_id}")
        resp.status = falcon.HTTP_200

    def validate_item_configuration(self, req: falcon.Request, resp: falcon.Response) -> None:
        class BoxConfig(BaseModel):
            obj: DrawingObjectWithBoundingBox

        error_msg: str | None = None
        try:
            data = req.media
            BoxConfig.model_validate({"obj": data})
        except ValidationError as e:
            error_msg = str(e)

        resp.media = {"errorMessage": error_msg}
        resp.status = falcon.HTTP_200

    def get_drawing_page_image(
        self,
        req: falcon.Request,
        resp: falcon.Response,
        drawing_id: str,
        page_index: str,
    ) -> None:
        page_index_ = int(page_index)
        item = self.items_by_id.get(drawing_id)
        if item is None:
            raise falcon.HTTPNotFound()

        pages: List[pymupdf.Page] = list(item.drawing_pdf.pages())
        if not (0 <= page_index_ < len(pages)):
            raise falcon.HTTPNotFound()

        bio = io.BytesIO()
        pix = pages[page_index_].get_pixmap(dpi=150)
        pix.pil_save(bio, format="PNG")
        bio.seek(0)

        resp.stream = bio
        resp.content_type = falcon.MEDIA_PNG
        resp.status = falcon.HTTP_200

    def build(self) -> falcon.App:
        app = falcon.App()
        app.add_static_route(self.ROUTE_FRONTEND, self.frontend_base_path)
        app.add_route(self.ROUTE_DRAWINGS, ResourceCallback(on_get=self.get_drawings))
        app.add_route(
            self.ROUTE_DRAWING,
            ResourceCallback(on_get=self.get_drawing),  # type: ignore
        )
        app.add_route(
            self.ROUTE_DRAWING_ANALYSIS,
            ResourceCallback(
                on_get=self.get_drawing_analysis,  # type: ignore
                on_put=self.put_drawing_analysis,  # type: ignore
            ),
        )
        app.add_route(
            self.ROUTE_DRAWING_IMAGE,
            ResourceCallback(on_get=self.get_drawing_page_image),  # type: ignore
        )
        app.add_route(self.ROUTE_VALIDATE, ResourceCallback(on_post=self.validate_item_configuration))
        return app

    def serve(self, host: str = "127.0.0.1", port: int = 9001) -> None:
        app = self.build()
        self.log(f"Open at http://{host}:{port}/frontend/editor.html")
        httpd = simple_server.make_server(host, port, app)
        httpd.serve_forever()
