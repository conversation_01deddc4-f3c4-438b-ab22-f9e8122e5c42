import contextlib
from collections.abc import Iterator
from enum import Enum, unique
from typing import Any, Generic, Type, TypeVar

from sqlalchemy import select
from sqlalchemy.orm import DeclarativeBase, Session

from qw_basic_rdb.interface import RelationalDatabase


@unique
class TransactionStrategy(str, Enum):
    NEW = "NEW"  # a new (nested) transaction will be created
    REUSE = "REUSE"  # requires an active transaction
    REUSE_OR_NEW = "REUSE_OR_NEW"  # only creates a new transaction in case there is no active one yet
    SKIP = "SKIP"  # no transaction will be created


@contextlib.contextmanager
def blocked_commit_method(session: Session) -> Iterator[None]:
    def commit_block() -> None:
        msg = (
            f"Using .commit() manually is discouraged, use proper values of {TransactionStrategy.__name__} "
            f"in conjunction with the 'begin_or_use_session' context manager,"
            f"if objects need to be update mid-transaction, use .flush(objs)"
        )
        raise RuntimeError(msg)

    commit_method = session.commit
    session.commit = commit_block  # type: ignore
    yield
    session.commit = commit_method  # type: ignore


@contextlib.contextmanager
def begin_or_use_session(
    rdb: RelationalDatabase,
    session: Session | None = None,
    strategy: TransactionStrategy = TransactionStrategy.SKIP,
) -> Iterator[Session]:
    close = False
    if session is None:
        session = rdb.create_and_get_session()
        close = True

    try:
        in_transaction = session.in_transaction()
        if strategy == TransactionStrategy.REUSE and not in_transaction:
            raise ValueError("Reusing a transaction is not possible, none is active")

        start_transaction = (strategy == TransactionStrategy.NEW) or (
            strategy == TransactionStrategy.REUSE_OR_NEW and not in_transaction
        )

        transaction_mgr: contextlib.AbstractContextManager[Any] = (
            session.begin(nested=in_transaction) if start_transaction else contextlib.nullcontext()
        )
        with transaction_mgr:
            with blocked_commit_method(session):
                yield session
    except Exception as e:
        if close:
            rdb.close_session()
        raise e

    if close:
        rdb.close_session()


T = TypeVar("T", bound=DeclarativeBase)


class RowNotFoundError(Exception):
    pass


class CommonTableOps(Generic[T]):
    def __init__(self, rdb: RelationalDatabase, table: Type[T]):
        self.rdb = rdb
        self.table = table
        if not hasattr(table, "id"):
            raise ValueError(f"{table.__name__} does not have an id column")

    def add(self, obj: T, session: Session) -> int:
        with begin_or_use_session(self.rdb, session, strategy=TransactionStrategy.REUSE) as s:
            s.add(obj)
            s.flush([obj])
            obj_id: int = obj.id  # type: ignore
        return obj_id

    def find_by_id(self, idx: int, session: Session | None = None) -> T | None:
        stmt = select(self.table).where(self.table.id == idx)  # type: ignore
        with begin_or_use_session(self.rdb, session) as s:
            obj: T | None = s.scalar(stmt)
        return obj

    def get_by_id(self, idx: int, session: Session | None = None) -> T:
        obj = self.find_by_id(idx, session)
        if obj is None:
            raise RowNotFoundError(f"Could not get {self.table.__name__} with id {idx}")
        return obj

    def delete_by_id(self, idx: int, session: Session | None = None) -> None:
        obj = self.get_by_id(idx, session)
        with begin_or_use_session(self.rdb, session, strategy=TransactionStrategy.REUSE_OR_NEW) as s:
            s.delete(obj)

    def delete_by_opt_id(self, idx: int | None, session: Session | None = None) -> None:
        if idx is None:
            return None
        self.delete_by_id(idx, session)
