#!/usr/bin/env sh

FILE=$(readlink -f "$0")
FILE_DIR=$(dirname "$FILE")
PROJECT_DIR=$(realpath $FILE_DIR/..)
REPO_DIR=$(realpath $PROJECT_DIR/..)

export PYTHONMALLOC=malloc
export PYTHONUNBUFFERED=1
export WORKER_TIMEOUT=300

cd ${PROJECT_DIR}/src
python3 entrypoint.py \
  --qw-mono-config "${REPO_DIR}/dev_data/app.yaml" \
  --reload \
  --bind 0.0.0.0:8000 \
  --workers 1 \
  --timeout 30
#  --access-logfile -
