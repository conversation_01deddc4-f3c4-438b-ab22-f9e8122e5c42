# Copilot Instructions for QualiWise Project

This document provides guidelines and context for GitHub Copilot when working with this codebase.

## Project Overview

This is a full-stack application with the following components:
- Python backend (`qw-mono` directory) using Poetry for dependency management
- Angular frontend (`qw-webui` directory)
- Docker setup for development and deployment

## Repository Structure

- `qw-mono/`: Python backend codebase
  - `src/`: Source code for backend services
  - `config/`: Configuration files
  - `docs/`: Documentation including OpenAPI specs
  - `scripts/`: Backend utility scripts

- `qw-webui/`: Angular frontend
  - `src/`: Source code for Angular application
  - `scripts/`: Frontend utility scripts

- `dev_data/`: Development data including test configurations
  - `app.yaml`: Application configuration
  - `keycloak/`: Authentication configuration
  - `postgres/`: Database initialization
  - `tenants/`: Multi-tenant test data

- `scripts/`: Global utility scripts for development and CI

## Coding Conventions

### Backend (Python)

1. **Architecture**:
   - API/Controllers layer: `qw_pfoertner/api/` - HTTP endpoints using Falcon
   - Service layer: `qw_trunk/service/` - Business logic
   - Persistence layer: `qw_monodb/table/` - Database models/tables
   - Common utilities: Various `qw_basic_*` packages for IAM, RDB, S3, etc.

2. **Type Safety**:
   - Use strict type checking with Python's type hints
   - Follow Python's type checking standards
   - Persistence layer should define its types
   - BaseModel or pydantic.dataclasses is preferred over TypedDict
   - Inline, in-function imports should be avoided
   - Informational logs should be avoided

3. **Dependency Management**:
   - Constructors should define all dependencies
   - Keep constructor code basic (store arguments)
   - Move transformations to factory methods

4. **Database Practices**:
   - Use explicit database routines with ORM query interface
   - Avoid ORM magic relationships and lazy loading
   - Use transaction management with `begin_or_use_session` context manager
   - All database modifications should happen in transactions

### Frontend (Angular)

1. **HTML Structure & Styling**:
   - Use `<div>`, `<span>`, `<p>` as primary elements
   - Avoid hierarchical elements like `<li>`, `<ul>`, `<h1>`, etc.
   - Use `<button>` only when `qw-btn-classic` is not suitable
   - All styles must be handled within HTML using Tailwind CSS

2. **Component Architecture**:
   - Use self-contained standalone components
   - Each component should manage its own state
   - Avoid tight coupling between components

3. **Method Guidelines**:
   - Use descriptive method names
   - Follow single responsibility principle
   - Mark methods as private if not accessed outside component
   - Avoid injection of components (99.99% of cases)

4. **Observables & State Management**:
   - Suffix observables with `$` (e.g., `userData$`)
   - Use `takeUntilDestroyed` for subscription management
   - Avoid nested subscriptions
   - Use signals only inside components
   - Use stateful services for state across components
   - Create `vm$` observable and subscribe to it in template
   - Prefer template-driven forms over reactive forms

5. **Localization**:
   - Use Angular's i18n for all user-facing text

## Development Environment

- Use VS Code with the extensions and settings listed in README.md
- Development can be done either:
  - In the Docker container using `scripts/dev_enter.sh [-b]`
  - In a local venv (recommended)

## Common Development Tasks

- Run application in dev mode:
  - Backend: `scripts/dev_enter.sh`, `cd qw-mono`, `scripts/dev_entrypoint.sh`
  - Frontend: `scripts/dev_enter.sh`, `cd qw-webui`, `npm install`, `ng serve --host 0.0.0.0`
  - Access at http://app.docker.localhost

- Generate API code: `scripts/dev_generate_all.sh`
- Update localization: `ng extract-i18n --output-path=src/locale`
- Reset dev data: Shut down containers, remove data directories, restart

## VS Code Settings

Refer to the IDE settings in README.md for proper TypeScript and Python configuration.

## Form Implementation Guidance

Use template-driven forms with signals and models following the pattern:
- Child input components handle field-level validation
- Parent form components maintain form state using signals
- Use computed signals for form-level validation
- Keep form logic at appropriate levels

## Tools & CI/CD

- Pre-commit hooks available in `scripts/ci_pre_commit.sh`
- CI pipeline configured in `.gitlab-ci.yml`
