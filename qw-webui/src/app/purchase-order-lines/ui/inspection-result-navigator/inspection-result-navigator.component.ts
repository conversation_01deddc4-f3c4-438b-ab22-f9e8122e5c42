import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  NgT<PERSON>plateOutlet,
} from "@angular/common"
import { Component, computed, DestroyRef, signal } from "@angular/core"
import { takeUntilDestroyed } from "@angular/core/rxjs-interop"
import { ActivatedRoute, Router, RouterLink } from "@angular/router"
import { FaIconComponent } from "@fortawesome/angular-fontawesome"
import {
  faCalendar,
  faChevronDown,
  faChevronRight,
  faCircleDown,
  faEllipsisVertical,
  faFilter,
  faTrash,
} from "@fortawesome/free-solid-svg-icons"
import { AppRoutes } from "@qw-core/constants/app-routes"
import { AuthService } from "@qw-core/services/auth.service"
import { DIALOG_MESSAGES } from "@qw-core/ui/dialog-temp/app-messages"
import { InspectionPlanNavigatorComponent } from "@qw-purchase-order-lines/ui/inspection-plan-navigator/inspection-plan-navigator.component"
import { InspectionResultDueDateInputComponent } from "@qw-purchase-order-lines/ui/inspection-result-due-date-input/inspection-result-due-date-input.component"
import { InspectionResultReportUserInputComponent } from "@qw-purchase-order-lines/ui/inspection-result-report-user-input/inspection-result-report-user-input.component"
import { QwTouchAreaDirective } from "@qw-shared-directives/qw-touch-area/qw-touch-area.directive"
import { QwDateFormatPipe } from "@qw-shared-pipes/qw-date-format/qw-date-format.pipe"
import {
  ContextMenuItem,
  ContextMenuService,
} from "@qw-shared-services/context-menu.service"
import { DialogConfirmationService } from "@qw-shared-services/dialog-confirmation.service"
import { InspectionTaskDataService } from "@qw-shared-services/inspection-task-data.service"
import {
  Inspection,
  InspectionTaskStateService,
} from "@qw-shared-services/inspection-task-state.service"
import {
  PolState,
  PolStateService,
} from "@qw-shared-services/pol-state.service"
import { UserInterfaceService } from "@qw-shared-services/user-interface.service"
import { BtnClassicComponent } from "@qw-shared-ui/btn-classic/btn-classic.component"
import {
  BehaviorSubject,
  combineLatest,
  map,
  Observable,
  of,
  shareReplay,
  switchMap,
} from "rxjs"

import { InspectionResultCreateReportComponent } from "../inspection-result-create-report/inspection-result-create-report.component"

import {
  QwInspectionReportDetails,
  QwInspectionResult,
  QwReportType,
} from "@qw-api/models"

interface ViewModel {
  polstate: PolState
  complete: Inspection[]
  incomplete: Inspection[]
  totalComplete: number
  totalIncomplete: number
  currentInspectionId: string | null
  filters: {
    isDueNextWeek: boolean
    isWithDueDate: boolean
    isWithoutDueDate: boolean
    isAssignedToMe: boolean
    isAssignedToPartner: boolean
    isOverdue: boolean
    isNonConforming: boolean
  }
  hasCompleteInspectionsActiveFilters: boolean
  hasIncompleteInspectionsActiveFilters: boolean
}

interface ReportSelectionState {
  isVisible: boolean
  selectedTypes: QwReportType[]
  currentParams: {
    inspectionId: number
    planId: number
    orderId: number
  } | null
  formData: QwInspectionReportDetails
  currentQuestionIndex: number
}

type FilterType =
  | "dueNextWeek"
  | "withDueDate"
  | "withoutDueDate"
  | "assignedToMe"
  | "assignedToPartner"
  | "overdue"
  | "nonConforming"

@Component({
  selector: "qw-inspection-result-navigator",
  templateUrl: "./inspection-result-navigator.component.html",
  standalone: true,
  imports: [
    NgIf,
    FaIconComponent,
    NgClass,
    NgTemplateOutlet,
    InspectionPlanNavigatorComponent,
    RouterLink,
    NgFor,
    BtnClassicComponent,
    InspectionResultReportUserInputComponent,
    AsyncPipe,
    QwDateFormatPipe,
    InspectionResultDueDateInputComponent,
    InspectionResultCreateReportComponent,
    QwTouchAreaDirective,
  ],
})
export class InspectionResultNavigatorComponent {
  constructor(
    private authService: AuthService,
    private contextMenuService: ContextMenuService,
    private destroyRef: DestroyRef,
    private inspectionTaskService: InspectionTaskDataService,
    private inspectionTaskStateService: InspectionTaskStateService,
    private polStateService: PolStateService,
    private route: ActivatedRoute,
    private router: Router,
    private dialogConfirmationService: DialogConfirmationService,
    private userInterfaceService: UserInterfaceService,
  ) {}

  // Manage these in a single signal state object
  isCompleteCollapsed = false
  isIncompleteCollapsed = false

  private now = signal(new Date())
  private nextWeek = computed(() => {
    const date = new Date(this.now())
    date.setDate(date.getDate() + 7)
    return date
  })

  getInspectionDueStatus = computed(() => (ins: Inspection) => {
    if (!ins.dueDate) return null

    const dueDate = new Date(ins.dueDate)
    const now = this.now()
    const nextWeek = this.nextWeek()

    if (dueDate < now) {
      return {
        type: "overdue",
        text: $localize`-overdue`,
        class: "text-qw-warn-500",
      }
    }

    if (dueDate > now && dueDate <= nextWeek) {
      return {
        type: "dueSoon",
        text: $localize`-due soon`,
        class: "text-orange-400",
      }
    }

    return null
  })

  getInspectionConformingStatus = computed(() => (ins: Inspection) => {
    if (ins.inspectorInfo.countNonConformMeasurements > 0) {
      return {
        type: "nonConforming",
        text: $localize`-out of spec`,
        class: "text-qw-warn-500",
      }
    }

    return null
  })

  // exposed to template
  protected readonly faCircleDown = faCircleDown
  protected readonly faChevronRight = faChevronRight
  protected readonly faChevronDown = faChevronDown
  protected readonly faFilter = faFilter
  protected readonly faEllipsisVertical = faEllipsisVertical

  protected readonly AppRoutes = AppRoutes

  // Filter for incomplete inspections =========================================
  private dueNextWeekFilter$ = new BehaviorSubject<boolean>(false)
  private withDueDatesFilter$ = new BehaviorSubject<boolean>(false)
  private withoutDueDatesFilter$ = new BehaviorSubject<boolean>(false)
  private assignedToMeFilter$ = new BehaviorSubject<boolean>(false)
  private assignedToPartnerFilter$ = new BehaviorSubject<boolean>(false)
  private overdueFilter$ = new BehaviorSubject<boolean>(false)
  private nonConformingFilter$ = new BehaviorSubject<boolean>(false)

  currentInspectionId$: Observable<string | null> =
    this.route.queryParamMap.pipe(map((params) => params.get("inspectionId")))

  private inspections$ = combineLatest([
    this.polStateService.getPolState(),
    this.inspectionTaskService.refreshAllTrigger$,
  ]).pipe(
    switchMap(([polstate, _]) =>
      this.inspectionTaskService.getTasksByPolId(polstate.purchaseOrderLine.id),
    ),
    switchMap((tasks) => {
      if (!tasks || tasks.length === 0) {
        return of([])
      }
      return combineLatest(
        tasks.map((task) =>
          this.inspectionTaskService.getInspectionFE(task.inspectionId).pipe(
            map(
              (inspectionDetail) =>
                ({
                  taskId: task.id,
                  inspectorTenant: task.inspectorTenant,
                  isInspectionAssignedInternally:
                    task.inspectorTenant.id === task.inspectionCreatorTenant.id,
                  isInspectionCreatedByCurrentTenant:
                    task.inspectionCreatorTenant.id ===
                    this.authService.getCurrentTenantId(),
                  inspectionId: task.inspectionId,
                  inspectionPlanId: inspectionDetail.summary.inspectionPlanId,
                  inspectionPlan: inspectionDetail.plan,
                  inspectionResult: inspectionDetail.result,
                  inspectorInfo: inspectionDetail.summary.inspectionFinish,
                  inspectionIsFinished: task.inspectionIsFinished,
                  progress: this.calculateProgress(
                    inspectionDetail.result,
                    task.inspectionId,
                    task.inspectionIsFinished,
                  ),
                  sampleSize: inspectionDetail.summary.sampleCount,
                  dueDate: task.dueDate,
                }) as Inspection,
            ),
          ),
        ),
      )
    }),
    shareReplay(1),
  )

  private inspectionNavState$ = combineLatest([
    this.polStateService.getPolState(),
    this.inspections$,
    this.currentInspectionId$,
  ]).pipe(
    map(([polstate, inspections, currentInspectionId]) => {
      const selectedInspection = inspections.find(
        (ins) => ins.inspectionId.toString() === currentInspectionId,
      )
      if (selectedInspection) {
        this.inspectionTaskStateService.updateState({
          selectedInspectionObj: selectedInspection,
        })
      }
      return { polstate, inspections, currentInspectionId }
    }),
    shareReplay(1),
  )

  vm$ = combineLatest({
    baseData: this.inspectionNavState$,
    isDueNextWeek: this.dueNextWeekFilter$,
    isWithDueDate: this.withDueDatesFilter$,
    isWithoutDueDate: this.withoutDueDatesFilter$,
    isAssignedToMe: this.assignedToMeFilter$,
    isAssignedToPartner: this.assignedToPartnerFilter$,
    isOverdue: this.overdueFilter$,
    isNonConforming: this.nonConformingFilter$,
  }).pipe(
    map(
      ({
        baseData,
        isDueNextWeek,
        isWithDueDate,
        isWithoutDueDate,
        isAssignedToMe,
        isAssignedToPartner,
        isOverdue,
        isNonConforming,
      }) => {
        const { polstate, inspections, currentInspectionId } = baseData
        const currentTenantId = this.authService.getCurrentTenantId()
        const now = new Date()

        // First separate complete and incomplete
        const complete = inspections.filter((ins) => ins.inspectionIsFinished)
        let filteredComplete = [...complete]
        const allIncomplete = inspections.filter(
          (ins) => !ins.inspectionIsFinished,
        )
        let filteredIncomplete = [...allIncomplete]

        // Apply non-conforming filter only to complete items if active
        if (isNonConforming) {
          filteredComplete = complete.filter(
            (inspection) =>
              inspection.inspectorInfo.countNonConformMeasurements > 0,
          )
        }

        // Apply other filters only to incomplete items
        if (
          isDueNextWeek ||
          isWithDueDate ||
          isWithoutDueDate ||
          isAssignedToMe ||
          isAssignedToPartner ||
          isOverdue
        ) {
          filteredIncomplete = allIncomplete.filter((inspection) => {
            const dueDate = inspection.dueDate
              ? new Date(inspection.dueDate)
              : null
            const hasDueDate = dueDate !== null
            const isAssignedToCurrentTenant =
              inspection.inspectorTenant.id === currentTenantId

            // Calculate if due next week (next 7 days)
            const nextWeek = new Date()
            nextWeek.setDate(nextWeek.getDate() + 7)
            const isDueNextWeekInspection =
              dueDate && dueDate > now && dueDate <= nextWeek

            const isOverdueInspection = dueDate && dueDate < now

            return (
              (!isDueNextWeek || isDueNextWeekInspection) &&
              (!isWithDueDate || hasDueDate) &&
              (!isWithoutDueDate || !hasDueDate) &&
              (!isAssignedToMe || isAssignedToCurrentTenant) &&
              (!isAssignedToPartner || !isAssignedToCurrentTenant) &&
              (!isOverdue || isOverdueInspection)
            )
          })
        }

        const hasIncompleteInspectionsActiveFilters =
          isDueNextWeek ||
          isWithDueDate ||
          isWithoutDueDate ||
          isAssignedToMe ||
          isAssignedToPartner ||
          isOverdue

        const hasCompleteInspectionsActiveFilters = isNonConforming

        return {
          polstate,
          complete: filteredComplete,
          incomplete: filteredIncomplete,
          totalComplete: complete.length,
          totalIncomplete: allIncomplete.length,
          currentInspectionId,
          filters: {
            isDueNextWeek,
            isWithDueDate,
            isWithoutDueDate,
            isAssignedToMe,
            isAssignedToPartner,
            isOverdue,
            isNonConforming,
          },
          hasCompleteInspectionsActiveFilters,
          hasIncompleteInspectionsActiveFilters,
        }
      },
    ),
    shareReplay(1),
  )

  private calculateProgress(
    inspectionResult: QwInspectionResult,
    inspectionId: number,
    inspectionFinished: boolean,
  ): number {
    const finishedLength = inspectionResult.availableResults.length
    const missingLength = inspectionResult.missingResults.filter(
      (result) => !result.optional,
    ).length
    const progress =
      finishedLength + missingLength === 0
        ? 0
        : Math.round((finishedLength / (finishedLength + missingLength)) * 100)

    // check if inspection is finished and marked as finish
    if (progress === 100 && !inspectionFinished) {
      this.inspectionTaskService
        .finishInspection(inspectionId)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe({
          next: (response) => {
            logger.log("Inspection finished:", response)
          },
          error: (error) => {
            logger.error("Error finishing inspection:", error)
          },
        })
    }

    return progress
  }

  toggleFilter(filter: FilterType, currentValue: boolean) {
    switch (filter) {
      case "dueNextWeek":
        this.dueNextWeekFilter$.next(!currentValue)
        break
      case "withDueDate":
        this.withDueDatesFilter$.next(!currentValue)
        break
      case "withoutDueDate":
        this.withoutDueDatesFilter$.next(!currentValue)
        break
      case "assignedToMe":
        this.assignedToMeFilter$.next(!currentValue)
        break
      case "assignedToPartner":
        this.assignedToPartnerFilter$.next(!currentValue)
        break
      case "overdue":
        this.overdueFilter$.next(!currentValue)
        break
      case "nonConforming":
        this.nonConformingFilter$.next(!currentValue)
        break
    }
  }

  clearAllFilters(event: Event): void {
    event.stopPropagation()
    this.dueNextWeekFilter$.next(false)
    this.withDueDatesFilter$.next(false)
    this.withoutDueDatesFilter$.next(false)
    this.assignedToMeFilter$.next(false)
    this.assignedToPartnerFilter$.next(false)
    this.overdueFilter$.next(false)
    this.nonConformingFilter$.next(false)
  }

  // Context menu ==============================================================

  hasContextMenuItems = computed(() => (inspection: Inspection): boolean => {
    // Check if any of these conditions is true (menu has at least one item)
    return (
      // Delete option
      inspection.isInspectionCreatedByCurrentTenant ||
      // Create Report option
      inspection.inspectionIsFinished ||
      // Update due date
      (!inspection.inspectionIsFinished &&
        inspection.isInspectionCreatedByCurrentTenant)
    )
  })

  openContextMenu(
    inspection: Inspection,
    vm: ViewModel,
    event: MouseEvent,
  ): void {
    const items: ContextMenuItem[] = [
      {
        label: $localize`Create Report`,
        icon: faCircleDown,
        action: () => {
          this.openCreateReportDialog(inspection)
        },
        visible: (ins) => ins.inspectionIsFinished,
      },
      {
        label: $localize`Update Due Date`,
        icon: faCalendar,
        action: () => {
          this.updateDueDate(inspection)
        },
        visible: (ins: Inspection) =>
          !ins.inspectionIsFinished && ins.isInspectionCreatedByCurrentTenant,
      },
      {
        label: $localize`Delete`,
        icon: faTrash,
        action: () => {
          this.confirmDelete(inspection, vm)
        },
        type: "danger",
        visible: (ins: Inspection) => ins.isInspectionCreatedByCurrentTenant,
      },
    ]

    this.contextMenuService.open(items, inspection, event)
  }

  private confirmDelete(inspection: Inspection, vm: ViewModel): void {
    this.dialogConfirmationService.show({
      title: $localize`Delete Inspection`,
      message: $localize`This action cannot be undone.`,
      type: "danger",
      confirmText: $localize`Delete`,
      cancelText: $localize`Cancel`,
      onConfirm: () => {
        this.inspectionTaskService
          .deleteInspectionFE(inspection.inspectionId)
          .subscribe({
            next: () => {
              this.dialogConfirmationService.close()
              this.userInterfaceService.showPopup(
                DIALOG_MESSAGES.DELETE_SUCCESS,
              )
              this.router.navigate([
                AppRoutes.fullPath(
                  "polInspections",
                  vm.polstate.purchaseOrderLine.id,
                ),
              ])
            },
            error: (error) => {
              logger.error("Error deleting inspection:", error)
              this.userInterfaceService.showPopup(
                DIALOG_MESSAGES.DELETE_FAIL_NOT_ALLOWED,
                4000,
              )
            },
          })
      },
    })
  }

  protected dueDateDialogVisible = signal(false)
  protected selectedTaskId = signal<number>(0)
  protected selectedDueDate = signal<string | null>(null)

  private updateDueDate(inspection: Inspection): void {
    this.selectedTaskId.set(inspection.taskId)
    this.selectedDueDate.set(inspection.dueDate)
    this.dueDateDialogVisible.set(true)
  }

  createReportDialogVisible = signal(false)
  selectedInspectionId = signal<string>("")
  selectedPlanId = signal<number>(0)
  shareSavedReportWithOtherTenant = signal(false)

  private openCreateReportDialog(inspection: Inspection): void {
    this.selectedInspectionId.set(inspection.inspectionId.toString())
    this.selectedPlanId.set(inspection.inspectionPlanId)
    this.createReportDialogVisible.set(true)
    this.shareSavedReportWithOtherTenant.set(
      !inspection.isInspectionAssignedInternally,
    ) // If internally assigned, do not share
  }

  // UI/View ===================================================================
  readonly localizedTitleForClearFilterButton = computed(
    () =>
      (hasFilters: boolean): string => {
        if (hasFilters) {
          return $localize`Clear filters`
        }
        return $localize`No filters applied`
      },
  )
}
