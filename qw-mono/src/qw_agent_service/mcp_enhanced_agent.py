"""MCP-enhanced RouterAgent implementation with FastMCP client integration."""
import asyncio
from typing import Any, Dict, Optional, Union, cast, List
from uuid import UUID, uuid4

from pydantic_ai import Agent as PydanticAgent, RunContext
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage
from fastmcp import Client
import httpx

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.models import AgentPromptRequest, AgentPromptResponse, AgentAction
from qw_agent_service.config import AgentServiceConfig
from qw_agent_service.minimal_agent import MinimalAgentContext, MinimalAgentResponse


class MCPEnhancedAgent:
    """
    MCP-enhanced RouterAgent that can use internal API tools via FastMCP client.
    """

    def __init__(
        self,
        api_key: str,
        mcp_server_url: str,
        model_name: str = "gpt-4o",
        temperature: float = 0.2,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the MCP-enhanced router agent.
        """
        self.api_key = api_key
        # Fix URL configuration - remove /mcp suffix for streamable-http
        self.mcp_server_url = mcp_server_url.rstrip('/mcp').rstrip('/')
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout
        self.logger = lf.get_logger(__name__)
        self.lf = lf
        self.agent_available = False
        self.usage = Usage()

        # Session management
        self.session_token: Optional[str] = None

        # MCP client
        self.mcp_client: Optional[Client] = None
        self.mcp_tools_available = False
        self.available_mcp_tools = []

        self._configure_agent()

    async def _setup_mcp_client(self) -> None:
        """Setup MCP client connection with dependency check."""
        try:
            self.logger.info(f"Setting up MCP client for: {self.mcp_server_url}")

            # First check if MCP server is available
            if not await self._check_mcp_server_health():
                self.logger.warning("MCP server not available, skipping MCP client setup")
                return

            # Create FastMCP client with session token if available
            self.mcp_client = self._create_mcp_client()

            # Test connection and get available tools
            await self._discover_mcp_tools()

            self.logger.info("MCP client setup completed successfully")

        except Exception as e:
            self.logger.error(f"Failed to setup MCP client: {e}")
            self.mcp_client = None
            self.mcp_tools_available = False

    def _create_mcp_client(self) -> Client:
        """Create MCP client with authentication headers if session token is available."""
        from fastmcp.client.transports import StreamableHttpTransport

        if self.session_token:
            # Create transport with session token in headers
            transport = StreamableHttpTransport(
                url=self.mcp_server_url,
                headers={"X-Session-Token": self.session_token}
            )
            self.logger.info(f"Created MCP client with session token: {self.session_token[:10]}...")
            return Client(transport)
        else:
            # Create client without authentication
            self.logger.info("Created MCP client without session token")
            return Client(self.mcp_server_url)

    async def _check_mcp_server_health(self) -> bool:
        """Check if MCP server is available."""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                # Try to connect to the MCP server
                response = await client.get(f"{self.mcp_server_url}/")
                # For MCP streamable-http, we expect a specific response
                return response.status_code in [200, 406]  # 406 is expected for non-MCP requests
        except Exception as e:
            self.logger.info(f"MCP server health check failed: {e}")
            return False

    async def _discover_mcp_tools(self) -> None:
        """Discover available tools from MCP server."""
        try:
            if not self.mcp_client:
                return

            # Connect to MCP server and list tools
            async with self.mcp_client:
                tools = await self.mcp_client.list_tools()
                self.logger.info(f"Discovered {len(tools)} MCP tools: {[tool.name for tool in tools]}")
                self.mcp_tools_available = len(tools) > 0

                # Store available tools for reference
                self.available_mcp_tools = [tool.name for tool in tools]

        except Exception as e:
            self.logger.error(f"Failed to discover MCP tools: {e}")
            self.mcp_tools_available = False

        # Initialize available tools list
        self.available_mcp_tools = []

    def _configure_agent(self) -> None:
        """
        Configure the pydantic-ai agent with MCP tools.
        """
        try:
            # Prepare model settings
            model_settings = ModelSettings(
                temperature=self.temperature,
                timeout=self.timeout,
            )

            # Setup OpenAI model with provider
            provider = OpenAIProvider(api_key=self.api_key)

            # Extract model name without provider prefix if needed
            model_name = self.model_name
            if model_name.startswith("openai:"):
                model_name = model_name.split(":")[-1]

            model = OpenAIModel(model_name, provider=provider)

            # Create the agent with MCP tools
            self.agent = PydanticAgent[MinimalAgentContext, str](
                model=model,
                model_settings=model_settings,
                retries=2,
                system_prompt=self._get_system_prompt(),
                deps_type=MinimalAgentContext,
            )

            # Add MCP tools to the agent
            self._register_mcp_tools()

            self.agent_available = True
            self.logger.info("MCP-enhanced router agent configured successfully")

        except Exception as e:
            self.logger.error(f"Failed to configure MCP-enhanced router agent: {e}")
            self.agent_available = False

    def _register_mcp_tools(self) -> None:
        """Register basic tools with the pydantic-ai agent."""

        @self.agent.tool
        async def set_session_token(ctx: RunContext[MinimalAgentContext], token: str) -> str:
            """Set the session token for API authentication."""
            self.session_token = token
            self.logger.info(f"Session token set: {token[:10]}...")

            # Recreate MCP client with new session token
            if self.mcp_client:
                try:
                    self.mcp_client = self._create_mcp_client()
                    self.logger.info("MCP client updated with new session token")
                except Exception as e:
                    self.logger.error(f"Failed to update MCP client with session token: {e}")

            return f"Session token configured successfully"

        @self.agent.tool
        async def get_system_status(ctx: RunContext[MinimalAgentContext]) -> str:
            """Get the current system status."""
            status = "System is operational."
            if self.mcp_tools_available:
                status += f" MCP integration available with {len(self.available_mcp_tools)} tools."
                if self.available_mcp_tools:
                    status += f" Available tools: {', '.join(self.available_mcp_tools)}"
            else:
                status += " MCP integration configured but no tools available."
            return status

        @self.agent.tool
        async def list_available_mcp_tools(ctx: RunContext[MinimalAgentContext]) -> str:
            """List all available MCP tools from the server."""
            try:
                if not self.mcp_client:
                    return "MCP client not available"

                async with self.mcp_client:
                    tools = await self.mcp_client.list_tools()
                    if tools:
                        tool_list = []
                        for tool in tools:
                            description = tool.description or "No description available"
                            tool_list.append(f"- {tool.name}: {description}")
                        return f"Available MCP tools ({len(tools)}):\n" + "\n".join(tool_list)
                    else:
                        return "No MCP tools are currently available"

            except Exception as e:
                self.logger.error(f"Error listing MCP tools: {e}")
                return f"Error retrieving MCP tools: {str(e)}"

        @self.agent.tool
        async def call_mcp_tool(ctx: RunContext[MinimalAgentContext], tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> str:
            """Call an MCP tool by name with arguments."""
            try:
                if not self.mcp_client:
                    return f"MCP client not available"

                if not self.mcp_tools_available:
                    return f"No MCP tools are available"

                async with self.mcp_client:
                    result = await self.mcp_client.call_tool(tool_name, arguments or {})
                    # Extract content from result
                    if result and len(result) > 0:
                        content = result[0]
                        # Try to get text content, fallback to string representation
                        try:
                            if hasattr(content, 'text'):
                                return str(getattr(content, 'text', ''))
                            else:
                                return str(content)
                        except Exception:
                            return str(content)
                    return f"Tool {tool_name} executed successfully"

            except Exception as e:
                self.logger.error(f"Error calling MCP tool {tool_name}: {e}")
                return f"Error executing tool {tool_name}: {str(e)}"

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the MCP-enhanced router agent.
        """
        return """
        You are a helpful AI assistant for a quality management system with access to internal APIs via MCP.

        You can help users with:
        - General questions about quality management
        - Information about inspection processes
        - Guidance on material certificates and technical drawings
        - Accessing and searching materials in the system
        - Workflow assistance

        You have access to the following tools:
        - set_session_token: Set authentication token for API access
        - get_system_status: Check system and MCP integration status
        - list_available_mcp_tools: Get a detailed list of all available MCP tools
        - call_mcp_tool: Call any available MCP tool by name with arguments

        To use MCP tools, first use list_available_mcp_tools to see what tools are available,
        then use call_mcp_tool with the tool name and required arguments.

        Before accessing any data, you may need to set a session token for authentication.
        Always provide helpful, accurate responses and use the available tools when appropriate.

        Keep your responses concise and professional.
        """

    async def set_session_token(self, token: str) -> None:
        """Set session token for API authentication."""
        self.session_token = token
        self.logger.info(f"Session token set for agent: {token[:10]}...")

    async def process(
        self,
        prompt: str,
        context: Optional[Dict[str, Any]] = None,
        usage: Optional[Usage] = None,
    ) -> MinimalAgentResponse:
        """
        Process a user prompt and return a response with MCP tool access.
        """
        if not self.agent_available:
            self.logger.error("Agent not available for processing")
            return MinimalAgentResponse(
                message="I'm currently unavailable. Please try again later.",
                actions=[]
            )

        # Setup MCP client if not already done (but don't block if server unavailable)
        if not self.mcp_client:
            await self._setup_mcp_client()

        try:
            # Prepare context - MinimalAgentContext inherits from Dict[str, Any]
            agent_context = MinimalAgentContext(context or {})

            # Run the agent with MCP tools available
            result = await self.agent.run(prompt, deps=agent_context, usage=usage)

            # Return response
            response_message = result.output if result.output else "I'm here to help! How can I assist you?"

            self.logger.info(f"MCP-enhanced agent processed prompt successfully: {len(response_message)} chars")

            return MinimalAgentResponse(
                message=response_message,
                actions=[]  # Actions could be extracted from tool calls in the future
            )

        except Exception as e:
            self.logger.error(f"Error processing prompt with MCP-enhanced agent: {e}")
            return MinimalAgentResponse(
                message="I encountered an error processing your request. Please try again.",
                actions=[]
            )

    async def cleanup(self) -> None:
        """Cleanup resources."""
        self.logger.info("Cleaning up MCP-enhanced agent resources")

    @classmethod
    def from_config(cls, config: AgentServiceConfig, mcp_server_url: str, lf: LogFactory = NO_LOG_FACTORY) -> "MCPEnhancedAgent":
        """
        Create MCPEnhancedAgent from configuration.
        """
        return cls(
            api_key=config.openai_api_key,
            mcp_server_url=mcp_server_url,
            model_name=config.agent_model_name,
            temperature=config.agent_temperature,
            timeout=config.agent_timeout,
            lf=lf
        )
