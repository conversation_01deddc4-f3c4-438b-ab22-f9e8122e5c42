"""This service is responsible for finding GDT boxes."""

import re
import time
from typing import Dict, List, Set, Union

from qw_drawing_toolkit.pdf.models.analysis_models import DatumBox, SymbolBox, SymbolMapperBox, TextBox
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysisContext
from qw_drawing_toolkit.pdf.models.gdt_models import GdtBox, GdtDatumReferenceBox
from qw_drawing_toolkit.pdf.models.geometry_models import LineDirection, LineSelection
from qw_drawing_toolkit.pdf.parser.geometry import LineCollection
from qw_drawing_toolkit.pdf.parser.symbol_mapper import Symbol, SymbolMapper
from qw_drawing_toolkit.pdf.utils.common_utils import find_bounding_box, find_text_boxes_in_box
from qw_drawing_toolkit.pdf.utils.gdt_utils import (
    compare_text_boxes,
    find_potential_datum_box,
    get_box_height_relative_to_text_direction,
    is_gd_t_candidate,
    merge_adjacent_text_boxes,
    rescan_for_missing_datum_box_alternate,
    sort_text_boxes,
)
from qw_drawing_toolkit.pdf.utils.geometry_utils import (
    find_lines_in_box,
    length_of_line,
    scale_box_from_center,
    sort_lines_by_x,
    sort_lines_by_y,
)
from qw_drawing_toolkit.pdf.utils.image_utils import extract_and_preprocess_image_within_bbox


class GdtService:
    def __init__(self, debug: bool):
        self.debug = debug

    def find_gd_t_boxes(
        self,
        ctx: DrawingAnalysisContext,
        extended_lines: LineCollection,
    ) -> tuple[
        List[GdtBox],
        List[TextBox],
        List[Union[TextBox, SymbolBox]],
        Dict[str, DatumBox],
        List[GdtDatumReferenceBox],
        Set[DatumBox],
    ]:
        """
        Find geometric dimensioning and tolerancing boxes in the drawing.

        Args:
            ctx: Drawing analysis context
            extended_lines: Extended line collection

        Returns:
            tuple: (
                gdt_boxes,
                updated_text_boxes_measurements_no_symbol,
                updated_text_boxes_measurement_symbols,
                updated_string_to_datum_box_map,
                updated_datum_reference_boxes_left_to_map,
                datum_boxes
            )
        """
        start_time = time.time()
        gd_t_boxes = []
        datum_boxes = set()

        # Create copies to avoid modifying the original collections
        updated_text_boxes_measurements_no_symbol = ctx.text_boxes_measurements_no_symbol.copy()
        updated_text_boxes_measurement_symbols = ctx.text_boxes_measurement_symbols.copy()
        updated_string_to_datum_box_map = ctx.string_to_datum_box_map.copy()
        updated_datum_reference_boxes_left_to_map = ctx.datum_reference_boxes_left_to_map.copy()

        min_length = 3 * ctx.point_proximity_tolerance
        lvs = extended_lines.select(LineSelection(only_dir=LineDirection.VERTICAL, min_norm=min_length))
        lhs = extended_lines.select(LineSelection(only_dir=LineDirection.HORIZONTAL, min_norm=min_length))

        for i in range(len(lvs)):
            # TODO replace this with _find_adjacent_lines() after it has been refactored to be
            #  compatible with LineCollection or even moved to geometry.py
            y1 = lvs.array[i, LineCollection.COL_Y1].item()
            y2 = lvs.array[i, LineCollection.COL_Y2].item()
            y_min = min(y1, y2) - ctx.point_proximity_tolerance
            y_max = max(y1, y2) + ctx.point_proximity_tolerance

            candidates = lhs.select(
                selection=LineSelection(
                    min_y1=y_min,
                    min_y2=y_min,
                    max_y1=y_max,
                    max_y2=y_max,
                )
            )

            x1 = lvs.array[i, LineCollection.COL_X1].item()
            x2 = lvs.array[i, LineCollection.COL_X2].item()
            x_min = min(x1, x2) - ctx.point_proximity_tolerance
            x_max = max(x1, x2) + ctx.point_proximity_tolerance
            cx1 = candidates.array[:, LineCollection.COL_X1]
            cx2 = candidates.array[:, LineCollection.COL_X2]
            mask_intersection = ((cx1 >= x_min) & (cx1 <= x_max)) | ((cx2 >= x_min) & (cx2 <= x_max))

            adjacent_horizontals = LineCollection(
                array=candidates.array[mask_intersection, :],
                has_direction=candidates.has_direction,
                has_distance=candidates.has_distance,
            ).get_all_as_line_segment()

            if len(adjacent_horizontals) < 2:
                continue

            if len(adjacent_horizontals) == 2:
                potential_datum_box: DatumBox | None = find_potential_datum_box(
                    lvs.get_as_line_segment(i),
                    adjacent_horizontals[0],
                    adjacent_horizontals[1],
                    updated_text_boxes_measurements_no_symbol,
                    ctx.point_proximity_tolerance,
                )
                if potential_datum_box:
                    updated_string_to_datum_box_map[potential_datum_box.datum] = potential_datum_box
                    datum_boxes.add(potential_datum_box)

            adjacent_horizontals = sorted(adjacent_horizontals, key=length_of_line, reverse=True)

            length = length_of_line(adjacent_horizontals[0])

            for horizontal in adjacent_horizontals:
                if not abs(length - length_of_line(horizontal)) <= ctx.point_proximity_tolerance:
                    continue

            adjacent_horizontals = sort_lines_by_y(adjacent_horizontals)

            line_index = 0
            while line_index < len(adjacent_horizontals) - 1:
                adjacent_horizontal_1 = adjacent_horizontals[line_index]
                adjacent_horizontal_2 = adjacent_horizontals[line_index + 1]
                line_index += 1

                a_h_1_y = adjacent_horizontal_1[0][1]
                a_h_2_y = adjacent_horizontal_2[0][1]

                max_a_h_y = max(a_h_1_y, a_h_2_y)
                min_a_h_y = min(a_h_1_y, a_h_2_y)

                # max_v_y = max(extended_line[0][1], extended_line[1][1])
                # min_v_y = min(extended_line[0][1], extended_line[1][1])

                length_a_h_1 = length_of_line(adjacent_horizontal_1)
                length_a_h_2 = length_of_line(adjacent_horizontal_2)

                length_vertical = lvs.array[i, LineCollection.COL_NORM] / (len(adjacent_horizontals) - 1)

                # max_v_y = min_v_y + length_vertical

                # diff_max_y = abs(max_a_h_y - max_v_y)
                # diff_min_y = abs(min_a_h_y - min_v_y)

                if (
                    abs(length_a_h_1 - length_a_h_2) < ctx.point_proximity_tolerance
                    and length_a_h_1 > 1.5 * length_vertical
                    and length_a_h_2 > 1.5 * length_vertical
                    # (diff_min_y <= ctx.point_proximity_tolerance or diff_max_y <=
                    # ctx.point_proximity_tolerance)
                ):
                    # x1 = min(extended_line[0][0], adjacent_horizontal_1[0][0], adjacent_horizontal_2[
                    # 0][0])
                    # y1 = min(min_v_y, min_a_h_y) x2 = max(extended_line[1][0],
                    # adjacent_horizontal_1[1][0], adjacent_horizontal_2[1][0]) y2 = max(max_v_y, max_a_h_y)
                    x1 = min(adjacent_horizontal_1[0][0], adjacent_horizontal_2[0][0])
                    y1 = min_a_h_y
                    x2 = max(adjacent_horizontal_1[1][0], adjacent_horizontal_2[1][0])
                    y2 = max_a_h_y
                    bbox = (x1, y1, x2, y2)
                    if is_gd_t_candidate(
                        bbox, updated_text_boxes_measurements_no_symbol, ctx.point_proximity_tolerance
                    ):
                        gd_t_boxes.append(bbox)

        unique_boxes = {}

        for bbox in gd_t_boxes:
            min_y, max_y = bbox[1], bbox[3]
            # min_x, max_x = bbox[0], bbox[2]
            # width = max_x - min_x
            # height = max_y - min_y

            unique_boxes[(min_y, max_y)] = bbox

        result: List[GdtBox] = []
        for u_bbox in list(unique_boxes.values()):
            gd_t_block_sections = []
            lines_in_bbox = find_lines_in_box(
                u_bbox, ctx.lines_drawing, ctx.point_proximity_tolerance, direction=LineDirection.VERTICAL
            )
            lines_in_bbox = sort_lines_by_x(lines_in_bbox)
            sorted_lines = sorted(lines_in_bbox, key=lambda li: li[0][0])

            filtered_lines = []
            prev_x = None

            for line in sorted_lines:
                x = line[0][0]
                if prev_x is None or abs(x - prev_x) > ctx.point_proximity_tolerance >= abs(
                    length_of_line(line) - abs(u_bbox[1] - u_bbox[3])
                ):
                    filtered_lines.append(line)
                    prev_x = x

            lines_in_bbox = filtered_lines

            line_index = 0
            while line_index < len(lines_in_bbox) - 1:
                gd_t_block_sections.append(
                    find_bounding_box(
                        [
                            lines_in_bbox[line_index][0],
                            lines_in_bbox[line_index][1],
                            lines_in_bbox[line_index + 1][0],
                            lines_in_bbox[line_index + 1][1],
                        ]
                    )
                )
                line_index += 1

            if len(gd_t_block_sections) < 2:
                continue

            symbol_block = gd_t_block_sections[0]
            symbol = SymbolMapper.map_image_to_symbol(
                extract_and_preprocess_image_within_bbox(ctx, scale_box_from_center(symbol_block, 0.9))
            )

            outer_bbox_text_boxes: List[TextBox] = find_text_boxes_in_box(
                u_bbox, ctx.text_boxes_unfiltered, ctx.point_proximity_tolerance
            )
            symbol_block_text_boxes: List[TextBox] = find_text_boxes_in_box(
                symbol_block, ctx.text_boxes_unfiltered, ctx.point_proximity_tolerance
            )
            measurement_text_box: TextBox = (
                outer_bbox_text_boxes[0] if len(symbol_block_text_boxes) == 0 else outer_bbox_text_boxes[1]
            )

            outer_bbox_text_boxes = sorted(outer_bbox_text_boxes, key=lambda tb: tb.bbox[0])

            text_box_count_diff = len(outer_bbox_text_boxes) - len(symbol_block_text_boxes)

            if text_box_count_diff < len(gd_t_block_sections) - 1:
                if text_box_count_diff == 1:
                    split = measurement_text_box.text.split()
                    if len(split) == len(gd_t_block_sections) - 1:
                        font = measurement_text_box.font
                        # symbol_lines = None
                        symbol_text_box = None
                        datum_boxes_list = []

                        if len(symbol_block_text_boxes) == 0:
                            # symbol_lines = self._find_lines_in_box(symbol_block, self._lines_drawing, tolerance=-2)
                            pass
                        else:
                            symbol_text_box = outer_bbox_text_boxes[0]
                            symbol_text_box.is_gd_t = True
                            symbol_text_box.is_symbol = True
                            # symbol_text_box.bbox = self._scale_box_from_center(gd_t_block_sections[0], 0.9)
                            if symbol_text_box in updated_text_boxes_measurement_symbols:
                                updated_text_boxes_measurement_symbols.remove(symbol_text_box)
                            if symbol_text_box in updated_text_boxes_measurements_no_symbol:
                                updated_text_boxes_measurements_no_symbol.remove(symbol_text_box)

                        updated_text_boxes_measurements_no_symbol.remove(measurement_text_box)

                        measurement_text_box = TextBox(
                            split[0],
                            font,
                            measurement_text_box.font_size,
                            scale_box_from_center(gd_t_block_sections[1], 0.8),
                            measurement_text_box.direction,
                            True,
                            False,
                        )

                        section_index = 2
                        while section_index < len(gd_t_block_sections):
                            split_2 = split[section_index - 1].split("-")

                            for datum_string in split_2:
                                datum_string = datum_string.strip()
                                datum_box = (
                                    updated_string_to_datum_box_map[datum_string]
                                    if datum_string in updated_string_to_datum_box_map.keys()
                                    else None
                                )

                                reference_box = GdtDatumReferenceBox(
                                    datum_string,
                                    scale_box_from_center(gd_t_block_sections[section_index], 0.8),
                                    datum_box,
                                )

                                datum_boxes_list.append(reference_box)

                                if not datum_box:
                                    updated_datum_reference_boxes_left_to_map.append(reference_box)

                            section_index += 1

                        result.append(
                            GdtBox(
                                SymbolMapperBox(symbol, scale_box_from_center(symbol_block, 0.9)),
                                measurement_text_box,
                                datum_boxes_list,
                                u_bbox,
                            )
                        )

                    else:
                        continue
                else:
                    continue
            elif text_box_count_diff == len(gd_t_block_sections) - 1:
                # symbol_lines = None
                symbol_text_box = None
                datum_boxes_list = []
                if len(symbol_block_text_boxes) == 0:
                    # symbol_lines = self._find_lines_in_box(gd_t_block_sections[0], self._lines_drawing, tolerance=-2)
                    pass
                else:
                    symbol_text_box = outer_bbox_text_boxes[0]
                    symbol_text_box.is_gd_t = True
                    symbol_text_box.is_symbol = True
                    if symbol_text_box in updated_text_boxes_measurement_symbols:
                        updated_text_boxes_measurement_symbols.remove(symbol_text_box)
                    if symbol_text_box in updated_text_boxes_measurements_no_symbol:
                        updated_text_boxes_measurements_no_symbol.remove(symbol_text_box)

                measurement_text_box.is_gd_t = True

                section_index = 2
                while section_index < len(gd_t_block_sections):
                    datum_text_box = (
                        outer_bbox_text_boxes[section_index - 1]
                        if len(symbol_block_text_boxes) == 0
                        else outer_bbox_text_boxes[section_index]
                    )

                    split_2 = datum_text_box.text.split("-")

                    for datum_string in split_2:
                        datum_string = datum_string.strip()
                        datum_box = (
                            updated_string_to_datum_box_map[datum_string]
                            if datum_string in updated_string_to_datum_box_map.keys()
                            else None
                        )

                        reference_box = GdtDatumReferenceBox(
                            datum_string,
                            scale_box_from_center(gd_t_block_sections[section_index], 0.8),
                            datum_box,
                        )

                        if not datum_box:
                            updated_datum_reference_boxes_left_to_map.append(reference_box)

                        datum_boxes_list.append(reference_box)

                    section_index += 1

                result.append(
                    GdtBox(
                        SymbolMapperBox(symbol, scale_box_from_center(gd_t_block_sections[0], 0.9)),
                        measurement_text_box,
                        datum_boxes_list,
                        u_bbox,
                    )
                )

                updated_text_boxes_measurements_no_symbol.remove(measurement_text_box)
            else:
                continue
        if self.debug:
            print("GdtService._find_gd_t_boxes: " + str(time.time() - start_time) + " Seconds")

        return (
            result,
            updated_text_boxes_measurements_no_symbol,
            updated_text_boxes_measurement_symbols,
            updated_string_to_datum_box_map,
            updated_datum_reference_boxes_left_to_map,
            datum_boxes,
        )

    def map_datum_reference_boxes_left_to_map(
        self,
        ctx: DrawingAnalysisContext,
    ) -> tuple[List[GdtDatumReferenceBox], Dict[str, DatumBox]]:
        """
        Map datum reference boxes that don't have a corresponding datum box.

        Args:
            ctx: Drawing analysis context

        Returns:
            tuple: (
                updated_datum_reference_boxes_left_to_map,
                updated_string_to_datum_box_map
            )
        """
        start_time = time.time()

        updated_datum_reference_boxes_left_to_map = ctx.datum_reference_boxes_left_to_map.copy()
        updated_string_to_datum_box_map = ctx.string_to_datum_box_map.copy()
        # Create a set of datum boxes from the context
        datum_boxes = set(datum_box for datum_box in ctx.string_to_datum_box_map.values())

        for datum_reference_box in updated_datum_reference_boxes_left_to_map:
            if datum_reference_box.datum not in updated_string_to_datum_box_map.keys():
                # Try to find the missing datum box
                (
                    updated_string_to_datum_box_map,
                    datum_boxes,
                    new_datum_box,
                ) = rescan_for_missing_datum_box_alternate(
                    datum_reference_box.datum,
                    updated_string_to_datum_box_map,
                    datum_boxes,
                    ctx.text_boxes_unfiltered,
                    ctx.max_width,
                    ctx.max_height,
                    ctx.point_proximity_tolerance,
                    ctx.lines,
                )
                if new_datum_box:
                    updated_string_to_datum_box_map[datum_reference_box.datum] = new_datum_box

            # Update the reference if the datum is now in the map
            if datum_reference_box.datum in updated_string_to_datum_box_map.keys():
                datum_reference_box.datum_box = updated_string_to_datum_box_map[datum_reference_box.datum]

        if self.debug:
            print(f"GdtService.map_datum_reference_boxes_left_to_map: {time.time() - start_time} seconds")

        return updated_datum_reference_boxes_left_to_map, updated_string_to_datum_box_map

    def merge_segmented_text_boxes(self, ctx: DrawingAnalysisContext) -> List[TextBox]:
        start_time = time.time()
        text_boxes_measurements_merged: List[TextBox] = ctx.text_boxes_measurements_merged.copy()
        text_boxes: Set[Union[TextBox, SymbolBox]] = set()
        text_boxes.update(ctx.text_boxes_measurements_no_symbol)
        text_boxes.update(ctx.text_boxes_measurement_symbols)

        merged_text_box_map: Dict[TextBox | SymbolBox, Set[Union[TextBox, SymbolBox]]] = {}
        for box in text_boxes:
            new_mappings = merge_adjacent_text_boxes(
                ctx.text_boxes_measurements_no_symbol,
                ctx.text_boxes_measurement_symbols,
                box,
                ctx.point_proximity_tolerance,
            )

            for key, value in new_mappings.items():
                if key in merged_text_box_map:
                    merged_text_box_map[key] |= value
                else:
                    merged_text_box_map[key] = value

        # this probably is better done recursively as it only finds closed circles with the depth of 2
        for box in merged_text_box_map.keys():
            for other_text_box in merged_text_box_map[box]:
                for other_text_box_2 in merged_text_box_map[other_text_box]:
                    merged_text_box_map[box] = merged_text_box_map[box].union(merged_text_box_map[other_text_box])
                    merged_text_box_map[box] = merged_text_box_map[box].union(merged_text_box_map[other_text_box_2])
                    merged_text_box_map[other_text_box] = merged_text_box_map[other_text_box].union(
                        merged_text_box_map[box]
                    )
                    merged_text_box_map[other_text_box] = merged_text_box_map[other_text_box].union(
                        merged_text_box_map[other_text_box_2]
                    )
                    merged_text_box_map[other_text_box_2] = merged_text_box_map[other_text_box_2].union(
                        merged_text_box_map[box]
                    )
                    merged_text_box_map[other_text_box_2] = merged_text_box_map[other_text_box_2].union(
                        merged_text_box_map[other_text_box]
                    )

        unique_merges: List[Set[Union[TextBox, SymbolBox]]] = []
        text_boxes_that_have_been_merged: Set[Union[TextBox, SymbolBox]] = set()

        for key in merged_text_box_map.keys():
            merged_set = merged_text_box_map[key]
            already_exists = False
            for unique_merge in unique_merges:
                if key in unique_merge:
                    already_exists = True
                    break

            if not already_exists:
                unique_merges.append(merged_set)

        for unique_merge in unique_merges:
            if len(unique_merge) > 1:
                direction = (0.0, 0.0)
                for box in unique_merge:
                    if not box.direction == (0, 0):
                        direction = box.direction
                        break

                max_font_size = 0.0
                for box in unique_merge:
                    box.direction = direction
                    max_font_size = max(max_font_size, box.font_size)

                min_box_height = min(get_box_height_relative_to_text_direction(box) for box in unique_merge)

                max_box_height = max(get_box_height_relative_to_text_direction(box) for box in unique_merge)

                tolerance_boxes: Set[TextBox | SymbolBox] = set()
                other_boxes: Set[TextBox | SymbolBox] = set()

                for box in unique_merge:
                    height = get_box_height_relative_to_text_direction(box)

                    if (
                        not box.is_symbol
                        and abs(max_box_height - min_box_height) > ctx.point_proximity_tolerance
                        and abs(min_box_height - height) < ctx.point_proximity_tolerance
                        and abs(box.font_size - max_font_size) > ctx.point_proximity_tolerance
                    ):
                        tolerance_boxes.add(box)
                    else:
                        other_boxes.add(box)

                unique_merge_list: List[TextBox | SymbolBox] = []

                sorted_tolerance_boxes = sort_text_boxes(tolerance_boxes, ctx.point_proximity_tolerance, True)
                sorted_other_boxes = sort_text_boxes(other_boxes, ctx.point_proximity_tolerance)

                if len(sorted_tolerance_boxes) == 2:
                    sorted_tolerance_boxes[0].text = (
                        f"{ctx.QW_UT_PREFIX}(" + str(sorted_tolerance_boxes[0].text.strip()) + ")"
                    )
                    sorted_tolerance_boxes[1].text = (
                        f"{ctx.QW_LT_PREFIX}(" + str(sorted_tolerance_boxes[1].text.strip()) + ")"
                    )
                if len(sorted_tolerance_boxes) == 1 and len(sorted_other_boxes) > 0:
                    tolerance_box = sorted_tolerance_boxes[0]
                    value_box = sorted_other_boxes[len(sorted_other_boxes) - 1]
                    compared_value = compare_text_boxes(tolerance_box, value_box, ctx.point_proximity_tolerance, True)

                    if compared_value < 0:
                        sorted_tolerance_boxes[0].text = (
                            f"{ctx.QW_UT_PREFIX}(" + str(sorted_tolerance_boxes[0].text.strip()) + ")"
                        )
                    elif compared_value > 0:
                        sorted_tolerance_boxes[0].text = (
                            f"{ctx.QW_LT_PREFIX}(" + str(sorted_tolerance_boxes[0].text.strip()) + ")"
                        )

                merge = True
                pattern_number = r"^\s*\d+(?:[.,]\d+)?\s*$"

                if len(sorted_other_boxes) > 1 and len(sorted_tolerance_boxes) == 0:
                    merge = False
                    for box in sorted_other_boxes:
                        if not re.fullmatch(pattern_number, box.text):
                            merge = True
                            break

                if merge:
                    unique_merge_list.extend(sorted_other_boxes)
                    unique_merge_list.extend(sorted_tolerance_boxes)

                    bbox_coords = []

                    merged_string = ""
                    font_size = 1.0
                    for box in unique_merge_list:
                        bbox_coords.append((box.bbox[0], box.bbox[1]))
                        bbox_coords.append((box.bbox[2], box.bbox[3]))
                        merged_string += box.text + " "
                        text_boxes_that_have_been_merged.add(box)
                        font_size = max(box.font_size, font_size)

                    if len(bbox_coords) > 1:
                        bbox = find_bounding_box(bbox_coords)
                        text_boxes_measurements_merged.append(
                            TextBox(
                                merged_string, Symbol.get_symbol_dummy_font(), font_size, bbox, direction, False, False
                            )
                        )

        for box in ctx.text_boxes_measurements_no_symbol:
            if box not in text_boxes_that_have_been_merged:
                text_boxes_measurements_merged.append(box)

        for box in ctx.text_boxes_measurement_symbols:
            if (
                box not in text_boxes_that_have_been_merged
                and isinstance(box, SymbolBox)
                and not box.symbol == Symbol.UNKNOWN
            ):
                text_boxes_measurements_merged.append(box)

        if self.debug:
            print("GdtService._merge_segmented_text_boxes: " + str(time.time() - start_time) + " Seconds")

        return text_boxes_measurements_merged
