{"name": "DIN_1688-1:1998-08", "tables": [{"name": "Tabelle 1", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 3.6, "lower": -3.6}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1250, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 4.1, "lower": -4.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 2.6, "lower": -2.6}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 2.9, "lower": -2.9}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1250, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1250, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 2.6, "lower": -2.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1250, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg"]], "tolerance": {"upper": 3.3, "lower": -3.3}}]}, {"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 Formg."]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["GTA 16/5 n. Formg."]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 Formg."]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg."]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg."]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["GTA 15/5 n. Formg."]], "tolerance": {"upper": 2.2, "lower": -2.2}}]}]}