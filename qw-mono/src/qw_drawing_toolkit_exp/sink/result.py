from typing import List, Sequence

from pydantic import BaseModel, Field, model_validator
from typing_extensions import Self


class ComparisonMatch(BaseModel):
    """Match of two bounding box based detections in a drawing"""

    actual: BaseModel | None
    matched_from_expected: List[BaseModel] = Field(description="More than 1 match is considered an error")

    @model_validator(mode="after")
    def ensure_not_empty(self) -> Self:
        if self.actual is None and len(self.matched_from_expected) == 0:
            raise ValueError("Both the actual as well as the matched results must not be empty")
        return self

    @property
    def misses_expected(self) -> bool:
        return self.actual is not None and len(self.matched_from_expected) == 0

    def is_correct(self, ignore_properties: Sequence[str] = ()) -> bool:
        if len(self.matched_from_expected) != 1 or self.actual is None:
            return False
        exp_dict = self.matched_from_expected[0].model_dump()
        act_dict = self.actual.model_dump()
        for prop in ignore_properties:
            exp_dict.pop(prop)
            act_dict.pop(prop)
        return exp_dict == act_dict


class ComparisonCounts(BaseModel):
    actual: int = 0
    expected: int = 0
    match_correct: int = 0
    match_incorrect: int = Field(
        default=0,
        description="Either 1 match with incorrect data or matching to more than 1",
    )
    match_without_actual: int = Field(
        default=0,
        description="Matches where the result from the actual output was missing",
    )
    match_without_expected: int = Field(
        default=0,
        description="Matches where the result from the expected output was missing",
    )

    def add(self, other_counts: Self) -> None:
        self.actual += other_counts.actual
        self.expected += other_counts.expected
        self.match_correct += other_counts.match_correct
        self.match_incorrect += other_counts.match_incorrect
        self.match_without_actual += other_counts.match_without_actual
        self.match_without_expected += other_counts.match_without_expected


class ComparisonRates(BaseModel):
    precision: float = Field(
        ge=0,
        le=1,
        description="How many of the actual detections were correct",
    )
    recall: float = Field(
        ge=0,
        le=1,
        description="How many of the expected detections were actually detected?",
    )
    misdetection: float = Field(
        ge=0,
        le=1,
        description="How many of the actual detections were unexpected and have no ground truth match",
    )

    @classmethod
    def from_counts(cls, counts: ComparisonCounts) -> Self:
        return cls(
            precision=cls.compute_precision(
                counts.match_correct, counts.match_incorrect, counts.match_without_expected
            ),
            recall=cls.compute_recall(counts.match_without_actual, counts.expected),
            misdetection=cls.compute_misdetection(counts.match_without_expected, counts.actual),
        )

    @classmethod
    def compute_precision(
        cls, count_match_correct: int, count_match_incorrect: int, count_match_without_expected: int
    ) -> float:
        denominator = count_match_correct + count_match_incorrect + count_match_without_expected
        if denominator == 0:
            return 0
        return count_match_correct / denominator

    @classmethod
    def compute_recall(cls, count_match_without_actual: int, count_expected: int) -> float:
        if count_expected == 0:
            return 0
        return 1 - (count_match_without_actual / count_expected)

    @classmethod
    def compute_misdetection(cls, match_without_expected: int, count_actual: int) -> float:
        if count_actual == 0:
            return 0
        return match_without_expected / count_actual


class ComparisonResult(BaseModel):
    sample_id: str
    diffs: List[ComparisonMatch]
    counts: ComparisonCounts
    rates: ComparisonRates
