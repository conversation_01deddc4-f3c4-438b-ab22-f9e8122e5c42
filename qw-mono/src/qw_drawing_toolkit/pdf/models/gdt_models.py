import json
from enum import Enum
from typing import Annotated, Any, Dict, List, Literal

from pydantic import Field

from qw_drawing_toolkit.pdf.models.analysis_models import Datum<PERSON>ox, SymbolMapperBox, TextBox
from qw_drawing_toolkit.pdf.models.base_models import (
    T_BBOX,
    DrawingAnalysisBaseModel,
    DrawingAnnotationType,
    DrawingBoundingBox,
    DrawingUnit,
    Float,
)

DrawingGdtDatumName = Annotated[str, Field(pattern=r"^[A-Z]$")]

# |------------------------------|
# |          Enums               |
# |------------------------------|


class DrawingGdtSymbol(str, Enum):
    # https://www.gdandtbasics.com/gdt-symbols/

    TRUE_POSITION = "TRUE_POSITION"
    FLATNESS = "FLATNESS"
    PERPENDICULARITY = "PERPENDICULARITY"
    STRAIGHTNESS = "STRAIGHTNESS"
    PARALLELISM = "PARALLELISM"
    CIRCULARITY = "CIRCULARITY"
    PROFILE_OF_A_SURFACE = "PROFILE_OF_A_SURFACE"
    ANGULARITY = "ANGULARITY"
    PROFILE_OF_A_LINE = "PROFILE_OF_A_LINE"
    CONCENTRICITY = "CONCENTRICITY"
    SYMMETRY = "SYMMETRY"
    CIRCULAR_RUNOUT = "CIRCULAR_RUNOUT"
    TOTAL_RUNOUT = "TOTAL_RUNOUT"
    CYLINDRYCITY = "CYLINDRYCITY"
    UNKNOWN = "UNKNOWN"

    @classmethod
    def from_string(cls, value: str) -> "DrawingGdtSymbol":
        try:
            return DrawingGdtSymbol(value)
        except ValueError:
            return DrawingGdtSymbol.UNKNOWN


# |------------------------------|
# |          Models              |
# |------------------------------|


class GdtDatumReferenceBox:
    def __init__(self, datum: str, bbox: T_BBOX, datum_box: DatumBox | None):
        self.datum = datum
        self.bbox = bbox
        self.datum_box = datum_box

    def to_dict(self) -> Dict[str, Any]:
        return {
            "datum": self.datum,
            "bbox": self.bbox,
            "datum_box": self.datum_box.to_dict() if self.datum_box else "N/A",
        }


class GdtBox:
    def __init__(
        self,
        symbol_mapper_box: SymbolMapperBox,
        measurement_text_box: TextBox,
        datum_boxes: List[GdtDatumReferenceBox],
        bbox: T_BBOX,
    ):
        self.symbol_mapper_box = symbol_mapper_box
        self.measurement_text_box = measurement_text_box
        self.datum_reference_boxes = datum_boxes
        self.bbox = bbox

    def to_dict(self) -> Dict[str, Any]:
        return {
            "symbol_mapper_box": self.symbol_mapper_box.to_dict(),
            "measurement_text_box": self.measurement_text_box.to_dict(),
            "datum_reference_boxes": [datum_box.to_dict() for datum_box in self.datum_reference_boxes],
            "bbox": self.bbox,
        }

    def to_json(self) -> str:
        return json.dumps(self.to_dict(), indent=4)


class DrawingGdtDatumAnnotation(DrawingAnalysisBaseModel):
    # https://www.gdandtbasics.com/datum/

    annotation_type: Literal[DrawingAnnotationType.GDT_DATUM] = DrawingAnnotationType.GDT_DATUM
    bounding_box: DrawingBoundingBox
    name: DrawingGdtDatumName


class DrawingGdtFrameAnnotation(DrawingAnalysisBaseModel):
    # https://www.gdandtbasics.com/feature-control-frame/

    annotation_type: Literal[DrawingAnnotationType.GDT_FRAME] = DrawingAnnotationType.GDT_FRAME
    bounding_box: DrawingBoundingBox
    symbol: DrawingGdtSymbol
    tolerance_value: Float
    tolerance_unit: DrawingUnit
    datum_references: List[DrawingGdtDatumName]
    # to be added: is_diameter, modifiers
