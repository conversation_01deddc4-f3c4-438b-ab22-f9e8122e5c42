{"name": "DIN_EN_ISO_9013:2003-07", "tables": [{"name": "Tabelle 6", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.04, "lower": -0.04}}, {"lowerValueLimit": {"value": 2.999, "inclusive": false}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2.999, "inclusive": false}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 2.999, "inclusive": false}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 9.999, "inclusive": true}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 9.999, "inclusive": true}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 2.1, "lower": -2.1}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 2.9, "lower": -2.9}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 3.8, "lower": -3.8}}, {"lowerValueLimit": {"value": 9.999, "inclusive": true}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 2.6, "lower": -2.6}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 3.6, "lower": -3.6}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 314.999, "inclusive": true}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">200&<=250"]], "tolerance": {"upper": 3.7, "lower": -3.7}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">200&<=250"]], "tolerance": {"upper": 4.2, "lower": -4.2}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">200&<=250"]], "tolerance": {"upper": 5.2, "lower": -5.2}}, {"lowerValueLimit": {"value": 314.999, "inclusive": true}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">250&<=300"]], "tolerance": {"upper": 4.4, "lower": -4.4}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">250&<=300"]], "tolerance": {"upper": 4.9, "lower": -4.9}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">250&<=300"]], "tolerance": {"upper": 5.9, "lower": -5.9}}]}, {"name": "Tabelle 7", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2.999, "inclusive": false}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">0&<=1"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 2.999, "inclusive": false}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">1&<=3,15"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 2.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 2.999, "inclusive": false}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">3,15&<=6,3"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">6,3&<=10"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 9.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 9.999, "inclusive": false}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">10&<=50"]], "tolerance": {"upper": 4.2, "lower": -4.2}}, {"lowerValueLimit": {"value": 9.999, "inclusive": true}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 2.6, "lower": -2.6}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 3.7, "lower": -3.7}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">50&<=100"]], "tolerance": {"upper": 4.9, "lower": -4.9}}, {"lowerValueLimit": {"value": 9.999, "inclusive": true}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 3.3, "lower": -3.3}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 3.4, "lower": -3.4}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 3.7, "lower": -3.7}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 4.4, "lower": -4.4}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">100&<=150"]], "tolerance": {"upper": 5.7, "lower": -5.7}}, {"lowerValueLimit": {"value": 9.999, "inclusive": true}, "upperValueLimit": {"value": 34.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 34.999, "inclusive": false}, "upperValueLimit": {"value": 124.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 124.999, "inclusive": false}, "upperValueLimit": {"value": 314.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 4.1, "lower": -4.1}}, {"lowerValueLimit": {"value": 314.999, "inclusive": false}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 5.2, "lower": -5.2}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">150&<=200"]], "tolerance": {"upper": 6.4, "lower": -6.4}}, {"lowerValueLimit": {"value": 314.999, "inclusive": true}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">200&<=250"]], "tolerance": {"upper": 5.2, "lower": -5.2}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">200&<=250"]], "tolerance": {"upper": 5.9, "lower": -5.9}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">200&<=250"]], "tolerance": {"upper": 7.2, "lower": -7.2}}, {"lowerValueLimit": {"value": 314.999, "inclusive": true}, "upperValueLimit": {"value": 999.999, "inclusive": true}, "rowIdentifiers": [[">250&<=300"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 999.999, "inclusive": false}, "upperValueLimit": {"value": 1999.999, "inclusive": true}, "rowIdentifiers": [[">250&<=300"]], "tolerance": {"upper": 6.7, "lower": -6.7}}, {"lowerValueLimit": {"value": 1999.999, "inclusive": false}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [[">250&<=300"]], "tolerance": {"upper": 7.9, "lower": -7.9}}]}]}