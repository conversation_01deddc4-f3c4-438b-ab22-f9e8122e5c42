from typing import List, Literal, Type

from pydantic import BaseModel, Field
from sqlalchemy import text
from sqlalchemy.orm import DeclarativeBase

from qw_basic_rdb.builder.postgres import PostgreSqlConfig
from qw_basic_rdb.builder.sqlite import SqliteConfig
from qw_basic_rdb.interface import RelationalDatabase
from qw_log_interface import NO_LOGGER, Logger


class SkipSchemaVerification(BaseModel):
    type: Literal["skip"] = "skip"

    def run(self, rdb: RelationalDatabase, logger: Logger = NO_LOGGER) -> None:
        logger.warning("Skipping verification of the database schema")


class LiquibaseSchemaVerification(BaseModel):
    # https://docs.liquibase.com/concepts/tracking-tables/databasechangelog-table.html
    type: Literal["liquibase"] = "liquibase"
    changelog_table_name: str = "databasechangelog"
    valid_tags: List[str] = Field(min_length=1)

    def run(self, rdb: RelationalDatabase, logger: Logger = NO_LOGGER) -> None:
        if not rdb.has_table(self.changelog_table_name):
            raise ValueError(f"Changelog table {self.changelog_table_name} does not exist, cannot perform verification")
        with rdb.create_and_get_session() as s:
            stmt = text(
                f"SELECT TAG "
                f"FROM {self.changelog_table_name} "
                f"ORDER BY DATEEXECUTED DESC, ORDEREXECUTED DESC "
                f"LIMIT 1"
            )
            result = s.execute(stmt)
            rows = result.fetchall()
            if len(rows) != 1:
                raise ValueError(
                    f"Could not determine last changeset, got {len(rows)} rows (instead of 1) for {stmt.text}"
                )
            act_tag: str = rows[0][0]
        rdb.close_session()

        if act_tag == "":
            raise ValueError("Last row does not have a tag")
        if act_tag not in self.valid_tags:
            raise ValueError(f"Expected schema version to be among {self.valid_tags} - got {act_tag}")

        logger.info(f"Verified liquibase schema version, current tag is {act_tag}, allowed are {self.valid_tags}")


class RelationalDatabaseConfig(BaseModel):
    backend: PostgreSqlConfig | SqliteConfig = Field(discriminator="type")
    schema_verification: LiquibaseSchemaVerification | SkipSchemaVerification = Field(discriminator="type")
    create_tables_if_not_exist: bool = False  # development option

    def build(self, base: Type[DeclarativeBase], echo: bool = False, logger: Logger = NO_LOGGER) -> RelationalDatabase:
        rdb = self.backend.build(base, echo, logger)

        if self.create_tables_if_not_exist:
            logger.info("Creating or updating tables")
            rdb.create_or_update_tables()

        self.schema_verification.run(rdb, logger)

        # TODO: check that actual tables

        return rdb
