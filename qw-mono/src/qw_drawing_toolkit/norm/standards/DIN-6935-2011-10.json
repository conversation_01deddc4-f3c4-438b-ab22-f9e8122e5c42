{"name": "DIN_6935:2011-10", "tables": [{"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["S 235"]], "tolerance": {"upper": 0.5, "lower": 0}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 8, "inclusive": true}, "rowIdentifiers": [["S 235"]], "tolerance": {"upper": 1, "lower": 0}}, {"lowerValueLimit": {"value": 8, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["S 235"]], "tolerance": {"upper": 1.5, "lower": 0}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["S 275"]], "tolerance": {"upper": 0.8, "lower": 0}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 8, "inclusive": true}, "rowIdentifiers": [["S 275"]], "tolerance": {"upper": 1.5, "lower": 0}}, {"lowerValueLimit": {"value": 8, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["S 275"]], "tolerance": {"upper": 2, "lower": 0}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["S 355"]], "tolerance": {"upper": 1, "lower": 0}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 8, "inclusive": true}, "rowIdentifiers": [["S 355"]], "tolerance": {"upper": 2, "lower": 0}}, {"lowerValueLimit": {"value": 8, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["S 355"]], "tolerance": {"upper": 3, "lower": 0}}]}, {"name": "Tabelle 4", "valueType": "angular", "valueUnit": "deg", "toleranceUnit": "deg", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["< 30"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["< 50"]], "tolerance": {"upper": 1.75, "lower": -1.75}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["< 80"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["< 120"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["> 120"]], "tolerance": {"upper": 1, "lower": -1}}]}]}