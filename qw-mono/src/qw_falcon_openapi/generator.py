import collections
import io
import re
from enum import Enum
from typing import Any, Dict, List, Sequence, Set, Type, TypeVar

import yaml
from pydantic import BaseModel, create_model

from qw_falcon_openapi.controller import OpenApiOp, OpenApiPathController
from qw_falcon_openapi.error import FalconOpenApiBadConfig
from qw_falcon_openapi.multipart import OpenApiOpMultipartInJsonOut
from qw_falcon_openapi.operation import (
    OpenApiOpBinaryInJsonOut,
    OpenApiOpBinaryOut,
    OpenApiOpJsonInBinaryOut,
    OpenApiOpJsonInJsonOut,
    OpenApiOpJsonOut,
    OpenApiOpSpecOptionsBinaryOut,
)
from qw_falcon_openapi.spec import (
    JsonSchema,
    OpenApiApiKeySecurityScheme,
    OpenApiComponents,
    OpenApiDocument,
    OpenApiInfo,
    OpenApiMediaType,
    OpenApiMimeType,
    OpenApiOperation,
    OpenApiParameter,
    OpenApiParameterType,
    OpenApiPathItem,
    OpenApiReference,
    OpenApiRequestBody,
    OpenApiResponse,
)

# disabling check, so type variables for operations are not required
# mypy: disable-error-code="type-arg"

T_ENUM = TypeVar("T_ENUM", bound=Enum)


class OpenApiParameterGenerator(object):
    """
    Utility class to check schemas of parameters. There should be no nested structure and
    """

    def __init__(
        self,
        model_type: Type[BaseModel],
        route: OpenApiPathController,
        op: OpenApiOp | None,
        param_type: OpenApiParameterType,
    ):
        self.model_type = model_type
        self.route = route
        self.op = op
        self.param_type = param_type

        self.schema = self.model_type.model_json_schema(by_alias=True)
        if "$defs" in self.schema or self.schema.get("type") != "object":
            raise FalconOpenApiBadConfig(f"Got complex parameters model from {self.location_hint}")
        self.properties: Dict[str, JsonSchema] = self.schema.get("properties", {})
        self.properties_required: List[str] = self.schema.get("required", [])

    @property
    def location_hint(self) -> str:
        route_name = self.route.__class__.__name__
        op_name = "None" if self.op is None else self.op.__class__.__name__
        return f"route={route_name} op={op_name} param_type={self.param_type}"

    def check_share_params(self, share_params: Sequence[str]) -> None:
        bad_share_params_path = set(share_params) - set(self.properties)
        if len(bad_share_params_path) > 0:
            bad_str = ", ".join(bad_share_params_path)
            raise FalconOpenApiBadConfig(
                f"Parameters to share that don't exist are configured in {self.location_hint}: {bad_str}"
            )

    def get_params(self) -> List[OpenApiParameter]:
        params = []
        for name, schema in self.properties.items():
            required = name in self.properties_required
            if not required and self.param_type == OpenApiParameterType.PATH:
                raise FalconOpenApiBadConfig(f"Path parameter {name} is optional, see {self.location_hint}")
            param = OpenApiParameter(name=name, in_=self.param_type, required=required, schema_=schema)
            params.append(param)
        return params


class OpenApiDocumentGeneratorOptions(BaseModel):
    prepend_schema_name_in_descriptions: bool = False


class OpenApiDocumentGenerator(object):
    def __init__(
        self,
        paths: Sequence[OpenApiPathController],
        title: str,
        version: str,
        opts: OpenApiDocumentGeneratorOptions | None = None,
    ):
        self.paths = paths
        self.title = title
        self.version = version
        self.opts = opts if opts is not None else OpenApiDocumentGeneratorOptions()

        self.generated_doc: OpenApiDocument | None = None
        self.collected_models: Set[Type[BaseModel]] = set()
        self.collected_params: Dict[str, OpenApiParameter] = {}
        self.collected_schemes: Dict[str, OpenApiApiKeySecurityScheme] = {}
        self.collected_operation_ids: Dict[str, List[OpenApiOp]] = collections.defaultdict(list)
        self.ref_template_models = "#/components/schemas/{model}"
        self.ref_template_params = "#/components/parameters/{model}"

    def __get_schemas(self) -> Dict[str, JsonSchema]:
        """
        Generate a temporary model that has one parameter with each given model,
        pydantic will then generate the schemas for all parameters into $def and taking care of duplication etc.
        """
        model_name = "SchemaContainerForJsonSchemaGeneration"
        if model_name in [m.__name__ for m in self.collected_models]:
            raise FalconOpenApiBadConfig(f"At least one schema uses a reserved name: {model_name}")
        model_params = {f"param{i}": (m, ...) for i, m in enumerate(self.collected_models)}
        model = create_model(model_name, **model_params)  # type: ignore
        model_schema = model.model_json_schema(by_alias=True, ref_template=self.ref_template_models)
        schemas: Dict[str, JsonSchema] = model_schema.get("$defs", {})

        if self.opts.prepend_schema_name_in_descriptions:
            for schema_name, schema in schemas.items():
                existing_desc = schema.get("description", "")
                schema["description"] = f"[schema: {schema_name}] {existing_desc}".strip()

        return schemas

    def __get_reference_for_model(self, model_type: Type[BaseModel]) -> OpenApiReference:
        ref_url = self.ref_template_models.format(model=model_type.__name__)
        self.collected_models.add(model_type)
        return OpenApiReference(ref=ref_url)

    def __get_reference_for_param(self, param: OpenApiParameter) -> OpenApiReference:
        name = param.name
        if name in self.collected_params and param != self.collected_params[name]:
            raise FalconOpenApiBadConfig(f"Parameter name {name} is used twice, cannot put both in the components")
        if name not in self.collected_params:
            self.collected_params[name] = param
        ref_url = self.ref_template_params.format(model=name)
        return OpenApiReference(ref=ref_url)

    def __generate_operation_item(self, path: OpenApiPathController, op: OpenApiOp) -> OpenApiOperation:
        route = path.route

        params_headers_gen = OpenApiParameterGenerator(op.t_params_headers, path, op, OpenApiParameterType.HEADER)
        params_headers = self.__generate_params(params_headers_gen, op.spec_options.share_params_headers)
        params_query_gen = OpenApiParameterGenerator(op.t_params_query, path, op, OpenApiParameterType.QUERY)
        params_query = self.__generate_params(params_query_gen, op.spec_options.share_params_query)
        params = params_headers + params_query

        req_body: OpenApiRequestBody | None = None
        responses: Dict[str, OpenApiResponse | OpenApiReference] = {}
        http_ok = "200"
        http_ok_default_description = "success"

        status_min = 300
        status_max = 599
        for status_code, description in op.spec_options.responses_error.items():
            if not status_min <= status_code <= status_max:
                raise FalconOpenApiBadConfig(
                    f"Operation '{op.__class__.__name__}' for route '{route}' specifies "
                    f"an invalid status code {status_code} (should be {status_min}...{status_max})"
                )
            responses[str(status_code)] = OpenApiResponse(description=description, content={})

        security: List[Dict[str, List[str]]] | None = None
        if op.spec_options.security_schemes is not None:
            security = []
            for scheme in op.spec_options.security_schemes:
                name = scheme.name
                other_scheme = self.collected_schemes.get(name)
                if other_scheme is not None:
                    if other_scheme != scheme:
                        raise FalconOpenApiBadConfig(
                            f"There exist two security schemes with name '{name}', but different configuration:\n"
                            f"{other_scheme.model_json_schema(by_alias=True)}\n"
                            f"{scheme.model_json_schema(by_alias=True)}"
                        )
                else:
                    self.collected_schemes[name] = scheme
                security.append({name: []})

        # definitions for convenience
        def add_binary_responses(binary_spec_options: OpenApiOpSpecOptionsBinaryOut) -> None:
            if len(binary_spec_options.responses) == 0:
                raise FalconOpenApiBadConfig(
                    f"Operation '{op.__class__.__name__}' for route '{route}' does not specify any responses"
                )
            for sc, (desc, mime_types) in binary_spec_options.responses.items():
                responses[str(sc)] = OpenApiResponse(
                    description=desc,
                    content={mt: OpenApiMediaType(schema_=None) for mt in mime_types},
                )

        def add_json_response(model_type: Type[BaseModel]) -> None:
            responses[http_ok] = OpenApiResponse(
                description=http_ok_default_description,
                content={OpenApiMimeType.JSON: OpenApiMediaType(schema_=self.__get_reference_for_model(model_type))},
            )

        def get_json_req_body(model_type: Type[BaseModel]) -> OpenApiRequestBody:
            return OpenApiRequestBody(
                required=True,
                content={OpenApiMimeType.JSON: OpenApiMediaType(schema_=self.__get_reference_for_model(model_type))},
            )

        if isinstance(op, OpenApiOpBinaryOut):
            add_binary_responses(op.spec_options)

        elif isinstance(op, OpenApiOpJsonInBinaryOut):
            req_body = get_json_req_body(op.t_in)
            add_binary_responses(op.spec_options)

        elif isinstance(op, OpenApiOpJsonOut):
            add_json_response(op.t_out)

        elif isinstance(op, OpenApiOpJsonInJsonOut):
            req_body = get_json_req_body(op.t_in)
            add_json_response(op.t_out)

        elif isinstance(op, OpenApiOpBinaryInJsonOut):
            # https://spec.openapis.org/oas/v3.1.0#considerations-for-file-uploads
            # https://swagger.io/docs/specification/describing-request-body/file-upload/
            req_body = OpenApiRequestBody(
                required=True,
                content={
                    mt: OpenApiMediaType(schema_={"type": "string", "format": "binary"}) for mt in op.mime_types_in
                },
            )
            add_json_response(op.t_out)

        elif isinstance(op, OpenApiOpMultipartInJsonOut):
            # https://spec.openapis.org/oas/v3.1.0#considerations-for-file-uploads
            # https://swagger.io/docs/specification/describing-request-body/multipart-requests/
            route_name = route.replace("/", "_")
            route_name = re.sub(r"![a-zA-Z0-9_]", "", route_name)
            model_name = f"MultipartSchema{route_name}"
            model_params = {}
            for part in op.def_in.part_defs.values():
                param_type = part.get_type()
                model_params[part.name] = (param_type, ...)

            t_in = create_model(model_name, **model_params)  # type: ignore
            media_type = OpenApiMediaType(schema_=self.__get_reference_for_model(t_in))

            req_body = OpenApiRequestBody(required=True, content={OpenApiMimeType.MULTIPART_FORM_DATA: media_type})
            add_json_response(op.t_out)

        else:
            raise ValueError(f"Cannot map unknown operation type {type(op)}")

        op_id = op.spec_options.op_id or op.__class__.__name__
        self.collected_operation_ids[op_id].append(op)
        return OpenApiOperation(
            operation_id=op_id,
            tags=list(path.spec_options.tags),
            request_body=req_body,
            responses=responses,
            parameters=params,
            security=security,
        )

    def __generate_params(
        self, gen: OpenApiParameterGenerator, share_params: Sequence[str]
    ) -> List[OpenApiParameter | OpenApiReference]:
        gen.check_share_params(share_params)
        params_ = gen.get_params()
        params = []
        param: OpenApiParameter | OpenApiReference
        for param_ in params_:
            param = param_
            if param_.name in share_params:
                param = self.__get_reference_for_param(param_)
            params.append(param)
        return params

    def __generate(self) -> OpenApiDocument:
        api_info = OpenApiInfo(title=self.title, version=self.version)

        path_items = {}
        for p in self.paths:
            params_path_gen = OpenApiParameterGenerator(p.route_params_type, p, None, OpenApiParameterType.PATH)
            params_path = self.__generate_params(params_path_gen, p.spec_options.share_params_path)

            get = p.method_to_op.get("get")
            put = p.method_to_op.get("put")
            post = p.method_to_op.get("post")
            delete = p.method_to_op.get("delete")

            route = p.route
            path_item = OpenApiPathItem(
                get=None if get is None else self.__generate_operation_item(p, get),
                put=None if put is None else self.__generate_operation_item(p, put),
                post=None if post is None else self.__generate_operation_item(p, post),
                delete=None if delete is None else self.__generate_operation_item(p, delete),
                parameters=params_path,
            )
            path_items[route] = path_item

        schemas = self.__get_schemas()
        parameters: Dict[str, OpenApiParameter | OpenApiReference] = {k: v for k, v in self.collected_params.items()}
        components = OpenApiComponents(schemas=schemas, parameters=parameters, security_schemes=self.collected_schemes)

        for op_id, ops in self.collected_operation_ids.items():
            if len(ops) > 1:
                op_types = [type(op) for op in ops]
                raise FalconOpenApiBadConfig(f"Multiple operations use id {op_id}: {op_types}")

        return OpenApiDocument(info=api_info, paths=path_items, components=components)

    def generate(self) -> OpenApiDocument:
        if self.generated_doc is None:
            self.generated_doc = self.__generate()
        return self.generated_doc

    def generate_json(self, stream: io.TextIOBase, indent: int | None = None, exclude_none: bool = True) -> None:
        spec = self.generate()
        stream.write(spec.model_dump_json(indent=indent, by_alias=True, exclude_none=exclude_none, warnings=False))

    def generate_yaml(self, stream: io.TextIOBase, exclude_none: bool = True) -> None:
        spec = self.generate()

        def represent_enum(dumper: "OpenApiDumper", data: T_ENUM) -> yaml.ScalarNode:
            return dumper.represent_str(str(data.value))

        class OpenApiDumper(yaml.SafeDumper):
            def __init__(self, *args: Any, **kwargs: Any):
                super().__init__(*args, **kwargs)
                self.add_representer(OpenApiMimeType, represent_enum)
                self.add_representer(OpenApiParameterType, represent_enum)

        stream.write("# this openapi specification is autogenerated\n")
        yaml.dump(
            spec.model_dump(by_alias=True, exclude_none=exclude_none),
            stream,
            Dumper=OpenApiDumper,
            sort_keys=False,
        )
