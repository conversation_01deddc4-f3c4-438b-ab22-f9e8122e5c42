"""Base models for the drawing toolkit"""

from enum import Enum, unique
from typing import Annotated, List, NamedTuple

from pydantic import BaseModel, ConfigDict, Field, model_validator
from pydantic.alias_generators import to_camel
from typing_extensions import Self

T_POINT = tuple[float, float]
T_LINE_SEGMENT = tuple[T_POINT, T_POINT]
T_BBOX = tuple[float, float, float, float]

Float = Annotated[float, Field(allow_inf_nan=False)]

# |------------------------------|
# |          Enums               |
# |------------------------------|


@unique
class DrawingAnnotationType(str, Enum):
    LENGTH = "length"
    RADIUS = "radius"
    ANGLE = "angle"
    DIAMETER = "diameter"
    DIAMETER_THREAD = "diameter_thread"
    DIAMETER_COUNTERSINK = "diameter_countersink"
    DIAMETER_COUNTERBORE = "diameter_counterbore"
    GDT_DATUM = "gdt_datum"
    GDT_FRAME = "gdt_frame"
    ROUGHNESS = "roughness"


@unique
class DrawingUnit(str, Enum):
    MICROMETER = "micrometer"
    MILLIMETER = "mm"
    DEGREE = "deg"


# |------------------------------|
# |          Models              |
# |------------------------------|


class DrawingAnalysisBaseModel(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)


class GdtSymbol(NamedTuple):
    id: int
    name: str
    contour: str


class DrawingPoint(DrawingAnalysisBaseModel):
    """Normalized (0-1) drawing coordinate in image style, i.e. origin is top left, x to the right, y to the bottom"""

    x: float = Field(ge=0, le=1)
    y: float = Field(ge=0, le=1)


class DrawingPolygon(DrawingAnalysisBaseModel):
    points: List[DrawingPoint] = Field(min_length=4)


class DrawingBoundingBox(DrawingAnalysisBaseModel):
    """
    Bounding box defined by the top left and bottom right box points. To visualize the boxes, the normalized x and y
    values need to be multiplied with corresponding image width or height respectively. For undistorted visualizations
    look up the document page size in millimeters and reuse this aspect ratio.
    """

    page_index: int
    p1: DrawingPoint
    p2: DrawingPoint
    polygon: DrawingPolygon | None = Field(
        default=None,
        description="May be provided if a more precise location can be determined, e.g. for slanted text",
    )

    @property
    def area(self) -> float:
        return (self.p2.x - self.p1.x) * (self.p2.y - self.p1.y)

    def contains_point(self, x: float, y: float) -> bool:
        return self.p1.x <= x <= self.p2.x and self.p1.y <= y <= self.p2.y

    @model_validator(mode="after")
    def ensure_correct_order_of_points(self) -> Self:
        if not (self.p1.x <= self.p2.x and self.p1.y <= self.p2.y):
            raise ValueError("Bounding box points are not sorted correctly")
        return self

    @model_validator(mode="after")
    def ensure_polygon_is_in_bounds(self) -> Self:
        if self.polygon is not None:
            for p in self.polygon.points:
                if not self.contains_point(p.x, p.y):
                    raise ValueError(f"Polygon point {p} is not inside the bounding box ({self.p1}, {self.p2})")
        return self
