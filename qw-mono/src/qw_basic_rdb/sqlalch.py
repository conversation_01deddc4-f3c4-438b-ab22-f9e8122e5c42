from typing import Any, Dict, Protocol, Type

from sqlalchemy import URL, Engine, create_engine, event, inspect
from sqlalchemy.orm import DeclarativeBase, Session
from sqlalchemy.pool import ConnectionPoolEntry


class OnConnectCallback(Protocol):
    def __call__(self, dbapi_connection: Any, connection_record: ConnectionPoolEntry) -> None:
        ...


class SqlAlchemyDatabase(object):
    def __init__(
        self,
        base: Type[DeclarativeBase],
        engine: Engine,
    ):
        self.base = base
        self.__engine = engine
        self.__session: Session | None = None

    @classmethod
    def from_url(
        cls,
        base_type: Type[DeclarativeBase],
        url: str | URL,
        echo: bool = False,
        extra_args: Dict[str, Any] | None = None,
        on_connect: OnConnectCallback | None = None,
    ) -> "SqlAlchemyDatabase":
        if extra_args is None:
            extra_args = {}
        engine = create_engine(url, echo=echo, **extra_args)
        if on_connect is not None:
            event.listen(engine, "connect", on_connect)
        return SqlAlchemyDatabase(base_type, engine)

    def has_table(self, table_name: str) -> bool:
        inspection = inspect(self.__engine)
        return inspection.has_table(table_name)

    def create_or_update_tables(self) -> None:
        self.base.metadata.create_all(self.__engine)

    def create_and_get_session(self) -> Session:
        if self.__session is not None:
            raise RuntimeError("There is already an active session, close this one before creating another one")
        self.__session = Session(self.__engine, autobegin=True)
        return self.__session

    def close_session(self) -> None:
        if self.__session is None:
            raise RuntimeError("There is no session to be closed")
        self.__session.close()
        self.__session = None
