import collections
from typing import Dict, Generator, List, Set, Tuple

import numpy as np
import numpy.typing as npt

from qw_drawing_toolkit.pdf.models.base_models import DrawingBoundingBox


class BoundingBoxMatchingResult(object):
    def __init__(
        self,
        actual_bboxes: List[DrawingBoundingBox],
        expected_bboxes: List[DrawingBoundingBox],
        links: List[Tuple[int, int]],
    ):
        self.actual_bboxes = actual_bboxes
        self.expected_bboxes = expected_bboxes
        self.__bbox_indices_by_act_bbox_index: Dict[int, Set[int]] = collections.defaultdict(set)
        self.__bbox_indices_by_exp_bbox_index: Dict[int, Set[int]] = collections.defaultdict(set)
        for act_result_index, exp_result_index in links:
            if not 0 <= act_result_index <= len(actual_bboxes):
                raise ValueError(f"Actual result index is out of bounds: {act_result_index}")
            if not 0 <= exp_result_index <= len(expected_bboxes):
                raise ValueError(f"Expected result index is out of bounds: {exp_result_index}")
            self.__bbox_indices_by_act_bbox_index[act_result_index].add(exp_result_index)
            self.__bbox_indices_by_exp_bbox_index[exp_result_index].add(act_result_index)

    def iter_over_match_idx_for_actual_bboxes(
        self,
    ) -> Generator[Tuple[int, List[int]], None, None]:
        for act_index in range(len(self.actual_bboxes)):
            exp_indices = self.__bbox_indices_by_act_bbox_index[act_index]
            yield act_index, list(exp_indices)

    def get_unmatched_expected_bboxes(self) -> List[int]:
        return [
            exp_index
            for exp_index in range(len(self.expected_bboxes))
            if len(self.__bbox_indices_by_exp_bbox_index[exp_index]) == 0
        ]

    def get_unmatched_actual_bboxes(self) -> List[int]:
        return [
            act_index
            for act_index in range(len(self.actual_bboxes))
            if len(self.__bbox_indices_by_act_bbox_index[act_index]) == 0
        ]


class BoundingBoxMatrix(object):
    COL_BBOX_INDEX = 0
    COL_PAGE_INDEX = 1
    COL_X1 = 2
    COL_Y1 = 3
    COL_X2 = 4
    COL_Y2 = 5
    COL_AREA = 6

    @classmethod
    def build(cls, bboxes: List[DrawingBoundingBox]) -> npt.NDArray[np.float32]:
        """
        Construct numpy matrix that holds bounding box attributes in column,
        the number of rows corresponds to the number of boxes.
        """
        return np.array(
            [
                (i, bbox.page_index, bbox.p1.x, bbox.p1.y, bbox.p2.x, bbox.p2.y, bbox.area)
                for i, bbox in enumerate(bboxes)
            ],
            dtype=np.float32,
        )


def compute_bounding_box_matches(
    actual_bboxes: List[DrawingBoundingBox],
    expected_bboxes: List[DrawingBoundingBox],
    iou_threshold: float,
) -> BoundingBoxMatchingResult:
    if len(actual_bboxes) == 0 or len(expected_bboxes) == 0:
        return BoundingBoxMatchingResult(actual_bboxes, expected_bboxes, links=[])

    mat_act = BoundingBoxMatrix.build(actual_bboxes)
    mat_exp = BoundingBoxMatrix.build(expected_bboxes)

    page_indices = set()
    page_indices.update(np.unique(mat_act[:, BoundingBoxMatrix.COL_PAGE_INDEX]).astype(np.int32).tolist())
    page_indices.update(np.unique(mat_exp[:, BoundingBoxMatrix.COL_PAGE_INDEX]).astype(np.int32).tolist())

    links = []
    for page_index in page_indices:
        mat_act_page = mat_act[mat_act[:, BoundingBoxMatrix.COL_PAGE_INDEX] == page_index]
        mat_exp_page = mat_exp[mat_exp[:, BoundingBoxMatrix.COL_PAGE_INDEX] == page_index]

        # repeating the data to cover all combinations of boxes for this page index
        act = np.repeat(mat_act_page, mat_exp_page.shape[0], axis=0)
        exp = np.vstack([mat_exp_page] * mat_act_page.shape[0])

        # bounding box iou can be computed efficiently
        # see also https://stackoverflow.com/a/42874377
        isect_x1 = np.maximum(act[:, BoundingBoxMatrix.COL_X1], exp[:, BoundingBoxMatrix.COL_X1])
        isect_y1 = np.maximum(act[:, BoundingBoxMatrix.COL_Y1], exp[:, BoundingBoxMatrix.COL_Y1])
        isect_x2 = np.minimum(act[:, BoundingBoxMatrix.COL_X2], exp[:, BoundingBoxMatrix.COL_X2])
        isect_y2 = np.minimum(act[:, BoundingBoxMatrix.COL_Y2], exp[:, BoundingBoxMatrix.COL_Y2])
        isect_area = np.maximum(isect_x2 - isect_x1, 0) * np.maximum(isect_y2 - isect_y1, 0)
        union_area = act[:, BoundingBoxMatrix.COL_AREA] + exp[:, BoundingBoxMatrix.COL_AREA] - isect_area

        # preemptively set division by 0 to 0
        # see also https://stackoverflow.com/a/37977222
        iou = np.divide(isect_area, union_area, out=np.zeros_like(isect_area), where=union_area != 0)
        iou_mask = iou >= iou_threshold

        for act_index, exp_index in zip(
            act[iou_mask, :][:, BoundingBoxMatrix.COL_BBOX_INDEX].astype(np.int32),
            exp[iou_mask, :][:, BoundingBoxMatrix.COL_BBOX_INDEX].astype(np.int32),
        ):
            links.append((act_index, exp_index))

    return BoundingBoxMatchingResult(actual_bboxes, expected_bboxes, links)
