{# WeasyPrint CSS - Clean Final Version #}

{% macro base_styles() %}
<style>
  @font-face {
    font-family: "Roboto";
    src: url(../assets/roboto-v47-greek_greek-ext_latin_latin-ext_math_symbols-regular.woff2) format('woff2');
    font-weight: 400;
    font-style: normal;
  }

  @font-face {
    font-family: "Roboto";
    src: url(../assets/roboto-v47-greek_greek-ext_latin_latin-ext_math_symbols-700.woff2) format('woff2');
    font-weight: 700;
    font-style: normal;
  }

  body {
    font-family: "Roboto", sans-serif;
    font-size: 12px;
    color: #000000;
    margin: 0;
    padding: 0;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
    table-layout: fixed;
    page-break-inside: auto;
  }


  td,
  th {
    border: 1px solid #000000;
    padding: 4px 2px;
    text-align: left;
    vertical-align: middle;
    word-wrap: break-word;
  }

  th {
    background-color: #f2f2f2;
    font-weight: 700;
  }

  .header-cell {
    font-weight: 700;
    background-color: #f0f0f0;
    text-align: center;
  }

  .measurement-cell {
    text-align: center;
  }

  .skipped-cell {
    background-color: #e0e0e0;
  }

  /* Utility Classes */
  .center-text {
    text-align: center;
  }

  .left {
    text-align: left;
  }

  .right {
    text-align: right;
  }

  /* Page break controls for better pagination */
  .no-page-break {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Keep table headers with content */
  thead {
    break-after: avoid;
  }

  /* Unified Evidence Image Container - Fits maximum available space */
  .evidence-image-container {
    width: 100%;
    height: 100%;
    max-width: 267mm;
    max-height: 180mm;
    margin: 0 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .evidence-image {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
  }

  .page-number::after {
    content: counter(page);
  }

  .page-count::after {
    content: counter(pages);
  }
</style>
{% endmacro %}

{% macro standard_styles(page_size) %}
<style>
  @page {
    size: {{ page_size }};
    margin: 1cm;
  }

  .table {
    margin-top: 20px;
  }

  .summary th {
    width: 100px;
  }
</style>
{% endmacro %}

{% macro fai_styles(page_size) %}
<style>
  @page {
    size: {{page_size}};
    margin: 1cm;

    @top-left {
      content: "FAI - Characteristic Assignment, Verification and Compatibility Evaluation";
      font-family: "Roboto", sans-serif;
      font-size: 12px;
    }

    @top-right {
      content: "Sheet " counter(page) " of " counter(pages);
      font-family: "Roboto", sans-serif;
      font-size: 12px;
    }
  }

  /* FAI Info Section */
  .fai-info-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 10px;
  }

  .fai-info-table td,
  .fai-info-table th {
    border: 1px solid #333333;
    padding: 4px;
    font-size: 11px;
  }

  .fai-info-table th {
    background-color: #f0f0f0;
    font-weight: 700;
  }

  .fai-url-link {
    word-wrap: break-word;
    white-space: normal;
    color: #2563eb;
  }

  /* FAI Column Headers - Repeat after page breaks */
  .fai-column-headers {
    width: 100%;
    border-collapse: collapse;
  }

  .fai-column-headers th {
    border: 1px solid #333333;
    padding: 3px 2px;
    font-size: 10px;
    font-weight: 700;
    background-color: #f0f0f0;
    text-align: center;
    vertical-align: middle;
  }

  .fai-small-text {
    font-size: 9px;
  }

  /* FAI Data Tables */
  .fai-data-table {
    width: 100%;
    page-break-inside: avoid;
  }

  .fai-data-table td {
    border: 0.5px solid #333333;
    margin: -0.5px 0 0 -0.5px;
    padding: 3px 2px;
    font-size: 10px;
    vertical-align: middle;
  }

  .fai-characteristic-number {
    background-color: #f8f9fa;
    font-weight: 700;
  }

  .fai-reference,
  .fai-description {
    text-align: left;
  }

  .fai-ok-cell,
  .fai-not-ok-cell,
  .fai-non-compliance {
    background-color: #fefefe;
  }

  .fai-special-tool,
  .fai-remark {
    background-color: #f8f9fa;
  }

  /* General FAI styles */
  td,
  th {
    border-color: #333333;
    padding: 4px;
    font-size: 12px;
  }

  a {
    word-wrap: break-word;
    white-space: normal;
  }
</style>
{% endmacro %}

{% macro ppf10_styles(page_size) %}
<style>
  @page {
    size: {{ page_size }};
    margin: 1cm;

    @top-left {
      content: "PPF - Product Related Deliverables";
      font-family: "Roboto", sans-serif;
      font-size: 12px;
    }

    @top-right {
      content: "Sheet " counter(page) " of " counter(pages);
      font-family: "Roboto", sans-serif;
      font-size: 12px;
    }
  }

  /* Drawing page specific styles */
  .drawing-page {
    width: 100%;
    height: 190mm;
    position: relative;
    box-sizing: border-box;
  }

  /* Clean deviation bar implementation */
  .deviation-bar-cell {
    text-align: center;
    background-color: #f0f0f0;
  }

  .deviation-bar-container {
    display: flex;
    width: 100%;
    height: 16px;
  }

  .deviation-bar-left {
    flex: 1;
    position: relative;
    display: flex;
    justify-content: flex-end;
  }

  .deviation-bar-left-fill {
    height: 100%;
    background-color: var(--color);
    width: var(--deviation);
  }

  .deviation-bar-center {
    width: 2px;
    background-color: #333;
    flex-shrink: 0;
  }

  .deviation-bar-right {
    flex: 1;
    position: relative;
  }

  .deviation-bar-right-fill {
    height: 100%;
    background-color: var(--color);
    width: var(--deviation);
  }

  /* Clean report info styling - Grid with border-collapse simulation */
  .report-info {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 2fr;
    margin-bottom: 10px;
  }

  .report-label,
  .report-value {
    padding: 4px 2px;
    border: 0.5px solid #000000;
    margin: -0.5px 0 0 -0.5px;
    display: flex;
    align-items: center;
    justify-content: start;
  }

  .report-label {
    background-color: #f0f0f0;
    font-weight: 700;
    text-align: left;
  }

  .report-value {
    background-color: white;
    text-align: left;
  }

  .report-url-link {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-all;
  }

  /* Details info styling - 6-column grid (3 sections × 2 columns each) */
  .details-info {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    margin-bottom: 10px;
  }

  .details-section-header {
    grid-column: span 2;
    background-color: #f0f0f0;
    font-weight: 700;
    text-align: left;
    padding: 4px 2px;
    border: 0.5px solid #000000;
    margin: -0.5px 0 0 -0.5px;
    display: flex;
    align-items: center;
    justify-content: start;
  }

  .details-label,
  .details-value {
    padding: 4px 2px;
    border: 0.5px solid #000000;
    margin: -0.5px 0 0 -0.5px;
    display: flex;
    align-items: center;
    justify-content: start;
  }

  .details-label {
    background-color: #f0f0f0;
    font-weight: 700;
    text-align: left;
  }

  .details-value {
    background-color: white;
    text-align: left;
  }
</style>
{% endmacro %}
