from pathlib import Path

import pytest

from qw_drawing_toolkit.pdf.services.pdf_validator import open_and_validate_from_path
from qw_drawing_toolkit_test.conftest import TEST_RESOURCES


class TestDrawingPdfService(object):
    @pytest.mark.parametrize(
        "path,expected_has_vectors",
        [(TEST_RESOURCES / "demo_drawing_as_image_in_pdf.pdf", False)],
    )
    def test_scan_detection(self, path: Path, expected_has_vectors: bool) -> None:
        with open(path, "rb"):
            _, has_vectors, _ = open_and_validate_from_path(path)
            assert has_vectors == expected_has_vectors
