from typing import Literal

from pydantic import AnyHttpUrl, BaseModel

from qw_basic_keycloak.impl.keycloak.client import KeycloakClient
from qw_basic_keycloak.openid.inspector import TokenInspector
from qw_basic_keycloak.openid.rest import IdentityProviderConfiguration, OpenIdRestClient
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class KeycloakIam(object):
    def __init__(
        self,
        client: KeycloakClient,
        realm: str,
        openid_redirect_uri: str,
        openid_client_id: str,
        openid_client_secret: str | None,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.client = client
        self.realm = realm
        self.openid_redirect_uri = openid_redirect_uri
        self.openid_client_id = openid_client_id
        self.openid_client_secret = openid_client_secret
        self.lf = lf

    def get_openid_issuer(self) -> str:
        return f"{self.client.host}/realms/{self.realm}"

    def get_openid_client(self) -> OpenIdRestClient:
        key_set = self.client.get_openid_key_set(self.realm)
        idp_config_1 = self.client.get_openid_configuration(self.realm)
        idp_config_2 = IdentityProviderConfiguration(
            authorization_endpoint=idp_config_1.authorization_endpoint,
            token_endpoint=idp_config_1.token_endpoint,
            userinfo_endpoint=idp_config_1.userinfo_endpoint,
            end_session_endpoint=idp_config_1.end_session_endpoint,
            key_data=key_set,
        )

        return OpenIdRestClient(
            self.openid_client_id,
            self.openid_redirect_uri,
            idp_config_2,
            self.openid_client_secret,
            self.lf,
        )

    def get_openid_token_inspector(self) -> TokenInspector:
        key_set = self.client.get_openid_key_set(self.realm)
        trusted_issuer = self.get_openid_issuer()
        return TokenInspector(key_set, trusted_issuer, lf=self.lf)

    def get_health(self) -> bool:
        return self.client.is_ready(timeout_seconds=2)


class KeycloakInitialHealthcheckConfig(BaseModel):
    timeout_in_seconds: int
    retry_count: int


class KeycloakIamConfig(BaseModel):
    type: Literal["keycloak"] = "keycloak"
    host: AnyHttpUrl
    initial_healthcheck: KeycloakInitialHealthcheckConfig | None = None
    realm: str

    openid_redirect_host: AnyHttpUrl
    openid_redirect_route: str
    openid_client_id: str
    openid_client_secret: str | None = None

    def build(self, lf: LogFactory = NO_LOG_FACTORY) -> KeycloakIam:
        client = KeycloakClient(str(self.host), lf=lf)
        redirect_host = str(self.openid_redirect_host).rstrip("/")
        redirect_route = self.openid_redirect_route.lstrip("/")
        iam = KeycloakIam(
            client,
            self.realm,
            f"{redirect_host}/{redirect_route}",
            self.openid_client_id,
            self.openid_client_secret,
            lf=lf,
        )

        if self.initial_healthcheck is not None:
            timeout = self.initial_healthcheck.timeout_in_seconds
            retry_count = self.initial_healthcheck.retry_count
            if not client.is_ready_with_retry(timeout, retry_count):
                raise RuntimeError(f"After {timeout} seconds keycloak is still not reachable")

        return iam
