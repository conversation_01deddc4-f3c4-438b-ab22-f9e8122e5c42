import { CommonModule } from "@angular/common"
import { Component, model } from "@angular/core"
import { DropdownComponent } from "@qw-shared-ui/dropdown/dropdown.component"

import { QwOrderType } from "@qw-api/models"

@Component({
  selector: "qw-order-type-input",
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: "./order-type-input.component.html",
})
export class OrderTypeInputComponent {
  protected readonly QwOrderType = QwOrderType

  // Required model input for value with a default
  value = model.required<QwOrderType>()

  // Define options for the dropdown
  protected readonly orderTypeOptions = [
    { value: QwOrderType.Purchase, label: $localize`Purchase Order` },
    { value: QwOrderType.Production, label: $localize`Production Order` },
  ]

  protected getCurrentOption() {
    return (
      this.orderTypeOptions.find((opt) => opt.value === this.value()) ??
      this.orderTypeOptions[0]
    )
  }

  // Handle dropdown selection change
  protected handleOrderTypeChange(selected: {
    value: QwOrderType
    label: string
  }) {
    if (selected.value !== this.value()) {
      this.value.set(selected.value)
    }
  }
}
