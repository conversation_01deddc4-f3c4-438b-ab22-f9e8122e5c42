import json

from joserfc.errors import InvalidPayloadError, InvalidTypeError
from joserfc.jws import extract_compact
from joserfc.jwt import Claims, Token
from joserfc.registry import Header
from joserfc.util import to_bytes


def decode_without_key(token_b64: str | bytes) -> Token:
    """
    Motivation for this method: Keycloak uses a symmetric key to sign refresh tokens.
    See also https://keycloak.discourse.group/t/rs256-for-refresh-tokens/6849.
    This method uses internals of joserfc and might break when moving to another / newer version of the library.
    Original method can be found at ``joserfc.jwt.decode``
    :param token_b64: a *signed* JWT, encrypted JWTs will not work
    :return: the encoded, but not validated (!) token
    """

    _value = to_bytes(token_b64)
    header: Header
    payload: bytes
    if _value.count(b".") == 4:
        raise ValueError("Encrypted JWTs cannot be processed without key")
    else:
        obj = extract_compact(_value)
        assert obj.payload is not None
        header = obj.headers()
        payload = obj.payload

    try:
        claims: Claims = json.loads(payload)
    except (TypeError, ValueError):
        raise InvalidPayloadError()

    token = Token(header, claims)
    typ = token.header.get("typ")
    # https://www.rfc-editor.org/rfc/rfc7519#section-5.1
    # If present, it is RECOMMENDED that its value be "JWT".
    if typ and typ != "JWT":
        raise InvalidTypeError()
    return token
