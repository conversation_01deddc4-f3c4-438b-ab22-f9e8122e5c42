{"name": "DIN_EN_10029:2011-02", "tables": [{"name": "Tabelle 1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 4.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 4.999, "inclusive": false}, "upperValueLimit": {"value": 7.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 7.999, "inclusive": false}, "upperValueLimit": {"value": 14.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 14.999, "inclusive": false}, "upperValueLimit": {"value": 24.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 1.0, "lower": -0.6}}, {"lowerValueLimit": {"value": 24.999, "inclusive": false}, "upperValueLimit": {"value": 39.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 39.999, "inclusive": false}, "upperValueLimit": {"value": 79.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 1.7, "lower": -0.9}}, {"lowerValueLimit": {"value": 79.999, "inclusive": false}, "upperValueLimit": {"value": 149.99, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 149.999, "inclusive": false}, "upperValueLimit": {"value": 249.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 249.999, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 3.5, "lower": -1.3}}, {"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 4.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 4.999, "inclusive": false}, "upperValueLimit": {"value": 7.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 0.9, "lower": -0.3}}, {"lowerValueLimit": {"value": 7.999, "inclusive": false}, "upperValueLimit": {"value": 14.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 1.1, "lower": -0.3}}, {"lowerValueLimit": {"value": 14.999, "inclusive": false}, "upperValueLimit": {"value": 24.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 1.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 24.999, "inclusive": false}, "upperValueLimit": {"value": 39.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 1.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 39.999, "inclusive": false}, "upperValueLimit": {"value": 79.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 2.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 79.999, "inclusive": false}, "upperValueLimit": {"value": 149.99, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 2.9, "lower": -0.3}}, {"lowerValueLimit": {"value": 149.999, "inclusive": false}, "upperValueLimit": {"value": 249.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 3.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 249.999, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 4.5, "lower": -0.3}}, {"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 4.999, "inclusive": true}, "rowIdentifiers": [["Klasse C"]], "tolerance": {"upper": 1.0, "lower": 0}}, {"lowerValueLimit": {"value": 4.999, "inclusive": false}, "upperValueLimit": {"value": 7.999, "inclusive": true}, "rowIdentifiers": [["Klasse C"]], "tolerance": {"upper": 1.2, "lower": 0}}, {"lowerValueLimit": {"value": 7.999, "inclusive": false}, "upperValueLimit": {"value": 14.999, "inclusive": true}, "rowIdentifiers": [["Klasse C"]], "tolerance": {"upper": 1.4, "lower": 0}}, {"lowerValueLimit": {"value": 14.999, "inclusive": false}, "upperValueLimit": {"value": 24.999, "inclusive": true}, "rowIdentifiers": [["Klasse C"]], "tolerance": {"upper": 1.6, "lower": 0}}, {"lowerValueLimit": {"value": 24.999, "inclusive": false}, "upperValueLimit": {"value": 39.999, "inclusive": true}, "rowIdentifiers": [["Klasse C"]], "tolerance": {"upper": 2.0, "lower": 0}}, {"lowerValueLimit": {"value": 39.999, "inclusive": false}, "upperValueLimit": {"value": 79.999, "inclusive": true}, "rowIdentifiers": [["Klasse C"]], "tolerance": {"upper": 2.6, "lower": 0}}, {"lowerValueLimit": {"value": 79.999, "inclusive": false}, "upperValueLimit": {"value": 149.99, "inclusive": true}, "rowIdentifiers": [["Klasse C"]], "tolerance": {"upper": 3.2, "lower": 0}}, {"lowerValueLimit": {"value": 149.999, "inclusive": false}, "upperValueLimit": {"value": 249.999, "inclusive": true}, "rowIdentifiers": [["Klasse C"]], "tolerance": {"upper": 3.6, "lower": 0}}, {"lowerValueLimit": {"value": 249.999, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Klasse C"]], "tolerance": {"upper": 4.8, "lower": 0}}, {"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 4.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 4.999, "inclusive": false}, "upperValueLimit": {"value": 7.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 7.999, "inclusive": false}, "upperValueLimit": {"value": 14.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 14.999, "inclusive": false}, "upperValueLimit": {"value": 24.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 24.999, "inclusive": false}, "upperValueLimit": {"value": 39.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 39.999, "inclusive": false}, "upperValueLimit": {"value": 79.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 79.999, "inclusive": false}, "upperValueLimit": {"value": 149.99, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 149.999, "inclusive": false}, "upperValueLimit": {"value": 249.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 249.999, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 2.4, "lower": -2.5}}]}, {"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 39.999, "inclusive": true}, "rowIdentifiers": [["Breite"]], "tolerance": {"upper": 20, "lower": 0}}, {"lowerValueLimit": {"value": 39.999, "inclusive": false}, "upperValueLimit": {"value": 149.999, "inclusive": true}, "rowIdentifiers": [["Breite"]], "tolerance": {"upper": 25, "lower": 0}}, {"lowerValueLimit": {"value": 149.999, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Breite"]], "tolerance": {"upper": 30, "lower": 0}}]}, {"name": "Tabelle 3", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3999.999, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 20, "lower": 0}}, {"lowerValueLimit": {"value": 3999.999, "inclusive": false}, "upperValueLimit": {"value": 5999.999, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 30, "lower": 0}}, {"lowerValueLimit": {"value": 5999.999, "inclusive": false}, "upperValueLimit": {"value": 7999.999, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 40, "lower": 0}}, {"lowerValueLimit": {"value": 7999.999, "inclusive": false}, "upperValueLimit": {"value": 9999.999, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 50, "lower": 0}}, {"lowerValueLimit": {"value": 9999.999, "inclusive": false}, "upperValueLimit": {"value": 14999.999, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 75, "lower": 0}}, {"lowerValueLimit": {"value": 14999.999, "inclusive": false}, "upperValueLimit": {"value": 20000, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 100, "lower": 0}}]}]}