"""
Post-processing models for PMI analysis.
This module defines data structures for PMI analysis results.
"""
from enum import Enum
from typing import Annotated, Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, field_validator


class BoundingPoly(BaseModel):
    """Model for bounding polygon of detected text"""

    vertices: List[Dict[str, int]] = Field(description="List of vertices defining the bounding polygon")


class NormalizedPoint(BaseModel):
    """Normalized (0-1) coordinate point"""

    x: float = Field(ge=0, le=1, description="Normalized X coordinate (0-1)")
    y: float = Field(ge=0, le=1, description="Normalized Y coordinate (0-1)")


class NormalizedPolygon(BaseModel):
    """Polygon with normalized (0-1) coordinates in clockwise order"""

    points: List[NormalizedPoint] = Field(
        min_length=4, description="List of normalized points defining the polygon in clockwise order"
    )


class Importance(str, Enum):
    """Importance level of a PMI element."""

    AUXILIARY = "AUXILIARY"
    THEORETICAL = "THEORETICAL"
    COMMON = "COMMON"
    CONTROL = "CONTROL"
    SPECIAL = "SPECIAL"


class Unit(str, Enum):
    """Measurement unit for PMI values."""

    MILLIMETER = "MILLIMETER"
    MICROMETER = "MICROMETER"
    DEGREE = "DEGREE"


class PMIType(str, Enum):
    """Type of PMI measurement."""

    DIAMETER = "DIAMETER"
    THREAD = "THREAD"
    GDT = "GDT"
    ANGLE = "ANGLE"
    LINEAR = "LINEAR"
    RADIUS = "RADIUS"
    CHAMFER = "CHAMFER"
    ROUGHNESS = "ROUGHNESS"


class GDTSymbol(str, Enum):
    """Geometric Dimensioning and Tolerancing symbols."""

    FLATNESS = "FLATNESS"
    STRAIGHTNESS = "STRAIGHTNESS"
    CIRCULARITY = "CIRCULARITY"
    CYLINDRICITY = "CYLINDRICITY"
    PROFILE_OF_LINE = "PROFILE_OF_LINE"
    PROFILE_OF_SURFACE = "PROFILE_OF_SURFACE"
    PERPENDICULARITY = "PERPENDICULARITY"
    ANGULARITY = "ANGULARITY"
    PARALLELISM = "PARALLELISM"
    CONCENTRICITY = "CONCENTRICITY"
    SYMMETRY = "SYMMETRY"
    POSITION = "POSITION"
    CIRCULAR_RUNOUT = "CIRCULAR_RUNOUT"
    TOTAL_RUNOUT = "TOTAL_RUNOUT"


class ThreadStandard(str, Enum):
    M = "M"
    UNC = "UNC"
    UNF = "UNF"
    BSP = "BSP"
    NPT = "NPT"
    TR = "TR"


class RoughnessCategory(str, Enum):
    """Roughness category for surface finish."""

    RA = "RA"
    RZ = "RZ"
    RY = "RY"
    RT = "RT"
    RQ = "RQ"


class SharedPMIValues(BaseModel):
    """Base class for shared PMI measurement values."""

    nominal_value: float = Field(description="Primary measurement value - if type is chamfer, this is the length value")
    lower_tol: Optional[float] = Field(None, description="Lower tolerance value")
    upper_tol: Optional[float] = Field(None, description="Upper tolerance value")
    unit: Unit = Field(description="Unit of measurement")
    raw_callout: Optional[str] = Field(None, description="Raw callout text")


class DiameterDetail(SharedPMIValues):
    """Details specific to diameter measurements."""

    type: Literal["DIAMETER"] = "DIAMETER"
    iso286_identifier: Optional[str] = Field(None, pattern="^[a-zA-Z]{1,2}[0-9]{1,2}$")
    # is_counterbore: Optional[bool] = Field(None, description="Whether this is a counterbore")
    # is_countersink: Optional[bool] = Field(None, description="Whether this is a countersink")
    # is_through_hole: Optional[bool] = Field(None, description="Whether this is a through hole")
    # depth: Optional[float] = Field(None, description="Depth value if specified")
    # depth_unit: Optional[Unit] = Field(None, description="Unit for depth measurement")


class ThreadDetail(SharedPMIValues):
    """Details specific to threaded features."""

    type: Literal["THREAD"] = "THREAD"
    pitch: Optional[float] = None
    iso965_identifier: Optional[str] = Field(None, pattern="^[0-9][a-zA-Z]([0-9][a-zA-Z])?$")
    thread_standard: Optional[ThreadStandard] = Field(None, description="Thread Standard")
    depth: Optional[float] = Field(None, description="Depth value if specified")
    depth_unit: Optional[Unit] = Field(None, description="Unit for depth measurement")


class RoughnessDetail(SharedPMIValues):
    """Details specific to surface roughness."""

    type: Literal["ROUGHNESS"] = "ROUGHNESS"
    roughness_category: Optional[RoughnessCategory] = Field(
        None, description="Roughness category (e.g., Ra, Ry, Rz, Rt, Rq)"
    )


class GDTDetail(SharedPMIValues):
    """Details specific to Geometric Dimensioning and Tolerancing."""

    type: Literal["GDT"] = "GDT"
    symbol: Optional[GDTSymbol] = Field(
        None,
        description=(
            "GDT symbol (e.g., flatness, perpendicularity). \n"
            "Visual descriptions: \n"
            "flatness: a horizontal parallelogram, slightly tilted, like a rhombus lying on its side; \n"
            "straightness: a single, straight, vertical line; \n"
            "cylindricity: a circle with two short parallel vertical lines on either side, \n"
            "forming a cylinder-like icon; \n"
            "circularity: a single perfect circle; \n"
            "perpendicularity: an upside-down capital 'T', like a vertical line intersecting a horizontal base line; \n"
            "parallelism: two short parallel diagonal lines leaning to the right; \n"
            "angularity: a right triangle lying on its side, with the right angle at the bottom left; \n"
            "position: a circle with a crosshair (like a target or sight); \n"
            "profile_of_surface: a semicircle with the flat edge on the bottom; \n"
            "profile_of_line: a semicircle with the flat edge on the top; \n"
            "total_runout: two upward diagonal arrows starting from the same base point; \n"
            "circular_runout: a single upward diagonal arrow; \n"
            "concentricity: two concentric circles, one inside the other (like a bullseye); \n"
            "symmetry: a horizontal line with two vertical lines mirrored above and below \n"
            "(like a bowtie or stylized 'H')."
        ),
    )
    datum_references: Optional[List[str]] = Field(None, description="Referenced datum features")
    material_condition: Optional[str] = Field(None, description="Material condition (e.g., MMC, LMC, RFS)")
    diameter_modifier: bool = Field(False, description="True if the FCF includes the Ø symbol")


class AngleDetail(SharedPMIValues):
    """Details specific to angular measurements."""

    type: Literal["ANGLE"] = "ANGLE"


class LinearDetail(SharedPMIValues):
    """Details specific to linear measurements."""

    type: Literal["LINEAR"] = "LINEAR"


class RadiusDetail(SharedPMIValues):
    """Details specific to radius measurements."""

    type: Literal["RADIUS"] = "RADIUS"


class ChamferDetail(SharedPMIValues):
    """Details specific to chamfers."""

    type: Literal["CHAMFER"] = "CHAMFER"
    angle: Optional[float] = Field(None, description="Angle of the chamfer")
    length: Optional[float] = Field(None, description="Length of the chamfer")
    length_unit: Optional[Unit] = Field(None, description="Unit for length measurement")


# This is a discriminated union based on the 'type' field
DetailType = Annotated[
    Union[
        DiameterDetail, ThreadDetail, GDTDetail, AngleDetail, LinearDetail, RadiusDetail, ChamferDetail, RoughnessDetail
    ],
    Field(discriminator="type"),
]


class PMIBlockAnalysisResultWithNonNormalizedBoundingPoly(BaseModel):
    """Schema of PMI block analysis with non-normalized bounding polygon (internal use only)."""

    importance: Importance = Field(
        description=(
            "Classifies the PMI based on its visual presentation and functional intent:\n\n"
            "- 'auxiliary': The PMI is enclosed in **parentheses** directly surrounding the annotation.\n\n"
            "- 'theoretical': The PMI is enclosed in a **rectangular box** directly surrounding the annotation.\n\n"
            "- 'special': The PMI is highlighted with a **capsule-shaped elongated oval** that directly "
            "surrounds the annotation.\n\n"
            "- 'control': The PMI is identified as GD&T or DIAMETER with explicit fits (e.g H7/g6) or THREAD "
            "or other types with extremely tight tolerances.\n\n"
            "- 'common': The PMI is not any of the above.\n\n"
        )
    )
    count: int = Field(1, description="Number of occurrences")
    detail: DetailType = Field(description="Type-specific details")
    bounding_poly: BoundingPoly = Field(description="Bounding polygon for this PMI block")


class PMIBlockAnalysisResult(BaseModel):
    """Schema of PMI block analysis (for API responses)."""

    importance: Importance = Field(
        description=(
            "Classifies the PMI based on its visual presentation and functional intent:\n\n"
            "- 'auxiliary': The PMI is enclosed in **parentheses** directly surrounding the annotation.\n\n"
            "- 'theoretical': The PMI is enclosed in a **rectangular box** directly surrounding the annotation.\n\n"
            "- 'special': The PMI is highlighted with a **capsule-shaped elongated oval** that directly "
            "surrounds the annotation.\n\n"
            "- 'control': The PMI is identified as GD&T or DIAMETER with explicit fits (e.g H7/g6) or THREAD "
            "or other types with extremely tight tolerances.\n\n"
            "- 'common': The PMI is not any of the above.\n\n"
        )
    )
    count: int = Field(1, description="Number of occurrences")
    detail: DetailType = Field(description="Type-specific details")

    @classmethod
    def from_full_result(cls, result: PMIBlockAnalysisResultWithNonNormalizedBoundingPoly) -> "PMIBlockAnalysisResult":
        """Create a version without bounding_poly from a full result."""
        return cls(importance=result.importance, count=result.count, detail=result.detail)


class ExtractedGeneralTolerance(BaseModel):
    """Extracted standard information from drawing text."""

    standard_type: Literal["ISO_2768", "ISO_286", "OTHER"] = Field(
        description="Type of standard (ISO 2768, ISO 286, or OTHER)"
    )
    raw_text: str = Field(
        description="Raw text of the standard as it appears in the drawing (e.g., 'ISO2768mK', 'ISO286')"
    )
    # For ISO 2768, these are used to look up tolerances in the tables
    row_identifiers: Optional[List[str]] = Field(
        None, description="Row identifiers extracted from the standard text (e.g., ['m', 'K'] for ISO2768mK)"
    )


class ExtractedMaterial(BaseModel):
    """Extracted material information from drawing text."""

    material_standard: Optional[List[str]] = Field(
        None,
        description="Standard that defines the material if specified. \n"
        "(e.g., EN 10025, ASTM A36, DIN 1.430, EN AW-5083)",
    )
    raw_material_name: Optional[str] = Field(
        None,
        description="Raw text of the material specification as it appears in the drawing. \n"
        "(e.g., AlCuMgPb, [Al Mg4.5Mn0.7] H111, 1.4301, AlMgSi1, S355J2+N, 1.0570 St52-3)",
    )

    @field_validator("raw_material_name", mode="before")
    @classmethod
    def ensure_string(cls, v: Any) -> Optional[str]:
        """Ensure raw_material_name is always a string, even if it looks like a number."""
        if v is not None:
            return str(v)
        return v


class PMIMetadata(BaseModel):
    """Metadata for a PMI block, including standards and materials information."""

    general_tolerance_info: Optional[ExtractedGeneralTolerance] = Field(
        default=None, description="General tolerance standard information"
    )
    material_info: Optional[ExtractedMaterial] = Field(default=None, description="Material information")


class NormalizedPMIBlockAnalysisResult(BaseModel):
    """PMI block analysis result with normalized coordinates."""

    # Normalized polygon (0-1 range)
    polygon: NormalizedPolygon

    # Result without bounding_poly (for API responses)
    result: PMIBlockAnalysisResult

    # Source of tolerance values
    tolerance_source: Optional[Literal["explicit", "ISO_2768", "ISO_286"]] = Field(
        None, description="Source of tolerance values (explicit, ISO_2768, or ISO_286)"
    )

    @classmethod
    def from_original(
        cls, original_result: PMIBlockAnalysisResultWithNonNormalizedBoundingPoly, polygon: NormalizedPolygon
    ) -> "NormalizedPMIBlockAnalysisResult":
        """Create a normalized result from an original result and polygon."""
        return cls(
            polygon=polygon,
            result=PMIBlockAnalysisResult.from_full_result(original_result),
            tolerance_source=None,
        )


class DrawingAnalysisResult(BaseModel):
    """Drawing analysis result with PMI blocks and metadata."""

    # Dictionary mapping classifications to lists of normalized PMI results
    results: Dict[str, List[NormalizedPMIBlockAnalysisResult]] = Field(
        description="Dictionary mapping classifications to lists of normalized PMI results"
    )

    # Metadata for the entire drawing
    metadata: Optional[PMIMetadata] = Field(
        default=None, description="Metadata for the entire drawing, including standards and materials"
    )
