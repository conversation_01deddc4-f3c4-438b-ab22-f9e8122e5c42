<ng-container *ngIf="vm$ | async as vm">
  <ng-container *ngIf="vm.polState.purchaseOrderLine.id">
    <div class="relative h-full w-full">
      <div class="absolute left-0 top-0 flex h-8 w-full items-center space-x-2">
        <ng-container *ngTemplateOutlet="navigation"></ng-container>
        <ng-container *ngTemplateOutlet="polSummary"></ng-container>
      </div>
      <div class="absolute inset-0 top-12 w-full flex-grow">
        <router-outlet></router-outlet>
      </div>
    </div>
  </ng-container>

  <ng-template #polSummary>
    <div
      class="no-scrollbar flex h-full w-full items-center space-x-1 overflow-x-auto"
    >
      <!-- Business Info (Customer/Supplier) -->
      <div
        *ngIf="vm.polState.purchaseOrder.info.type === QwOrderType.Purchase"
        class="flex h-full flex-shrink-0 items-center space-x-1 truncate rounded-md bg-qw-accent-50 p-1"
      >
        <div
          class="flex aspect-square w-6 flex-none items-center justify-center rounded-lg text-qw-primary-700"
        >
          <fa-icon
            [icon]="faBriefcase"
            [title]="vm.polState.isSupplier ? 'Customer' : 'Supplier'"
          ></fa-icon>
        </div>
        <span class="text-sm">
          {{
            vm.polState.isSupplier
              ? vm.polState.purchaseOrder.customer.prettyName
              : vm.polState.supplier?.prettyName
          }}
        </span>
      </div>

      <!-- Order ID -->
      <div
        class="flex h-full flex-shrink-0 items-center space-x-1 truncate rounded-md bg-qw-accent-50 p-1"
      >
        <div
          class="flex aspect-square w-6 flex-none items-center justify-center rounded-lg text-qw-primary-700"
        >
          <fa-icon [icon]="faTruck" title="Order ID"></fa-icon>
        </div>
        <span class="text-sm">
          {{ vm.polState.purchaseOrder.internalReference }}/{{
            vm.polState.purchaseOrderLine.internalReference
          }}
        </span>
      </div>

      <!-- Material -->
      <div
        *ngIf="!vm.polState.isSupplier"
        class="flex h-full flex-shrink-0 items-center space-x-1 truncate rounded-md bg-qw-accent-50 p-1"
      >
        <div
          class="flex aspect-square w-6 flex-none items-center justify-center rounded-lg text-qw-primary-700"
        >
          <fa-icon [icon]="faCube" title="Material Name"></fa-icon>
        </div>
        <span class="text-sm">
          {{ vm.polState.material?.internalReference }}/{{
            vm.polState.material?.name
          }}
        </span>
      </div>

      <!-- Quantity -->
      <div
        class="flex h-full flex-shrink-0 items-center space-x-1 truncate rounded-md bg-qw-accent-50 p-1"
      >
        <div
          class="flex aspect-square w-6 flex-none items-center justify-center rounded-lg text-qw-primary-700"
        >
          <fa-icon [icon]="faHashtag" [title]="'Order Quantity'"></fa-icon>
        </div>
        <span class="text-sm">{{ vm.polState.purchaseOrderLine.count }}</span>
      </div>

      <!-- Expected Date -->
      <div
        class="flex h-full flex-shrink-0 items-center space-x-1 truncate rounded-md bg-qw-accent-50 p-1"
      >
        <div
          class="flex aspect-square w-6 flex-none items-center justify-center rounded-lg text-qw-primary-700"
        >
          <fa-icon [icon]="faCalendarCheck" title="Expected"></fa-icon>
        </div>
        <span class="text-sm">
          {{ vm.polState.purchaseOrderLine.expectedDeliveryDate | date }}
        </span>
      </div>
    </div>

    <!-- Share Link Button -->
    <div
      class="ml-auto flex h-8 w-8 shrink-0 cursor-pointer justify-end"
      (click)="shareLink()"
    >
      <fa-icon
        class="flex h-full w-full items-center justify-center rounded-lg text-gray-700 hover:bg-gray-200"
        [icon]="faArrowUpFromBracket"
      ></fa-icon>
    </div>
  </ng-template>

  <!-- Navigation Template -->
  <ng-template #navigation>
    <div class="flex items-center space-x-1">
      <a
        class="w-min-w relative flex h-8 min-w-8 flex-none items-center justify-center space-x-1 rounded-lg bg-gray-300 px-2 text-white"
        [routerLink]="
          AppRoutes.fullPath('polFiles', vm.polState.purchaseOrderLine.id)
        "
        routerLinkActive="bg-qw-primary-500"
        #filesLink="routerLinkActive"
      >
        <fa-icon [icon]="faFolderOpen"></fa-icon>
        <span
          i18n
          class="mx-1 text-sm"
          [ngClass]="{ hidden: !filesLink.isActive }"
        >
          Docs
        </span>

        <!-- Technical Drawing Status -->
        <ng-container
          *ngIf="vm.polState.purchaseOrderLine?.documentStats as docStats"
        >
          <div
            *ngIf="docStats.countTechnicalDrawings === 0"
            class="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-xs text-qw-warn-500"
            title="Missing required files"
          >
            <fa-icon [icon]="faCircleExclamation"></fa-icon>
          </div>
          <div
            *ngIf="docStats.countTechnicalDrawings > 0"
            class="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-xs text-qw-primary-500"
            title="All files submitted"
          >
            <fa-icon [icon]="faCircleCheck"></fa-icon>
          </div>
        </ng-container>
      </a>
      <!-- Inspections Link -->
      <a
        class="w-min-w relative flex h-8 min-w-8 flex-none items-center justify-center rounded-lg bg-gray-300 px-2 text-white"
        [routerLink]="
          AppRoutes.fullPath('polInspections', vm.polState.purchaseOrderLine.id)
        "
        routerLinkActive="bg-qw-primary-500"
        #inspectionsLink="routerLinkActive"
      >
        <fa-icon [icon]="faRulerCombined"></fa-icon>
        <span
          i18n
          class="mx-1 text-sm"
          [ngClass]="{ hidden: !inspectionsLink.isActive }"
        >
          Inspections
        </span>

        <!-- Inspection Status - Priority: Non-conformity > Unfinished > All Complete -->
        <ng-container
          *ngIf="vm.polState.purchaseOrderLine?.inspectionStats as inspStats"
        >
          <!-- Non-conformity -->
          <div
            *ngIf="inspStats.countCompletedInspectionsWithNonConformity > 0"
            class="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-xs text-qw-warn-500"
            title="Inspections with issues"
          >
            <fa-icon [icon]="faCircleExclamation"></fa-icon>
          </div>

          <!-- Unfinished jobs -->
          <div
            *ngIf="
              inspStats.countCompletedInspectionsWithNonConformity === 0 &&
              inspStats.countIncompleteInspections > 0
            "
            class="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-xs text-orange-400"
            title="Incomplete inspections"
          >
            <fa-icon [icon]="faClock"></fa-icon>
          </div>

          <!-- All Complete and No Issues -->
          <div
            *ngIf="
              inspStats.countCompletedInspectionsWithNonConformity === 0 &&
              inspStats.countIncompleteInspections === 0 &&
              inspStats.countCompletedInspections > 0
            "
            class="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-xs text-qw-primary-500"
            title="All inspections completed successfully"
          >
            <fa-icon [icon]="faCircleCheck"></fa-icon>
          </div>
        </ng-container>
      </a>
      <a
        class="w-min-w flex h-8 min-w-8 flex-none items-center justify-center rounded-lg bg-gray-300 px-2 text-white"
        [routerLink]="
          AppRoutes.fullPath('polChats', vm.polState.purchaseOrderLine.id)
        "
        routerLinkActive="bg-qw-primary-500"
        #chatsLink="routerLinkActive"
      >
        <fa-icon [icon]="faMessage"></fa-icon>
        <span
          i18n
          class="mx-1 text-sm"
          [ngClass]="{ hidden: !chatsLink.isActive }"
        >
          Messages
        </span>
      </a>
    </div>
  </ng-template>
</ng-container>
