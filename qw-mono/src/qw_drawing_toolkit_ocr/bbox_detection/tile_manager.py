"""
Tile management module for processing drawing tiles in OCR.
This module provides functionality to group tiles, create sliding windows,
and handle coordinate transformations for OCR results.
"""
import io
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, cast

import cv2
import numpy as np
import numpy.typing as npt
from PIL import Image

from qw_drawing_toolkit_ocr.bbox_detection.models import BoxCoords, Detection, DrawingPoint, OcrResult
from qw_log_interface import NO_LOGGER, Logger


# Define NormalizedBoxCoords locally
class NormalizedBoxCoords(List[DrawingPoint]):
    """List of normalized DrawingPoint coordinates defining a box"""

    pass


TileData = Tuple[bytes, int, int]  # image_data, x, y


@dataclass
class Tile:
    """Class representing a single tile from a drawing"""

    tile_id: str
    tile_x: int
    tile_y: int
    width: int
    height: int
    image_data: bytes


@dataclass
class TileGroup:
    """Class representing a group of tiles to be processed together"""

    tiles: List[Tile]
    offset_x: int
    offset_y: int
    stitched_image: Optional[npt.NDArray[np.uint8]] = None

    @property
    def top_left_x(self) -> int:
        """Get the x-coordinate of the top-left corner of the group"""
        return self.offset_x

    @property
    def top_left_y(self) -> int:
        """Get the y-coordinate of the top-left corner of the group"""
        return self.offset_y

    @property
    def tile_ids(self) -> List[str]:
        """Get list of tile IDs in this group"""
        return [tile.tile_id for tile in self.tiles]

    def __str__(self) -> str:
        """String representation of tile group"""
        return f"TileGroup(offset=({self.offset_x}, {self.offset_y}), " f"tiles={len(self.tiles)}, ids={self.tile_ids})"


class TileGroupManager:
    """
    Manager class for handling tile groups and sliding windows.
    """

    def __init__(
        self,
        logger: Logger = NO_LOGGER,
        group_width: int = 2,
        group_height: int = 2,
        slide_step_x: int = 1,
        slide_step_y: int = 1,
    ):
        """
        Initialize the tile group manager.

        Args:
            logger: Logger instance
            group_width: Number of tiles in the x-direction for each group
            group_height: Number of tiles in the y-direction for each group
            slide_step_x: Number of tiles to slide in the x-direction (1 for maximum overlap)
            slide_step_y: Number of tiles to slide in the y-direction (1 for maximum overlap)
        """
        self.logger = logger
        self.group_width = group_width
        self.group_height = group_height
        self.slide_step_x = slide_step_x
        self.slide_step_y = slide_step_y

    def create_tile_object(
        self, tile_id: str, tile_x: int, tile_y: int, width: int, height: int, image_data: bytes
    ) -> Tile:
        """
        Create a Tile object from the given parameters.

        Args:
            tile_id: Unique identifier for the tile
            tile_x: X-coordinate of the tile in the drawing
            tile_y: Y-coordinate of the tile in the drawing
            width: Width of the tile in pixels
            height: Height of the tile in pixels
            image_data: Raw image data as bytes

        """
        return Tile(tile_id=tile_id, tile_x=tile_x, tile_y=tile_y, width=width, height=height, image_data=image_data)

    def group_tiles(self, tiles: List[Tile]) -> List[TileGroup]:
        """
        Group tiles into 2x2 groups with sliding window.

        Args:
            tiles: List of all tiles from the drawing

        """
        if not tiles:
            self.logger.warning("No tiles provided for grouping")
            return []

        # Find the max x and y coordinates to determine drawing dimensions
        max_x = 0
        max_y = 0

        # Create a 2D dictionary to access tiles by their coordinates
        tile_dict: Dict[int, Dict[int, Tile]] = {}

        for tile in tiles:
            x, y = tile.tile_x, tile.tile_y

            if x > max_x:
                max_x = x
            if y > max_y:
                max_y = y

            if x not in tile_dict:
                tile_dict[x] = {}

            tile_dict[x][y] = tile

        self.logger.info(f"Grid size: {max_x + 1}x{max_y + 1} tiles")

        # Create tile groups using sliding window
        tile_groups: List[TileGroup] = []

        for start_y in range(0, max_y + 2 - self.group_height, self.slide_step_y):
            for start_x in range(0, max_x + 2 - self.group_width, self.slide_step_x):
                group_tiles: List[Tile] = []

                # Collect tiles for this group
                for y in range(start_y, start_y + self.group_height):
                    for x in range(start_x, start_x + self.group_width):
                        if x in tile_dict and y in tile_dict[x]:
                            group_tiles.append(tile_dict[x][y])

                # Only create a group if we have at least one tile
                if group_tiles:
                    # Find the minimum x and y coordinates in this group
                    min_x: int = min(tile.tile_x for tile in group_tiles)
                    min_y: int = min(tile.tile_y for tile in group_tiles)

                    tile_groups.append(TileGroup(tiles=group_tiles, offset_x=min_x, offset_y=min_y))

        return tile_groups

    def stitch_tile_group(self, group: TileGroup, tile_size: int) -> npt.NDArray[np.uint8]:
        """
        Stitch tiles in a group into a single image.

        Args:
            group: TileGroup to stitch
            tile_size: Expected size of a full tile (for positioning)

        """
        if not group.tiles:
            self.logger.warning("Empty tile group, cannot stitch")
            return np.zeros((1, 1, 3), dtype=np.uint8)

        # Find the extents of the stitched image
        min_x = min(tile.tile_x for tile in group.tiles)
        max_x = max(tile.tile_x for tile in group.tiles)
        min_y = min(tile.tile_y for tile in group.tiles)
        max_y = max(tile.tile_y for tile in group.tiles)

        # Get actual dimensions of each tile
        tiles_with_dimensions: List[Tuple[Tile, int, int]] = []
        for tile in group.tiles:
            try:
                img_bytes = io.BytesIO(tile.image_data)
                img = Image.open(img_bytes)
                actual_width, actual_height = img.size
                tiles_with_dimensions.append((tile, actual_width, actual_height))
            except Exception as e:
                self.logger.error(f"Error getting tile dimensions for {tile.tile_id}: {str(e)}")
                tiles_with_dimensions.append((tile, tile_size, tile_size))  # Fallback to standard size

        # Calculate dimensions of the stitched image - use actual dimensions of tiles
        # This allows us to handle non-square tiles in the last row/column
        max_width_per_x: Dict[int, int] = {}
        max_height_per_y: Dict[int, int] = {}

        for tile_tuple in tiles_with_dimensions:
            tile, width, height = tile_tuple
            x, y = tile.tile_x, tile.tile_y
            max_width_per_x[x] = max(max_width_per_x.get(x, 0), width)
            max_height_per_y[y] = max(max_height_per_y.get(y, 0), height)

        # Calculate the position offset for each column and row
        x_offsets: Dict[int, int] = {min_x: 0}
        y_offsets: Dict[int, int] = {min_y: 0}

        for x in range(min_x + 1, max_x + 1):
            x_offsets[x] = x_offsets[x - 1] + max_width_per_x.get(x - 1, tile_size)

        for y in range(min_y + 1, max_y + 1):
            y_offsets[y] = y_offsets[y - 1] + max_height_per_y.get(y - 1, tile_size)

        # Calculate total width and height
        total_width: int = x_offsets.get(max_x, 0) + max_width_per_x.get(max_x, tile_size)
        total_height: int = y_offsets.get(max_y, 0) + max_height_per_y.get(max_y, tile_size)

        # Create a blank canvas
        stitched = np.zeros((total_height, total_width, 3), dtype=np.uint8)

        # Place each tile on the canvas using its actual dimensions
        for tile_tuple in tiles_with_dimensions:
            tile, width, height = tile_tuple
            try:
                # Convert tile image bytes to numpy array
                img_bytes = io.BytesIO(tile.image_data)
                img = Image.open(img_bytes)
                img_array = np.array(img)

                # Convert to BGR for OpenCV if needed
                if len(img_array.shape) == 3 and img_array.shape[2] == 3:
                    img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
                elif len(img_array.shape) == 3 and img_array.shape[2] == 4:
                    img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2BGR)

                # Calculate position using offsets
                x_pos = x_offsets[tile.tile_x]
                y_pos = y_offsets[tile.tile_y]

                # Place the tile using its actual dimensions
                h, w = img_array.shape[:2]
                stitched[y_pos : y_pos + h, x_pos : x_pos + w] = img_array

            except Exception as e:
                self.logger.error(f"Error stitching tile {tile.tile_id}: {str(e)}")

        # Store the stitched image in the group
        group.stitched_image = stitched

        return stitched

    def transform_coordinates(
        self,
        result: OcrResult,
        offset_x: int,
        offset_y: int,
        tile_size: int,
        canvas_width: int = 0,
        canvas_height: int = 0,
    ) -> OcrResult:
        """
        Transform OCR result coordinates to be relative to the full drawing.
        Optionally normalizes coordinates to 0-1 range.

        Args:
            result: OcrResult with coordinates relative to the tile group
            offset_x: X-coordinate of the top-left tile in the group
            offset_y: Y-coordinate of the top-left tile in the group
            tile_size: Size of each tile in pixels
            normalize: Whether to normalize coordinates to 0-1 range
            canvas_width: Width of the full drawing canvas (needed for normalization)
            canvas_height: Height of the full drawing canvas (needed for normalization)
        """
        transformed_result = OcrResult()

        for detection in result.detections:
            try:
                # Safely extract components with proper type casting
                box_coords = cast(BoxCoords, detection[0])
                text = str(detection[1])
                confidence = float(detection[2])

                # Transform coordinates
                transformed_coords: BoxCoords = []
                for point in box_coords:
                    if len(point) >= 2:
                        # Ensure x and y are numeric
                        x_val = float(point[0])
                        y_val = float(point[1])

                        tx = x_val + (offset_x * tile_size)
                        ty = y_val + (offset_y * tile_size)
                        transformed_coords.append([tx, ty])

                # Normalize coordinates if requested
                normalized_coords = self.normalize_coordinates(transformed_coords, canvas_width, canvas_height)
                # Convert NormalizedBoxCoords to list format for compatibility
                norm_coords_list = [[point.x, point.y] for point in normalized_coords]
                transformed_detection: Detection = [norm_coords_list, text, confidence]

                transformed_result.detections.append(transformed_detection)

            except Exception as e:
                self.logger.warning(f"Error transforming coordinates: {str(e)}")

        return transformed_result

    def save_tile_group_image(
        self, group: TileGroup, output_dir: Union[str, Path], tile_size: int, name_prefix: str = "tile_group"
    ) -> str:
        """
        Save a tile group image to disk for debugging.

        Args:
            group: TileGroup to save
            output_dir: Directory to save the image
            tile_size: Size of each tile in pixels
            name_prefix: Prefix for the filename

        """
        output_dir = Path(output_dir) if isinstance(output_dir, str) else output_dir
        output_dir.mkdir(parents=True, exist_ok=True)

        # Stitch the image if not already done
        if group.stitched_image is None:
            image = self.stitch_tile_group(group, tile_size)
        else:
            image = group.stitched_image

        # Generate filename
        filename = f"{name_prefix}_{group.offset_x}_{group.offset_y}.png"
        filepath = output_dir / filename

        # Save the image
        cv2.imwrite(str(filepath), image)
        self.logger.info(f"Saved tile group image to {filepath}")

        return str(filepath)

    def normalize_coordinates(
        self, box_coords: BoxCoords, canvas_width: int, canvas_height: int
    ) -> NormalizedBoxCoords:
        """
        Convert absolute coordinates to normalized (0-1) coordinates.

        Args:
            box_coords: Absolute coordinates [[x1,y1], [x2,y2], ...]
            canvas_width: Width of the full drawing canvas
            canvas_height: Height of the full drawing canvas

        Returns:
            List of normalized DrawingPoint coordinates
        """
        normalized_coords = NormalizedBoxCoords()

        if canvas_width <= 0 or canvas_height <= 0:
            self.logger.warning("Cannot normalize coordinates with invalid canvas dimensions")
            return normalized_coords

        for point in box_coords:
            if len(point) >= 2:
                x_val = float(point[0])
                y_val = float(point[1])

                # Normalize coordinates to 0-1 range
                nx = max(0.0, min(1.0, x_val / canvas_width))
                ny = max(0.0, min(1.0, y_val / canvas_height))

                normalized_coords.append(DrawingPoint(x=nx, y=ny))

        return normalized_coords
