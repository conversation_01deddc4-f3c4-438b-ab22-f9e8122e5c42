from functools import partial

from pydantic import BaseModel

from qw_basic_iam_policy.attributes.group import MembershipParser, parse_membership, parse_membership_no_op
from qw_basic_iam_policy.token import ValidAccessToken, ValidIdToken
from qw_basic_keycloak.openid.inspector import TokenInspector
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_tenant_config.registry import TenantConfigRegistry


class QwPolicySettings(BaseModel):
    property_groups: str = "qw_groups"
    property_roles: str = "qw_roles"


class QwPolicy(object):
    def __init__(
        self,
        inspector: TokenInspector,
        membership_parser: MembershipParser,
        settings: QwPolicySettings,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.inspector = inspector
        self.membership_parser = membership_parser
        self.settings = settings
        self.logger = lf.get_logger(__name__)

    @classmethod
    def build(
        cls,
        inspector: TokenInspector,
        tenant_registry: TenantConfigRegistry | None,
        settings: QwPolicySettings | None = None,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> "QwPolicy":
        if tenant_registry is None:
            membership_parser = parse_membership_no_op
        else:
            membership_parser = partial(parse_membership, tenant_registry=tenant_registry)
        if settings is None:
            settings = QwPolicySettings()
        return cls(
            inspector=inspector,
            membership_parser=membership_parser,
            settings=settings,
            lf=lf,
        )

    def inspect_access_token(self, access_tkn: str) -> ValidAccessToken | None:
        tkn = self.inspector.validate_token(access_tkn, expected_type="Bearer")
        if tkn is None:
            return None
        return ValidAccessToken.from_valid_token(
            valid_tkn=tkn,
            parse_membership=self.membership_parser,
            property_roles=self.settings.property_roles,
            property_groups=self.settings.property_groups,
            logger=self.logger,
        )

    def inspect_id_token(self, id_tkn: str) -> ValidIdToken | None:
        tkn = self.inspector.validate_token(id_tkn, expected_type="ID")
        if tkn is None:
            return None
        return ValidIdToken.from_valid_token(
            valid_tkn=tkn,
            parse_membership=self.membership_parser,
            property_groups=self.settings.property_groups,
            logger=self.logger,
        )
