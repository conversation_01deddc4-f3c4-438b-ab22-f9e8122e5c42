import pytest

from qw_drawing_toolkit.norm.tolerance_collection import ToleranceCollection

tolerance_collection = ToleranceCollection.from_default_directory()


class TestNormModel(object):
    @pytest.mark.parametrize(
        "standard_name, table_name, row_id, value, tolerance",
        [
            ("ISO_2768", "Table 1.1", "f", 6, 0.05),
            ("ISO_2768", "Table 1.1", "f", 6.0001, 0.1),
            ("ISO_2768", "Table 1.1", "m", 50, 0.3),
            ("ISO_2768", "Table 1.1", "c", 2, 0.2),
            ("ISO_2768", "Table 1.1", "v", 3000, 8),
        ],
    )
    def test_table_range_lookup(
        self, standard_name: str, table_name: str, row_id: str, value: float, tolerance: float | None
    ) -> None:
        standard = tolerance_collection.get_standard(standard_name)
        table = next(t for t in standard.tables if t.name == table_name)

        value_range = table.lookup_range((row_id,), value)

        assert value_range is not None
        assert value_range.tolerance.upper == tolerance
        assert value_range.tolerance.lower == -tolerance
