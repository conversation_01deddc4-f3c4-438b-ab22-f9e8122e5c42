import html
import re
import urllib.parse as urlparse
from typing import Dict, Protocol

import requests
from pydantic import BaseModel, Field

from qw_basic_keycloak.interface import CodeFlowAuthInit
from qw_basic_keycloak.openid.dtos.authorization import AuthenticationResponse


class CodeFlowAuthMock(Protocol):
    """
    Interfaces for mocks that implement/fake the authentication. This is only intended for testing.
    """

    def auth(self, auth_init: CodeFlowAuthInit, username: str, password: str) -> AuthenticationResponse:
        ...


class KeycloakAuthenticationCookies(BaseModel):
    auth_session_id: str = Field(alias="AUTH_SESSION_ID")
    auth_session_id_legacy: str = Field(alias="AUTH_SESSION_ID_LEGACY")
    kc_restart: str = Field(alias="KC_RESTART")


class KeycloakSessionCookies(BaseModel):
    keycloak_identity: str = Field(alias="KEYCLOAK_IDENTITY")
    keycloak_identity_legacy: str = Field(alias="KEYCLOAK_IDENTITY_LEGACY")
    keycloak_session: str = Field(alias="KEYCLOAK_SESSION")
    keycloak_session_legacy: str = Field(alias="KEYCLOAK_SESSION_LEGACY")


class KeycloakAuthenticationResponse(AuthenticationResponse):
    session_state: str

    @classmethod
    def parse_from_redirect_url(cls, url: str) -> "KeycloakAuthenticationResponse":
        query_string = urlparse.urlparse(url).query
        query_data = urlparse.parse_qs(query_string)
        data = {k: values[0] for k, values in query_data.items()}
        return cls(**data)


class KeycloakAuthenticationMock(object):
    """
    This is a class intended for development purposes,
    in production this part of the flow will be performed by the user
    """

    def auth(self, init_data: CodeFlowAuthInit, username: str, password: str) -> KeycloakAuthenticationResponse:
        auth_website = requests.post(str(init_data.auth_url), data=init_data.req.model_dump())
        html_doc = auth_website.text
        m = re.search(r"<form.*id=\"kc-form-login\".*action=\"(?P<URL>.*)\" method=\"post\">", html_doc)
        if m is None:
            raise ValueError("Cannot find login action in html")
        auth_url = html.unescape(m.group("URL"))
        auth_cookies = KeycloakAuthenticationCookies(**auth_website.cookies.get_dict())

        form_params: Dict[str, str] = {"username": username, "password": password, "credentialId": ""}
        auth_cookies_dict = auth_cookies.model_dump(by_alias=True)
        resp_redirect = requests.post(auth_url, data=form_params, cookies=auth_cookies_dict, allow_redirects=False)
        if resp_redirect.status_code != 302:
            raise RuntimeError(f"Expected redirect, but got status {resp_redirect.status_code}")

        # session_cookies = KeycloakSessionCookies(**resp_redirect.cookies.get_dict())  # type: ignore
        # print(session_cookies.model_dump_json(indent=2))
        return KeycloakAuthenticationResponse.parse_from_redirect_url(resp_redirect.headers["location"])
