"""This service is responsible for finding symbol boxes."""

import time
from collections import defaultdict
from typing import Dict, List

from qw_drawing_toolkit.pdf.models.analysis_models import SymbolBox, TextBox
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysisContext
from qw_drawing_toolkit.pdf.parser.symbol_mapper import Symbol, SymbolMapper
from qw_drawing_toolkit.pdf.utils.common_utils import is_box_in_box
from qw_drawing_toolkit.pdf.utils.symbol_utils import (
    calculate_average_radius,
    calculate_centroid,
    filter_circles_with_center_line,
    find_circles,
)
from qw_drawing_toolkit.pdf.utils.text_utils import is_degrees, is_diameter, is_plus_minus, is_symbol


class SymbolService:
    def __init__(self, debug: bool):
        self.debug = debug

    def find_symbol_text_boxes(
        self,
        ctx: DrawingAnalysisContext,
    ) -> tuple[List[TextBox], Dict[Symbol, List[SymbolBox]], List[TextBox | SymbolBox]]:
        """
        Find symbol text boxes.

        Args:
            ctx: Drawing analysis context
            text_boxes_measurements_no_symbol: Current list of measurement text boxes without symbols

        Returns:
            tuple: (
                updated_text_boxes_measurements_no_symbol,
                symbol_map,
                text_boxes_measurement_symbols
            )
        """
        start_time = time.time()
        converted: List[SymbolBox | TextBox] = []
        text_boxes = [box for box in ctx.text_boxes if box not in ctx.text_boxes_title_block]

        updated_text_boxes_measurements_no_symbol = ctx.text_boxes_measurements_no_symbol.copy()
        symbol_map: Dict[Symbol, List[SymbolBox]] = defaultdict(list)

        for text_box in text_boxes:
            if not is_symbol(text_box):
                continue

            if text_box in updated_text_boxes_measurements_no_symbol:
                updated_text_boxes_measurements_no_symbol.remove(text_box)

            text_box.is_symbol = True
            symbol = Symbol.UNKNOWN

            if is_degrees(text_box):
                symbol = Symbol.DEGREES
            if is_diameter(text_box):
                symbol = Symbol.DIAMETER
            if is_plus_minus(text_box):
                symbol = Symbol.PLUSMINUS

            if symbol == Symbol.UNKNOWN:
                symbol = SymbolMapper.map_font_cipher_to_symbol(text_box.text, text_box.font)

            symbol_box = SymbolBox(symbol, text_box.bbox)

            if is_degrees(text_box) or is_diameter(text_box) or is_plus_minus(text_box):
                symbol_box.text = (
                    text_box.text.replace("ø", Symbol.get_symbol_string_prefix() + str(Symbol.DIAMETER.value) + " ")
                    .replace("⌀", Symbol.get_symbol_string_prefix() + str(Symbol.DIAMETER.value) + " ")
                    .replace("∅", Symbol.get_symbol_string_prefix() + str(Symbol.DIAMETER.value) + " ")
                    .replace("⊘", Symbol.get_symbol_string_prefix() + str(Symbol.DIAMETER.value) + " ")
                    # the \u008E is used by some of spanflugs standard fonts to indicate a crookes diameter symbol.
                    # this could lead to issues further down the line, if other companies use the same unicode
                    # placeholder for somthing different. Unlikely but possible.
                    .replace("\u008E", Symbol.get_symbol_string_prefix() + str(Symbol.DIAMETER.value) + " ")
                    .replace("°", Symbol.get_symbol_string_prefix() + str(Symbol.DEGREES.value) + " ")
                    .replace("±", Symbol.get_symbol_string_prefix() + str(Symbol.PLUSMINUS.value) + " ")
                )

            if symbol == Symbol.UNKNOWN:
                symbol_box.text = text_box.text
                symbol_box.font = text_box.font

            symbol_box.direction = text_box.direction

            converted.append(symbol_box)

            symbol_map[symbol].append(symbol_box)

        if self.debug:
            print(f"TextService.find_symbol_text_boxes: {time.time() - start_time} seconds")

        return updated_text_boxes_measurements_no_symbol, symbol_map, converted

    def find_symbol_bounding_boxes(
        self, ctx: DrawingAnalysisContext
    ) -> tuple[Dict[Symbol, List[SymbolBox]], List[TextBox], List[TextBox | SymbolBox]]:
        """
        Find and extract symbol bounding boxes from drawing elements.

        This method identifies graphical symbols (like diameter symbols) that are
        drawn as geometric elements rather than text characters.

        Args:
            ctx: Drawing analysis context containing all drawing elements

        Returns:
            tuple: (
                symbol_map: Dictionary mapping symbol types to their bounding boxes,
                text_boxes_measurements_no_symbol: Updated list of measurement text boxes without symbols,
                text_boxes_measurement_symbols: List of identified symbol boxes
            )
        """
        start_time = time.time()

        if self.debug:
            print("DrawingAnalyzerPage._find_symbol_bounding_boxes: " + str(time.time() - start_time) + " Seconds")

        symbol_map, text_boxes_measurements_no_symbol, text_boxes_measurement_symbols = self._extract_diameter_symbols(
            ctx
        )
        return symbol_map, text_boxes_measurements_no_symbol, text_boxes_measurement_symbols

    def _extract_diameter_symbols(
        self, ctx: DrawingAnalysisContext
    ) -> tuple[Dict[Symbol, List[SymbolBox]], List[TextBox], List[TextBox | SymbolBox]]:
        """
        Extract diameter symbols from circles in the drawing.

        This method identifies circles that represent diameter symbols by:
        1. Finding all circles in the drawing
        2. Filtering out false positives (text boxes inside circles)
        3. Filtering circles to keep only those with center lines (diameter symbols)
        4. Creating symbol boxes for the identified diameter symbols

        Args:
            ctx: Drawing analysis context containing lines and text boxes

        Returns:
            tuple: (
                symbol_map: Dictionary mapping Symbol.DIAMETER to list of diameter symbol boxes,
                text_boxes_measurements_no_symbol: Updated list with false positives removed,
                text_boxes_measurement_symbols: Updated list with diameter symbols added
            )
        """
        symbol_map: Dict[Symbol, List[SymbolBox]] = defaultdict(list)
        text_boxes_measurements_no_symbol = ctx.text_boxes_measurements_no_symbol.copy()
        text_boxes_measurement_symbols = ctx.text_boxes_measurement_symbols.copy()

        if not ctx.line_collection:
            print("SymbolService._extract_diameter_symbols: Line collection is not set")
            return symbol_map, text_boxes_measurements_no_symbol, text_boxes_measurement_symbols

        circles = find_circles(ctx.lines_unfiltered, ctx.point_proximity_tolerance)
        for circle in circles:
            # TODO move elsewhere and improve (this just removes some false positives for horschs drawings)
            centroid = calculate_centroid(circle)
            radius = calculate_average_radius(circle, centroid)
            box_size = 2 * radius
            x_1 = centroid[0] - box_size
            x_2 = centroid[0] + box_size
            y_1 = centroid[1] - box_size
            y_2 = centroid[1] + box_size

            min_x = min(x_1, x_2)
            max_x = max(x_1, x_2)
            min_y = min(y_1, y_2)
            max_y = max(y_1, y_2)

            count = 0
            false_positive = None
            for box in text_boxes_measurements_no_symbol:
                if is_box_in_box(box.bbox, (min_x, min_y, max_x, max_y), ctx.point_proximity_tolerance):
                    false_positive = box
                    count += 1
            if count == 1 and false_positive is not None:
                text_boxes_measurements_no_symbol.remove(false_positive)

        circles = filter_circles_with_center_line(
            circles, ctx.lines, ctx.point_proximity_tolerance, ctx.line_collection
        )

        for circle in circles:
            centroid = calculate_centroid(circle)
            radius = calculate_average_radius(circle, centroid)
            box_size = 2 * radius
            x_1 = centroid[0] - box_size
            x_2 = centroid[0] + box_size
            y_1 = centroid[1] - box_size
            y_2 = centroid[1] + box_size

            min_x = min(x_1, x_2)
            max_x = max(x_1, x_2)
            min_y = min(y_1, y_2)
            max_y = max(y_1, y_2)
            symbol_box = SymbolBox(Symbol.DIAMETER, (min_x, min_y, max_x, max_y))
            symbol_map[Symbol.DIAMETER].append(symbol_box)
            text_boxes_measurement_symbols.append(symbol_box)

        return symbol_map, text_boxes_measurements_no_symbol, text_boxes_measurement_symbols
