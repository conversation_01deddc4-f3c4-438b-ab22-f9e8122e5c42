import argparse
import os
from pathlib import Path
from typing import Any, Dict, List, Sequence, Type

import falcon
from gunicorn.app.base import BaseApplication  # type: ignore
from gunicorn.config import AccessLog, Bind, Config, Reload, Setting, Timeout, Workers  # type: ignore


class QwMonoWSGIApp(BaseApplication):  # type: ignore
    def __init__(self, qw_mono_config: Path, qw_mono_overwrite_config: Path | None, gunicorn_settings: Dict[str, Any]):
        self.qw_mono_config = qw_mono_config
        self.qw_mono_overwrite_config = qw_mono_overwrite_config
        self.gunicorn_settings = gunicorn_settings
        super().__init__()

    def init(self, parser, opts, args) -> None:  # type: ignore
        pass

    def load(self) -> falcon.App:
        # imports (and "descendant imports") that happen outside `load` will not be reloaded properly for --reload
        # https://github.com/benoitc/gunicorn/issues/1562#issuecomment-365522064
        from qw_config.loader import load_config
        from qw_log.factory import QwLogFactory
        from qw_mono.app import QwAppInfo, QwMonoApp, SimpleAppContext
        from qw_mono.config import QwMonoConfig

        # Main app needs to be properly initialized with a broker URL to connect
        # to RabbitMQ. We also need to that before any other code that might use
        # "current_app" from celery is executed. That's why this needs to be done
        # before any module imports Celery tasks.
        from qw_worker.module import QwWorkerModule

        QwWorkerModule.from_config(self.qw_mono_config, self.qw_mono_overwrite_config)

        app_info = QwAppInfo(
            name="qw-mono",
            version=os.environ.get("BUILD_VERSION", "0.0.0.0"),
            commit=os.environ.get("BUILD_COMMIT", "0" * 40),
        )
        cfg = load_config(QwMonoConfig, self.qw_mono_config, self.qw_mono_overwrite_config)
        log_factory = QwLogFactory(app_info.version)
        log_factory.init_logs(cfg.logging)
        ctx = SimpleAppContext(log_factory=log_factory, config=cfg, info=app_info)
        return QwMonoApp.from_context(ctx).build()

    def load_config(self) -> None:
        cfg: Config = self.cfg
        for k, v in self.gunicorn_settings.items():
            cfg.set(k, v)


def entrypoint(cli_input: Sequence[str] | None = None) -> None:
    parser = argparse.ArgumentParser("qw-mono-app")

    parser.add_argument("--qw-mono-config", metavar="FILE", type=Path, required=True)
    parser.add_argument("--qw-mono-overwrite-config", metavar="FILE", type=Path, default=None)

    gunicorn_settings: List[Type[Setting]] = [AccessLog, Bind, Workers, Reload, Timeout]
    for setting_type in gunicorn_settings:
        setting_type().add_option(parser)

    args = parser.parse_args(cli_input)
    gunicorn_args = {}
    for setting in gunicorn_settings:
        value = getattr(args, setting.name)
        if value is not None:
            gunicorn_args[setting.name] = value

    QwMonoWSGIApp(args.qw_mono_config, args.qw_mono_overwrite_config, gunicorn_args).run()


# # debug
# if __name__ == "__main__":
#     entrypoint(["--qw-mono-config", "/home/<USER>/code/qualiwise/qw-mono/dev_data/app.yaml"])
