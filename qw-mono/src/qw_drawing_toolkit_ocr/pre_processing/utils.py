from pathlib import Path

import cv2
import numpy as np
import numpy.typing as npt

from qw_drawing_toolkit_ocr.pre_processing.models import DocumentText
from qw_log_interface import NO_LOGGER, <PERSON>gger


def visualize_pre_processed_results(
    image: npt.NDArray[np.uint8],
    document_text: DocumentText,
    output_dir: Path,
    logger: Logger = NO_LOGGER,
) -> None:
    """
    Visualize Google Vision API results with both text blocks and elements.
    Creates two visualization images: one for text blocks and one for text elements.

    Args:
        image: Original image
        document_text: DocumentText object with text blocks and elements
        output_dir: Directory to save visualization images
    """

    # Create a copy of the original image for blocks visualization
    blocks_viz = image.copy()

    # Draw rectangles for each text block
    for i, block in enumerate(document_text.text_blocks):
        try:
            # Get vertices from the bounding polygon
            vertices = block.bounding_poly.vertices

            # Extract coordinates
            points = []
            for vertex in vertices:
                if "x" in vertex and "y" in vertex:
                    points.append([vertex["x"], vertex["y"]])

            # Convert to numpy array
            pts = np.array(points, dtype=np.int32)

            # Check if we have valid points
            if pts.size > 0:
                # Draw filled polygon with transparency
                overlay = blocks_viz.copy()
                cv2.fillPoly(overlay, [pts], (0, 255, 0))  # Green fill

                # Blend with original image
                alpha = 0.3
                cv2.addWeighted(overlay, alpha, blocks_viz, 1 - alpha, 0, blocks_viz)

                # Draw outline
                cv2.polylines(blocks_viz, [pts], True, (0, 255, 0), 2)  # Green outline

        except Exception as e:
            logger.warning(f"Error visualizing text block {i}: {str(e)}")

    # Save blocks visualization
    blocks_path = output_dir / "2_google_vision_blocks.png"
    cv2.imwrite(str(blocks_path), blocks_viz)

    # Create a copy of the original image for elements visualization
    elements_viz = image.copy()

    # Draw rectangles for each text element
    element_count = 0
    for block in document_text.text_blocks:
        for element in block.elements:
            try:
                # Get vertices from the bounding polygon
                vertices = element.bounding_poly.vertices

                # Extract coordinates
                points = []
                for vertex in vertices:
                    if "x" in vertex and "y" in vertex:
                        points.append([vertex["x"], vertex["y"]])

                # Convert to numpy array
                pts = np.array(points, dtype=np.int32)

                # Check if we have valid points
                if pts.size > 0:
                    # Draw filled polygon with transparency
                    overlay = elements_viz.copy()
                    cv2.fillPoly(overlay, [pts], (255, 0, 0))  # Red fill

                    # Blend with original image
                    alpha = 0.3
                    cv2.addWeighted(overlay, alpha, elements_viz, 1 - alpha, 0, elements_viz)

                    # Draw outline
                    cv2.polylines(elements_viz, [pts], True, (255, 0, 0), 1)  # Red outline

                element_count += 1

            except Exception as e:
                logger.warning(f"Error visualizing text element {element_count}: {str(e)}")

    # Save elements visualization
    elements_path = output_dir / "2_google_vision_elements.png"
    cv2.imwrite(str(elements_path), elements_viz)
