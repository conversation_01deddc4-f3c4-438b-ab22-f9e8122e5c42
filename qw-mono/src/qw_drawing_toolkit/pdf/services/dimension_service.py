"""This service is responsible for finding dimension boxes."""

import re
import time
from typing import List

from qw_drawing_toolkit.pdf.models.analysis_models import DimensionTextBox, DimensionType, RoughnessCategory
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysisContext
from qw_drawing_toolkit.pdf.parser.symbol_mapper import Symbol


class DimensionService:
    def __init__(self, debug: bool):
        self.debug = debug

    def assemble_dimension_boxes(self, ctx: DrawingAnalysisContext) -> List[DimensionTextBox]:
        """Assemble dimension boxes from the text boxes in the drawing.

        This method processes text boxes that contain measurement information and assembles them into
        DimensionTextBox objects. It handles various measurement formats including:
        - Diameters and countersinks
        - Tolerances (upper, lower, and combined)
        - Angles in degrees
        - Surface roughness measurements
        - Radii
        - Threads and pitches
        - Tolerance grades and fits
        - Basic lengths

        Args:
            ctx: The DrawingAnalysisContext containing the text boxes and analysis settings

        Returns:
            List[DimensionTextBox]: A list of assembled dimension boxes with parsed measurement information
        """
        # TODO add chamfer detection / mapping
        start_time = time.time()
        dimension_boxes: List[DimensionTextBox] = []
        # pattern_upper_tolerance = rf'ctx.QW_UT_PREFIX}\([+-]?\d+[,.]?\d*\)
        # (\s+{Symbol.DEGREES.get_string_representation()})?'
        pattern_equals = r".*="
        pattern_upper_tolerance = rf"{ctx.QW_UT_PREFIX}\([+-]?\d+[,.]?\d*\)"
        pattern_lower_tolerance = rf"{ctx.QW_LT_PREFIX}\([+-]?\d+[,.]?\d*\)"
        pattern_combined_tolerance = Symbol.PLUSMINUS.get_string_representation() + r"\s*\d+[,.]?\d*"
        pattern_diameter_counter_sink = (
            Symbol.DIAMETER.get_string_representation()
            + r"\s*\d+[,.]?\d*\s*[Xx]\s*\d+[,.]?\d*\s*"
            + Symbol.DEGREES.get_string_representation()
        )
        pattern_thru = r"\s*THRU(\s*ALL)?"
        pattern_diameter = Symbol.DIAMETER.get_string_representation() + r"\s*\d+[,.]?\d*"
        pattern_diameter_reversed = r"\d+[,.]?\d*\s*" + Symbol.DIAMETER.get_string_representation()
        pattern_multiplier = r"\d+[,.]?\d*\s*[Xx]"
        pattern_degrees = r"\s*\d+[,.]?\d*\s*" + Symbol.DEGREES.get_string_representation()
        pattern_roughness = r"\s*([Rr][ayz]|[Ss][m]?|tp)\s*(\d+[,.]?\d*)\s*"
        pattern_radius = r"\s*[Rr]\s*\d+[,.]?\d*\s*"
        pattern_thread = r"M\s*[0-9]{1,2}\s*x?\s*[0-9]*\.?[0-9]*"
        pattern_pitch = r"x\s*([0-9]*\.?[0-9]+)"
        pattern_tolerance_grade = r"[0-9]{1,2}\s*[a-zA-Z]{1,2}"
        pattern_fit = r"[a-zA-Z]{1,2}[0-9]{1,2}"
        pattern_length = r"\d+[,.]?\d*"

        # this pattern fixes a peculiar notation, where the explicit tolerance is just written with a + OR -
        # in without any positional change, so we will catch this and interpret it accordingly
        pattern_singular_explicit_tolerance_in_line = r"\b(\d+[,.]?\d*)\s*([+-])\s*(\d+[,.]?\d*)\b"

        for box in ctx.text_boxes_measurements_merged:
            dimension_box = DimensionTextBox(bounding_box=box.bbox)
            dimension_box.original_text = box.text
            remainder_of_text = re.sub(",", ".", box.text)

            only_ungrouped_symbols = True

            for split_string in re.split(r"\s+", remainder_of_text.strip()):
                if Symbol.from_string(split_string.strip()) == Symbol.UNKNOWN:
                    only_ungrouped_symbols = False
                    break

            if only_ungrouped_symbols:
                # self.dimension_boxes.append(dimension_box)
                continue

            stray_tolerance_base_string = remainder_of_text
            stray_tolerances = []

            for index in range(2):
                match = re.search(pattern_singular_explicit_tolerance_in_line, stray_tolerance_base_string)
                if match:
                    tolerance = match.group(2) + match.group(3)
                    replacer = re.escape(match.group(2)) + r"\s*" + re.escape(match.group(3))
                    stray_tolerances.append((float(tolerance), replacer))
                    stray_tolerance_base_string = re.sub(replacer, "", stray_tolerance_base_string)
            if len(stray_tolerances) > 0:
                stray_tolerances.sort(key=lambda x: x[0])

                if len(stray_tolerances) == 1:
                    tolerance_value, replacer = stray_tolerances[0]
                    prefix = ctx.QW_UT_PREFIX if tolerance_value >= 0 else ctx.QW_LT_PREFIX
                    replacements = [(prefix, tolerance_value, replacer)]
                elif len(stray_tolerances) == 2:
                    replacements = [
                        (ctx.QW_LT_PREFIX, stray_tolerances[0][0], stray_tolerances[0][1]),
                        (ctx.QW_UT_PREFIX, stray_tolerances[1][0], stray_tolerances[1][1]),
                    ]

                for prefix, value, replacer in replacements:
                    remainder_of_text = re.sub(replacer, f"{prefix}({value})", remainder_of_text)

            matches = re.findall(pattern_equals, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")

            matches = re.findall(pattern_upper_tolerance, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                #
                # if Symbol.DEGREES.get_string_representation() in match:
                #     match = match.replace(r"\s*" + Symbol.DEGREES.get_string_representation(), "")
                #     # this specific case is currently not needed as units are specified by main value

                match_text = match_text.replace(f"{ctx.QW_UT_PREFIX}(", "")
                match_text = match_text.replace(")", "")
                dimension_box.upper_tolerance = float(match_text)

            re.findall(pattern_diameter, box.text)

            matches = re.findall(pattern_lower_tolerance, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")

                match_text = match_text.replace(f"{ctx.QW_LT_PREFIX}(", "")
                match_text = match_text.replace(")", "")
                dimension_box.lower_tolerance = float(match_text)

            re.findall(pattern_diameter, box.text)

            matches = re.findall(pattern_combined_tolerance, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")

                match_text = re.sub(Symbol.PLUSMINUS.get_string_representation() + r"\s*", "", match_text)
                dimension_box.upper_tolerance = float(match_text)
                dimension_box.lower_tolerance = -1 * float(match_text)

            matches = re.findall(pattern_diameter_counter_sink, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                match_text = re.sub(Symbol.DIAMETER.get_string_representation() + r"\s*", "", match_text)
                match_text = re.sub(Symbol.DEGREES.get_string_representation() + r"\s*", "", match_text)
                split = re.split(r"[Xx]", match_text)
                dimension_box.diameter = float(split[0])
                dimension_box.angle = float(split[1])
                dimension_box.dimension_type = DimensionType.COUNTERSINK

            match = re.search(pattern_thru, remainder_of_text)
            if match:
                match_text = match.group()
                remainder_of_text = remainder_of_text.replace(match_text, "")

                if "ALL" in match_text:
                    dimension_box.thru_all = True

                dimension_box.depth = float("inf")
                dimension_box.thru = True

            matches = re.findall(pattern_diameter, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                match_text = re.sub(Symbol.DIAMETER.get_string_representation() + r"\s*", "", match_text)
                dimension_box.diameter = float(match_text)
                dimension_box.dimension_type = DimensionType.DIAMETER

            matches = re.findall(pattern_diameter_reversed, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                match_text = re.sub(Symbol.DIAMETER.get_string_representation() + r"\s*", "", match_text)
                dimension_box.diameter = float(match_text)
                dimension_box.dimension_type = DimensionType.DIAMETER

            matches = re.findall(pattern_thread, remainder_of_text)

            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                dimension_box.thread = re.sub(pattern_pitch, "", match_text)
                pitch_match = re.search(pattern_pitch, match_text)
                if pitch_match:
                    dimension_box.pitch = float(pitch_match.group(0).replace("x", ""))
                dimension_box.dimension_type = DimensionType.THREAD

            matches = re.findall(pattern_multiplier, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                match_text = re.sub(r"\s*[Xx]", "", match_text)
                dimension_box.multiplier = float(match_text)

            matches = re.findall(pattern_degrees, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                match_text = re.sub(r"\s*" + Symbol.DEGREES.get_string_representation(), "", match_text)
                dimension_box.angle = float(match_text)
                dimension_box.dimension_type = DimensionType.ANGLE

            match = re.search(pattern_roughness, remainder_of_text)
            if match:
                dimension_box.value = float(match.group(2))
                dimension_box.roughness_category = RoughnessCategory.from_string(match.group(1).lower())
                dimension_box.dimension_type = DimensionType.ROUGHNESS
                remainder_of_text = remainder_of_text.replace(match.group(0), "")

            matches = re.findall(pattern_radius, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                match_text = re.sub(r"[Rr]\s*", "", match_text)
                dimension_box.radius = float(match_text)
                dimension_box.dimension_type = DimensionType.RADIUS

            matches = re.findall(pattern_tolerance_grade, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                dimension_box.tolerance_grade = re.sub(r"\s*", "", match_text)

            matches = re.findall(pattern_fit, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                dimension_box.fit = str(match_text)

            matches = re.findall(pattern_length, remainder_of_text)
            if len(matches) == 1:
                match_text = str(matches[0])
                remainder_of_text = remainder_of_text.replace(match_text, "")
                dimension_box.length = float(match_text)
                if dimension_box.dimension_type == DimensionType.UNKNOWN:
                    dimension_box.dimension_type = DimensionType.LENGTH

            if len(remainder_of_text.strip()) > 0:
                dimension_box.unmatched_text = remainder_of_text

            # print(dimension_box.to_dict())
            dimension_boxes.append(dimension_box)

            if self.debug:
                if len(remainder_of_text.strip()) > 0:
                    print("---")
                    print("unmatched (" + str(ctx.page_index) + "): " + remainder_of_text)
                    print("original: " + dimension_box.original_text)

        if self.debug:
            print("DrawingAnalyzerPage._assemble_dimension_boxes: " + str(time.time() - start_time) + " Seconds")

        return dimension_boxes
