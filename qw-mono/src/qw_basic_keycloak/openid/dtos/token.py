from enum import Enum, unique
from typing import Any, Dict, Literal

from pydantic import AnyHttpUrl, BaseModel, Field


@unique
class GrantType(str, Enum):
    AUTHORIZATION_CODE = "authorization_code"
    REFRESH_TOKEN = "refresh_token"
    # this enum is not complete


class TokenRequest(BaseModel):
    """https://openid.net/specs/openid-connect-core-1_0.html#TokenRequest"""

    grant_type: GrantType
    code: str
    redirect_uri: AnyHttpUrl
    client_id: str
    client_secret: str | None = None


class JwtToken(BaseModel):
    """Simple container of the decoded token"""

    raw: str
    header: Dict[str, Any]
    claims: Dict[str, Any]
    signature_checked: bool


class TokenResponse(BaseModel):
    """https://openid.net/specs/openid-connect-core-1_0.html#TokenResponse"""

    token_type: Literal["Bearer"]
    access_token: str
    refresh_token: str
    id_token: str
    expires_in_seconds: int = Field(alias="expires_in")
    refresh_expires_in_seconds: int = Field(alias="refresh_expires_in")  # keycloak specific?

    # keycloak specific?
    session_state: str | None = None
    scope: str | None = None
    # "not-before-policy"


class RefreshTokenRequest(BaseModel):
    """https://openid.net/specs/openid-connect-core-1_0.html#RefreshingAccessToken"""

    grant_type: Literal[GrantType.REFRESH_TOKEN] = GrantType.REFRESH_TOKEN
    refresh_token: str
    client_id: str
    client_secret: str | None = None


class InitiateLogoutRequest(BaseModel):
    """https://openid.net/specs/openid-connect-rpinitiated-1_0.html"""

    id_token_hint: str
    client_id: str
    post_logout_redirect_uri: AnyHttpUrl | None
