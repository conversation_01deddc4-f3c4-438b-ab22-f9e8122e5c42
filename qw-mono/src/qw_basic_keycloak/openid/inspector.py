from datetime import datetime, timezone
from typing import Literal

from joserfc import jwk, jwt
from joserfc.errors import <PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel, ValidationError

from qw_basic_keycloak.joserfc_ext import decode_without_key
from qw_basic_keycloak.openid.dtos.token import Jwt<PERSON><PERSON>
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class ValidToken(BaseModel):
    src: JwtToken
    type: str
    issuer: str
    subject: str
    ts_start: datetime
    ts_end: datetime


class TokenInspector(object):
    """
    Utility class for backend apps that don't orchestrate user authentication, but authorize interaction based on
    access tokens and the claims that are stored within. Usually the access token is sent in the request header as
    ``Authorization: Bearer <token_base64>``.
    """

    def __init__(
        self,
        key_data: jwk.KeyFlexible,
        trusted_issuer: str | None,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.key_data = key_data
        self.trusted_idp = trusted_issuer
        self.logger = lf.get_logger(__name__)

    def __inspect_token(self, token: JwtToken) -> ValidToken:
        """
        Convert token into a more convenient structure for checking access rights.
        :param token: the decoded token
        :return: the converted token
        """
        ts_start = datetime.utcfromtimestamp(token.claims.get("nbf", token.claims["iat"])).replace(tzinfo=timezone.utc)
        ts_end = datetime.utcfromtimestamp(token.claims["exp"]).replace(tzinfo=timezone.utc)
        return ValidToken(
            src=token,
            type=token.claims["typ"],
            issuer=str(token.claims["iss"]).rstrip("/"),
            subject=token.claims["sub"],
            ts_start=ts_start,
            ts_end=ts_end,
        )

    def validate_token(
        self,
        token: str | bytes,
        expected_type: Literal["ID", "Bearer", "Refresh"] = "Bearer",
        check_signature: bool = True,
    ) -> ValidToken | None:
        """
        Any incoming token must be validated with this method. Requests where the signature of the token
        cannot be validated must be denied! Otherwise, attackers could manufacture any token and
        gain access without even stealing the private key used for signing by the identity provider.
        Additional checks (e.g. not expired, correct issuer) are performed.
        :param token: the encoded token
        :param expected_type: expected type of JWT
        :param check_signature: should the signature be validated (available for unknown symmetric keys,
        e.g. we don't know the symmetric key that keycloak or another idp uses internally for the refresh token)
        :return: the decoded token *if* the signature was valid and all other checks passed
        """
        try:
            if check_signature:
                tkn_decoded = jwt.decode(token, self.key_data)
            else:
                tkn_decoded = decode_without_key(token)
        except JoseError:
            self.logger.error(f"JWT could not be decoded (check_signature={check_signature})")
            return None

        tkn = JwtToken(
            raw=token if isinstance(token, str) else token.decode("utf-8"),
            header=tkn_decoded.header,
            claims=tkn_decoded.claims,
            signature_checked=check_signature,
        )

        try:
            inspected_tkn = self.__inspect_token(tkn)
        except (KeyError, ValidationError) as e:
            self.logger.error("JWT could not be inspected", exc_info=e)
            return None

        if inspected_tkn.issuer != self.trusted_idp:
            self.logger.error(f"JWT was issued by untrusted party: {inspected_tkn.issuer}")
            return None

        if inspected_tkn.type != expected_type:
            self.logger.error(f"JWT has unexpected type '{inspected_tkn.type}', expected '{expected_type}'")
            return None

        ts_now = datetime.now(timezone.utc)
        if ts_now < inspected_tkn.ts_start:
            td = inspected_tkn.ts_start - ts_now
            self.logger.error(f"JWT only valid from {inspected_tkn.ts_start} (that is in {td})")
            return None

        if ts_now > inspected_tkn.ts_end:
            td = ts_now - inspected_tkn.ts_end
            self.logger.error(f"JWT expired {inspected_tkn.ts_end} (that was {td} ago)")
            return None

        return inspected_tkn
