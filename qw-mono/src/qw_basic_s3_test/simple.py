import io
from dataclasses import dataclass
from pathlib import PurePosixPath
from typing import List, Literal

import pytest

from qw_basic_s3.interface import S3Storage


@dataclass
class ListObjsScenario:
    path: str
    trailing_slash: Literal["", "/", "auto"]
    recursive: bool
    exp_paths: List[str]


class TestS3Storage(object):
    def test_simple_usecase(self, s3: S3Storage) -> None:
        bucket_name = "bucket"
        path = PurePosixPath("my/test/obj.txt")
        content = "test!"
        content_raw = content.encode("utf-8")

        s3.create_bucket(bucket_name)

        # size or part size needs to be specified
        with pytest.raises(ValueError):
            s3.put_object(bucket_name, path, io.BytesIO(content_raw), content_type="text/plain")
        s3.put_object(bucket_name, path, io.BytesIO(content_raw), size=len(content_raw), content_type="text/plain")

        objs = list(s3.list_objects(bucket_name, path.parent, recursive=False))

        assert len(objs) == 1
        assert objs[0].path == path

        obj = s3.get_object(bucket_name, objs[0].path)
        content2 = obj.read().decode("utf-8")
        assert content == content2

    def test_list_objects(self, s3: S3Storage) -> None:
        bucket_name = "bucket-list-test"
        content_raw = "test".encode("utf-8")
        path1 = PurePosixPath("my/test/obj.txt")
        path2 = PurePosixPath("my/test2/obj.txt")
        path3 = PurePosixPath("my/obj_without_extension")
        path4 = PurePosixPath("my/folder.ext/obj.txt")
        path5 = PurePosixPath("my/text.txt")
        all_paths = (path1, path2, path3, path4, path5)

        s3.create_bucket(bucket_name)
        for p in all_paths:
            s3.put_object(bucket_name, p, io.BytesIO(content_raw), size=len(content_raw), content_type="text/plain")

        all_paths_str = [str(p) for p in all_paths]
        my_path_str = ["my"]
        my_children_str = ["/".join(p.parts[:2]) for p in all_paths]
        my_te_str1 = [str(p) for p in (path1, path2, path5)]
        my_te_str2 = [str(p) for p in (path1.parent, path2.parent, path5)]

        scenarios: List[ListObjsScenario] = [
            ListObjsScenario(path="", trailing_slash="/", recursive=False, exp_paths=[]),
            ListObjsScenario(path="", trailing_slash="/", recursive=True, exp_paths=[]),
            ListObjsScenario(path="", trailing_slash="", recursive=False, exp_paths=my_path_str),
            ListObjsScenario(path="", trailing_slash="", recursive=True, exp_paths=all_paths_str),
            # "m" recursive
            ListObjsScenario(path="m", trailing_slash="", recursive=True, exp_paths=all_paths_str),
            ListObjsScenario(path="m", trailing_slash="auto", recursive=True, exp_paths=[]),
            ListObjsScenario(path="m", trailing_slash="/", recursive=True, exp_paths=[]),
            # "m" not recursive
            ListObjsScenario(path="m", trailing_slash="", recursive=False, exp_paths=my_path_str),
            ListObjsScenario(path="m", trailing_slash="auto", recursive=False, exp_paths=[]),
            ListObjsScenario(path="m", trailing_slash="/", recursive=False, exp_paths=[]),
            # "my" recursive
            ListObjsScenario(path="my", trailing_slash="", recursive=True, exp_paths=all_paths_str),
            ListObjsScenario(path="my", trailing_slash="auto", recursive=True, exp_paths=all_paths_str),
            ListObjsScenario(path="my", trailing_slash="/", recursive=True, exp_paths=all_paths_str),
            # "my" not recursive
            ListObjsScenario(path="my", trailing_slash="", recursive=False, exp_paths=my_path_str),
            ListObjsScenario(path="my", trailing_slash="auto", recursive=False, exp_paths=my_children_str),
            ListObjsScenario(path="my", trailing_slash="/", recursive=False, exp_paths=my_children_str),
            # "my/te" recursive
            ListObjsScenario(path="my/te", trailing_slash="", recursive=True, exp_paths=my_te_str1),
            ListObjsScenario(path="my/te", trailing_slash="auto", recursive=True, exp_paths=[]),
            ListObjsScenario(path="my/te", trailing_slash="/", recursive=True, exp_paths=[]),
            # "my/te" not recursive
            ListObjsScenario(path="my/te", trailing_slash="", recursive=False, exp_paths=my_te_str2),
            ListObjsScenario(path="my/te", trailing_slash="auto", recursive=False, exp_paths=[]),
            ListObjsScenario(path="my/te", trailing_slash="/", recursive=False, exp_paths=[]),
        ]

        for i, scenario in enumerate(scenarios):
            objs = s3.list_objects(
                bucket_name,
                PurePosixPath(scenario.path),
                trailing_slash=scenario.trailing_slash,
                recursive=scenario.recursive,
            )
            act_paths = set(str(obj.path) for obj in objs)
            assert act_paths == set(scenario.exp_paths), f"Scenario {i} failed ({scenario})"

    def test_double_dots_are_not_allowed(self, s3: S3Storage) -> None:
        data = b"test"
        size = len(data)

        bucket_name = "bucket"
        s3.create_bucket(bucket_name)
        s3.put_object(bucket_name, PurePosixPath("my/test/obj.txt"), io.BytesIO(data), size)
        s3.put_object(
            bucket_name, PurePosixPath("obj/with/more/than/2/dots/...../somewhere.txt"), io.BytesIO(data), size
        )

        with pytest.raises(ValueError):
            s3.put_object(bucket_name, PurePosixPath("my/test/../obj.txt"), io.BytesIO(data), size)

        with pytest.raises(ValueError):
            s3.get_object(bucket_name, PurePosixPath("my/test/xyz/../obj.txt"))

        with pytest.raises(ValueError):
            s3.remove_object(bucket_name, PurePosixPath("my/test/xyz/../obj.txt"))

        with pytest.raises(ValueError):
            obj_gen = s3.list_objects(bucket_name, PurePosixPath("my/test/xyz/.."))
            next(obj_gen)

    def test_bucket_operations(self, s3: S3Storage) -> None:
        names = ["test1", "test2", "test3"]
        for name in names:
            assert not s3.bucket_exists(name)
        for name in names:
            s3.create_bucket(name)
        for name in names:
            assert s3.bucket_exists(name)
        assert set(s3.list_buckets()) == set(names)

        name_remove = names[1]
        s3.remove_bucket(name_remove)
        assert not s3.bucket_exists(name_remove)
