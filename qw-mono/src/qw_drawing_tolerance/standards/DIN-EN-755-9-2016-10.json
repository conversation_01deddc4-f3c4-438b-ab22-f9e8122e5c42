{"name": "DIN_EN_755_9:2016-10", "tables": [{"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["CD <= 100"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["CD <= 100"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["CD <= 100"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["CD <= 100"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 2.1, "lower": -2.1}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 450, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 450, "inclusive": false}, "upperValueLimit": {"value": 600, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 3.8, "lower": -3.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 450, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 450, "inclusive": false}, "upperValueLimit": {"value": 600, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 4.2, "lower": -4.2}}, {"lowerValueLimit": {"value": 600, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 5, "lower": -5}}]}, {"name": "Tabelle 3", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["CD <= 100"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["CD <= 100"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["CD <= 100"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["CD <= 100"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["100 < CD <= 200"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["200 < CD <= 300"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 450, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 450, "inclusive": false}, "upperValueLimit": {"value": 600, "inclusive": true}, "rowIdentifiers": [["300 < CD <= 500"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 450, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 3.8, "lower": -3.8}}, {"lowerValueLimit": {"value": 450, "inclusive": false}, "upperValueLimit": {"value": 600, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 600, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["500 < CD <= 800"]], "tolerance": {"upper": 6, "lower": -6}}]}, {"name": "Tabelle 5", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 2.5, "lower": -2.5}}]}, {"name": "Tabelle 6", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["A 300 < CD <= 500"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A 300 < CD <= 500"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A 300 < CD <= 500"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A 300 < CD <= 500"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A 300 < CD <= 500"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A 300 < CD <= 500"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A 300 < CD <= 500"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A 300 < CD <= 500"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["A 300 < CD <= 500"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A 500 < CD <= 800 "]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A 500 < CD <= 800 "]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A 500 < CD <= 800 "]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A 500 < CD <= 800 "]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A 500 < CD <= 800 "]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A 500 < CD <= 800 "]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A 500 < CD <= 800 "]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["A 500 < CD <= 800 "]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B 300 < CD <= 500"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B 300 < CD <= 500"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B 300 < CD <= 500"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B 300 < CD <= 500"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B 300 < CD <= 500"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["B 300 < CD <= 500"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["B 300 < CD <= 500"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B 500 < CD <= 800"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B 500 < CD <= 800"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B 500 < CD <= 800"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B 500 < CD <= 800"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B 500 < CD <= 800"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["B 500 < CD <= 800"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["B 500 < CD <= 800"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C 300 < CD <= 500"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C 300 < CD <= 500"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C 300 < CD <= 500"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C 300 < CD <= 500"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C 300 < CD <= 500"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["C 300 < CD <= 500"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["C 300 < CD <= 500"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C 500 < CD <= 800"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C 500 < CD <= 800"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C 500 < CD <= 800"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C 500 < CD <= 800"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C 500 < CD <= 800"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["C 500 < CD <= 800"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["C 500 < CD <= 800"]], "tolerance": {"upper": 3.3, "lower": -3.3}}]}, {"name": "Tabelle 7", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A CD <= 100"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["A 100 < CD <= 300"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["B CD <= 100"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["B 100 < CD <= 300"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["C CD <= 100"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["C 100 < CD <= 300"]], "tolerance": {"upper": 2.7, "lower": -2.7}}]}, {"name": "Tabelle 8", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 1.5, "inclusive": true}, "rowIdentifiers": [["A CD <= 500"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 1.5, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A CD <= 500"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A CD <= 500"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A CD <= 500"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A CD <= 500"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A CD <= 500"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A CD <= 500"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A CD <= 500"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["A CD <= 500"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["A CD <= 800"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["A CD <= 800"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["A CD <= 800"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["A CD <= 800"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["A CD <= 800"]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["A CD <= 800"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["A CD <= 800"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["A CD <= 800"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B CD <= 500"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B CD <= 500"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B CD <= 500"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B CD <= 500"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B CD <= 500"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["B CD <= 500"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["B CD <= 500"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["B CD <= 800"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["B CD <= 800"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["B CD <= 800"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["B CD <= 800"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["B CD <= 800"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["B CD <= 800"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["B CD <= 800"]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C CD <= 500"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C CD <= 500"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C CD <= 500"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C CD <= 500"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C CD <= 500"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["C CD <= 500"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 1.5, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["C CD <= 800"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["C CD <= 800"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["C CD <= 800"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["C CD <= 800"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["C CD <= 800"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["C CD <= 800"]], "tolerance": {"upper": 3.7, "lower": -3.7}}]}, {"name": "Tabelle 9", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L <= 2 000"]], "tolerance": {"upper": 5, "lower": 0}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L <= 2 000"]], "tolerance": {"upper": 7, "lower": 0}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 450, "inclusive": true}, "rowIdentifiers": [["L <= 2 000"]], "tolerance": {"upper": 8, "lower": 0}}, {"lowerValueLimit": {"value": 450, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["L <= 2 000"]], "tolerance": {"upper": 9, "lower": 0}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["2 000 < L <= 5 000"]], "tolerance": {"upper": 7, "lower": 0}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["2 000 < L <= 5 000"]], "tolerance": {"upper": 9, "lower": 0}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 450, "inclusive": true}, "rowIdentifiers": [["2 000 < L <= 5 000"]], "tolerance": {"upper": 11, "lower": 0}}, {"lowerValueLimit": {"value": 450, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["2 000 < L <= 5 000"]], "tolerance": {"upper": 14, "lower": 0}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["5 000 < L <= 10 000"]], "tolerance": {"upper": 10, "lower": 0}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["5 000 < L <= 10 000"]], "tolerance": {"upper": 12, "lower": 0}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 450, "inclusive": true}, "rowIdentifiers": [["5 000 < L <= 10 000"]], "tolerance": {"upper": 14, "lower": 0}}, {"lowerValueLimit": {"value": 450, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["5 000 < L <= 10 000"]], "tolerance": {"upper": 16, "lower": 0}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["10 000 < L <= 15 000"]], "tolerance": {"upper": 16, "lower": 0}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["10 000 < L <= 15 000"]], "tolerance": {"upper": 18, "lower": 0}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 450, "inclusive": true}, "rowIdentifiers": [["10 000 < L <= 15 000"]], "tolerance": {"upper": 20, "lower": 0}}, {"lowerValueLimit": {"value": 450, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["10 000 < L <= 15 000"]], "tolerance": {"upper": 22, "lower": 0}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["15 000 < L <= 25 000"]], "tolerance": {"upper": 22, "lower": 0}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["15 000 < L <= 25 000"]], "tolerance": {"upper": 24, "lower": 0}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 450, "inclusive": true}, "rowIdentifiers": [["15 000 < L <= 25 000"]], "tolerance": {"upper": 28, "lower": 0}}, {"lowerValueLimit": {"value": 450, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["15 000 < L <= 25 000"]], "tolerance": {"upper": 30, "lower": 0}}]}]}