from typing import Dict, List, Sequence, Tuple

from qw_drawing_toolkit.norm.model import (
    Tolerance,
    ToleranceStandard,
    ToleranceUnit,
    ToleranceValueType,
    ValueToToleranceRange,
    ValueToToleranceTable,
)
from qw_drawing_toolkit.pdf.models.base_models import DrawingUnit
from qw_log_interface import NO_LOGGER, Logger

TO_DRAWING_UNIT: Dict[ToleranceUnit, DrawingUnit] = {
    ToleranceUnit.MILLIMETER: DrawingUnit.MILLIMETER,
    ToleranceUnit.MICROMETER: DrawingUnit.MICROMETER,
    ToleranceUnit.DEGREE: DrawingUnit.DEGREE,
}

FROM_DRAWING_UNIT: Dict[DrawingUnit, ToleranceUnit] = {unit: du for (du, unit) in TO_DRAWING_UNIT.items()}

CONVERSION_MAP: Dict[ToleranceUnit, Dict[ToleranceUnit, float]] = {
    ToleranceUnit.MILLIMETER: {ToleranceUnit.MICROMETER: 1000},
    ToleranceUnit.MICROMETER: {ToleranceUnit.MILLIMETER: 0.001},
}


class ToleranceQueryResult(Tolerance):
    unit: ToleranceUnit


class ToleranceQuery(object):
    def __init__(
        self,
        row_identifier: Tuple[str, ...],
        value_type: ToleranceValueType,
        value_in: float,
        unit_in: ToleranceUnit,
        unit_out: ToleranceUnit | None = None,
        in_table_names: Sequence[str] | None = None,
        logger: Logger = NO_LOGGER,
    ):
        self.unit_in = unit_in
        self.unit_out = unit_out

        self.row_identifier = row_identifier
        self.value_in = value_in
        self.value_type = value_type
        self.in_table_names = in_table_names
        self.logger = logger

    @classmethod
    def convert_value(cls, value: float, unit_in: ToleranceUnit, unit_out: ToleranceUnit) -> float | None:
        if unit_in == unit_out:
            return value
        factor = CONVERSION_MAP.get(unit_in, {}).get(unit_out)
        if factor is None:
            return None
        return value * factor

    @classmethod
    def can_be_converted(cls, unit_in: ToleranceUnit, unit_out: ToleranceUnit) -> bool:
        return unit_in == unit_out or CONVERSION_MAP.get(unit_in, {}).get(unit_out) is not None

    def get_value_as(self, unit: ToleranceUnit) -> float | None:
        return self.convert_value(self.value_in, self.unit_in, unit)

    def find_tables(self, standard: ToleranceStandard) -> List[ValueToToleranceTable]:
        tables = []
        for t in standard.tables:
            if t.value_type != self.value_type:
                continue
            if self.in_table_names is not None and t.name not in self.in_table_names:
                continue
            tables.append(t)
        return tables

    def find_range_in(self, standard: ToleranceStandard) -> Tuple[ValueToToleranceTable, ValueToToleranceRange] | None:
        tables = self.find_tables(standard)
        if len(tables) > 1:
            self.logger.error(f"Found {len(tables)} tables from standard {standard.name}")

        for t in tables:
            # check input/output compatibility
            converted_value = self.get_value_as(t.value_unit)
            output_is_ok = self.unit_out is None or self.can_be_converted(t.tolerance_unit, self.unit_out)
            if converted_value is None or not output_is_ok:
                continue

            tol_range = t.lookup_range(row_identifier=self.row_identifier, value=converted_value, logger=self.logger)
            if tol_range is not None:
                return t, tol_range

        return None

    def find_in(self, standard: ToleranceStandard) -> ToleranceQueryResult | None:
        result = self.find_range_in(standard)
        if result is None:
            return None

        table, tol_range = result
        upper = tol_range.tolerance.upper
        lower = tol_range.tolerance.lower
        act_unit = table.tolerance_unit
        exp_unit = act_unit if self.unit_out is None else self.unit_out
        lower = self.convert_value(lower, act_unit, exp_unit)  # type: ignore
        upper = self.convert_value(upper, act_unit, exp_unit)  # type: ignore
        return ToleranceQueryResult(upper=upper, lower=lower, unit=exp_unit)
