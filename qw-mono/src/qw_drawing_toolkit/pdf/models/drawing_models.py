from collections import defaultdict
from enum import Enum, unique
from pathlib import Path
from typing import Annotated, Any, Dict, List, Literal, Set, TextIO, Union

import pymupdf
import yaml
from pydantic import Field, model_validator
from typing_extensions import Self

from qw_drawing_toolkit.pdf.models.analysis_models import Da<PERSON><PERSON>ox, DimensionTextBox, SymbolBox, TextBox
from qw_drawing_toolkit.pdf.models.base_models import (
    T_BBOX,
    T_LINE_SEGMENT,
    DrawingAnalysisBaseModel,
    DrawingAnnotationType,
    DrawingBoundingBox,
    DrawingUnit,
    Float,
)
from qw_drawing_toolkit.pdf.models.gdt_models import (
    DrawingGdtDatumAnnotation,
    DrawingGdtFrameAnnotation,
    GdtBox,
    GdtDatumReferenceBox,
)
from qw_drawing_toolkit.pdf.parser.geometry import LineCollection
from qw_drawing_toolkit.pdf.parser.symbol_mapper import Symbol

# |------------------------------|
# |          Enums               |
# |------------------------------|


@unique
class DrawingNorm(str, Enum):
    ISO_261 = "ISO_261"
    ISO_286 = "ISO_286"
    ISO_2768 = "ISO_2768"
    DIN_16742_2013_10 = "DIN_16742:2013-10"

    @classmethod
    def from_str(cls, value: str) -> "DrawingNorm":
        try:
            return cls(value)
        except ValueError:
            raise ValueError(f"'{value}' is not a valid DrawingNorm")


@unique
class DrawingReportStatus(str, Enum):
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"
    UNSUPPORTED = "UNSUPPORTED"


# |------------------------------|
# |          Models              |
# |------------------------------|


class DrawingPage(DrawingAnalysisBaseModel):
    width_mm: Float
    height_mm: Float


class DrawingBaseTolerance(DrawingAnalysisBaseModel):
    upper: Float  # there are cases where the upper tolerance may be less than 0 (e.g. for a tight fit)
    lower: Float

    @model_validator(mode="after")
    def ensure_that_upper_is_larger_than_lower(self) -> Self:
        if self.upper < self.lower:
            raise ValueError(f"Upper needs to be larger than lower tolerance, {self.upper} < {self.lower}")
        return self


class DrawingExplicitTolerance(DrawingBaseTolerance):
    """A tolerance that is denoted directly at the value in the drawing"""

    tolerance_type: Literal["explicit"] = "explicit"


class DrawingExplicitIso286FitTolerance(DrawingBaseTolerance):
    """A tolerance that is denoted directly at the value in the drawing in a format dedicated for fits, e.g. H7"""

    tolerance_type: Literal["explicit_iso286_fit"] = "explicit_iso286_fit"
    identifier: str = Field(pattern="^[a-zA-Z]{1,2}[0-9]{1,2}$")


class DrawingDefaultTolerance(DrawingBaseTolerance):
    """A tolerance that was looked up from the relevant default tolerance table"""

    tolerance_type: Literal["default"] = "default"
    source: DrawingNorm = DrawingNorm.ISO_2768


class DrawingLengthValue(DrawingAnalysisBaseModel):
    value: Float  # TODO: use decimal library?
    unit: Literal[DrawingUnit.MILLIMETER, DrawingUnit.MICROMETER] = DrawingUnit.MILLIMETER
    explicit_tolerance: DrawingExplicitTolerance | DrawingExplicitIso286FitTolerance | None = None
    default_tolerance: DrawingDefaultTolerance | None = None


class DrawingAngularValue(DrawingAnalysisBaseModel):
    value: Float
    unit: Literal[DrawingUnit.DEGREE] = DrawingUnit.DEGREE
    explicit_tolerance: DrawingExplicitTolerance | None = None
    default_tolerance: DrawingDefaultTolerance | None = None


class DrawingRoughnessCategory(DrawingAnalysisBaseModel):
    identifier: str = Field(pattern="^[Rr][ayz]|[Ss][m]?|tp$")


class DrawingLengthAnnotation(DrawingAnalysisBaseModel):
    annotation_type: Literal[DrawingAnnotationType.LENGTH] = DrawingAnnotationType.LENGTH
    bounding_box: DrawingBoundingBox
    length: DrawingLengthValue


class DrawingRadiusAnnotation(DrawingAnalysisBaseModel):
    annotation_type: Literal[DrawingAnnotationType.RADIUS] = DrawingAnnotationType.RADIUS
    bounding_box: DrawingBoundingBox
    radius: DrawingLengthValue


class DrawingDepthValue(DrawingAnalysisBaseModel):
    depth_type: Literal["value"] = "value"
    depth: DrawingLengthValue


class DrawingDepthThru(DrawingAnalysisBaseModel):
    depth_type: Literal["thru"] = "thru"
    all: bool


DrawingDepth = Annotated[DrawingDepthValue | DrawingDepthThru, Field(discriminator="depth_type")]


class DrawingThreadIso261(DrawingAnalysisBaseModel):
    thread_type: Literal[DrawingNorm.ISO_261] = DrawingNorm.ISO_261
    identifier: str = Field(pattern="^M[0-9]{1,2}$")
    tolerance_identifier: str | None = Field(default=None, pattern="^[0-9][a-zA-Z]([0-9][a-zA-Z])?$")  # e.g. 6H
    pitch: DrawingLengthValue | None = None
    depth: DrawingDepth | None = None


DrawingThread = DrawingThreadIso261


class DrawingThreadAnnotation(DrawingAnalysisBaseModel):
    annotation_type: Literal[DrawingAnnotationType.DIAMETER_THREAD] = DrawingAnnotationType.DIAMETER_THREAD
    bounding_box: DrawingBoundingBox
    thread: DrawingThread


class DrawingCountersinkAnnotation(DrawingAnalysisBaseModel):
    annotation_type: Literal[DrawingAnnotationType.DIAMETER_COUNTERSINK] = DrawingAnnotationType.DIAMETER_COUNTERSINK
    bounding_box: DrawingBoundingBox
    diameter: DrawingLengthValue
    angle: DrawingAngularValue


class DrawingCounterboreAnnotation(DrawingAnalysisBaseModel):
    annotation_type: Literal[DrawingAnnotationType.DIAMETER_COUNTERBORE] = DrawingAnnotationType.DIAMETER_COUNTERBORE
    bounding_box: DrawingBoundingBox
    diameter: DrawingLengthValue
    depth: DrawingDepth


# missing?
# counterdrill
# spotface


class DrawingDiameterAnnotation(DrawingAnalysisBaseModel):
    annotation_type: Literal[DrawingAnnotationType.DIAMETER] = DrawingAnnotationType.DIAMETER
    bounding_box: DrawingBoundingBox
    diameter: DrawingLengthValue
    depth: DrawingDepth | None = None
    # add link to threads, countersink and counterbores?


class DrawingAngularAnnotation(DrawingAnalysisBaseModel):
    annotation_type: Literal[DrawingAnnotationType.ANGLE] = DrawingAnnotationType.ANGLE
    bounding_box: DrawingBoundingBox
    angle: DrawingAngularValue


class DrawingRoughnessAnnotation(DrawingAnalysisBaseModel):
    annotation_type: Literal[DrawingAnnotationType.ROUGHNESS] = DrawingAnnotationType.ROUGHNESS
    bounding_box: DrawingBoundingBox
    roughness: DrawingLengthValue
    category: DrawingRoughnessCategory

    @model_validator(mode="after")
    def ensure_value_is_in_micrometer(self) -> Self:
        if not self.roughness.unit == DrawingUnit.MICROMETER:
            raise ValueError
        return self


DrawingObjectWithBoundingBox = Annotated[
    DrawingLengthAnnotation
    | DrawingRadiusAnnotation
    | DrawingAngularAnnotation
    | DrawingDiameterAnnotation
    | DrawingThreadAnnotation
    | DrawingCounterboreAnnotation
    | DrawingCountersinkAnnotation
    | DrawingGdtDatumAnnotation
    | DrawingGdtFrameAnnotation
    | DrawingRoughnessAnnotation,
    Field(discriminator="annotation_type"),
]


class DrawingGeneralNormIso2768(DrawingAnalysisBaseModel):
    name: Literal[DrawingNorm.ISO_2768] = DrawingNorm.ISO_2768
    row_identifier_1: str = Field(pattern="^[fmcv]$")
    row_identifier_2: str = Field(pattern="^[HKL]$")


DrawingGeneralNorm = DrawingGeneralNormIso2768


# Annotated[DrawingNormIso2768, Field(discriminator="name")]  # more norms to come


class DrawingAnalysisResult(DrawingAnalysisBaseModel):
    # title_block: ...
    lengths: List[DrawingLengthAnnotation] = []
    angles: List[DrawingAngularAnnotation] = []
    radii: List[DrawingRadiusAnnotation] = []
    diameters: List[DrawingDiameterAnnotation] = []
    threads: List[DrawingThreadAnnotation] = []
    counterbores: List[DrawingCounterboreAnnotation] = []
    countersinks: List[DrawingCountersinkAnnotation] = []
    roughness: List[DrawingRoughnessAnnotation] = []
    # bores: List[...]
    gdt_frames: List[DrawingGdtFrameAnnotation] = []
    gdt_datums: List[DrawingGdtDatumAnnotation] = []
    norms: List[DrawingGeneralNorm] = []


class DrawingDocument(DrawingAnalysisBaseModel):
    pages: List[DrawingPage]


class DrawingAnalysisReport(DrawingAnalysisBaseModel):
    analysis_status: DrawingReportStatus = DrawingReportStatus.COMPLETED
    default_tolerance_lookup_status: DrawingReportStatus = DrawingReportStatus.COMPLETED

    @property
    def any_failure(self) -> bool:
        return (
            self.analysis_status == DrawingReportStatus.FAILED
            or self.default_tolerance_lookup_status == DrawingReportStatus.FAILED
        )


class DrawingAnalysis(DrawingAnalysisBaseModel):
    version: Literal["1.0"] = "1.0"
    document: DrawingDocument
    result: DrawingAnalysisResult
    report: DrawingAnalysisReport = Field(default_factory=DrawingAnalysisReport)

    @classmethod
    def from_yaml(cls, text_buf: TextIO) -> Self:
        data = yaml.safe_load(text_buf)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, path: Path) -> Self:
        with path.open("r") as f:
            return cls.from_yaml(f)

    def to_yaml(self, text_buf: TextIO) -> None:
        class SafeDumperWithEnums(yaml.SafeDumper):
            # https://github.com/yaml/pyyaml/issues/51#issuecomment-734782475
            def represent_data(self, data: Any) -> yaml.Node:
                if isinstance(data, str) and isinstance(data, Enum):
                    return self.represent_str(data.value)
                return super().represent_data(data)

        yaml.dump(data=self.model_dump(by_alias=True, exclude_none=True), stream=text_buf, Dumper=SafeDumperWithEnums)

    def to_yaml_file(self, path: Path) -> None:
        with path.open("w") as f:
            self.to_yaml(f)


class DrawingAnalysisContext:
    def __init__(
        self,
        page: pymupdf.Page,
        page_index: int,
        drawing_analysis_result: DrawingAnalysisResult,
        point_proximity_tolerance: float,
        zoom: float,
        resolution_booster: int,
        bezier_segments: int,
    ):
        """
        Context for drawing analysis.
        """
        self.QW_UT_PREFIX = "qw-ut"  # upper tolerance
        self.QW_LT_PREFIX = "qw-lt"  # lower tolerance

        # Basic properties and configuration
        self.page = page
        self.rotation = page.rotation
        self.page_index = page_index
        self.zoom = zoom
        self.point_proximity_tolerance = point_proximity_tolerance
        self.bezier_segments = bezier_segments
        self.resolution_booster = resolution_booster
        self.max_width = page.mediabox_size.x * zoom
        self.max_height = page.mediabox_size.y * zoom

        # Geometry data
        self.lines: List[T_LINE_SEGMENT] = []
        self.lines_unfiltered: List[T_LINE_SEGMENT] = []
        self.lines_drawing: List[T_LINE_SEGMENT] = []
        self.ellipses_raw: List[List[float]] = []
        self.rectangles_raw: List[T_BBOX] = []
        self.line_collection: LineCollection | None = None
        self.line_collection_horizontal: LineCollection | None = None
        self.line_collection_vertical: LineCollection | None = None
        self.drawing_box: T_BBOX | None = None
        self.title_block_sections: List[T_BBOX] = []
        # Text data
        self.text_boxes_unfiltered: List[TextBox] = []
        self.text_boxes: List[TextBox] = []
        self.text_boxes_measurements_merged: List[TextBox] = []
        self.text_boxes_measurements_no_symbol: List[TextBox] = []
        self.text_boxes_title_block: List[TextBox] = []

        # Dimension and measurement data
        self.dimension_boxes: List[DimensionTextBox] = []
        self.text_boxes_measurement_symbols: List[TextBox | SymbolBox] = []
        self.symbol_map: Dict[Symbol, List[SymbolBox]] = defaultdict(list)

        # GDT data
        self.datum_boxes: Set[DatumBox] = set()
        self.datum_reference_boxes_left_to_map: List[GdtDatumReferenceBox] = []
        self.string_to_datum_box_map: Dict[str, DatumBox] = {}
        self.gd_t_boxes: List[GdtBox] = []

        # Organization and mapping structures
        self.verticals_by_x: Dict[float, List[T_LINE_SEGMENT]] = defaultdict(list)
        self.horizontals_by_y: Dict[float, List[T_LINE_SEGMENT]] = defaultdict(list)
        self.merged_text_box_map: Dict[TextBox | SymbolBox, Set[Union[TextBox, SymbolBox]]] = defaultdict(set)
        self.direction_to_text_boxes_map: Dict[tuple[float, float], Set[TextBox]] = defaultdict(set)

        self.drawing_analysis_result = drawing_analysis_result
