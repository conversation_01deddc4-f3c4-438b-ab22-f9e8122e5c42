from enum import Enum, unique
from typing import Type

from sqlalchemy.orm import DeclarativeBase

from qw_basic_rdb.builder.base import RelationalDatabaseConfig, SkipSchemaVerification
from qw_basic_rdb.builder.postgres import PostgreSqlConfig
from qw_basic_rdb.builder.sqlite import SqliteConfig
from qw_basic_rdb.interface import RelationalDatabase
from qw_log_interface import NO_LOGGER, Logger


@unique
class RdbMode(str, Enum):
    POSTGRES = "postgres"
    IN_MEMORY = "in_memory"


class RdbTestFactory(object):
    def __init__(self, base: Type[DeclarativeBase], postgres_cfg: PostgreSqlConfig, mode: RdbMode, echo: bool = False):
        self.base = base
        self.postgres_cfg = postgres_cfg
        self.mode = mode
        self.echo = echo

    def create(self, logger: Logger = NO_LOGGER) -> RelationalDatabase:
        backend: SqliteConfig | PostgreSqlConfig
        backend = SqliteConfig.in_memory() if self.mode == RdbMode.IN_MEMORY else self.postgres_cfg
        rdb_config = RelationalDatabaseConfig(
            backend=backend,
            schema_verification=SkipSchemaVerification(),
            create_tables_if_not_exist=self.mode == RdbMode.IN_MEMORY,
        )
        return rdb_config.build(self.base, echo=self.echo, logger=logger)
