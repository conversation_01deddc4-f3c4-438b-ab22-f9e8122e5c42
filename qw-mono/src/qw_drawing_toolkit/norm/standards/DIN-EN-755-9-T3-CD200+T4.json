{"name": "DIN_EN_755-9:2008-06+4_CD200", "tables": [{"name": "Tabelle 1", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["H CD <= 200"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["H CD <= 200"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["H CD <= 200"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["H CD <= 200"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["H CD <= 200"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["H CD <= 200"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 20"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 20"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 20"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 20"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 20"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 20"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 30"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 30"]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 30"]], "tolerance": {"upper": 1.05, "lower": -1.05}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 30"]], "tolerance": {"upper": 1.35, "lower": -1.35}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 30"]], "tolerance": {"upper": 1.65, "lower": -1.65}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 30"]], "tolerance": {"upper": 2.05, "lower": -2.05}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 40"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 40"]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 40"]], "tolerance": {"upper": 1.15, "lower": -1.15}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 40"]], "tolerance": {"upper": 1.45, "lower": -1.45}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 40"]], "tolerance": {"upper": 1.75, "lower": -1.75}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 40"]], "tolerance": {"upper": 2.15, "lower": -2.15}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 60"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 80"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 80"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 80"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 80"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 80"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 80"]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 100"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 100"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 100"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 100"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 100"]], "tolerance": {"upper": 2.1, "lower": -2.1}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 100"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 125"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 125"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 125"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 125"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 125"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 125"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 150"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 150"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 150"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 150"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 150"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 150"]], "tolerance": {"upper": 2.9, "lower": -2.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 180"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 180"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 180"]], "tolerance": {"upper": 2.1, "lower": -2.1}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 180"]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 180"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 180"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 210"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 210"]], "tolerance": {"upper": 2.1, "lower": -2.1}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 210"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 210"]], "tolerance": {"upper": 2.6, "lower": -2.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 210"]], "tolerance": {"upper": 2.9, "lower": -2.9}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 210"]], "tolerance": {"upper": 3.3, "lower": -3.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E <= 250"]], "tolerance": {"upper": 2.1, "lower": -2.1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E <= 250"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E <= 250"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E <= 250"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E <= 250"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E <= 250"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["E > 250"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["E > 250"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["E > 250"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["E > 250"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["E > 250"]], "tolerance": {"upper": 3.3, "lower": -3.3}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["E > 250"]], "tolerance": {"upper": 3.7, "lower": -3.7}}]}]}