# Material Certificate Analysis - Current As-Is State

## 📋 **Overview**

The material certificate analysis feature implements a **dual-track approach** where traditional synchronous analysis runs alongside a new asynchronous multi-agentic PydanticAI flow. The traditional implementation is kept temporarily while the agentic flow is being perfected.

## 🏗️ **Architecture Overview**

### **Two Parallel Processing Tracks:**

1. **Traditional Analysis** (qw_trunk/service/material_certificate/)
   - Synchronous Gemini-based analysis using PyMuPDF for PDF→Image conversion
   - Direct JSON schema validation with manual response cleaning
   - Immediate database storage via SQLAlchemy
   - Production-ready and stable

2. **Agentic Analysis** (qw_material_certificate_analysis/)
   - Asynchronous PydanticAI multi-agent workflow
   - Google Cloud Vision API for OCR processing
   - Foreign key database-inspired design with structured models
   - Debug-mode parallel execution with comprehensive output files
   - Under development/validation

---

## 📁 **Directory Structure**

### **qw_material_certificate_analysis/** (New Agentic Module)
```
qw_material_certificate_analysis/
├── __init__.py                     # Module description
├── agents/                         # PydanticAI agents
│   ├── models.py                   # Foreign key database-inspired models
│   ├── metadata_agent.py           # Conversational metadata collection
│   ├── header_details_agent.py     # Header/batch extraction
│   ├── chemical_composition_agent.py # Chemical analysis extraction
│   └── mechanical_properties_agent.py # Mechanical properties extraction
├── analysis/
│   └── agentic_service.py          # Main multi-agent orchestration service
├── vision/                         # Google Cloud Vision API integration
│   ├── models.py                   # Vision processing models
│   ├── vision_service.py           # Main vision processing service
│   └── google_vision_service.py    # Dedicated Google Vision API client
└── debug_output/                   # Debug files (not tracked)
    └── file_160_20250610_201919/   # Example debug session
```

### **qw_trunk/service/material_certificate/** (Traditional Module)
```
qw_trunk/service/material_certificate/
├── material_certificate.py        # Main service and hook generator
├── analysis.py                    # Traditional Gemini-based analysis
├── models.py                      # Traditional Pydantic models
├── prompts.py                     # Analysis prompts
├── db.py                          # Database operations
├── file_hook.py                   # File processing hook (dual-track coordinator)
└── agentic_analysis_client.py     # Client for queueing agentic tasks
```

---

## 🔄 **Processing Flow**

### **File Upload Processing (file_hook.py)**

When a material certificate PDF is uploaded:

1. **Phase 1: Traditional Analysis (Synchronous)**
   - PDF → PyMuPDF → Images conversion (150 DPI, WEBP format)
   - Page storage in database with S3 object references
   - Traditional Gemini 2.0 Flash analysis with JSON schema
   - Results stored in S3 and database
   - Status: COMPLETED or FAILURE

2. **Phase 2: Agentic Analysis (Asynchronous)**
   - Queue worker task for parallel agentic analysis
   - Task: `qw_worker.tasks.material_certificate_3_1_v1.analyze_material_certificate_agentic`
   - Results saved to debug files for inspection
   - No database storage (debug mode only)

### **Traditional Analysis Flow**
```
PDF → PyMuPDF → Images → Gemini 2.0 Flash → JSON → Manual Cleaning → Pydantic Validation → Database
```

### **Agentic Analysis Flow**
```
PDF → Google Vision API → Text → PydanticAI Multi-Agent → Debug Files
     ↓
1. Vision Processing (Google Cloud Vision API with 2x scaling)
2. Metadata Agent (Conversational context building with chat history)
3. Parallel Extraction (Header, Chemical, Mechanical agents with deps)
4. Direct Aggregation (Python foreign key matching, no AI agent)
```

---

## 🤖 **PydanticAI Agent Architecture**

### **Agent Design Philosophy**
- **Database-inspired**: Foreign key relationships (batch_number, heat_number)
- **Conversational**: Metadata agent builds context through chat history
- **Specialized**: Each agent has single responsibility
- **Parallel**: Extraction agents run simultaneously with shared metadata context
- **Type-safe**: Strong Pydantic model validation throughout

### **4 Core Agents (V1 Implementation):**

1. **Metadata Agent** (`metadata_agent.py`)
   - **Model**: Gemini 2.5 Flash Preview
   - **Role**: Conversational expert building progressive context
   - **Output**: `CertificateMetadata` with batch numbers, page mapping, analysis context
   - **Features**: Chat history enabled, conversational instructions

2. **Header Details Agent** (`header_details_agent.py`)
   - **Model**: Gemini 2.5 Flash Preview
   - **Role**: Database specialist extracting header records
   - **Output**: `List[HeaderDetails]` with batch_number (PK), heat_number (SK)
   - **Dependencies**: Receives metadata context via deps parameter

3. **Chemical Composition Agent** (`chemical_composition_agent.py`)
   - **Model**: Gemini 2.5 Flash Preview
   - **Role**: Laboratory analyst extracting chemical data
   - **Output**: `List[ChemicalComposition]` with foreign keys
   - **Elements**: C, Mn, Si, P, S, Cr, Ni, Mo, Cu, Al, V, Ti, Nb, B, N, Ceq, etc.

4. **Mechanical Properties Agent** (`mechanical_properties_agent.py`)
   - **Model**: Gemini 2.5 Flash Preview
   - **Role**: Testing engineer extracting mechanical test data
   - **Output**: `List[MechanicalProperties]` with foreign keys
   - **Data**: TensileTest and List[ImpactTest] with comprehensive properties

**Note**: Aggregation is now handled by direct Python foreign key matching instead of an AI agent for reliability.

---

## 📊 **Data Models Comparison**

### **Traditional Models** (qw_trunk/service/material_certificate/models.py)
```python
class Plate(BaseModel):
    batch_number: Optional[str]
    heat: Optional[str]
    manufacturer: Optional[str]
    batch_details: Optional[BatchDetails]
    chemical_composition: Optional[ChemicalComposition]
    mechanical_properties: Optional[MechanicalProperties]
    impact_test_conditions: Optional[List[ImpactTestConditions]]
```

### **Agentic Models** (qw_material_certificate_analysis/agents/models.py)
```python
# Foreign key design with separate normalized tables
class HeaderDetails(BaseModel):
    batch_number: str  # Primary Key
    heat_number: Optional[str]  # Secondary Key
    manufacturer: Optional[str]
    product_description: Optional[str]
    material_grade: Optional[str]
    thickness_mm: Optional[Decimal]
    width_mm: Optional[Decimal]
    length_mm: Optional[Decimal]
    weight_kg: Optional[Decimal]

class ChemicalComposition(BaseModel):
    batch_number: str  # Foreign Key
    heat_number: Optional[str]  # Foreign Key
    # Comprehensive chemical elements
    C: Optional[Decimal]  # Carbon
    Mn: Optional[Decimal]  # Manganese
    Si: Optional[Decimal]  # Silicon
    # ... (P, S, Cr, Ni, Mo, Cu, Al, V, Ti, Nb, B, N, Ca, H, Sn, As)
    Ceq: Optional[Decimal]  # Carbon equivalent

class MechanicalProperties(BaseModel):
    batch_number: str  # Foreign Key
    heat_number: Optional[str]  # Foreign Key
    tensile_test: Optional[TensileTest]
    impact_tests: List[ImpactTest]  # Multiple impact tests possible

class Product(BaseModel):  # Final aggregated result
    batch_number: str
    heat_number: Optional[str]
    header_details: Optional[HeaderDetails]
    chemical_composition: Optional[ChemicalComposition]
    mechanical_properties: Optional[MechanicalProperties]
```

---

## 🔧 **Technology Stack**

### **Traditional Analysis**
- **Vision**: PyMuPDF for PDF → Image conversion (150 DPI, WEBP)
- **AI**: Google Gemini 2.0 Flash with direct JSON schema
- **Validation**: Pydantic models with manual JSON response cleaning
- **Storage**: S3 + PostgreSQL via SQLAlchemy

### **Agentic Analysis**
- **Vision**: Google Cloud Vision API for OCR (2x scaling for quality)
- **AI**: PydanticAI with Gemini 2.5 Flash Preview
- **Framework**: PydanticAI 0.1.10+ with dependency injection
- **Agents**: 4 specialized PydanticAI agents + direct Python aggregation
- **Storage**: Debug files only (no database integration yet)

---

## 🚀 **Worker Task Integration**

### **Task**: `qw_worker.tasks.material_certificate_3_1_v1.analyze_material_certificate_agentic`
- **Purpose**: Run agentic analysis in parallel with traditional analysis
- **Mode**: Debug/comparison only
- **Output**: Debug files in `qw_material_certificate_analysis/debug_output/`
- **Timeout**: 30 minutes (1800 seconds)
- **Retries**: 1 attempt maximum
- **Task Name**: Registered in `qw_worker.task_registry.py`

### **Debug Output Structure**
```
debug_output/file_{revision_id}_{timestamp}/
├── combined_text_for_agents.txt           # Full extracted text from Vision API
├── agent_result_metadata.json             # Metadata agent output
├── agent_result_header_details.json       # Header extraction results
├── agent_result_chemical_compositions.json # Chemical analysis results
├── agent_result_mechanical_properties.json # Mechanical test results
├── aggregation_input.json                 # Complete foreign key structure
├── agentic_final_result.json              # Final aggregated response
├── google_vision_processed.json           # Processed Vision API results
├── google_vision_raw_response.json        # Raw Vision API response
├── page_00_image.webp                     # Individual page images (PNG→WEBP)
├── page_00_extracted_text.txt             # Individual page text
└── ...
```

---

## 🎯 **Current Status**

### **✅ Implemented & Working**
- Traditional analysis pipeline (production-ready)
- Complete PydanticAI V1 agent architecture with dependency injection
- Google Cloud Vision API integration with dedicated service
- Worker task for parallel agentic analysis
- Comprehensive debug output system with timestamped directories
- Foreign key database-inspired models with serialization cleanup
- Direct Python aggregation (no AI agent for reliability)

### **🔄 In Progress**
- Validation of agentic analysis accuracy vs traditional
- Performance optimization of multi-agent workflow
- Comparison analysis between both approaches

### **📋 Future Plans**
- Once agentic analysis is validated, traditional analysis will be removed
- Agentic flow will become the primary processing method
- Database integration for agentic results
- Migration to match technical drawing processing pattern

---

## 🔍 **Key Design Decisions**

### **Dual-Track Approach**
- **Rationale**: Allows validation of new agentic approach while maintaining production stability
- **Traditional**: Proven, stable, immediate database storage
- **Agentic**: Advanced, experimental, debug-mode validation

### **Foreign Key Design**
- **Inspiration**: Database normalization principles
- **Benefits**: Eliminates complex matching logic, enables simple aggregation
- **Implementation**: batch_number as primary key, heat_number as secondary key
- **Redundancy**: Foreign keys kept in nested models for PydanticAI validation, cleaned during serialization

### **Conversational Metadata**
- **Approach**: Chat history enabled for progressive context building
- **Benefits**: Leverages LLM conversational abilities for better understanding
- **Output**: Rich context guides subsequent extraction agents via dependency injection

### **Debug-First Development**
- **Strategy**: Comprehensive debug output for analysis validation
- **Files**: Raw API responses, intermediate results, final aggregation
- **Purpose**: Enable thorough comparison between traditional and agentic approaches

### **Direct Aggregation**
- **Decision**: Use Python foreign key matching instead of AI agent for aggregation
- **Rationale**: More reliable, faster, and deterministic than AI-based aggregation
- **Implementation**: Simple nested loops matching batch_number + heat_number

---

## 📈 **Performance Characteristics**

### **Traditional Analysis**
- **Speed**: Fast (single Gemini 2.0 Flash call per page)
- **Accuracy**: Good (established prompts and validation)
- **Reliability**: High (production-tested)
- **Debugging**: Limited (final JSON output only)

### **Agentic Analysis**
- **Speed**: Slower (4 agent calls + Vision API processing)
- **Accuracy**: Under validation (specialized agents may be more accurate)
- **Reliability**: Under development (new architecture)
- **Debugging**: Excellent (comprehensive intermediate outputs)

---

## 🔧 **Technical Implementation Details**

### **PydanticAI Dependency Injection**
- **Pattern**: `Agent[DepsType, OutputType]` with `@agent.system_prompt` decorators
- **Context**: Metadata passed to extraction agents via `deps` parameter
- **Working**: Agents receive `RunContext[CertificateMetadata]` with batch numbers and page locations

### **Vision Processing Pipeline**
- **PDF Type Detection**: Native vs Raster PDF detection
- **Image Extraction**: PyMuPDF with 2x scaling matrix for quality
- **OCR Processing**: Google Cloud Vision API with dedicated service
- **Page Limit**: Maximum 10 pages enforced

### **File Hook Integration**
- **Phase 1**: Synchronous traditional analysis during database transaction
- **Phase 2**: Asynchronous agentic task queueing after transaction
- **Error Handling**: Traditional analysis failure doesn't prevent agentic task queueing

---

This dual-track implementation allows for careful validation and comparison of the new agentic approach while maintaining production stability through the traditional analysis pipeline.
