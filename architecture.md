# QW-Mono Backend Architecture Documentation

## Table of Contents

1. [Container Architecture & Communication](#container-architecture--communication)
2. [API Endpoint Structure](#api-endpoint-structure)
3. [Falcon Framework Implementation](#falcon-framework-implementation)
4. [Docker Infrastructure](#docker-infrastructure)
5. [Database Architecture](#database-architecture)
6. [Service Layer Architecture](#service-layer-architecture)

---

## Container Architecture & Communication

### Container Overview

The QW-Mono system consists of **7 main containers** orchestrated via Docker Compose:

| Container | Name | Purpose | Ports |
|-----------|------|---------|-------|
| **postgres** | `qw-mono-dev-postgres` | PostgreSQL database | 5432 |
| **minio** | `qw-mono-dev-minio` | S3-compatible object storage | 9000, 9001 |
| **keycloak** | `qw-mono-dev-keycloak` | Identity & Access Management | 8080 |
| **collabora** | `qw-mono-dev-collabora` | Document editing service | 9980 |
| **runtime** | `qw-mono-dev-runtime` | Main backend application | 4200 |
| **rabbitmq** | `qw-mono-dev-rabbitmq` | Message queue for async tasks | 5672, 15672 |
| **worker** | `qw-mono-dev-worker` | Background task processor | - |
| **traefik** | `qw-mono-dev-traefik` | Load balancer & reverse proxy | 80, 8080 |

### Network Topology

- **Single Docker Network**: All containers communicate via the `traefik` network
- **Service Discovery**: Containers use hostname-based communication (e.g., `postgres.docker.localhost`)
- **External Access**: Traefik provides reverse proxy with domain-based routing (`*.docker.localhost`)

### Communication Patterns

1. **Frontend ↔ Backend**: HTTP API calls through Traefik reverse proxy
2. **Backend ↔ Database**: Direct PostgreSQL connections via SQLAlchemy
3. **Backend ↔ Storage**: S3-compatible API calls to MinIO
4. **Backend ↔ Auth**: OpenID Connect integration with Keycloak
5. **Backend ↔ Queue**: Celery task distribution via RabbitMQ
6. **Backend ↔ Documents**: WOPI protocol integration with Collabora

---

## API Endpoint Structure

### Framework Architecture

The API layer uses a **custom Falcon-based OpenAPI framework** (`qw_falcon_openapi`) that provides:

- Automatic OpenAPI specification generation
- Type-safe request/response handling
- Structured routing with prefixes and tags
- Built-in validation and error handling

### Endpoint Categories

#### Internal APIs (`/api/internal/*`)
- **Health Check**: `/api/internal/get_health` - System health monitoring
- **Info**: `/api/internal/get_info` - Application version and build information

#### Session Management (`/api/v1/session/*`)
- **Login Init**: `/api/v1/session/login/init` - Start OAuth2 flow
- **Login Conclude**: `/api/v1/session/login/conclude` - Complete OAuth2 flow
- **Logout**: `/api/v1/session/logout/init` - Session termination
- **Session Info**: `/api/v1/session/session` - Current session details

#### Agent Processing (`/api/v1/agent/*`)
- **Process Prompt**: `/api/v1/agent/process-prompt` - AI agent interaction endpoint

#### Business Logic APIs
- **File Resources** (`/api/v1/file-resource/*`): Upload, download, drawing analysis
- **Inspections** (`/api/v1/inspection/*`): Plans, results, actions
- **Materials** (`/api/v1/material/*`): Material management and lookup
- **Orders** (`/api/v1/order/*`): Order and task management
- **Users** (`/api/v1/user/*`): User management and profiles
- **Tenants** (`/api/v1/tenant/*`): Multi-tenant organization management

#### Integration APIs
- **WOPI** (`/api/v1/wopi/*`): Document editing integration
- **Search** (`/api/v1/search/*`): Full-text search functionality
- **Chat** (`/api/v1/chat/*`): Chat message management
- **Tolerances** (`/api/v1/tolerance/*`): Engineering tolerance standards

### Request/Response Flow

```
HTTP Request → Traefik → Falcon App → OpenApiRouting → Controller → Service → Database/S3
```

1. **Traefik** routes requests based on hostname
2. **Falcon App** handles WSGI request processing
3. **OpenApiRouting** matches routes and validates parameters
4. **Controllers** implement endpoint logic with dependency injection
5. **Services** provide business logic operations
6. **Data Layer** handles persistence and external integrations

---

## Falcon Framework Implementation

### Application Bootstrap

The application follows a structured initialization pattern:

**Entry Point** (`qw-mono/src/entrypoint.py`):
```python
# Loads configuration and starts Gunicorn WSGI server
```

**Server Configuration** (`qw-mono/src/qw_mono/server.py`):
```python
class QwMonoWSGIApp(BaseApplication):
    def load(self) -> falcon.App:
        cfg = load_config(QwMonoConfig, self.qw_mono_config, self.qw_mono_overwrite_config)
        log_factory = QwLogFactory(app_info.version)
        ctx = SimpleAppContext(log_factory=log_factory, config=cfg, info=app_info)
        return QwMonoApp.from_context(ctx).build()
```

**Application Assembly** (`qw-mono/src/qw_mono/app.py`):
```python
def build(self) -> falcon.App:
    app = falcon.App()
    app.req_options.auto_parse_form_urlencoded = True

    # Configure modules with dependency injection
    self.pfoertner.configure_app(
        app=app,
        app_info=self.info,
        tenant_registry=self.tenant_registry,
        trunk_module=self.trunk,
        drawing_tolerance_module=drawing_tolerance_module,
    )
    return app
```

### Dependency Injection Pattern

The system uses a **factory-based dependency injection** pattern:

1. **Configuration Loading**: YAML-based configuration with environment overrides
2. **Module Assembly**: Each module (`QwPfoertnerModule`, `QwTrunkModule`) creates its dependencies
3. **Service Registration**: Services are injected into controllers via constructors
4. **Context Propagation**: Request context flows through the service layer

### Authentication & Authorization

**Session-Based Authentication**:
- **Keycloak Integration**: OpenID Connect provider
- **Cookie-Based Sessions**: Secure session tokens in HTTP cookies
- **Policy Engine**: Role-based access control via `QwPolicy`

**Authentication Flow**:
```python
# qw-mono/src/qw_pfoertner/api/auth.py
def get_access_token_and_verify_from_cookies(session_service, cookies):
    # Extract and validate session token from cookies
    # Return authentication context for request processing
```

### Custom OpenAPI Framework

**Route Registration** (`qw-mono/src/qw_falcon_openapi/app.py`):
```python
class OpenApiRouting:
    def add(self, controller: OpenApiPathController) -> None:
        # Register controller with automatic OpenAPI spec generation
        self.app.add_route(route, controller)
        self.controllers.append(controller)
```

**Controller Pattern** (`qw-mono/src/qw_falcon_openapi/controller.py`):
```python
class OpenApiPathController:
    def process(self, processor, req, resp, path_params):
        # Type-safe parameter mapping and validation
        # Automatic JSON serialization/deserialization
        # Structured error handling
```

---

## Docker Infrastructure

### Build Strategy

The system uses **multi-stage Docker builds** with shared base layers for efficiency:

**Base Stages** (`Dockerfile`):
```dockerfile
FROM ubuntu:22.04 AS base
FROM base AS base-python          # Python runtime + dependencies
FROM base-python AS base-dev      # Development tools + hot reload
FROM base AS build-qw-mono        # Production backend build
FROM base-python AS build-worker  # Background worker build
```

### Container Orchestration

**Development Environment** (`docker-compose.yml`):
- **Hot Reload**: Source code mounted as volumes for development
- **Service Dependencies**: Explicit dependency management via `depends_on`
- **Health Checks**: Built-in health monitoring for critical services
- **Environment Variables**: Configuration via environment files

**Key Configuration Patterns**:
```yaml
# Runtime container with development setup
runtime:
  build:
    target: base-dev
  volumes:
    - ./:/home/<USER>/repo  # Hot reload for development
  working_dir: /home/<USER>/repo
  entrypoint: ["/bin/bash"]  # Interactive development shell
```

### Network Configuration

**Single Network Architecture**:
```yaml
networks:
  traefik:

# All services join the traefik network
# Traefik provides service discovery and routing
traefik:
  networks:
    traefik:
      aliases:
        - "app.docker.localhost"
        - "postgres.docker.localhost"
        - "minio.docker.localhost"
```

### Service Dependencies

**Startup Order**:
1. **Infrastructure Services**: PostgreSQL, MinIO, RabbitMQ, Keycloak
2. **Application Services**: Runtime, Worker
3. **Proxy Layer**: Traefik (routes to all services)

---

## Database Architecture

### SQLAlchemy Configuration

**Database Interface** (`qw-mono/src/qw_basic_rdb/interface.py`):
```python
class RelationalDatabase(Protocol):
    def create_and_get_session(self) -> Session:
        # Single active session per request pattern

    def close_session(self) -> None:
        # Cleanup session after request
```

**Implementation** (`qw-mono/src/qw_basic_rdb/sqlalch.py`):
```python
class SqlAlchemyDatabase:
    def create_and_get_session(self) -> Session:
        if self.__session is not None:
            raise RuntimeError("There is already an active session")
        self.__session = Session(self.__engine, autobegin=True)
        return self.__session
```

### Session Management

**Transaction Context Manager** (`qw-mono/src/qw_basic_rdb/common.py`):
```python
@contextlib.contextmanager
def begin_or_use_session(
    rdb: RelationalDatabase,
    session: Session | None = None,
    strategy: TransactionStrategy = TransactionStrategy.SKIP,
) -> Iterator[Session]:
    # Handles transaction lifecycle with automatic rollback on exceptions
    # Supports nested transactions and session reuse patterns
```

**Transaction Strategies**:
- **NEW**: Create new nested transaction
- **REUSE**: Require existing active transaction
- **REUSE_OR_NEW**: Create transaction only if none exists
- **SKIP**: No transaction management

### Data Layer Patterns

**Common Table Operations** (`qw-mono/src/qw_basic_rdb/common.py`):
```python
class CommonTableOps(Generic[T]):
    def add(self, obj: T, session: Session) -> int:
        with begin_or_use_session(self.rdb, session, strategy=TransactionStrategy.REUSE) as s:
            s.add(obj)
            s.flush([obj])  # Get ID without committing
            return obj.id
```

**Database Configuration** (`dev_data/app.yaml`):
```yaml
mono_db:
  create_tables_if_not_exist: yes
  backend:
    type: postgres
    host: postgres.docker.localhost
    pool_size: 10
    pool_max_overflow: 15
    pool_timeout_seconds: 10
```

### Schema Management

**Table Definitions**: Located in `qw-mono/src/qw_monodb/table/`
- **Base**: Common table base class with SQLAlchemy declarative mapping
- **Pfoertner**: Session and authentication tables
- **Trunk**: Business logic tables (drawings, materials, orders, etc.)

**Schema Verification**: Liquibase integration for production schema validation

---

## Service Layer Architecture

### Module Structure

The service layer follows a **modular architecture** with clear separation of concerns:

**Core Modules**:
- **QwPfoertnerModule**: API layer, authentication, session management
- **QwTrunkModule**: Business logic services and domain operations
- **QwDrawingToleranceModule**: Engineering tolerance standards

### Business Services

**Agent Service** (`qw-mono/src/qw_trunk/service/agent/agent_service.py`):
```python
class AgentService:
    def __init__(self, api_key: str, service_provider: ServiceProvider, lf: LogFactory):
        # PydanticAI-based agent system for AI interactions
        self.router_agent = RouterAgent(api_key=api_key, lf=lf)
        self.service_provider = service_provider
```

**Key Service Categories**:

1. **File Management**:
   - `FileResourceService`: File upload/download operations
   - `S3ObjectService`: Object storage abstraction
   - `DrawingService`: Technical drawing processing

2. **Business Logic**:
   - `MaterialService`: Material catalog management
   - `OrderService`: Order and task management
   - `InspectionPlanService`: Quality inspection workflows

3. **Integration Services**:
   - `WopiService`: Document editing integration
   - `ChatDatabaseService`: Chat message persistence
   - `HealthCheckService`: System monitoring

### Service Provider Pattern

**Dependency Injection** (`qw-mono/src/qw_trunk/service/agent/service_provider.py`):
```python
class ServiceProvider:
    def __init__(
        self,
        drawing_analysis_client: TechnicalDrawingAnalysisClient,
        file_service: FileResourceService,
        material_service: MaterialService,
        # ... other services
    ):
        # Centralized service registry for agent tools
```

### Configuration Management

**YAML-Based Configuration**:
- **Base Config**: `qw-mono/config/app.yaml` (production defaults)
- **Development Overrides**: `dev_data/app.yaml` (local development)
- **Environment Variables**: Runtime configuration overrides

**Configuration Pattern**:
```python
class QwTrunkConfig(QwTrunkBaseConfig):
    db: RelationalDatabaseConfig
    s3: S3StorageConfig

    @classmethod
    def from_config(cls, config: QwTrunkConfig, tenant_registry: TenantConfigRegistry, lf: LogFactory):
        # Factory method pattern for service initialization
```

### Integration Patterns

**Authentication Integration**:
- **Keycloak**: OpenID Connect provider for user authentication
- **Session Service**: Cookie-based session management
- **Policy Engine**: Role-based access control

**Storage Integration**:
- **PostgreSQL**: Primary data persistence via SQLAlchemy
- **MinIO S3**: Object storage for files and documents
- **RabbitMQ**: Asynchronous task processing via Celery

**External Services**:
- **Collabora**: Document editing via WOPI protocol
- **AI Services**: OpenAI, Claude, Groq integration for agent functionality

---

## Architectural Strengths

1. **Modular Design**: Clear separation between API, service, and data layers
2. **Type Safety**: Comprehensive use of Python type hints and Pydantic models
3. **Configuration Management**: YAML-based configuration with environment overrides
4. **Container Isolation**: Each service runs in dedicated containers with specific responsibilities
5. **Transaction Management**: Robust database transaction handling with automatic rollback
6. **OpenAPI Integration**: Automatic API documentation generation and validation

## Areas for Optimization

1. **Session Management**: Current single-session-per-thread pattern may cause issues with gthread workers
2. **Container Communication**: Multiple network hops for complex request flows
3. **Agent Architecture**: PydanticAI integration adds complexity for AI-powered features
4. **Database Connections**: Connection pooling configuration may need tuning for high load

---

*This documentation reflects the current state of the QW-Mono backend architecture as of the latest codebase analysis.*
