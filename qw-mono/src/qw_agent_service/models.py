"""Request/response models for FastAPI Agent Service - copied from trunk for compatibility."""
from typing import Any, Dict, List, Optional
from uuid import UUID
from pydantic import BaseModel, Field


class AgentAction(BaseModel):
    """Represents an action that the agent wants the frontend to perform."""
    
    action_type: str = Field(description="The type of action to perform")
    component: str = Field(description="The component that should handle the action")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Parameters for the action")


class AgentPromptRequest(BaseModel):
    """Request model for agent prompt processing."""
    
    prompt: str = Field(description="The user's prompt to process")
    session_id: Optional[UUID] = Field(default=None, description="Optional session ID for conversation continuity")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Optional context information")


class AgentPromptResponse(BaseModel):
    """Response model for agent prompt processing."""
    
    message: str = Field(description="The agent's response message")
    session_id: UUID = Field(description="Session ID for conversation continuity")
    actions: List[AgentAction] = Field(default_factory=list, description="List of actions for the frontend to perform")


class HealthResponse(BaseModel):
    """Health check response model."""
    
    status: str = Field(description="Service health status")
    service: str = Field(description="Service name")
    version: str = Field(default="1.0.0", description="Service version")
    agent_available: bool = Field(description="Whether the agent is available")


class ErrorResponse(BaseModel):
    """Error response model."""
    
    detail: str = Field(description="Error detail message")
    error_code: Optional[str] = Field(default=None, description="Optional error code")
    request_id: Optional[str] = Field(default=None, description="Optional request ID for tracking")
