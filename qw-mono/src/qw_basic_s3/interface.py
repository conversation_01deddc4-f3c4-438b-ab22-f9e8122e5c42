import io
from dataclasses import dataclass
from pathlib import PurePosixPath
from typing import Binary<PERSON>, Generator, List, Literal, Protocol

from qw_basic_s3.schema import S3StorageSchema


class S3StorageError(Exception):
    pass


@dataclass
class S3Object:
    path: PurePosixPath
    is_dir: bool


class S3ObjectBytes(io.BytesIO):
    def __init__(self, payload: bytes, content_type: str):
        super().__init__(payload)
        self.content_length = len(payload)
        self.content_type = content_type

    def writable(self) -> bool:
        return False


class S3Storage(Protocol):
    """Inspired by https://min.io/docs/minio/linux/developers/python/API.html"""

    def get_name(self) -> str:
        ...

    def get_schema(self) -> S3StorageSchema:
        ...

    def create_bucket(self, name: str) -> bool:
        ...

    def remove_bucket(self, name: str) -> bool:
        ...

    def bucket_exists(self, name: str) -> bool:
        ...

    def list_buckets(self) -> List[str]:
        ...

    def put_object(
        self,
        bucket: str,
        path: PurePosixPath,
        bio: BinaryIO,
        size: int = -1,
        content_type: str = "application/octet-stream",
        part_size: int = 0,
    ) -> bool:
        ...

    def remove_object(self, bucket: str, path: PurePosixPath) -> bool:
        ...

    def get_object(self, bucket: str, path: PurePosixPath, offset: int = 0, length: int = 0) -> S3ObjectBytes:
        ...

    def list_objects(
        self,
        bucket: str,
        prefix: PurePosixPath | None,
        trailing_slash: Literal["", "/", "auto"] = "auto",
        recursive: bool = False,
    ) -> Generator[S3Object, None, None]:
        ...
