/* tslint:disable */
/* eslint-disable */
import { QwProductType } from '../models/qw-product-type';

/**
 * [schema: CertificateMetadata] Comprehensive metadata collected by the metadata agent with chat history.
 * This builds context progressively and enables chitchat benefits.
 */
export interface QwCertificateMetadata {

  /**
   * Conversational context about the certificate structure, e.g., 'I found 2 steel plates on pages 1-2, with chemical data on page 1 and mechanical properties on page 2'
   */
  analysis_context: string;

  /**
   * List of all batch numbers found in the certificate
   */
  batch_numbers: Array<string>;

  /**
   * Page numbers containing chemical composition data
   */
  pages_with_chemical_composition: Array<number>;

  /**
   * Page numbers containing header/batch details
   */
  pages_with_header_details: Array<number>;

  /**
   * Page numbers containing mechanical properties
   */
  pages_with_mechanical_properties: Array<number>;

  /**
   * Type of product this certificate covers
   */
  product_type: QwProductType;

  /**
   * Total number of individual products/items in the certificate
   */
  total_product_count: number;
}
