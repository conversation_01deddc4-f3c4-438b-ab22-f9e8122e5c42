import pytest
from joserfc import jwk, jwt

from qw_basic_keycloak.joserfc_ext import decode_without_key


class TestJoseRfcExtension(object):
    def test_decode_without_key_only_works_with_signed_jwts(self) -> None:
        # from https://jose.authlib.org/en/dev/guide/jwt/#jws-jwe
        header = {"alg": "A128KW", "enc": "A128GCM"}
        claims = {"iss": "https://idp.org"}
        key = jwk.OctKey.generate_key(128)
        token_b64 = jwt.encode(header, claims, key)
        with pytest.raises(ValueError, match="Encrypted JWTs cannot be processed without key"):
            decode_without_key(token_b64)

    def test_decode_without_key_equivalence(self) -> None:
        header = {"alg": "RS256"}
        claims = {"iss": "https://idp.org"}
        key = jwk.RSAKey.generate_key()
        token_b64 = jwt.encode(header, claims, key)

        token_1 = jwt.decode(token_b64, key)
        token_2 = decode_without_key(token_b64)

        assert token_1.header == token_2.header
        assert token_1.claims == token_2.claims
