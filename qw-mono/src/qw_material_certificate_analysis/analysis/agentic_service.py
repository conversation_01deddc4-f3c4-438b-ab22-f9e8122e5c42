"""PydanticAI multi-agent analysis service for material certificates - V1 Redesign."""
import asyncio
import json
from pathlib import Path
from typing import Any, List, Optional, cast

from qw_log_interface import NO_LOGGER, Logger
from qw_material_certificate_analysis.agents.chemical_composition_agent import create_chemical_composition_agent
from qw_material_certificate_analysis.agents.header_details_agent import create_header_details_agent
from qw_material_certificate_analysis.agents.mechanical_properties_agent import create_mechanical_properties_agent

# Import new V1 agents
from qw_material_certificate_analysis.agents.metadata_agent import create_metadata_agent
from qw_material_certificate_analysis.agents.models import (
    AggregationInput,
    CertificateMetadata,
    ChemicalComposition,
    HeaderDetails,
    MaterialCertificateAnalysisResponse,
    MechanicalProperties,
    Product,
)
from qw_material_certificate_analysis.vision.models import VisionProcessingResult
from qw_material_certificate_analysis.vision.vision_service import MaterialCertificateVisionService


class AgenticAnalysisService:
    """Multi-agent analysis service using PydanticAI."""

    def __init__(
        self,
        gemini_api_key: str,
        debug_mode: bool = False,
        debug_output_dir: Optional[Path] = None,
        logger: Logger = NO_LOGGER,
    ):
        """Initialize the agentic analysis service."""
        self.logger = logger
        self.debug_mode = debug_mode
        self.gemini_api_key = gemini_api_key
        self.debug_output_dir: Optional[Path]

        # Create debug output directory if debug mode is enabled
        if debug_mode:
            if debug_output_dir is None:
                # Create debug directory inside the module
                module_dir = Path(__file__).parent.parent
                self.debug_output_dir = module_dir / "debug_output"
            else:
                self.debug_output_dir = debug_output_dir

            # Ensure debug directory exists
            self.debug_output_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Debug output directory: {self.debug_output_dir}")
        else:
            self.debug_output_dir = None

        # Initialize vision service
        self.vision_service = MaterialCertificateVisionService(
            gemini_api_key=gemini_api_key, debug_mode=debug_mode, debug_output_dir=self.debug_output_dir, logger=logger
        )

        # Store API key for creating fresh agents per analysis
        self.gemini_api_key = gemini_api_key

    def analyze_certificate(self, pdf_bytes: bytes) -> MaterialCertificateAnalysisResponse:
        """
        Analyze material certificate with proper agent lifecycle management.

        CRITICAL FIX FOR PYDANTIC-AI GEMINI EVENT LOOP ISSUE:

        GitHub Issue: https://github.com/pydantic/pydantic-ai/issues/748
        Problem: PydanticAI's Gemini provider caches httpx.AsyncHTTPTransport globally,
        causing "RuntimeError: Event loop is closed" when transports outlive event loops.

        Root Cause: PydanticAI team's oversight - they share HTTP transports between
        different event loops without proper isolation. The cached transport keeps
        references to closed event loops through:
        transport._pool._connections[0]._connection._network_stream._stream.transport_stream._transport._loop

        Our Solution:
        1. Create fresh agents per analysis (no reuse)
        2. Explicitly close all HTTP clients before event loop ends
        3. Proper event loop lifecycle management

        This is PydanticAI team's fault, not Google/Gemini's fault.
        """
        # Handle async operations internally with proper cleanup (following technical drawing pattern)
        try:
            # Create a new event loop to isolate async operations
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self._analyze_certificate_async(pdf_bytes))
            finally:
                # Ensure all pending tasks are completed and connections are closed
                try:
                    # Cancel any remaining tasks
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()

                    # Wait for cancelled tasks to finish
                    if pending:
                        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

                    # Close the loop
                    loop.close()
                finally:
                    # Reset the event loop for the thread
                    asyncio.set_event_loop(None)
        except Exception as e:
            self.logger.error(f"Error in material certificate analysis: {str(e)}")
            raise

    async def _analyze_certificate_async(self, pdf_bytes: bytes) -> MaterialCertificateAnalysisResponse:
        """Internal async implementation with proper agent lifecycle management."""
        self.logger.info("Starting V1 agentic multi-agent analysis")

        # Create fresh agents for this analysis to avoid HTTP client reuse
        self.logger.info("Creating fresh PydanticAI agents with lifecycle management")
        metadata_agent = create_metadata_agent(self.gemini_api_key)
        header_details_agent = create_header_details_agent(self.gemini_api_key)
        chemical_composition_agent = create_chemical_composition_agent(self.gemini_api_key)
        mechanical_properties_agent = create_mechanical_properties_agent(self.gemini_api_key)

        try:
            # Phase 1: Vision processing
            vision_result = await self.vision_service.process_pdf(pdf_bytes)
            self.logger.info(f"Vision processing completed: {vision_result.total_pages} pages")

            # Combine all extracted text for agent processing
            full_text = self._combine_vision_text(vision_result)

            # Save debug files if enabled
            if self.debug_mode and self.debug_output_dir:
                self._save_combined_text(full_text)

            # Phase 2: Metadata collection (conversational with chat history)
            self.logger.info("Running metadata agent with conversational approach")
            metadata_result = await metadata_agent.run(full_text)
            metadata = metadata_result.output
            self.logger.info(
                f"Metadata collected: {metadata.total_product_count} products of type {metadata.product_type}"
            )
            self.logger.info(f"Batch numbers identified: {metadata.batch_numbers}")

            # Phase 3: Parallel extraction with foreign key design
            self.logger.info("Starting parallel extraction agents with foreign key approach")

            # Run extraction agents in parallel, each receiving metadata context
            header_task = header_details_agent.run(full_text, deps=metadata)
            chemical_task = chemical_composition_agent.run(full_text, deps=metadata)
            mechanical_task = mechanical_properties_agent.run(full_text, deps=metadata)

            # Wait for all extraction agents to complete
            header_result, chemical_result, mechanical_result = await asyncio.gather(
                header_task, chemical_task, mechanical_task
            )

            # Continue with the rest of the analysis...
            return await self._complete_analysis(metadata, header_result, chemical_result, mechanical_result)

        finally:
            # CRITICAL: Explicitly close all agent HTTP clients before event loop ends
            self.logger.info("Cleaning up agent HTTP connections")
            agents = [
                metadata_agent,
                header_details_agent,
                chemical_composition_agent,
                mechanical_properties_agent,
            ]

            # Close all HTTP clients
            close_tasks = []
            for agent in agents:
                if hasattr(agent, "model") and hasattr(agent.model, "client"):
                    if hasattr(agent.model.client, "aclose"):
                        close_tasks.append(agent.model.client.aclose())

            if close_tasks:
                await asyncio.gather(*close_tasks, return_exceptions=True)
                self.logger.info(f"Closed {len(close_tasks)} HTTP client connections")

            # Wait a brief moment for any remaining cleanup
            await asyncio.sleep(0.01)

    async def _complete_analysis(
        self, metadata: CertificateMetadata, header_result: Any, chemical_result: Any, mechanical_result: Any
    ) -> MaterialCertificateAnalysisResponse:
        """Complete the analysis with the agent results."""
        # Extract outputs from agent results
        header_details = cast(List[HeaderDetails], header_result.output or [])
        chemical_compositions = cast(List[ChemicalComposition], chemical_result.output or [])
        mechanical_properties = cast(List[MechanicalProperties], mechanical_result.output or [])

        self.logger.info(
            f"Extraction completed: {len(header_details)} headers, "
            f"{len(chemical_compositions)} chemical, {len(mechanical_properties)} mechanical"
        )

        # Phase 4: Simple aggregation using foreign key matching
        self.logger.info("Running aggregation agent with foreign key matching")

        # Prepare aggregation input with foreign key design
        aggregation_input = AggregationInput(
            metadata=metadata,
            header_details=header_details,
            chemical_compositions=chemical_compositions,
            mechanical_properties=mechanical_properties,
        )

        # Save intermediate results if debug mode
        if self.debug_mode and self.debug_output_dir:
            self._save_intermediate_results_v1(aggregation_input)

        # Phase 4: Simple foreign key aggregation (no AI needed - just data transformation)
        self.logger.info("Performing foreign key aggregation using direct Python")

        # Create products by matching foreign keys
        products: List[Product] = []
        for header in aggregation_input.header_details:
            # Find matching chemical composition by batch_number and heat_number
            chemical_comp: Optional[ChemicalComposition] = None
            for chem in aggregation_input.chemical_compositions:
                if chem.batch_number == header.batch_number and chem.heat_number == header.heat_number:
                    chemical_comp = chem
                    break

            # Find matching mechanical properties by batch_number and heat_number
            mechanical_props: Optional[MechanicalProperties] = None
            for mech in aggregation_input.mechanical_properties:
                if mech.batch_number == header.batch_number and mech.heat_number == header.heat_number:
                    mechanical_props = mech
                    break

            # Create product with foreign key matches
            product = Product(
                batch_number=header.batch_number,
                heat_number=header.heat_number,
                header_details=header,
                chemical_composition=chemical_comp,
                mechanical_properties=mechanical_props,
            )
            products.append(product)

        # Create final response
        final_result = MaterialCertificateAnalysisResponse(metadata=aggregation_input.metadata, products=products)

        self.logger.info("V1 multi-agent analysis with direct aggregation completed successfully")
        return final_result

    def _combine_vision_text(self, vision_result: VisionProcessingResult) -> str:
        """Combine text from all pages into a single document."""
        combined_text: List[str] = []

        for i, document_text in enumerate(vision_result.vision_results):
            page_header = f"\n=== PAGE {i+1} ===\n"
            combined_text.append(page_header)
            combined_text.append(document_text.text)
            combined_text.append("\n")

        return "".join(combined_text)

    def _save_combined_text(self, full_text: str) -> None:
        """Save combined text for debugging."""
        if not self.debug_output_dir:
            return

        try:
            debug_dir = Path(self.debug_output_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)

            text_file = debug_dir / "combined_text_for_agents.txt"
            with open(text_file, "w", encoding="utf-8") as f:
                f.write(full_text)

            self.logger.info("Saved combined text for agent processing")

        except Exception as e:
            self.logger.error(f"Error saving combined text: {e}")

    def _save_intermediate_results_v1(self, aggregation_input: AggregationInput) -> None:
        """Save V1 intermediate agent results for debugging."""
        if not self.debug_output_dir:
            return

        try:
            debug_dir = Path(self.debug_output_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)

            # Save metadata
            metadata_file = debug_dir / "agent_result_metadata.json"
            with open(metadata_file, "w", encoding="utf-8") as f:
                json.dump(aggregation_input.metadata.model_dump(), f, indent=2, default=str)

            # Save header details
            header_file = debug_dir / "agent_result_header_details.json"
            with open(header_file, "w", encoding="utf-8") as f:
                json.dump([h.model_dump() for h in aggregation_input.header_details], f, indent=2, default=str)

            # Save chemical compositions
            chemical_file = debug_dir / "agent_result_chemical_compositions.json"
            with open(chemical_file, "w", encoding="utf-8") as f:
                json.dump([c.model_dump() for c in aggregation_input.chemical_compositions], f, indent=2, default=str)

            # Save mechanical properties
            mechanical_file = debug_dir / "agent_result_mechanical_properties.json"
            with open(mechanical_file, "w", encoding="utf-8") as f:
                json.dump([m.model_dump() for m in aggregation_input.mechanical_properties], f, indent=2, default=str)

            # Save complete aggregation input
            aggregation_file = debug_dir / "aggregation_input.json"
            with open(aggregation_file, "w", encoding="utf-8") as f:
                json.dump(aggregation_input.model_dump(), f, indent=2, default=str)

            self.logger.info("Saved V1 intermediate agent results with foreign key structure")

        except Exception as e:
            self.logger.error(f"Error saving V1 intermediate results: {e}")

    @classmethod
    def from_config(
        cls,
        gemini_api_key: str,
        save_debug_files: bool = False,
        debug_output_dir: Optional[str] = None,
        logger: Logger = NO_LOGGER,
    ) -> "AgenticAnalysisService":
        """Create service from configuration following project pattern."""
        debug_path = Path(debug_output_dir) if debug_output_dir else None

        return cls(
            gemini_api_key=gemini_api_key, debug_mode=save_debug_files, debug_output_dir=debug_path, logger=logger
        )
