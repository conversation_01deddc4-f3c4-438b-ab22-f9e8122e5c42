"""Text utils"""

import re
from typing import List

from qw_drawing_toolkit.pdf.models.analysis_models import TextBox
from qw_drawing_toolkit.pdf.parser.symbol_mapper import SymbolMapper


def is_only_letters(text: str) -> bool:
    """
    Check if a string contains only alphabetic characters.

    Args:
        text: The string to check

    Returns:
        True if the string contains only letters, False otherwise
    """
    return bool(re.match("^[a-zA-Z]+$", text))


def is_only_numbers(text: str) -> bool:
    """
    Check if a string contains only numeric characters.

    Args:
        text: The string to check

    Returns:
        True if the string contains only numbers, False otherwise
    """
    return bool(re.match("^[0-9]+$", text))


def is_symbol(text_box: TextBox) -> bool:
    """
    Determine if a TextBox contains a symbol.

    Checks if the font indicates a symbol or if the text contains special characters
    like diameter, degrees, or plus-minus symbols.

    Args:
        text_box: The TextBox to check

    Returns:
        True if the TextBox contains a symbol, False otherwise
    """
    return (
        ("Solid" in text_box.font and "Edge" in text_box.font and "Symbol" in text_box.font)
        or SymbolMapper.is_font_mapped(text_box.font)
        or is_diameter(text_box)
        or is_degrees(text_box)
        or is_plus_minus(text_box)
    )


def is_diameter(text_box: TextBox) -> bool:
    """
    Check if a TextBox contains a diameter symbol.

    Args:
        text_box: The TextBox to check

    Returns:
        True if the TextBox contains a diameter symbol and fewer than 3 lowercase letters,
        False otherwise
    """
    text = text_box.text
    lower_case_letter_count = len(re.findall(r"[a-z]", text))
    valid = (
        "ø" in text or "⌀" in text or "∅" in text or "⊘" in text or "\u008E" in text
    ) and lower_case_letter_count < 3
    if valid:
        return True
    else:
        return False


def is_degrees(text_box: TextBox) -> bool:
    """
    Check if a TextBox contains a degree symbol.

    Args:
        text_box: The TextBox to check

    Returns:
        True if the TextBox contains a degree symbol, False otherwise
    """
    text = text_box.text
    return "°" in text


def is_plus_minus(text_box: TextBox) -> bool:
    """
    Check if a TextBox contains a plus-minus symbol.

    Args:
        text_box: The TextBox to check

    Returns:
        True if the TextBox contains a plus-minus symbol, False otherwise
    """
    text = text_box.text
    return "±" in text


def remove_text_text_boxes(text_boxes: List[TextBox]) -> List[TextBox]:
    """
    Filter out TextBoxes that appear to be regular text.

    Keeps TextBoxes that have at most 2 lowercase letters and contain at least one digit,
    or consist of just a plus or minus sign.

    Args:
        text_boxes: List of TextBoxes to filter

    Returns:
        Filtered list of TextBoxes
    """
    return [
        text_box
        for text_box in text_boxes
        if sum(c.isalpha() and c.islower() for c in text_box.text) <= 2
        and any(c.isdigit() for c in text_box.text)
        or text_box.text.strip() == "+"
        or text_box.text.strip() == "-"
    ]


def remove_single_letter_or_single_number_text_boxes(text_boxes: List[TextBox]) -> List[TextBox]:
    """
    Filter out TextBoxes that contain only a single letter or number.

    Keeps TextBoxes that are symbols, plus/minus signs, have multiple characters,
    contain only numbers, or contain a mix of letters and other characters.
    Also filters out TextBoxes containing colons or two-character strings starting with '0'.

    Args:
        text_boxes: List of TextBoxes to filter

    Returns:
        Filtered list of TextBoxes

    Note:
        Implemented as a for loop to avoid low-level C errors when using PyCharm debugger.
    """
    # this has been changed to a for loop since this gave low level C errors in the interpreter whilst attaching the
    # PyCharm debugger.
    valid_text_boxes: List[TextBox] = []
    for text_box in text_boxes:
        text = text_box.text.strip()

        if (
            is_symbol(text_box)
            or text in ["+", "-"]
            or len(text) > 1
            or is_only_numbers(text)
            or not is_only_letters(text)
        ):
            if ":" not in text and not (len(text) == 2 and text.startswith("0")):
                valid_text_boxes.append(text_box)

    return valid_text_boxes


def remove_custom_filter_text_boxes(text_boxes: List[TextBox]) -> List[TextBox]:
    """
    Filter out TextBoxes that match a custom pattern.

    Removes TextBoxes containing text that matches the pattern of a letter followed by a hyphen.

    Args:
        text_boxes: List of TextBoxes to filter

    Returns:
        Filtered list of TextBoxes
    """
    custom_pattern_1 = r"[A-Za-z]-"
    return [text_box for text_box in text_boxes if (len(re.findall(custom_pattern_1, text_box.text)) == 0)]
