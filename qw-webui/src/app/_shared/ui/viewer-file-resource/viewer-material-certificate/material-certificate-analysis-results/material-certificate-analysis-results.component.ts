import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Outlet } from "@angular/common"
import { Component, computed, inject, input } from "@angular/core"
import { FaIconComponent } from "@fortawesome/angular-fontawesome"
import { faCopy } from "@fortawesome/free-solid-svg-icons"
import { DIALOG_MESSAGES } from "@qw-core/ui/dialog-temp/app-messages"
import { MaterialCertificateResourceStateService } from "@qw-shared-services/material-certificate-resource-state.service"
import { UserInterfaceService } from "@qw-shared-services/user-interface.service"
import { checkFilterValue } from "@qw-shared-utils/qw-check-filter-value"

import { QwProduct } from "@qw-api/models"

interface ChemicalStatus {
  element: string
  value: number
  status: "ok" | "nok" | null
}

@Component({
  selector: "qw-material-certificate-analysis-results",
  templateUrl: "./material-certificate-analysis-results.component.html",
  standalone: true,
  providers: [],
  imports: [<PERSON><PERSON><PERSON>, <PERSON><PERSON>or, <PERSON>sonPip<PERSON>, <PERSON><PERSON>emplateOutlet, FaIconComponent],
})
export class MaterialCertificateAnalysisResultsComponent {
  private stateService = inject(MaterialCertificateResourceStateService)
  private userInterfaceService = inject(UserInterfaceService)
  analysis = input.required<QwProduct[]>()

  protected readonly faCopy = faCopy

  protected readonly currentProduct = computed(() => {
    const product = this.analysis()
    const index = this.stateService.currentPlateIndex()
    return index >= 0 && index < product.length ? product[index] : null
  })

  // Actions ===================================================================
  protected async copyAsTable(): Promise<void> {
    const plate = this.currentProduct()
    if (!plate) return

    // Helper function to remove units from strings
    const removeUnits = (str: string): string => {
      return str
        .replace(/\s*\([^)]*\)/g, "") // Remove anything in parentheses
        .replace(/(°C|MPa|J|%)/g, "") // Remove specific unit symbols
        .trim() // Clean up any remaining whitespace
    }

    // Collect base headers
    const baseHeaders = new Set<string>()

    // Basic info headers
    this.manufacturerInfo().fields.forEach((field) =>
      baseHeaders.add(removeUnits(field.label)),
    )

    // Chemical composition headers
    this.chemicalComposition().forEach((item) => baseHeaders.add(item.element))

    // Mechanical properties headers
    this.mechanicalProperties().fields.forEach((field) =>
      baseHeaders.add(removeUnits(field.label)),
    )

    // Initialize base values
    const valuesByHeader: Record<string, string> = {}
    Array.from(baseHeaders).forEach((header) => {
      valuesByHeader[header] = ""
    })

    // Fill in base values
    // Manufacturer info
    this.manufacturerInfo().fields.forEach((field) => {
      valuesByHeader[removeUnits(field.label)] = field.value ?? ""
    })

    // Chemical composition values
    this.chemicalComposition().forEach((item) => {
      valuesByHeader[item.element] = item.value.toString()
    })

    // Mechanical properties
    this.mechanicalProperties().fields.forEach((field) => {
      valuesByHeader[removeUnits(field.label)] = field.value ?? ""
    })

    // Impact test headers and values - now separated by test number
    const impactTestHeaders = new Set<string>()
    if (this.impactTests().tests.length) {
      this.impactTests().tests.forEach((test: any, index: number) => {
        test.fields.forEach((field: any) => {
          const header =
            $localize`Impact Test` + ` ${index + 1} ${removeUnits(field.label)}`
          impactTestHeaders.add(header)
          valuesByHeader[header] = field.value ?? ""
        })
      })
    }

    // Combine all headers
    const allHeaders = [...baseHeaders, ...impactTestHeaders]

    // Create Excel-compatible TSV content
    const headerRow = allHeaders.join("\t")
    const valueRow = allHeaders
      .map((header) => {
        const value = valuesByHeader[header]
        return value?.replace(/[\t\n\r]/g, " ") ?? ""
      })
      .join("\t")

    const clipboardContent = [headerRow, valueRow].join("\r\n")

    try {
      await navigator.clipboard.writeText(clipboardContent)
      this.userInterfaceService.showPopup(DIALOG_MESSAGES.COPY_SUCCESS)
    } catch (err) {
      logger.error("Failed to copy:", err)
    }
  }

  // View ======================================================================
  protected readonly manufacturerInfo = computed(() => ({
    title: $localize`Manufacturer Info`,
    fields: [
      {
        label: $localize`Manufacturer`,
        value: this.currentProduct()?.header_details?.manufacturer,
      },
      {
        label: $localize`Batch Number`,
        value: this.currentProduct()?.batch_number,
      },
      {
        label: $localize`Heat Number`,
        value: this.currentProduct()?.heat_number,
      },
      {
        label: $localize`Product Description`,
        value: this.currentProduct()?.header_details?.product_description,
      },
      {
        label: $localize`Grade`,
        value: this.currentProduct()?.header_details?.material_grade,
      },
      {
        label: $localize`Weight`,
        value: this.getFormattedValue(
          this.currentProduct()?.header_details?.weight_kg,
          `kg`,
        ),
      },
      {
        label: $localize`Dimensions`,
        value: this.getDimensions({
          thickness: this.currentProduct()?.header_details?.thickness_mm,
          width: this.currentProduct()?.header_details?.width_mm,
          length: this.currentProduct()?.header_details?.length_mm,
        }),
        unit: `mm`,
      },
    ],
  }))

  protected readonly chemicalComposition = computed(() => {
    const plate = this.currentProduct()
    const filters = this.stateService.filters()

    if (!plate?.chemical_composition) return []

    return Object.entries(plate.chemical_composition)
      .filter(([key, value]) => {
        // Exclude metadata fields that are not actual chemical elements
        const metadataFields = ["batch_number", "heat_number"]
        return (
          !metadataFields.includes(key) && value !== null && value !== undefined
        )
      })
      .map(([element, value]): ChemicalStatus => {
        const filter = filters.find(
          (f) => f.element.toLowerCase() === element.toLowerCase(),
        )

        return {
          element,
          value: value as number,
          status: filter
            ? checkFilterValue(value as number, filter)
              ? "ok"
              : "nok"
            : null,
        }
      })
  })

  protected readonly mechanicalProperties = computed(() => ({
    title: $localize`Mechanical Properties`,
    fields: [
      {
        label: $localize`Direction`,
        value: this.getFormattedValue(
          this.currentProduct()?.mechanical_properties?.tensile_test
            ?.test_direction,
        ),
      },
      {
        label: $localize`Temperature`,
        value: this.getFormattedValue(
          this.currentProduct()?.mechanical_properties?.tensile_test
            ?.test_temperature_celsius,
          `°C`,
        ),
      },
      {
        label: $localize`Tensile Strength`,
        value: this.getFormattedValue(
          this.currentProduct()?.mechanical_properties?.tensile_test
            ?.tensile_strength_mpa,
          `MPa`,
        ),
      },
      {
        label: $localize`Yield Strength`,
        value: this.getFormattedValue(
          this.currentProduct()?.mechanical_properties?.tensile_test
            ?.yield_strength_mpa,
          `MPa`,
        ),
      },
      {
        label: $localize`Elongation`,
        value: this.getFormattedValue(
          this.currentProduct()?.mechanical_properties?.tensile_test
            ?.elongation_percent,
          `%`,
        ),
      },
    ],
  }))

  protected readonly impactTests = computed(() => ({
    title: $localize`Impact Test Results`,
    tests: (
      this.currentProduct()?.mechanical_properties?.impact_tests ?? []
    ).map((test: any) => ({
      fields: [
        {
          label: $localize`Direction`,
          value: this.getFormattedValue(test.test_direction),
        },
        {
          label: $localize`Temperature`,
          value: this.getFormattedValue(test.test_temperature_celsius, `°C`),
        },
        {
          label: `Kv1`,
          value: this.getFormattedValue(test.impact_energy_1_joules, `J`),
        },
        {
          label: `Kv2`,
          value: this.getFormattedValue(test.impact_energy_2_joules, `J`),
        },
        {
          label: `Kv3`,
          value: this.getFormattedValue(test.impact_energy_3_joules, `J`),
        },
        {
          label: $localize`Average`,
          value: this.getFormattedValue(test.impact_energy_average_joules, `J`),
        },
      ],
    })),
  }))

  private getDimensions(dimensions: any): string {
    if (!dimensions) return "N/A"
    const { thickness, width, length } = dimensions
    return [thickness, width, length]
      .map((val) => this.getFormattedValue(val))
      .join(" x ")
  }

  private getFormattedValue(value: any, unit?: string): string {
    if (value === null || value === undefined) return "N/A"

    // If it's a string, parse it and remove insignificant zeros
    if (typeof value === "string") {
      const num = parseFloat(value)
      if (!isNaN(num)) {
        const formatted = num.toString()
        return unit ? `${formatted} ${unit}` : formatted
      }
      return value
    }

    // For numbers, convert to string without insignificant zeros
    if (typeof value === "number") {
      if (isNaN(value)) return "N/A"
      const formatted = value.toString()
      return unit ? `${formatted} ${unit}` : formatted
    }

    return value.toString()
  }

  protected getFieldClasses(item: ChemicalStatus): string {
    const baseClasses =
      "peer h-7 w-full rounded-lg border-gray-300 text-xs text-gray-700 shadow-sm"

    switch (item.status) {
      case "nok":
        return `${baseClasses} bg-qw-warn-50 border-qw-warn-300`
      case "ok":
        return `${baseClasses} bg-qw-primary-50 border-qw-primary-300`
      default:
        return `${baseClasses} bg-gray-50`
    }
  }

  protected getLabelClasses(item: ChemicalStatus): string {
    const baseClasses = "text-gray-600"

    switch (item.status) {
      case "nok":
        return `${baseClasses} text-qw-warn-600`
      case "ok":
        return `${baseClasses} text-qw-primary-600`
      default:
        return baseClasses
    }
  }
}
