import io
from pathlib import PurePosixPath
from typing import Any, cast

import fitz
import pymupdf
from celery import current_app
from PIL import Image
from sqlalchemy.orm import Session as RdbSession

from qw_log_interface import NO_LOG_FACTORY, Logger
from qw_monodb.table.trunk.material_certificate import MaterialCertificatePage
from qw_trunk.service.material_certificate.db import MaterialCertificateDatabaseService
from qw_trunk.service.material_certificate.material_certificate_analysis_client import MaterialCertificateAnalysisClient
from qw_trunk.service.resource.file import AddFileResourceRevisionHook, FileRevisionData, InvalidPdfError
from qw_trunk.service.resource.s3_layout import FileResourceFolderLayout
from qw_trunk.service.resource.s3_object import S3ObjectInput, S3ObjectInputData, S3ObjectService


class AddMaterialCertificateRevisionHook(AddFileResourceRevisionHook):
    def __init__(
        self,
        db_service: MaterialCertificateDatabaseService,
        s3_obj_service: S3ObjectService,
        logger: Logger,
        page_dpi: int = 150,
    ):
        self.db_service = db_service
        self.s3_obj_service = s3_obj_service
        self.page_dpi = page_dpi
        self.logger = logger
        self.page_images: list[tuple[io.BytesIO, str]] = []  # (bio, mime_type) pairs
        self.pdf_data: bytes | None = None
        self.pending_analysis_task: dict[str, Any] | None = None

        self.analysis_client = MaterialCertificateAnalysisClient(rdb=db_service.rdb, lf=NO_LOG_FACTORY)

    def _convert_pdf_page_to_image(self, page: pymupdf.Page, dpi: int = 150) -> tuple[io.BytesIO, str]:
        """Convert PDF page to WebP image."""
        bio = io.BytesIO()
        zoom = dpi / 72.0
        matrix = pymupdf.Matrix(zoom, zoom)
        pix = page.get_pixmap(matrix=matrix)

        if pix.alpha and pix.n == 4:
            img = Image.frombytes("RGBA", (pix.width, pix.height), pix.samples)
        else:
            img = Image.frombytes("RGB", (pix.width, pix.height), pix.samples)

        img.save(bio, format="WEBP", lossless=True)
        bio.seek(0)
        return bio, "image/webp"

    def before_file_resource_revision_stored(
        self, rev_data: FileRevisionData, session: RdbSession
    ) -> None:  # noqa: ARG002
        """Convert and store images before any analysis with page limit validation."""
        if self.page_images:
            raise RuntimeError("Processing was already performed")

        # Read the PDF and validate page count
        pdf_bio = io.BytesIO(rev_data.data.read())
        pdf_doc = fitz.open(stream=pdf_bio, filetype="pdf")

        try:
            page_count = len(pdf_doc)

            # Validate page limit (reject 10+ pages)
            if page_count > 10 or page_count == 0:
                raise InvalidPdfError(f"Material certificate has {page_count} pages. Should be 1-10 pages.")

            # Set page_count on FileRevisionData for database storage
            rev_data.page_count = page_count

            # Convert pages to images directly
            self.page_images = []
            for page in pdf_doc:
                bio, mime_type = self._convert_pdf_page_to_image(page, self.page_dpi)
                self.page_images.append((bio, mime_type))

            # Store PDF data for analysis
            self.pdf_data = pdf_bio.getvalue()

            self.logger.info(f"Successfully converted {page_count} pages (within limit)")

        except Exception as e:
            self.logger.error(f"Failed to process PDF: {str(e)}")
            raise
        finally:
            pdf_doc.close()
            rev_data.data.seek(0)  # Reset read pointer

    def __add_resource(
        self, bucket: str, path: PurePosixPath, bio: io.BytesIO, content_type: str, session: RdbSession
    ) -> int:
        # Reset buffer position to start and verify content
        bio.seek(0)
        size = bio.getbuffer().nbytes
        if size == 0:
            raise ValueError(f"Empty buffer provided for {path}")

        self.logger.info(f"Adding resource {path}, size: {size} bytes")

        s3_input = S3ObjectInput(
            bucket=bucket,
            path=path,
            data=S3ObjectInputData(
                bio=bio,
                size=size,
                content_type=content_type,
            ),
        )
        return self.s3_obj_service.add_object(s3_input, session=session)

    def after_file_resource_revision_stored(
        self,
        file_resource_revision_id: int,
        folder_layout: FileResourceFolderLayout,
        tenant_bucket: str,
        session: RdbSession | None = None,
    ) -> None:
        """Two-phase process: Phase 1 during transaction, Phase 2 after transaction."""
        if session is not None:
            # Phase 1: Called during transaction - store pages and prepare analysis
            self._store_pages_and_prepare_analysis(file_resource_revision_id, folder_layout, tenant_bucket, session)
        else:
            # Phase 2: Called after transaction - queue analysis task
            self._queue_analysis_if_ready(file_resource_revision_id)

    def _store_pages_and_prepare_analysis(
        self,
        file_resource_revision_id: int,
        folder_layout: FileResourceFolderLayout,
        tenant_bucket: str,
        session: RdbSession,
    ) -> None:
        """Phase 1: Store pages and prepare analysis (called during transaction)."""
        if not self.page_images:
            raise RuntimeError("No processing results available")

        # Store pages first
        for page_index, (page_bio, mime_type) in enumerate(self.page_images):
            try:
                image_ref_id = self.__add_resource(
                    tenant_bucket,
                    folder_layout.path_to_material_certificate_page_image(page_index),
                    page_bio,
                    mime_type,
                    session=session,
                )

                page_id = self.db_service.material_certificate_page.add(
                    obj=MaterialCertificatePage(
                        file_resource_revision_id=file_resource_revision_id,
                        page_index=page_index,
                        image_obj_id=image_ref_id,
                    ),
                    session=session,
                )
                self.logger.info(f"Stored page {page_id} (index={page_index})")
            except Exception as e:
                self.logger.error(f"Failed to store page {page_index}: {str(e)}")
                raise

        # Prepare analysis task parameters for Phase 2
        if self.pdf_data:
            analysis_task_params = self.analysis_client.start_analysis(
                bucket=tenant_bucket,
                file_resource_revision_id=file_resource_revision_id,
                pdf_data=self.pdf_data,
                session=session,
            )
            # Store for Phase 2
            self.pending_analysis_task = analysis_task_params

            analysis_id = analysis_task_params["analysis_id"]
            self.logger.info(f"Prepared analysis task for analysis_id {analysis_id}")

    def _queue_analysis_if_ready(self, file_resource_revision_id: int) -> None:
        """Phase 2: Queue analysis task if prepared (called after transaction)."""
        if hasattr(self, "pending_analysis_task") and self.pending_analysis_task:
            try:
                task_result = cast(Any, current_app.send_task)(
                    self.pending_analysis_task["task_name"], kwargs=self.pending_analysis_task["kwargs"]
                )
                self.logger.info(f"Queued analysis task {task_result.id} for file revision {file_resource_revision_id}")
            except Exception as e:
                self.logger.error(f"Failed to queue analysis task for file revision {file_resource_revision_id}: {e}")
                # Don't raise - we don't want to fail if task queueing fails
            finally:
                # Clear the pending task
                self.pending_analysis_task = None
