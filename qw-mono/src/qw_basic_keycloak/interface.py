from typing import Protocol, Sequence

from pydantic import AnyHttpUrl, BaseModel

from qw_basic_keycloak.openid.dtos.authorization import AuthenticationRequest, AuthenticationResponse
from qw_basic_keycloak.openid.dtos.token import TokenResponse
from qw_basic_keycloak.openid.inspector import TokenInspector


class CodeFlowAuthInit(BaseModel):
    """
    All information that the user agent needs to initiate the authentication. The req needs to be sent encoded as
    application/x-www-form-urlencoded either with GET in the query string or with POST in the body.
    https://openid.net/specs/openid-connect-core-1_0.html#AuthRequest
    https://datatracker.ietf.org/doc/html/rfc6749#section-3.1
    """

    auth_url: AnyHttpUrl
    req: AuthenticationRequest


class CodeFlowAuthMock(Protocol):
    """
    Interfaces for mocks that implement/fake the authentication. This is only intended for testing.
    """

    def auth(self, auth_init: CodeFlowAuthInit) -> AuthenticationResponse:
        ...


class OpenIdConnectError(Exception):
    ...


class OpenIdClient(Protocol):
    def get_redirect_uri(self) -> str:
        ...

    def flow_code_initiate(self, state: str, nonce: str, additional_scopes: Sequence[str] = ()) -> CodeFlowAuthInit:
        """
        ..
          .---------.                                        .------------.                     .--------------.
          |UserAgent|                                        |RelyingParty|                     |OpenIdProvider|
          '---------'                                        '------------'                     '--------------'
               |                                                   |                                   |
               |                 Accesses Resource                 |                                   |
               |-------------------------------------------------->|                                   |
               |                                                   |                                   |
               |   Access Denied (no/expired RP Session Cookie)    |                                   |
               |<--------------------------------------------------|                                   |
               |                                                   |                                   |
               |                    Start Login                    |                                   |
               |-------------------------------------------------->|                                   |
               |                                                   |                                   |
               |                Code Flow Init Data                |                                   |
               |<--------------------------------------------------|                                   |
               |                                                   |                                   |
               |                                    Start Code Flow|                                   |
               |-------------------------------------------------------------------------------------->|
               |                                                   |                                   |
               |                              Redirect to Authentication                               |
               |<--------------------------------------------------------------------------------------|
               |                                                   |                                   |
               |            Authenticates (maybe skipped if still active OP Session Cookie)            |
               |-------------------------------------------------------------------------------------->|
               |                                                   |                                   |
               |                             Redirect After Authentication                             |
               |<--------------------------------------------------------------------------------------|
               |                                                   |                                   |
               |           Redirect After Authentication           |                                   |
               |-------------------------------------------------->|                                   |
               |                                                   |                                   |
               |                                                   |Request Access Token with Auth Code|
               |                                                   |---------------------------------->|
               |                                                   |                                   |
               |                                                   |       Receive Access Token        |
               |                                                   |<----------------------------------|
               |                                                   |                                   |
               |Redirect to Resource (state), set RP Session Cookie|                                   |
               |<--------------------------------------------------|                                   |
               |                                                   |                                   |
          .---------.                                        .------------.                     .--------------.
          |UserAgent|                                        |RelyingParty|                     |OpenIdProvider|
          '---------'                                        '------------'                     '--------------'


        For reference see also https://openid.net/specs/openid-connect-core-1_0.html#CodeFlowSteps.
        However, this flow chart is extended with personal recommendation of the relying party session,
        OpenId Connect should not be used on every single request to the service.

        The chart was generated with https://arthursonzogni.com/Diagon/#Sequence, see input below::

          UserAgent -> RelyingParty: Accesses Resource
          RelyingParty -> UserAgent: Access Denied (no/expired RP Session Cookie)
          UserAgent -> RelyingParty: Start Login
          RelyingParty -> UserAgent: Code Flow Init Data
          UserAgent -> OpenIdProvider: Start Code Flow
          OpenIdProvider -> UserAgent: Redirect to Authentication
          UserAgent -> OpenIdProvider: Authenticates (maybe skipped if still active OP Session Cookie)
          OpenIdProvider -> UserAgent: Redirect After Authentication
          UserAgent -> RelyingParty: Redirect After Authentication
          RelyingParty -> OpenIdProvider: Request Access Token with Auth Code
          OpenIdProvider -> RelyingParty: Receive Access Token
          RelyingParty -> UserAgent: Redirect to Resource (state), set RP Session Cookie

        """
        ...

    def flow_code_conclude(self, authorization_code: str, timeout_seconds: float = 10) -> TokenResponse:
        """Can raise OpenIdConnectError"""
        ...

    def try_refresh_token(self, refresh_token: str, timeout_seconds: float = 10) -> TokenResponse:
        """Can raise OpenIdConnectError"""
        ...

    def initiate_logout(
        self, id_token: str, post_logout_redirect_uri: str | None = None, timeout_seconds: float = 10
    ) -> None:
        """Can raise OpenIdConnectError"""
        # https://openid.net/specs/openid-connect-rpinitiated-1_0.html
        ...


class IdentityAccessManagement(Protocol):
    def get_openid_issuer(self) -> str:
        ...

    def get_openid_client(self) -> OpenIdClient:
        ...

    def get_openid_token_inspector(self) -> TokenInspector:
        ...

    def get_health(self) -> bool:
        ...
