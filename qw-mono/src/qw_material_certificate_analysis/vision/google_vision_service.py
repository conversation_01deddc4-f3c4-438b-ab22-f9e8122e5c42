"""Google Cloud Vision API service for material certificate analysis."""
import asyncio
import base64
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import cv2
import numpy as np
import numpy.typing as npt
import requests

from qw_log_interface import NO_LOGGER, Logger
from qw_material_certificate_analysis.vision.models import BoundingPoly, DocumentText, TextBlock, TextElement


class GoogleCloudVisionApiService:
    """Google Cloud Vision API service for material certificate text extraction."""

    def __init__(
        self,
        api_key: str,
        debug_mode: bool = False,
        debug_output_dir: Optional[Path] = None,
        logger: Logger = NO_LOGGER,
    ):
        """Initialize the Google Cloud Vision API service."""
        self.api_key = api_key
        self.debug_mode = debug_mode
        self.debug_output_dir = debug_output_dir
        self.logger = logger

    async def extract_text_from_image(self, image: npt.NDArray[np.uint8]) -> Tuple[DocumentText, Dict[str, Any]]:
        """
        Extract text from image using Google Cloud Vision API.

        Args:
            image: Image as numpy array

        Returns:
            Tuple of (DocumentText with extracted text, raw API response)
        """
        try:
            # Scale image if needed to avoid API limits
            scaled_image, scale_x, scale_y, original_dimensions = self._scale_image_if_needed(image)

            # Log scaling information
            if scale_x != 1.0 or scale_y != 1.0:
                self.logger.info(
                    f"Image scaled for Google Vision API (scale factors: x={scale_x:.3f}, y={scale_y:.3f})"
                )
                self.logger.info(f"Original dimensions: {original_dimensions[0]}x{original_dimensions[1]}")

            # Convert numpy array to base64
            _, buffer = cv2.imencode(".png", scaled_image)
            content = base64.b64encode(buffer.tobytes()).decode("utf-8")

            # Prepare API request
            payload = {"requests": [{"image": {"content": content}, "features": [{"type": "DOCUMENT_TEXT_DETECTION"}]}]}

            # Make API request
            url = f"https://vision.googleapis.com/v1/images:annotate?key={self.api_key}"
            response = await asyncio.to_thread(lambda: requests.post(url, json=payload))
            response.raise_for_status()
            result = response.json()

            # Process response
            document_text = self._process_vision_response(result, scale_x, scale_y)

            # Save debug files if enabled
            if self.debug_mode and self.debug_output_dir:
                self._save_debug_files(document_text, result)

            return document_text, result

        except Exception as e:
            self.logger.error(f"Error in Google Vision API text extraction: {str(e)}")
            return DocumentText(text="", text_blocks=[]), {}

    def _scale_image_if_needed(
        self, image: npt.NDArray[np.uint8]
    ) -> Tuple[npt.NDArray[np.uint8], float, float, Tuple[int, int]]:
        """Scale image if it exceeds Google Vision API limits."""
        height, width = image.shape[:2]
        original_dimensions = (width, height)

        # Google Vision API limits: 75 megapixels, max dimension 20,000 pixels
        max_pixels = 75_000_000
        max_dimension = 20_000

        current_pixels = width * height
        scale_x = scale_y = 1.0

        # Check if scaling is needed
        if current_pixels > max_pixels or width > max_dimension or height > max_dimension:
            # Calculate scale factor
            pixel_scale = (max_pixels / current_pixels) ** 0.5
            dimension_scale_x = max_dimension / width if width > max_dimension else 1.0
            dimension_scale_y = max_dimension / height if height > max_dimension else 1.0

            # Use the most restrictive scale
            scale_x = scale_y = min(pixel_scale, dimension_scale_x, dimension_scale_y)

            # Apply scaling
            new_width = int(width * scale_x)
            new_height = int(height * scale_y)
            scaled_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA).astype(np.uint8)

            return scaled_image, scale_x, scale_y, original_dimensions

        return image, scale_x, scale_y, original_dimensions

    def _process_vision_response(self, result: Dict[str, Any], scale_x: float, scale_y: float) -> DocumentText:
        """Process Google Vision API response into DocumentText."""
        full_text = ""
        text_blocks: List[TextBlock] = []

        if "responses" in result and len(result["responses"]) > 0:
            response_data = result["responses"][0]

            # Get full text
            if "fullTextAnnotation" in response_data:
                full_text = response_data["fullTextAnnotation"].get("text", "")

            # Process text blocks
            if "fullTextAnnotation" in response_data and "pages" in response_data["fullTextAnnotation"]:
                for page in response_data["fullTextAnnotation"]["pages"]:
                    for block in page.get("blocks", []):
                        block_text = ""
                        block_elements = []

                        # Get block bounding box
                        block_vertices = []
                        if "boundingBox" in block:
                            block_vertices = block["boundingBox"].get("vertices", [])

                        # Process paragraphs in block
                        for paragraph in block.get("paragraphs", []):
                            para_text = ""

                            # Process words in paragraph
                            for word in paragraph.get("words", []):
                                word_text = ""
                                word_vertices = []

                                if "boundingBox" in word:
                                    word_vertices = word["boundingBox"].get("vertices", [])

                                # Get symbols in word
                                for symbol in word.get("symbols", []):
                                    word_text += symbol.get("text", "")

                                para_text += word_text + " "

                                # Create text element for word
                                if word_text.strip():
                                    word_poly = BoundingPoly(vertices=word_vertices)
                                    if scale_x != 1.0 or scale_y != 1.0:
                                        word_poly = self._transform_coordinates(word_poly, scale_x, scale_y)

                                    element = TextElement(text=word_text.strip(), bounding_poly=word_poly)
                                    block_elements.append(element)

                            block_text += para_text + "\n"

                        # Create text block
                        if block_text.strip():
                            block_poly = BoundingPoly(vertices=block_vertices)
                            if scale_x != 1.0 or scale_y != 1.0:
                                block_poly = self._transform_coordinates(block_poly, scale_x, scale_y)

                            text_block = TextBlock(
                                text=block_text.strip(), bounding_poly=block_poly, elements=block_elements
                            )
                            text_blocks.append(text_block)

        return DocumentText(text=full_text, text_blocks=text_blocks)

    def _transform_coordinates(self, poly: BoundingPoly, scale_x: float, scale_y: float) -> BoundingPoly:
        """Transform coordinates back to original image scale."""
        transformed_vertices = []
        for vertex in poly.vertices:
            transformed_vertex = {"x": int(vertex.get("x", 0) / scale_x), "y": int(vertex.get("y", 0) / scale_y)}
            transformed_vertices.append(transformed_vertex)

        return BoundingPoly(vertices=transformed_vertices)

    def _save_debug_files(self, document_text: DocumentText, raw_response: Dict[str, Any]) -> None:
        """Save debug files for analysis."""
        if not self.debug_output_dir:
            return

        try:
            debug_dir = Path(self.debug_output_dir)
            debug_dir.mkdir(parents=True, exist_ok=True)

            # Save processed results
            processed_results = {
                "text": document_text.text,
                "text_blocks": [
                    {
                        "text": block.text,
                        "bounding_poly": {"vertices": block.bounding_poly.vertices},
                        "elements": [
                            {
                                "text": element.text,
                                "bounding_poly": {"vertices": element.bounding_poly.vertices},
                            }
                            for element in block.elements
                        ],
                    }
                    for block in document_text.text_blocks
                ],
            }

            # Save processed results
            processed_file = debug_dir / "google_vision_processed.json"
            with open(processed_file, "w") as f:
                json.dump(processed_results, f, indent=2)

            # Save raw response
            raw_file = debug_dir / "google_vision_raw_response.json"
            with open(raw_file, "w") as f:
                json.dump(raw_response, f, indent=2)

            self.logger.info(f"Saved Google Vision debug files to {debug_dir}")

        except Exception as e:
            self.logger.error(f"Error saving Google Vision debug files: {e}")
