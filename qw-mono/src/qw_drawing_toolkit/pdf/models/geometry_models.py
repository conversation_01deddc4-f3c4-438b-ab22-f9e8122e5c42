from dataclasses import dataclass
from enum import Enum, unique


@unique
class LineDirection(Enum):
    HORIZONTAL = 1
    VERTICAL = 2


@dataclass
class LineSelection:
    only_dir: LineDirection | None = None
    min_norm: float | None = None
    max_norm: float | None = None
    min_x1: float | None = None
    max_x1: float | None = None
    min_x2: float | None = None
    max_x2: float | None = None
    min_y1: float | None = None
    max_y1: float | None = None
    min_y2: float | None = None
    max_y2: float | None = None
