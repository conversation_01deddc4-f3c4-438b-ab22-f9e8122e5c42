{"name": "DIN_6930-2:2011-10_ueber_10", "tables": [{"name": "Tabelle 1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 4, "lower": -4}}]}, {"name": "Tabelle 2", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 10, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 10, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 10, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 10, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 6, "lower": -6}}]}, {"name": "Tabelle 4", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 2.4, "lower": -2.4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 3.2, "lower": -3.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 8, "lower": -8}}]}, {"name": "Tabelle 5", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 10, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["f"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 10, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["m"]], "tolerance": {"upper": 15, "lower": -15}}, {"lowerValueLimit": {"value": 63, "inclusive": true}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 25, "lower": -25}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 40, "lower": -40}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["g"]], "tolerance": {"upper": 60, "lower": -60}}, {"lowerValueLimit": {"value": 63, "inclusive": true}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 25, "lower": -25}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 40, "lower": -40}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["sg"]], "tolerance": {"upper": 60, "lower": -60}}]}]}