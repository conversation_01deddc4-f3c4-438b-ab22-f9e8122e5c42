"""
Vision processing module for OCR results.
This module provides functionality to process OCR results using Google Vision API
for document text detection from technical drawings.
"""
import asyncio
import base64
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import cv2
import numpy as np
import numpy.typing as npt
import requests

from qw_drawing_toolkit_ocr.bbox_detection.models import OcrR<PERSON>ult
from qw_drawing_toolkit_ocr.pre_processing.models import BoundingPoly, DocumentText, TextBlock, TextElement
from qw_log_interface import NO_LOGGER, Logger


class VisionProcessor:
    """
    Class for processing OCR results using Google Vision API.
    """

    def __init__(
        self,
        google_api_key: str,
        save_images_for_debugging: bool,
        save_json_for_debugging: bool,
        output_dir: Optional[Path] = None,
        logger: Logger = NO_LOGGER,
    ):
        """
        Initialize the VisionProcessor.

        Args:
            google_api_key: Google Vision API key
            save_images_for_debugging: Whether to save images for debugging
            save_json_for_debugging: Whether to save JSON for debugging
            output_dir: Optional directory to save results to
            logger: Logger instance
        """
        self.google_api_key = google_api_key
        self.logger = logger
        self.output_dir = output_dir
        self.save_images_for_debugging = save_images_for_debugging
        self.save_json_for_debugging = save_json_for_debugging

    def _scale_image_if_needed(
        self, image: npt.NDArray[np.uint8], max_pixels: int = 70_000_000
    ) -> Tuple[npt.NDArray[np.uint8], float, float, Tuple[int, int]]:
        """
        Scale the image if it exceeds the maximum number of pixels.

        Args:
            image: The image to scale
            max_pixels: Maximum number of pixels allowed (default: 70M)

        Returns:
            Tuple of (scaled_image, scale_x, scale_y, original_dimensions)
        """
        # Get original dimensions
        h, w = image.shape[:2]
        original_dimensions = (w, h)

        # Calculate total pixels
        total_pixels = h * w

        # If under the limit, return the original image
        if total_pixels <= max_pixels:
            return image, 1.0, 1.0, original_dimensions

        # Calculate scaling factor
        scale_factor = np.sqrt(max_pixels / total_pixels)

        # Calculate new dimensions (maintain aspect ratio)
        new_w = int(w * scale_factor)
        new_h = int(h * scale_factor)

        # Scale the image
        scaled_image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)

        # Calculate actual scaling factors
        scale_x = new_w / w
        scale_y = new_h / h

        self.logger.info(
            f"Scaled image from {w}x{h} ({total_pixels} pixels) to {new_w}x{new_h} ({new_w * new_h} pixels)"
        )

        # Ensure the scaled image has the correct type
        return np.asarray(scaled_image, dtype=np.uint8), scale_x, scale_y, original_dimensions

    def _transform_coordinates_to_original(
        self, bounding_poly: BoundingPoly, scale_x: float, scale_y: float
    ) -> BoundingPoly:
        """
        Transform coordinates from scaled space back to original space.

        Args:
            bounding_poly: BoundingPoly with coordinates in scaled space
            scale_x: X scaling factor
            scale_y: Y scaling factor

        Returns:
            BoundingPoly with coordinates in original space
        """
        transformed_vertices = []

        for vertex in bounding_poly.vertices:
            # Transform coordinates back to original space
            transformed_vertex = {"x": int(vertex["x"] / scale_x), "y": int(vertex["y"] / scale_y)}
            transformed_vertices.append(transformed_vertex)

        return BoundingPoly(vertices=transformed_vertices)

    async def process_with_google_vision(
        self,
        image: npt.NDArray[np.uint8],
    ) -> Tuple[DocumentText, Dict[str, Any]]:
        """
        Use Google Cloud Vision API to perform document text detection on the image.
        This implementation uses the REST API directly with an API key.

        Args:
            image: The full processed image as a numpy array.
            output_dir: Optional directory to save the raw Google Vision API response.

        Returns:
            Tuple of (DocumentText object with the detected text, raw Google Vision API response)
        """
        try:
            # Scale image if needed to avoid Google Vision API limits
            scaled_image, scale_x, scale_y, original_dimensions = self._scale_image_if_needed(image)

            # Log scaling information
            if scale_x != 1.0 or scale_y != 1.0:
                self.logger.info(
                    f"Image scaled for Google Vision API (scale factors: x={scale_x:.3f}, y={scale_y:.3f})"
                )
                self.logger.info(f"Original dimensions: {original_dimensions[0]}x{original_dimensions[1]}")

            # Convert numpy array to bytes and encode as base64
            _, buffer = cv2.imencode(".png", scaled_image)
            content = base64.b64encode(buffer.tobytes()).decode("utf-8")

            # Prepare request payload for Vision API
            payload = {"requests": [{"image": {"content": content}, "features": [{"type": "DOCUMENT_TEXT_DETECTION"}]}]}

            # Make request to Vision API
            url = f"https://vision.googleapis.com/v1/images:annotate?key={self.google_api_key}"

            # Use asyncio.to_thread to run requests in a separate thread
            response = await asyncio.to_thread(lambda: requests.post(url, json=payload))
            response.raise_for_status()
            result = response.json()

            # Extract text and annotations from response
            full_text = ""
            text_blocks: List[TextBlock] = []

            if "responses" in result and result["responses"]:
                response_data = result["responses"][0]

                # Extract full text from fullTextAnnotation
                if "fullTextAnnotation" in response_data:
                    full_text = response_data["fullTextAnnotation"].get("text", "")

                    # Get individual text elements from textAnnotations
                    word_elements = []
                    if "textAnnotations" in response_data:
                        # Skip the first annotation which is the entire text
                        for annotation in response_data["textAnnotations"][1:]:
                            text = annotation.get("description", "")
                            bounding_poly = annotation.get("boundingPoly", {})
                            vertices = bounding_poly.get("vertices", [])

                            # Create TextElement object with transformed coordinates if needed
                            element_poly = BoundingPoly(vertices=vertices)
                            if scale_x != 1.0 or scale_y != 1.0:
                                element_poly = self._transform_coordinates_to_original(element_poly, scale_x, scale_y)

                            text_element = TextElement(text=text, bounding_poly=element_poly)
                            word_elements.append(text_element)

                    # Process text by paragraphs/blocks and associate with individual elements
                    # Extract text blocks from fullTextAnnotation
                    text_blocks = []
                    if "pages" in response_data["fullTextAnnotation"]:
                        for page in response_data["fullTextAnnotation"]["pages"]:
                            for block in page.get("blocks", []):
                                block_text = ""
                                block_elements = []

                                # Get bounding box for this block
                                block_vertices = []
                                if "boundingBox" in block:
                                    block_vertices = block["boundingBox"].get("vertices", [])

                                # Get all text from paragraphs in this block
                                for paragraph in block.get("paragraphs", []):
                                    para_text = ""

                                    # Get paragraph bounding box
                                    # para_vertices = []
                                    # if "boundingBox" in paragraph:
                                    #     para_vertices = paragraph["boundingBox"].get("vertices", [])

                                    # Collect all words in this paragraph
                                    for word in paragraph.get("words", []):
                                        word_text = ""

                                        # Get word bounding box
                                        word_vertices = []
                                        if "boundingBox" in word:
                                            word_vertices = word["boundingBox"].get("vertices", [])

                                        # Get the word text
                                        word_text_content = "".join(
                                            [s.get("text", "") for s in word.get("symbols", [])]
                                        )

                                        # Only create an element if we have valid vertices
                                        # Skip elements entirely when vertices are not found
                                        matching_element = None
                                        if word_vertices:
                                            # Create element with transformed coordinates if needed
                                            word_poly = BoundingPoly(vertices=word_vertices)
                                            if scale_x != 1.0 or scale_y != 1.0:
                                                word_poly = self._transform_coordinates_to_original(
                                                    word_poly, scale_x, scale_y
                                                )

                                            matching_element = TextElement(
                                                text=word_text_content,
                                                bounding_poly=word_poly,
                                            )

                                        if matching_element:
                                            block_elements.append(matching_element)

                                        # Build word text with proper spacing
                                        for symbol in word.get("symbols", []):
                                            word_text += symbol.get("text", "")

                                            # Check for breaks
                                            if "property" in symbol and "detectedBreak" in symbol["property"]:
                                                break_type = symbol["property"]["detectedBreak"].get("type", "")
                                                if break_type in ["SPACE", "SURE_SPACE"]:
                                                    word_text += " "
                                                elif break_type in ["LINE_BREAK", "EOL_SURE_SPACE"]:
                                                    word_text += "\n"

                                        para_text += word_text

                                    block_text += para_text

                                # Transform block vertices if needed
                                transformed_block_vertices = block_vertices
                                if (scale_x != 1.0 or scale_y != 1.0) and block_vertices:
                                    block_poly = BoundingPoly(vertices=block_vertices)
                                    transformed_block_poly = self._transform_coordinates_to_original(
                                        block_poly, scale_x, scale_y
                                    )
                                    transformed_block_vertices = transformed_block_poly.vertices

                                # Split block text by line breaks and create separate blocks
                                if block_text.strip():
                                    lines = block_text.strip().split("\n")

                                    # If there are multiple lines, create separate blocks
                                    if len(lines) > 1:
                                        for line in lines:
                                            if line.strip():
                                                # Find elements that belong to this line
                                                line_elements = [
                                                    element for element in block_elements if element.text in line
                                                ]

                                                # Create TextBlock object for this line
                                                text_block = TextBlock(
                                                    text=line.strip(),
                                                    bounding_poly=BoundingPoly(vertices=transformed_block_vertices),
                                                    elements=line_elements,
                                                )
                                                text_blocks.append(text_block)
                                    else:
                                        # Create TextBlock object for the single line
                                        text_block = TextBlock(
                                            text=block_text.strip(),
                                            bounding_poly=BoundingPoly(vertices=transformed_block_vertices),
                                            elements=block_elements,
                                        )
                                        text_blocks.append(text_block)

            # Save results if output_dir is provided and debugging is enabled
            if self.save_json_for_debugging and self.output_dir is not None:
                try:
                    # Save the processed results as JSON
                    processed_results = {
                        "text": full_text,
                        "text_blocks": [
                            {
                                "text": block.text,
                                "bounding_poly": {"vertices": block.bounding_poly.vertices},
                                "elements": [
                                    {
                                        "text": element.text,
                                        "bounding_poly": {"vertices": element.bounding_poly.vertices},
                                    }
                                    for element in block.elements
                                ],
                            }
                            for block in text_blocks
                        ],
                    }

                    processed_file = self.output_dir / "2_google_vision.json"
                    with open(processed_file, "w") as f:
                        json.dump(processed_results, f, indent=2)
                    self.logger.info(f"Saved processed Google Vision results to {processed_file}")
                except Exception as e:
                    self.logger.error(f"Error saving Google Vision results: {str(e)}")
            elif self.save_json_for_debugging and self.output_dir is None:
                self.logger.warning("Cannot save Google Vision results: output_dir is None")

            return DocumentText(text=full_text, text_blocks=text_blocks), result

        except Exception as e:
            self.logger.error(f"Error in document text detection: {str(e)}")
            return DocumentText(text=""), {}

    async def process_ocr_result(
        self,
        image: npt.NDArray[np.uint8],
        ocr_result: OcrResult,
    ) -> Tuple[OcrResult, DocumentText]:
        """
        Process OCR results and perform document text detection with Google Vision.

        Args:
            image: Original image as numpy array
            ocr_result: OCR result from RapidOCR
            output_dir: Directory to save Google Vision results

        Returns:
            Tuple of (original OCR result, document text from Google Vision)
        """
        # Perform document text detection with Google Vision API
        document_text, _ = await self.process_with_google_vision(image)

        return ocr_result, document_text

    def process_ocr_result_sync(
        self,
        image: npt.NDArray[np.uint8],
        ocr_result: OcrResult,
    ) -> Tuple[OcrResult, DocumentText]:
        """
        Synchronous version of process_ocr_result.

        Args:
            image: Original image as numpy array
            ocr_result: OCR result from RapidOCR
            output_dir: Directory to save Google Vision results

        Returns:
            Tuple of (original OCR result, document text from Google Vision)
        """
        # This will handle image scaling and coordinate transformation
        return asyncio.run(self.process_ocr_result(image, ocr_result))
