from collections import Counter
from typing import Dict, List, Sequence

from pydantic import BaseModel, model_validator


class S3BucketDefinition(BaseModel):
    name: str
    # probably more to come


class S3StorageSchema(BaseModel):
    buckets: List[S3BucketDefinition]

    @classmethod
    def create_simple(cls, names: Sequence[str] = ()) -> "S3StorageSchema":
        buckets = [S3BucketDefinition(name=name) for name in names]
        return cls(buckets=buckets)

    @model_validator(mode="after")
    def check_for_unique_name(self) -> "S3StorageSchema":
        counts = Counter([b.name for b in self.buckets])
        for name, count in counts.items():
            if count > 1:
                raise ValueError(f"Bucket name '{name}' is more than once ({count} times)")
        return self

    @property
    def buckets_by_name(self) -> Dict[str, S3BucketDefinition]:
        return {b.name: b for b in self.buckets}
