"""
Grid detection utilities for PMI processing.
This module provides functions for detecting grid elements in technical drawings.
"""
from typing import Callable, Dict, List, Literal, Set, Tuple

from qw_drawing_toolkit_ocr.pre_processing.models import AlignmentLine, BlockInfo, DocumentText, EdgeConfig, TextBlock
from qw_drawing_toolkit_ocr.pre_processing.pmi_geometry import extract_coordinates
from qw_log_interface import NO_LOGGER, Logger


def is_grid_letter_or_number(text: str) -> bool:
    """
    Check if a text is likely a grid letter or number.

    Args:
        text: The text to check

    Returns:
        True if the text is a single letter or a number between 1-7
    """
    # Check if it's a single letter
    if len(text) <= 2 and text.isalpha():
        return True

    # Check if it's a number between 1-7
    try:
        num = int(text)
        return 1 <= num <= 7
    except ValueError:
        return False


def find_alignment_lines(
    edge: Literal["top", "bottom", "left", "right"],
    grid_blocks: List[BlockInfo],
    all_blocks: List[BlockInfo],
    edge_configs: Dict[Literal["top", "bottom", "left", "right"], EdgeConfig],
    alignment_tolerance: float,
    logger: Logger = NO_LOGGER,
) -> List[AlignmentLine]:
    """
    Find precise alignment lines for the specified edge zone.

    Args:
        edge: Edge zone ("top", "bottom", "left", or "right")
        grid_blocks: List of blocks that match the grid pattern
        all_blocks: List of all blocks with their center coordinates
        edge_configs: Configuration for different edges
        alignment_tolerance: Tolerance for alignment in pixels
        logger: Logger instance

    Returns:
        List of alignment lines, each with a coordinate and list of aligned blocks
    """
    # Get edge configuration from the edge_configs dictionary
    edge_config = edge_configs.get(
        edge, EdgeConfig(coord_key="center_y", sort_key=lambda x: 0.0, description="default")
    )
    coord_key: str = edge_config.coord_key
    edge_key = f"in_{edge}"

    # Filter the grid blocks that are in this edge zone
    edge_grid_blocks = [b for b in grid_blocks if getattr(b, edge_key)]

    # If fewer than 2 grid blocks in this edge, there's no clear alignment line
    if len(edge_grid_blocks) < 2:
        return []

    # Group blocks by their alignment coordinate with tolerance
    alignment_groups: Dict[int, List[BlockInfo]] = {}

    for block in edge_grid_blocks:
        coord_value = getattr(block, coord_key)
        # Use integer division with small bucket size to group closely aligned elements
        group_key = int(coord_value / alignment_tolerance)

        if group_key not in alignment_groups:
            alignment_groups[group_key] = []

        alignment_groups[group_key].append(block)

    # Find alignment lines with at least 2 grid elements
    alignment_lines: List[AlignmentLine] = []

    for group_key, blocks in alignment_groups.items():
        if len(blocks) >= 2:
            # Calculate the precise alignment coordinate (average of the group)
            alignment_coord = sum(getattr(b, coord_key) for b in blocks) / len(blocks)

            # Find all blocks (not just grid pattern blocks) that align with this line
            aligned_blocks: List[TextBlock] = []

            for block in all_blocks:
                # Check if this block is closely aligned with the line
                if abs(getattr(block, coord_key) - alignment_coord) <= alignment_tolerance:
                    aligned_blocks.append(block.block)

            alignment_lines.append(AlignmentLine(coordinate=alignment_coord, blocks=aligned_blocks))

            logger.info(
                f"Found {edge} alignment line at {coord_key}={alignment_coord:.1f} "
                f"with {len(aligned_blocks)} blocks"
            )

    # Handle cases when there are multiple parallel bands in the same zone
    # Keep only the most extreme one according to the edge
    if len(alignment_lines) > 1:
        logger.info(f"Found multiple ({len(alignment_lines)}) parallel bands in {edge} zone")

        # Use the edge_config for sorting
        if edge in edge_configs:
            sort_key: Callable[[AlignmentLine], float] = edge_configs[edge].sort_key
            description: str = edge_configs[edge].description

            # Sort based on the appropriate key
            alignment_lines.sort(key=sort_key)
            selected_line = alignment_lines[0]

            # Log with appropriate description and coordinate type
            coord_type = "y" if coord_key == "center_y" else "x"
            logger.info(f"Selected {description} line at {coord_type}={selected_line.coordinate:.1f}")
            return [selected_line]

    return alignment_lines


def find_aligned_elements(
    document_text: DocumentText,
    edge_configs: Dict[Literal["top", "bottom", "left", "right"], EdgeConfig],
    edge_zone_percentage: float = 0.1,
    alignment_tolerance: float = 20.0,
    logger: Logger = NO_LOGGER,
) -> Tuple[List[TextBlock], List[TextBlock]]:
    """
    Find grid elements at the edges of the document.

    Args:
        document_text: Document text from Google Vision
        edge_configs: Configuration for different edges
        edge_zone_percentage: Percentage of document edge zones
        alignment_tolerance: Tolerance for alignment in pixels
        logger: Logger instance

    Returns:
        Tuple of (horizontal_grid_blocks, vertical_grid_blocks)
    """
    # First, find document boundaries from all blocks
    all_x_coords: List[float] = []
    all_y_coords: List[float] = []

    for block in document_text.text_blocks:
        vertices = block.bounding_poly.vertices
        if vertices:
            x_coords, y_coords = extract_coordinates(vertices)
            all_x_coords.extend(x_coords)
            all_y_coords.extend(y_coords)

    # If no coordinate information, return empty results
    if not all_x_coords or not all_y_coords:
        logger.warning("No coordinate information available for grid detection")
        return [], []

    # Calculate document boundaries
    min_x: float = min(all_x_coords)
    max_x: float = max(all_x_coords)
    min_y: float = min(all_y_coords)
    max_y: float = max(all_y_coords)

    # Define edge zones (5% from each edge)
    edge_x_threshold: float = (max_x - min_x) * edge_zone_percentage
    edge_y_threshold: float = (max_y - min_y) * edge_zone_percentage

    # Define the four edge zones
    top_zone: Tuple[float, float] = (min_y, min_y + edge_y_threshold)
    bottom_zone: Tuple[float, float] = (max_y - edge_y_threshold, max_y)
    left_zone: Tuple[float, float] = (min_x, min_x + edge_x_threshold)
    right_zone: Tuple[float, float] = (max_x - edge_x_threshold, max_x)

    # Find all blocks that match the grid pattern (single letter or number 1-7)
    # and store them with their center coordinates
    grid_pattern_blocks: List[BlockInfo] = []
    all_blocks_with_centers: List[BlockInfo] = []

    for block in document_text.text_blocks:
        vertices = block.bounding_poly.vertices
        if not vertices:
            continue

        x_coords, y_coords = extract_coordinates(vertices)
        if not x_coords or not y_coords:
            continue

        center_x: float = sum(x_coords) / len(x_coords)
        center_y: float = sum(y_coords) / len(y_coords)

        # Determine if this block is in an edge zone
        in_top: bool = top_zone[0] <= center_y <= top_zone[1]
        in_bottom: bool = bottom_zone[0] <= center_y <= bottom_zone[1]
        in_left: bool = left_zone[0] <= center_x <= left_zone[1]
        in_right: bool = right_zone[0] <= center_x <= right_zone[1]

        block_info = BlockInfo(
            block=block,
            center_x=center_x,
            center_y=center_y,
            in_top=in_top,
            in_bottom=in_bottom,
            in_left=in_left,
            in_right=in_right,
            is_grid_pattern=is_grid_letter_or_number(block.text.strip()),
        )

        all_blocks_with_centers.append(block_info)

        # Collect blocks that match the grid pattern and are in edge zones
        if block_info.is_grid_pattern and (in_top or in_bottom or in_left or in_right):
            grid_pattern_blocks.append(block_info)

    # Find precise alignment lines for each edge
    horizontal_grid_blocks: List[TextBlock] = []
    vertical_grid_blocks: List[TextBlock] = []

    # Find alignment lines in the top edge zone
    top_alignment_lines = find_alignment_lines(
        "top", grid_pattern_blocks, all_blocks_with_centers, edge_configs, alignment_tolerance, logger
    )

    # Find alignment lines in the bottom edge zone
    bottom_alignment_lines = find_alignment_lines(
        "bottom", grid_pattern_blocks, all_blocks_with_centers, edge_configs, alignment_tolerance, logger
    )

    # Find alignment lines in the left edge zone
    left_alignment_lines = find_alignment_lines(
        "left", grid_pattern_blocks, all_blocks_with_centers, edge_configs, alignment_tolerance, logger
    )

    # Find alignment lines in the right edge zone
    right_alignment_lines = find_alignment_lines(
        "right", grid_pattern_blocks, all_blocks_with_centers, edge_configs, alignment_tolerance, logger
    )

    # Collect all blocks that are aligned with these lines
    for line in top_alignment_lines + bottom_alignment_lines:
        horizontal_grid_blocks.extend(line.blocks)

    for line in left_alignment_lines + right_alignment_lines:
        vertical_grid_blocks.extend(line.blocks)

    # Log statistics about grid elements
    unique_grid_blocks: Set[Tuple[str, Tuple[str, ...]]] = set()
    for block in horizontal_grid_blocks + vertical_grid_blocks:
        unique_grid_blocks.add((block.text.strip(), tuple(str(v) for v in block.bounding_poly.vertices)))

    return horizontal_grid_blocks, vertical_grid_blocks
