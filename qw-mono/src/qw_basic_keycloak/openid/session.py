from typing import <PERSON><PERSON>

from qw_log_interface import NO_LOG_FACTORY, LogFactory


class SessionTokenChecksum(object):
    """
    Simple modulo 256 checksum formatted as hex (i.e. 2 characters long), useful to detect guessed session tokens,
    maybe a bit neurotic, but yeah ...
    """

    @classmethod
    def compute(cls, payload: str, salt: int = 0) -> str:
        payload_sum = sum(payload.encode())
        checksum = (payload_sum + salt) % 256
        return f"{checksum:02x}"

    @classmethod
    def compute_and_format(cls, payload: str, salt: int = 0) -> str:
        checksum = cls.compute(payload, salt)
        return payload + checksum

    @classmethod
    def parse(cls, payload_with_checksum: str) -> Tuple[str, str]:
        payload = payload_with_checksum[:-2]
        checksum = payload_with_checksum[-2:]
        return payload, checksum

    @classmethod
    def verify(cls, payload_with_checksum: str, salt: int = 0) -> bool:
        if len(payload_with_checksum) <= 2:
            return False
        payload, checksum = cls.parse(payload_with_checksum)
        checksum_expected = cls.compute(payload, salt)
        return checksum == checksum_expected


class SessionTokenGateway(object):
    """
    Utility class to attach a checksum to the session id before sending it to external clients for session
    management (usually in cookies).
    """

    def __init__(self, salt: int, lf: LogFactory = NO_LOG_FACTORY):
        self.salt = salt
        self.logger = lf.get_logger(__name__)

    def create_session_token(self, session_id: str) -> str:
        return SessionTokenChecksum.compute_and_format(session_id, self.salt)

    def read_session_token(self, token: str) -> str | None:
        if not SessionTokenChecksum.verify(token, self.salt):
            self.logger.error("Could not verify checksum on session token")
            return None

        session_id, _ = SessionTokenChecksum.parse(token)
        return session_id
