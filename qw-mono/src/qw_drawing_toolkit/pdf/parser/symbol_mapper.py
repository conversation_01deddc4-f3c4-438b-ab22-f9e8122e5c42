from enum import Enum, unique
from typing import List

import cv2 as cv
import numpy as np
import numpy.typing as npt

from qw_drawing_toolkit.pdf.models.base_models import GdtSymbol
from qw_drawing_toolkit.pdf.parser.gdt_symbols import GdtSymbols


@unique
class Symbol(int, Enum):
    UNKNOWN = 0
    DIAMETER = 1
    COUNTERSINK = 2
    DEGREES = 3
    PLUSMINUS = 4
    CIRCULARITY = 5
    SYMMETRY = 6
    FLATNESS = 7
    STRAIGHTNESS = 8
    PARALLELISM = 9
    CONCENTRICITY = 10
    ANGULARITY = 11
    PROFILE_OF_A_SURFACE = 12
    CYLINDRICITY = 13
    POSITION = 14
    PERPENDICULARITY = 15
    PROFILE_OF_A_LINE = 16
    TOTAL_RUNOUT = 17
    CIRCULAR_RUNOUT = 18

    @staticmethod
    def from_int(value: int) -> Enum:
        try:
            return Symbol(value)
        except ValueError:
            return Symbol.UNKNOWN

    @staticmethod
    def get_symbol_string_prefix() -> str:
        return "qw-sym-"

    def get_string_representation(self) -> str:
        return self.get_symbol_string_prefix() + str(self.value)

    @staticmethod
    def get_symbol_dummy_font() -> str:
        return "qw-sym-dummy-font"

    @staticmethod
    def from_string(symbol_string: str) -> Enum:
        prefix = Symbol.get_symbol_string_prefix()
        if symbol_string.startswith(prefix):
            try:
                value = int(symbol_string[len(prefix) :])
                return Symbol.from_int(value)
            except (ValueError, KeyError):
                return Symbol.UNKNOWN
        return Symbol.UNKNOWN


class SymbolMapper:
    font_mappers = {
        "SolidEdgeANSI1Symbols": {
            "u": Symbol.PLUSMINUS,
            "v": Symbol.DEGREES,
            "O": Symbol.DIAMETER,
            "s": Symbol.COUNTERSINK,
            "e": Symbol.CIRCULARITY,
            "d": Symbol.SYMMETRY,
            "r": Symbol.FLATNESS,
            "t": Symbol.STRAIGHTNESS,
            "i": Symbol.PARALLELISM,
            "a": Symbol.CONCENTRICITY,
            "g": Symbol.ANGULARITY,
            "h": Symbol.PROFILE_OF_A_SURFACE,
            "j": Symbol.CYLINDRICITY,
            "l": Symbol.POSITION,
            "n": Symbol.PERPENDICULARITY,
            "m": Symbol.PROFILE_OF_A_LINE,
            "_": Symbol.TOTAL_RUNOUT,
            "^": Symbol.CIRCULAR_RUNOUT,
        },
        "SolidEdgeISO1Symbols": {
            "u": Symbol.PLUSMINUS,
            "v": Symbol.DEGREES,
            "O": Symbol.DIAMETER,
            "s": Symbol.COUNTERSINK,
            "e": Symbol.CIRCULARITY,
            "d": Symbol.SYMMETRY,
            "r": Symbol.FLATNESS,
            "t": Symbol.STRAIGHTNESS,
            "i": Symbol.PARALLELISM,
            "a": Symbol.CONCENTRICITY,
            "g": Symbol.ANGULARITY,
            "h": Symbol.PROFILE_OF_A_SURFACE,
            "j": Symbol.CYLINDRICITY,
            "l": Symbol.POSITION,
            "n": Symbol.PERPENDICULARITY,
            "m": Symbol.PROFILE_OF_A_LINE,
            "_": Symbol.TOTAL_RUNOUT,
            "^": Symbol.CIRCULAR_RUNOUT,
        },
        "AIGDT": {
            "`": Symbol.PLUSMINUS,
            # "": Symbol.DEGREES,
            "n": Symbol.DIAMETER,
            "w": Symbol.COUNTERSINK,
            "e": Symbol.CIRCULARITY,
            "i": Symbol.SYMMETRY,
            "c": Symbol.FLATNESS,
            "u": Symbol.STRAIGHTNESS,
            "f": Symbol.PARALLELISM,
            "r": Symbol.CONCENTRICITY,
            "a": Symbol.ANGULARITY,
            "d": Symbol.PROFILE_OF_A_SURFACE,
            "g": Symbol.CYLINDRICITY,
            "j": Symbol.POSITION,
            "b": Symbol.PERPENDICULARITY,
            "k": Symbol.PROFILE_OF_A_LINE,
            "t": Symbol.TOTAL_RUNOUT,
            "h": Symbol.CIRCULAR_RUNOUT,
            # the following AIGDT mappings are not from the official fonts but an attempt to
            # fix the wrong mapping in Spanflug pdfs
            "L": Symbol.POSITION,
            "D": Symbol.PERPENDICULARITY,
            "E": Symbol.FLATNESS,
            "H": Symbol.PARALLELISM,
            "B": Symbol.PLUSMINUS,
        },
    }

    @classmethod
    def is_font_mapped(cls, font: str) -> bool:
        return font in cls.font_mappers.keys()

    @classmethod
    def map_font_cipher_to_symbol(cls, cipher: str, font: str) -> Symbol:
        mapper = cls.font_mappers.get(font)
        if mapper is not None:
            symbol = mapper.get(cipher.strip())
            if symbol is not None:
                return symbol
        return Symbol.UNKNOWN

    @classmethod
    def map_image_to_symbol(cls, image: npt.NDArray[np.uint8], block_size: int = 101) -> GdtSymbol:
        processed_image = cls._preprocess(image, block_size)
        contours = cls._get_contours(processed_image)
        return GdtSymbols.match_contours(contours)

    @classmethod
    def _preprocess(cls, img: npt.NDArray[np.uint8], block_size: int = 101) -> npt.NDArray[np.uint8]:
        adaptive_thresh = cv.adaptiveThreshold(
            img, 255, cv.ADAPTIVE_THRESH_GAUSSIAN_C, cv.THRESH_BINARY_INV, block_size, 2
        )

        kernel = np.ones((3, 3), np.uint8)
        return cv.morphologyEx(adaptive_thresh, cv.MORPH_CLOSE, kernel)  # type: ignore

    @classmethod
    def _get_contours(cls, img: npt.NDArray[np.uint8]) -> List[npt.NDArray[np.float32]]:
        edges = cv.Canny(img, 50, 150, apertureSize=7)
        contours, _ = cv.findContours(edges, cv.RETR_TREE, cv.CHAIN_APPROX_SIMPLE)
        filtered_contours = [cnt for cnt in contours if cv.contourArea(cnt) > 100]
        return list(sorted(filtered_contours, key=cv.contourArea, reverse=True))  # type: ignore
