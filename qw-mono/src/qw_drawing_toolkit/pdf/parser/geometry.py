from typing import List, Sequence

import numpy as np
import numpy.typing as npt
from typing_extensions import Self

from qw_drawing_toolkit.pdf.models.base_models import T_LINE_SEGMENT
from qw_drawing_toolkit.pdf.models.geometry_models import LineDirection, LineSelection
from qw_drawing_toolkit.pdf.utils.geometry_utils import (
    extend_lines,
    find_horizontals_for_vertical_cluster,
    find_vertical_clusters,
)


class LineCollection(object):
    """
    A collection of line segments with efficient operations for filtering and analysis.

    This class stores line segments in a NumPy array for efficient processing and provides
    methods for selecting, filtering, and analyzing lines based on various criteria.
    """

    COL_ID = 0
    COL_X1 = 1
    COL_Y1 = 2
    COL_X2 = 3
    COL_Y2 = 4

    COL_DIR = 5
    COL_NORM = 6

    # # https://en.wikipedia.org/wiki/Polar_coordinate_system (useful for computing intersections)
    # COL_NORMAL_X = 7
    # COL_NORMAL_Y = 8
    # COL_NORMAL_DIST = 9

    def __init__(self, array: npt.NDArray[np.float32], has_direction: bool, has_distance: bool):
        """
        Initialize a LineCollection with a NumPy array of line segments.

        Args:
            array: NumPy array containing line segment data
            has_direction: Whether direction information is computed and stored
            has_distance: Whether distance information is computed and stored
        """
        self.array = array
        self.has_direction = has_direction
        self.has_distance = has_distance

    @classmethod
    def build(
        cls,
        lines: Sequence[T_LINE_SEGMENT],
        compute_direction: bool = True,
        compute_distance: bool = True,
        proximity_tolerance: float = 0,
        extend_lines: bool = False,
    ) -> Self:
        """
        Build a LineCollection from a sequence of line segments.

        Args:
            lines: Sequence of line segments to include in the collection
            compute_direction: Whether to compute and store line directions
            compute_distance: Whether to compute and store line lengths
            proximity_tolerance: Tolerance for determining if lines are horizontal or vertical
            extend_lines: Whether to extend and merge collinear lines

        Returns:
            A new LineCollection instance
        """
        line_count = len(lines)
        array: npt.NDArray[np.float32] = np.zeros((line_count, 7), dtype=np.float32)
        array[:, 0] = np.arange(line_count)
        array[:, 1:5] = [(l1[0], l1[1], l2[0], l2[1]) for (l1, l2) in lines]
        array[:, 5:] = -1

        # if compute_polar:
        #     # array[:, cls.COL_NORMAL_X] =
        #     pass

        if compute_direction:
            mask_horizontal = np.abs(array[:, cls.COL_Y1] - array[:, cls.COL_Y2]) <= proximity_tolerance
            mask_vertical = np.abs(array[:, cls.COL_X1] - array[:, cls.COL_X2]) <= proximity_tolerance
            array[mask_horizontal, cls.COL_DIR] = LineDirection.HORIZONTAL.value
            array[mask_vertical, cls.COL_DIR] = LineDirection.VERTICAL.value

        if compute_distance:
            norm = np.linalg.norm(array[:, [cls.COL_X1, cls.COL_Y1]] - array[:, [cls.COL_X2, cls.COL_Y2]], axis=1)
            array[:, cls.COL_NORM] = norm

        collection = cls(
            array,
            has_direction=compute_direction,
            has_distance=compute_distance,
        )

        if extend_lines:
            return collection._extend_vertical_and_horizontal_lines(proximity_tolerance=proximity_tolerance)
        else:
            return collection

    @classmethod
    def empty(cls) -> Self:
        """
        Create an empty LineCollection.
        """
        return cls(np.zeros((0, 7), dtype=np.float32), has_direction=False, has_distance=False)

    def __len__(self) -> int:
        """
        Get the number of line segments in the collection.

        Returns:
            Number of line segments
        """
        return self.array.shape[0]

    def get_as_line_segment(self, line_index: int) -> T_LINE_SEGMENT:
        """
        Get a line segment at the specified index in the standard format.

        Args:
            line_index: Index of the line segment to retrieve

        Returns:
            Line segment as ((x1, y1), (x2, y2))
        """
        x1 = float(self.array[line_index, LineCollection.COL_X1])
        y1 = float(self.array[line_index, LineCollection.COL_Y1])
        x2 = float(self.array[line_index, LineCollection.COL_X2])
        y2 = float(self.array[line_index, LineCollection.COL_Y2])
        return (x1, y1), (x2, y2)

    def get_all_as_line_segment(self) -> List[T_LINE_SEGMENT]:
        """
        Get all line segments in the collection in the standard format.

        Returns:
            List of all line segments as ((x1, y1), (x2, y2))
        """
        return [self.get_as_line_segment(i) for i in range(len(self))]

    def build_select_mask(self, selection: LineSelection) -> npt.NDArray[np.bool_]:
        """
        Build a boolean mask for selecting lines based on specified criteria.

        Args:
            selection: LineSelection object containing filter criteria

        Returns:
            Boolean mask that can be applied to the array to select matching lines

        Raises:
            ValueError: If no filter criteria are provided
        """
        row_masks: List[npt.NDArray[np.bool_]] = []
        if selection.only_dir is not None:
            row_masks.append(self.array[:, self.COL_DIR] == selection.only_dir.value)
        if selection.min_norm is not None:
            row_masks.append(self.array[:, self.COL_NORM] >= selection.min_norm)
        if selection.max_norm is not None:
            row_masks.append(self.array[:, self.COL_NORM] <= selection.max_norm)
        if selection.min_x1 is not None:
            row_masks.append(self.array[:, self.COL_X1] >= selection.min_x1)
        if selection.max_x1 is not None:
            row_masks.append(self.array[:, self.COL_X1] <= selection.max_x1)
        if selection.min_x2 is not None:
            row_masks.append(self.array[:, self.COL_X2] >= selection.min_x2)
        if selection.max_x2 is not None:
            row_masks.append(self.array[:, self.COL_X2] <= selection.max_x2)
        if selection.min_y1 is not None:
            row_masks.append(self.array[:, self.COL_Y1] >= selection.min_y1)
        if selection.max_y1 is not None:
            row_masks.append(self.array[:, self.COL_Y1] <= selection.max_y1)
        if selection.min_y2 is not None:
            row_masks.append(self.array[:, self.COL_Y2] >= selection.min_y2)
        if selection.max_y2 is not None:
            row_masks.append(self.array[:, self.COL_Y2] <= selection.max_y2)

        if len(row_masks) == 0:
            raise ValueError("At least one filter needs to be provided")

        row_mask = row_masks[0]
        for rm in row_masks[1:]:
            row_mask = row_mask & rm
        return row_mask

    def select(self, selection: LineSelection) -> Self:
        """
        Select line segments that match the specified criteria.

        Args:
            selection: LineSelection object containing filter criteria

        Returns:
            A new LineCollection containing only the selected line segments
        """
        row_mask = self.build_select_mask(selection)
        return self.__class__(
            array=self.array[row_mask, :],
            has_direction=self.has_direction,
            has_distance=self.has_distance,
        )

    def find_table_structures(self, x_tolerance: float = 3.0, y_tolerance: float = 3.0) -> List[List[T_LINE_SEGMENT]]:
        """
        Identify potential table structures in the line collection.

        Tables are identified as sets of vertical lines that form clusters, connected by
        horizontal lines. This method finds such structures based on the specified tolerances.

        Args:
            x_tolerance: Maximum allowed difference in x-coordinates for connecting horizontal lines
            y_tolerance: Maximum allowed difference in y-coordinates for clustering vertical lines

        Returns:
            List of potential table structures, where each structure is a list of line segments
        """
        vertical_lines = self.select(LineSelection(only_dir=LineDirection.VERTICAL)).get_all_as_line_segment()
        horizontal_lines = self.select(LineSelection(only_dir=LineDirection.HORIZONTAL)).get_all_as_line_segment()

        vertical_clusters = find_vertical_clusters(vertical_lines, y_tolerance)

        tables = []
        for cluster in vertical_clusters:
            if len(cluster) > 2:
                table_lines = find_horizontals_for_vertical_cluster(cluster, horizontal_lines, x_tolerance)
                if len(table_lines) > 2:
                    tables.append(table_lines + cluster)

        return tables

    def _extend_vertical_and_horizontal_lines(self, proximity_tolerance: float = 0) -> Self:
        """
        Extend and merge collinear horizontal and vertical lines.

        This internal method separates horizontal and vertical lines, extends them using
        the utility function, and then recombines them with any non-horizontal/vertical lines.

        Args:
            proximity_tolerance: Maximum allowed distance between line segments for merging

        Returns:
            A new LineCollection with extended lines
        """
        horizontal_lines = self.select(LineSelection(only_dir=LineDirection.HORIZONTAL)).get_all_as_line_segment()
        vertical_lines = self.select(LineSelection(only_dir=LineDirection.VERTICAL)).get_all_as_line_segment()

        merged_horizontal_lines = extend_lines(horizontal_lines, LineDirection.HORIZONTAL, proximity_tolerance)
        merged_vertical_lines = extend_lines(vertical_lines, LineDirection.VERTICAL, proximity_tolerance)

        non_horiz_vert_mask = (self.array[:, self.COL_DIR] != LineDirection.HORIZONTAL.value) & (
            self.array[:, self.COL_DIR] != LineDirection.VERTICAL.value
        )
        non_horiz_vert_lines = [self.get_as_line_segment(i) for i in np.where(non_horiz_vert_mask)[0]]

        combined_lines = merged_horizontal_lines + merged_vertical_lines + non_horiz_vert_lines

        return self.build(combined_lines, compute_direction=True, compute_distance=True, extend_lines=False)
