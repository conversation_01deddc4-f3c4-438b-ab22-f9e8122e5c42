{"name": "Tolerances_based_on_decimal_places,_Angles", "tables": [{"name": "Tabelle 1", "valueType": "angular", "valueUnit": "deg", "toleranceUnit": "deg", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["0.5°/0.5°/0.5°/0.5°/0.5°"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["0.5°/0.5°/0.5°/0.5°/0.5°"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["0.5°/0.5°/0.5°/0.5°/0.5°"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["0.5°/0.5°/0.5°/0.5°/0.5°"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["0.5°/0.5°/0.5°/0.5°/0.5°"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["1°/0.3'/0.05'/-/-"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["1°/0.3'/0.05'/-/-"]], "tolerance": {"upper": 0.005, "lower": -0.005}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["1°/0.3'/0.05'/-/-"]], "tolerance": {"upper": 0.00083, "lower": -0.00083}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["1°/0.5°/-/-/-"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["1°/0.5°/-/-/-"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["1°/1°/1°/1°/1°"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["1°/1°/1°/1°/1°"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["1°/1°/1°/1°/1°"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["1°/1°/1°/1°/1°"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["1°/1°/1°/1°/1°"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["2°/1°/0.3'/-/-"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["2°/1°/0.3'/-/-"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["2°/1°/0.3'/-/-"]], "tolerance": {"upper": 0.005, "lower": -0.005}}]}]}