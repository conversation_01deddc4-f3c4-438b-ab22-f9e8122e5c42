{"name": "DIN_1687-3:1980-10", "tables": [{"name": "Tabelle 1", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 15 Formg."]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 15 n. Formg."]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.22, "lower": -0.22}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.26, "lower": -0.26}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.31, "lower": -0.31}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.37, "lower": -0.37}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.44, "lower": -0.44}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 14 Formg."]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.28, "lower": -0.28}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.32, "lower": -0.32}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.36, "lower": -0.36}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.41, "lower": -0.41}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.57, "lower": -0.57}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.64, "lower": -0.64}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["GTA 14 n. Formg."]], "tolerance": {"upper": 1.1, "lower": -1.1}}]}]}