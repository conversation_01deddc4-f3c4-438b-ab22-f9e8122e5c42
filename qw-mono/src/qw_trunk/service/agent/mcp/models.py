from typing import Any, Dict, List, Literal
from pydantic import BaseModel


class MCPServerConfig(BaseModel):
    enabled: bool = True
    host: str = "0.0.0.0"
    port: int = 8000  # Changed from 3001 to 8000 to align with FastMCP default
    transport: Literal["stdio", "sse", "streamable-http"] = "streamable-http"
    api_base_url: str = "http://localhost:8000"
    internal_token: str
    enable_auto_generated_tools: bool = True
    enable_manual_tools: bool = True
    openapi_spec_path: str = "docs/qw-pfoertner-openapi.yaml"

    @classmethod
    def from_config(cls, config_data: Dict[str, Any]) -> "MCPServerConfig":
        """Create MCPServerConfig from configuration dictionary."""
        return cls(**config_data)


class MCPClientConfig(BaseModel):
    internal_server_url: str = "http://qw-mono-dev-mcp:8000/mcp"  # Use internal container network
    external_servers: List[Dict[str, Any]] = []
    keep_clients_alive: bool = True  # Keep FastMCP clients open for long-running services


class MCPSettings(BaseModel):
    server: MCPServerConfig
    client: MCPClientConfig
