/* tslint:disable */
/* eslint-disable */
import { QwChemicalComposition } from '../models/qw-chemical-composition';
import { QwHeaderDetails } from '../models/qw-header-details';
import { QwMechanicalProperties } from '../models/qw-mechanical-properties';

/**
 * [schema: Product] Final aggregated product combining all data sources.
 * Assembled by matching foreign keys from the three main data tables.
 */
export interface QwProduct {

  /**
   * Unique batch/item identifier
   */
  batch_number: string;

  /**
   * Chemical analysis results
   */
  chemical_composition?: (QwChemicalComposition | null);

  /**
   * Header/batch information
   */
  header_details?: (QwHeaderDetails | null);

  /**
   * Heat/Smelt number
   */
  heat_number?: (string | null);

  /**
   * Mechanical test results
   */
  mechanical_properties?: (QwMechanicalProperties | null);
}
