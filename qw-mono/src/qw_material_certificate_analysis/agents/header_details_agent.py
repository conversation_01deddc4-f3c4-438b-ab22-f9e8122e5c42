"""Head<PERSON> details extraction agent with database mindset and foreign key design."""
from typing import List

from pydantic_ai import Agent, RunContext
from pydantic_ai.models.gemini import GeminiModel
from pydantic_ai.providers.google_gla import GoogleGLAProvider

from qw_material_certificate_analysis.agents.models import CertificateMetadata, HeaderDetails


def create_header_details_agent(api_key: str) -> Agent[CertificateMetadata, List[HeaderDetails]]:
    """Create the header details extraction agent with database mindset."""
    provider = GoogleGLAProvider(api_key=api_key)
    model = GeminiModel("gemini-2.5-flash-preview-05-20", provider=provider)

    agent = Agent(
        model=model,
        deps_type=CertificateMetadata,
        output_type=List[HeaderDetails],
    )

    @agent.system_prompt
    async def get_system_prompt(ctx: RunContext[CertificateMetadata]) -> str:
        """Generate system prompt with access to metadata dependencies."""
        metadata = ctx.deps

        return f"""You are a database specialist extracting header/batch details from material certificates.

THINK LIKE A DATABASE: You are inserting records into a "header_details" table.
Each record MUST include batch_number as the primary key for later aggregation.

METADATA CONTEXT (USE THIS INFORMATION):
- Total products to extract: {metadata.total_product_count}
- Expected batch numbers: {metadata.batch_numbers}
- Pages with header details: {metadata.pages_with_header_details}
- Analysis context: {metadata.analysis_context}

EXTRACTION RULES:

1. FOREIGN KEY REQUIREMENTS (CRITICAL):
   - batch_number: REQUIRED - Must match one of: {metadata.batch_numbers}
   - heat_number: OPTIONAL - Secondary key if available
   - These keys will be used to match with chemical and mechanical data later

2. MANUFACTURER INFORMATION:
   - manufacturer: Company name (capitalize first letter only, e.g., "Thyssenkrupp")

3. PRODUCT SPECIFICATIONS:
   - product_description: Detailed description (e.g., "Hot Rolled Heavy Plate")
   - material_grade: Steel grade specification (e.g., "S355J2+N", "S235JR")

4. PHYSICAL DIMENSIONS:
   - thickness_mm: Thickness in millimeters
   - width_mm: Width in millimeters
   - length_mm: Length in millimeters
   - weight_kg: Weight in kilograms

EXTRACTION PROCESS:

1. Focus on pages {metadata.pages_with_header_details} for header details
2. Extract exactly {metadata.total_product_count} HeaderDetails records
3. For each expected batch number {metadata.batch_numbers}:
   - Extract one HeaderDetails record
   - Ensure batch_number matches exactly
   - Include heat_number if found
   - Fill in all available header information

DATABASE MINDSET:
Think: "INSERT INTO header_details (batch_number, heat_number, manufacturer, product_description, material_grade,
thickness_mm, width_mm, length_mm, weight_kg, manufacturer_address) VALUES (...)"

QUALITY CHECKS:
- Every record MUST have a batch_number from: {metadata.batch_numbers}
- Extract exactly {metadata.total_product_count} records
- Dimensions should be in millimeters (convert if necessary)
- Weights should be in kilograms
- Manufacturer names should be properly capitalized

Return a List[HeaderDetails] with one record per product/batch.
"""

    return agent
