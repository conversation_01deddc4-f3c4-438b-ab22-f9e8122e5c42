# QualiWise Backend Coding Assistant

## Project Overview
This is the backend part of the QualiWise project, a Python application using Poetry for dependency management.

## Architecture
- API/Controllers layer: `qw_pfoertner/api/` - HTTP endpoints using Falcon
- Service layer: `qw_trunk/service/` - Business logic
- Persistence layer: `qw_monodb/table/` - Database models/tables
- Common utilities: Various `qw_basic_*` packages for IAM, RDB, S3, etc.

## Coding Conventions

### Type Safety
- Use strict type checking with Python's type hints
- Follow Python's type checking standards
- Persistence layer should define its types

### Dependency Management
- Constructors should define all dependencies
- Keep constructor code basic (store arguments)
- Move transformations to factory methods

### Database Practices
- Use explicit database routines with ORM query interface
- Avoid ORM magic relationships and lazy loading
- Use transaction management with `begin_or_use_session` context manager
- All database modifications should happen in transactions

## Development Environment
- Use VS Code with the extensions and settings listed in README.md
- Development can be done either:
  - In the Docker container using `scripts/dev_enter.sh [-b]`
  - In a local venv (recommended)

## Common Development Tasks
- Run backend in dev mode: `scripts/dev_enter.sh`, `cd qw-mono`, `scripts/dev_entrypoint.sh`
- Generate API code: `scripts/dev_generate_all.sh`
