from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_monodb.table.trunk.resource import FileResourceLabel
from qw_tenant_config.model.config import TenantConfig
from qw_trunk.service.material_certificate.db import MaterialCertificateDatabaseService
from qw_trunk.service.material_certificate.file_hook import AddMaterialCertificateRevisionHook
from qw_trunk.service.resource.file import AddFileResourceRevisionHook, FileResourceHookGenerator
from qw_trunk.service.resource.s3_object import S3ObjectService


class MaterialCertificateService(FileResourceHookGenerator):
    def __init__(
        self,
        db_service: MaterialCertificateDatabaseService,
        s3_service: S3ObjectService,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> None:
        self.db_service = db_service
        self.s3_service = s3_service
        self.logger = lf.get_logger(__name__)

    def generate_hook_for(
        self, file_label: FileResourceLabel, mime_type: str, tenant_config: TenantConfig  # noqa: ARG002
    ) -> AddFileResourceRevisionHook | None:
        is_material_cert_pdf = file_label == FileResourceLabel.MATERIAL_CERTIFICATE and mime_type == "application/pdf"

        if not is_material_cert_pdf:
            return None

        return AddMaterialCertificateRevisionHook(
            db_service=self.db_service,
            s3_obj_service=self.s3_service,
            logger=self.logger,
        )
