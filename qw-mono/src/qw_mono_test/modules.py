import ast
from dataclasses import dataclass
from enum import Enum, unique
from pathlib import Path
from typing import Dict, Generator, List, Sequence, Set, Tuple

import pytest

from qw_mono_test.conftest import PROJECT

SRC = PROJECT / "src"


@unique
class Module(Enum):
    QW_BASIC_KEYCLOAK = "qw_basic_keycloak"
    QW_BASIC_RDB = "qw_basic_rdb"
    QW_BASIC_S3 = "qw_basic_s3"
    QW_BASIC_IAM_POLICY = "qw_basic_iam_policy"
    QW_CONFIG = "qw_config"
    QW_DRAWING_TOLERANCE = "qw_drawing_tolerance"
    QW_DRAWING_TOOLKIT = "qw_drawing_toolkit"
    QW_DRAWING_TOOLKIT_EDITOR = "qw_drawing_toolkit_editor"
    QW_DRAWING_TOOLKIT_EXP = "qw_drawing_toolkit_exp"
    QW_DRAWING_TOOLKIT_OCR = "qw_drawing_toolkit_ocr"
    QW_FALCON_OPENAPI = "qw_falcon_openapi"
    QW_INSPECTION = "qw_inspection"
    QW_MATERIAL_CERTIFICATE_ANALYSIS = "qw_material_certificate_analysis"
    QW_LOG = "qw_log"
    QW_LOG_INTERFACE = "qw_log_interface"
    QW_MONO = "qw_mono"
    QW_MONODB = "qw_monodb"
    QW_PFOERTNER = "qw_pfoertner"
    QW_TENANT_CONFIG = "qw_tenant_config"
    QW_TRUNK = "qw_trunk"
    QW_WOPI = "qw_wopi"
    QW_WORKER = "qw_worker"


DEPS: Dict[Module, List[Module]] = {
    Module.QW_BASIC_IAM_POLICY: [
        Module.QW_BASIC_KEYCLOAK,
        Module.QW_LOG_INTERFACE,
        Module.QW_TENANT_CONFIG,
    ],
    Module.QW_BASIC_KEYCLOAK: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_BASIC_RDB: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_BASIC_S3: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_DRAWING_TOLERANCE: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_DRAWING_TOOLKIT: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_MATERIAL_CERTIFICATE_ANALYSIS: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_MONODB: [
        Module.QW_BASIC_RDB,  # TODO: move now_utc into monodb pacakge?
    ],
    Module.QW_DRAWING_TOOLKIT_EXP: [
        Module.QW_DRAWING_TOOLKIT,
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_DRAWING_TOOLKIT_EDITOR: [
        Module.QW_DRAWING_TOOLKIT,
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_DRAWING_TOOLKIT_OCR: [
        Module.QW_BASIC_S3,
        Module.QW_DRAWING_TOLERANCE,
        Module.QW_LOG,
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_FALCON_OPENAPI: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_INSPECTION: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_MONO: [
        Module.QW_CONFIG,
        Module.QW_LOG,
        Module.QW_PFOERTNER,
        Module.QW_TENANT_CONFIG,
        Module.QW_TRUNK,
    ],
    Module.QW_PFOERTNER: [
        Module.QW_BASIC_IAM_POLICY,
        Module.QW_BASIC_KEYCLOAK,
        Module.QW_BASIC_RDB,
        Module.QW_DRAWING_TOLERANCE,
        Module.QW_DRAWING_TOOLKIT,
        Module.QW_FALCON_OPENAPI,
        Module.QW_LOG_INTERFACE,
        Module.QW_MONODB,
        Module.QW_TENANT_CONFIG,
        Module.QW_TRUNK,
        Module.QW_WORKER,
    ],
    Module.QW_TRUNK: [
        Module.QW_BASIC_IAM_POLICY,
        Module.QW_BASIC_KEYCLOAK,
        Module.QW_BASIC_RDB,
        Module.QW_BASIC_S3,
        Module.QW_DRAWING_TOLERANCE,
        Module.QW_DRAWING_TOOLKIT,
        Module.QW_DRAWING_TOOLKIT_OCR,
        Module.QW_INSPECTION,
        Module.QW_LOG_INTERFACE,
        Module.QW_MONODB,
        Module.QW_TENANT_CONFIG,
        Module.QW_WOPI,
    ],
    Module.QW_TENANT_CONFIG: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_WOPI: [
        Module.QW_LOG_INTERFACE,
    ],
    Module.QW_WORKER: [
        Module.QW_BASIC_RDB,
        Module.QW_BASIC_S3,
        Module.QW_CONFIG,
        Module.QW_DRAWING_TOOLKIT_OCR,
        Module.QW_DRAWING_TOOLKIT,
        Module.QW_LOG_INTERFACE,
        Module.QW_MATERIAL_CERTIFICATE_ANALYSIS,
        Module.QW_MONODB,
        Module.QW_TENANT_CONFIG,
        Module.QW_TRUNK,  # REMOVE IF YOU CAN
    ],
}


def iter_dependencies(module_trail: List[Module]) -> Generator[Sequence[Module], None, None]:
    assert len(module_trail) > 1
    m_first = module_trail[0]
    m_last = module_trail[-1]
    if m_first == m_last:
        yield module_trail  # cyclic dependency
    elif m_last not in DEPS:
        yield module_trail
    else:
        for m in DEPS[m_last]:
            yield from iter_dependencies(list(module_trail) + [m])


def collect_dependencies(module: Module) -> Set[Module]:
    all_dependencies: Set[Module] = set()

    for m_direct in DEPS.get(module, []):
        all_dependencies.add(m_direct)
        for trail in iter_dependencies([module, m_direct]):
            all_dependencies.update(trail[1:])

    return all_dependencies


@dataclass
class ImportStatement:
    path: Path
    lines: Tuple[int, int]
    import_chain: Sequence[str]

    @property
    def lines_str(self) -> str:
        a, b = self.lines
        if a == b:
            return f"line {a}"
        return f"lines {a}-{b}"


def iter_import_statements(path_module: Path) -> Generator[ImportStatement, None, None]:
    # https://stackoverflow.com/a/9049549
    for p in path_module.rglob("*"):
        p_rel = p.relative_to(path_module).resolve()
        if "__pycache__" in p_rel.parts:
            continue

        if p.suffix == ".py":
            with p.open("r") as f:
                src = f.read()
            root = ast.parse(src, filename=str(p))
            for node in ast.iter_child_nodes(root):
                import_chain: List[str] = []
                if isinstance(node, ast.Import):
                    pass
                elif isinstance(node, ast.ImportFrom):
                    if node.module is not None:
                        import_chain = node.module.split(".")
                else:
                    continue

                for n in node.names:  # type: ignore
                    ic = list(import_chain) + [n.name]
                    yield ImportStatement(path=p, import_chain=ic, lines=(node.lineno, node.end_lineno or node.lineno))


class TestModules(object):
    def test_all_modules_are_configured(self) -> None:
        ignore_suffixes = ("_test", "_client", "__pycache__")
        act_modules = set()

        for d in SRC.iterdir():
            if not d.is_dir():
                continue
            dir_name = d.name
            if dir_name.endswith(ignore_suffixes):
                continue
            try:
                module = Module(dir_name)
            except ValueError:
                pytest.fail(f"Source directory '{dir_name}' is unknown")
            act_modules.add(module)

        assert act_modules == set(Module)

    def test_for_cyclic_dependencies(self) -> None:
        for m_origin, m_direct_deps in DEPS.items():
            for m_direct_dep in m_direct_deps:
                for trail in iter_dependencies([m_origin, m_direct_dep]):
                    is_cyclic = trail[0] == trail[-1]
                    assert not is_cyclic, f"Found cyclic dependency: {' -> '.join(m.value for m in trail)}"

    def test_dependency_imports(self) -> None:
        all_imports = set(Module)

        for m in Module:
            all_dependencies = collect_dependencies(m)
            forbidden_imports = all_imports - all_dependencies - {m}
            if len(forbidden_imports) == 0:
                continue

            forbidden_imports_texts = tuple([fi.value for fi in forbidden_imports])
            for stmt in iter_import_statements(SRC / m.value):
                m2_value = stmt.import_chain[0]
                is_forbidden_import = m2_value in forbidden_imports_texts
                if is_forbidden_import:
                    msg = "\n".join(
                        [
                            f"'{m.value}' is not expected to import '{m2_value}', however it happens",
                            f"in file {stmt.path}",
                            f"in {stmt.lines_str}",
                            f"in {'.'.join(stmt.import_chain)}",
                        ]
                    )
                    pytest.fail(msg)
