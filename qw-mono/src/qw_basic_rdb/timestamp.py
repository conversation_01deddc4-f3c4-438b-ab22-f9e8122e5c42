from datetime import datetime, timezone

# # https://stackoverflow.com/questions/13370317/sqlalchemy-default-datetime
# # https://docs.sqlalchemy.org/en/20/core/compiler.html#utc-timestamp-function
# class utcNow(expression.FunctionElement):
#     type = DateTime(timezone=False)
#     inherit_cache = True


# TODO: move timestamp stuff into the DBs?
def now_utc(remove_tzinfo: bool = False) -> datetime:
    dt = datetime.now(timezone.utc)
    if remove_tzinfo:
        dt = dt.replace(tzinfo=None)
    return dt
