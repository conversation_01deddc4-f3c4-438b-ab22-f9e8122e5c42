"""This service is responsible for handling tolerances."""

from typing import Optional, Sequence, Tuple

from qw_drawing_tolerance.collection import ToleranceCollection
from qw_drawing_tolerance.model import ToleranceQueryResult, ToleranceStandard, ToleranceUnit
from qw_drawing_tolerance.query import ToleranceQuery
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class ToleranceService:
    def __init__(self, lf: LogFactory = NO_LOG_FACTORY):
        self.collection = ToleranceCollection.from_default_directory()
        self.logger = lf.get_logger(__name__)

    def list_available_tolerance_tables(self) -> Sequence[ToleranceStandard]:
        """Return all available tolerance standards"""
        return self.collection.list_available_standards()

    def lookup_tolerance(
        self,
        tolerance_name: str,
        table_name: str,
        value: float,
        row_identifiers: Tuple[str, ...],
        value_unit: Optional[ToleranceUnit] = None,
        tolerance_unit: Optional[ToleranceUnit] = None,
    ) -> Optional[ToleranceQueryResult]:
        """Look up tolerance range for given parameters"""
        try:
            standard = self.collection.get_standard(tolerance_name)
            table = next((t for t in standard.tables if t.name == table_name), None)
            if table is None:
                return None

            query = ToleranceQuery(
                row_identifier=row_identifiers,
                value_in=value,
                unit_in=value_unit or table.value_unit,
                unit_out=tolerance_unit,
                in_table_names=[table_name],
                logger=self.logger,
            )

            return query.find_in(standard)
        except (LookupError, ValueError) as e:
            self.logger.error(f"Error looking up tolerance: {e}")
            return None
