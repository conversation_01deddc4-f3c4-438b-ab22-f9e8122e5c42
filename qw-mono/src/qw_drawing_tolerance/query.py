from typing import List, Sequence, Tuple

from qw_drawing_tolerance.model import (
    ToleranceQueryResult,
    ToleranceStandard,
    ToleranceUnit,
    ValueToToleranceRange,
    ValueToToleranceTable,
)
from qw_log_interface import NO_LOGGER, Logger


class ToleranceQuery(object):
    def __init__(
        self,
        row_identifier: Tuple[str, ...],
        value_in: float,
        unit_in: ToleranceUnit,
        unit_out: ToleranceUnit | None = None,
        in_table_names: Sequence[str] | None = None,
        logger: Logger = NO_LOGGER,
    ):
        self.unit_in = unit_in
        self.unit_out = unit_out
        self.row_identifier = row_identifier
        self.value_in = value_in
        self.in_table_names = in_table_names
        self.logger = logger

    def get_value_as(self, unit: ToleranceUnit) -> float | None:
        return self.value_in if self.unit_in == unit else None

    def find_tables(self, standard: ToleranceStandard) -> List[ValueToToleranceTable]:
        tables: List[ValueToToleranceTable] = []
        for t in standard.tables:
            if self.in_table_names is not None and t.name not in self.in_table_names:
                continue
            tables.append(t)
        return tables

    def find_range_in(self, standard: ToleranceStandard) -> Tuple[ValueToToleranceTable, ValueToToleranceRange] | None:
        tables = self.find_tables(standard)
        if len(tables) > 1:
            self.logger.error(f"Found {len(tables)} tables from standard {standard.name}")

        for t in tables:
            # check input/output compatibility
            converted_value = self.get_value_as(t.value_unit)
            output_is_ok = self.unit_out is None or t.tolerance_unit == self.unit_out
            if converted_value is None or not output_is_ok:
                continue

            tol_range = t.lookup_range(row_identifier=self.row_identifier, value=converted_value, logger=self.logger)
            if tol_range is not None:
                return t, tol_range

        return None

    @staticmethod
    def _truncate_to_3_decimals(value: float) -> float:
        # Convert to string with all decimals
        str_val = str(value)
        # Split into parts before and after decimal
        if "." not in str_val:
            return value
        integer_part, decimal_part = str_val.split(".")
        # Truncate decimal part to 3 digits if longer
        truncated_decimal = decimal_part[:3]
        # Reconstruct the number
        return float(f"{integer_part}.{truncated_decimal}")

    def find_in(self, standard: ToleranceStandard) -> ToleranceQueryResult | None:
        result = self.find_range_in(standard)
        if result is None:
            return None

        table, tol_range = result
        upper = self._truncate_to_3_decimals(tol_range.tolerance.upper)
        lower = self._truncate_to_3_decimals(tol_range.tolerance.lower)
        exp_unit = table.tolerance_unit if self.unit_out is None else self.unit_out

        if table.tolerance_unit != exp_unit:
            self.logger.warning(f"Tolerance unit mismatch: {table.tolerance_unit} != {exp_unit}")
            return None

        return ToleranceQueryResult(upper=upper, lower=lower, unit=exp_unit)
