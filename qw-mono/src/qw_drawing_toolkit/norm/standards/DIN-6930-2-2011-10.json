{"name": "DIN_6930-2:2011-10", "tables": [{"name": "Tabelle 3", "valueType": "angular", "valueUnit": "deg", "toleranceUnit": "deg", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["1=< f <= 6"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["1=< m <= 6"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["1=< g <= 6"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["1=< sg <= 6"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["f <= 10"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["m <= 10"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["g <= 10"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["sg <= 10"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["f <= 25"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["m <= 25"]], "tolerance": {"upper": 0.83333, "lower": -0.83333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["g <= 25"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["sg <= 25"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["f <= 63"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["m <= 63"]], "tolerance": {"upper": 0.83333, "lower": -0.83333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["g <= 63"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["sg <= 63"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["f <= 160"]], "tolerance": {"upper": 0.33333, "lower": -0.33333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["m <= 160"]], "tolerance": {"upper": 0.41667, "lower": -0.41667}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["g <= 160"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["sg <= 160"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["f <= 400"]], "tolerance": {"upper": 0.16667, "lower": -0.16667}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["m <= 400"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["g <= 400"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["sg <= 400"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["f <= 1000"]], "tolerance": {"upper": 0.08333, "lower": -0.08333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["m <= 1000"]], "tolerance": {"upper": 0.16667, "lower": -0.16667}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["g <= 1000"]], "tolerance": {"upper": 0.33333, "lower": -0.33333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["sg <= 1000"]], "tolerance": {"upper": 0.33333, "lower": -0.33333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["f <= 2500"]], "tolerance": {"upper": 0.08333, "lower": -0.08333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["m <= 2500"]], "tolerance": {"upper": 0.16667, "lower": -0.16667}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["g <= 2500"]], "tolerance": {"upper": 0.33333, "lower": -0.33333}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["sg <= 2500"]], "tolerance": {"upper": 0.33333, "lower": -0.33333}}]}]}