from enum import Enum, unique

from qw_basic_s3.builder import S3StorageConfig
from qw_basic_s3.impl.memory import MemoryS3StorageConfig
from qw_basic_s3.impl.minio import MinioS3StorageConfig
from qw_basic_s3.interface import S3Storage
from qw_basic_s3.schema import S3StorageSchema
from qw_log_interface import NO_LOG_FACTORY, LogFactory


@unique
class S3Mode(str, Enum):
    MINIO = "minio"
    IN_MEMORY = "in_memory"


class S3TestFactory(object):
    def __init__(self, bucket_prefix: str, minio_cfg: MinioS3StorageConfig, mode: S3Mode):
        """
        Useful for testing code depending on a s3 storage. With pytest parametrized fixtures are possible,
        so code can be tested both with in-memory and a running s3 instance.
        """
        self.test_prefix = bucket_prefix
        self.minio_cfg = minio_cfg
        self.mode = mode

    def create(self, schema: S3StorageSchema | None = None, lf: LogFactory = NO_LOG_FACTORY) -> S3Storage:
        if schema is None:
            schema = S3StorageSchema.create_simple()

        backend: MemoryS3StorageConfig | MinioS3StorageConfig
        backend = MemoryS3StorageConfig() if self.mode == S3Mode.IN_MEMORY else self.minio_cfg
        s3_config = S3StorageConfig(backend=backend, create_buckets_if_not_exist=self.mode == S3Mode.IN_MEMORY)
        return s3_config.build(schema, lf=lf)
