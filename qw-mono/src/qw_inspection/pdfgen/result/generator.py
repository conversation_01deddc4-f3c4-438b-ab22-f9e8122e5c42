import io
import logging
import re
from typing import Any, Dict

import jinja2
import weasyprint
from jinja2 import Template
from typing_extensions import Self

from qw_inspection.pdfgen.result.gen_input import InspectionReportContextMapperInput, ReportType
from qw_inspection.pdfgen.result.strings import INSPECTION_STRINGS
from qw_inspection.pdfgen.result.template import TEMPLATES_DIR, HtmlContext, InspectionContext, TemplatePageSize
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class InspectionHtmlContextMapperInterface(object):
    def map(self, ctx_input: InspectionReportContextMapperInput, page_size: TemplatePageSize) -> InspectionContext:
        raise NotImplementedError()


class InspectionPdfGenerationError(Exception):
    pass


class InspectionResultHtmlTemplate(object):
    def __init__(self, jinja_template: Template, width_in_mm: float, height_in_mm: float):
        self.jinja_template = jinja_template
        self.page_size = TemplatePageSize(width_in_mm=width_in_mm, height_in_mm=height_in_mm)

    def render(self, data: Dict[str, Any]) -> str:
        return self.jinja_template.render(data)

    @classmethod
    def load(cls, env: jinja2.Environment, name: str) -> Self:
        if env.loader is None:
            raise RuntimeError("Environment loader is unexpectedly not available")
        source, _, _ = env.loader.get_source(env, name)

        # check for page size
        matches = list(re.finditer(r"page_size=[\"'](?P<PAGE_SIZE>A[1234] (landscape|portrait))[\"']", source))
        if len(matches) != 1:
            raise ValueError(f"Found {len(matches)} page size pattern matches, but expected exactly 1")
        match = matches[0].group("PAGE_SIZE")
        width_mm, height_mm = {
            "A1": (594, 841),
            "A2": (420, 594),
            "A3": (297, 420),
            "A4": (210, 297),
        }[match[:2]]
        if "landscape" in match:
            width_mm, height_mm = height_mm, width_mm

        t = env.get_template(name)
        return cls(jinja_template=t, width_in_mm=width_mm, height_in_mm=height_mm)


class InspectionResultHtmlGenerator(object):
    def __init__(
        self,
        mapper: InspectionHtmlContextMapperInterface,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(searchpath=TEMPLATES_DIR),
            undefined=jinja2.StrictUndefined,
        )

        self.templates: Dict[ReportType, InspectionResultHtmlTemplate] = {}
        for rt in ReportType:
            self.templates[rt] = InspectionResultHtmlTemplate.load(self.env, f"{rt.lower()}.jinja2")

        self.mapper = mapper
        self.strings_by_lang = INSPECTION_STRINGS
        self.logger = lf.get_logger(__name__)

    def map_and_generate_pdf(self, ctx_input: InspectionReportContextMapperInput) -> io.BytesIO:
        template = self.templates[ctx_input.opts.report_type]
        ctx = self.mapper.map(ctx_input, template.page_size)

        html_context = HtmlContext(inspection=ctx, strings=self.strings_by_lang[ctx_input.opts.lang])
        html_context_dict = html_context.model_dump(by_alias=True)

        html = template.render(html_context_dict)

        try:
            # Configure logging for font processing - otherwise too noisy
            font_loggers = ["fontTools.subset", "fontTools.ttLib.ttFont", "fontTools.subset.timer"]
            for logger_name in font_loggers:
                logger = logging.getLogger(logger_name)
                logger.setLevel(logging.WARNING)

            # Keep WeasyPrint warnings visible for debugging
            weasyprint_logger = logging.getLogger("weasyprint")
            weasyprint_logger.setLevel(logging.WARNING)

            self.logger.info(f"Generating PDF with page size from template: {template.page_size}")

            # Debug: Log a snippet of the HTML to check font CSS
            html_snippet = html[:1000] if len(html) > 1000 else html
            self.logger.info(f"HTML snippet for font debugging: {html_snippet}")

            # Use WeasyPrint for PDF generation with base_url for relative asset paths
            base_url = TEMPLATES_DIR.resolve().as_uri()  # Use templates dir as base for ../assets/ paths
            pdf_document = weasyprint.HTML(string=html, base_url=base_url).write_pdf()
            if pdf_document is None:
                raise InspectionPdfGenerationError("WeasyPrint returned None for PDF document")
            pdf_buffer = io.BytesIO(pdf_document)
            pdf_buffer.seek(0)
            return pdf_buffer
        except Exception as e:
            raise InspectionPdfGenerationError(f"Could not generate PDF with WeasyPrint, error: {e}")
