{"name": "DIN_7715-1:1977-02", "tables": [{"name": "Tabelle 1", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["Klasse H2 (F)"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 1.7, "lower": -1.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["Klasse H3 (F)"]], "tolerance": {"upper": 2.2, "lower": -2.2}}]}, {"name": "Tabelle 3", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Nennma� < 50 (C)"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Nennma� < 140 (C)"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Nennma� < 315 (C)"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Nennma� > 315 (C)"]], "tolerance": {"upper": 0.5, "lower": -0.5}}]}, {"name": "Tabelle 4", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.08, "lower": -0.08}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["Klasse H1"]], "tolerance": {"upper": 0.75, "lower": -0.75}}]}]}