import io
from typing import Callable

import pymupdf

from qw_drawing_toolkit.engine import QwDrawingToolkit
from qw_drawing_toolkit.pdf.models.base_models import DrawingUnit
from qw_drawing_toolkit.pdf.models.config_models import DrawingAnalyzerOpts
from qw_drawing_toolkit.pdf.models.drawing_models import (
    DrawingExplicitIso286FitTolerance,
    DrawingGeneralNormIso2768,
    DrawingNorm,
    DrawingReportStatus,
)


def create_pdf_doc_and_get_bytes(design: Callable[[pymupdf.Page], None] | None = None, margin: int = 30) -> io.BytesIO:
    doc = pymupdf.open()
    page: pymupdf.Page = doc.new_page()

    # purpose of these random lines is:
    # * bypass the scan check
    # * satisfy the analyzer (needs at least one vertical and one horizontal line)
    width, height = page.mediabox_size

    rect_shape = page.new_shape()
    rect = pymupdf.Rect(margin, margin, width - margin, height - margin)
    rect_shape.draw_rect(rect)
    rect_shape.finish(color=(1, 0, 0), width=2)
    rect_shape.commit()

    # faking the title block with some more rectangles, however it does not seem to get detected
    title_block_line_count = 3
    title_block_line_height = int((height - 2 * margin) * 0.05)
    title_block_line_width = int((width - 2 * margin) * 0.3)

    for i in range(title_block_line_count):
        y_offset = height - margin - (i + 1) * title_block_line_height
        tbl_shape = page.new_shape()
        tbl = pymupdf.Rect(margin, y_offset, margin + title_block_line_width, y_offset + title_block_line_height)
        tbl_shape.draw_rect(tbl)
        tbl_shape.finish(color=(1, 0, 0), width=2)
        tbl_shape.commit()

    # adding some more lines, tbh not entirely sure why those make it work :shrug:
    margin2 = int(margin * 1.2)
    margin3 = int(margin * 2)
    vertices = [
        ((margin2, margin2), (width - margin2 - 1, margin2)),
        ((width - margin2, margin2), (width - margin2, height - margin2)),
        ((margin3, margin3), (width - margin3 - 1, margin3)),
        ((width - margin3, margin3), (width - margin3, height - margin3)),
    ]

    for v1, v2 in vertices:
        shape = page.new_shape()
        shape.draw_line(v1, v2)
        shape.finish(color=(1, 0, 0), width=2, closePath=False)
        shape.commit()

    if design is not None:
        design(page)

    bio = io.BytesIO()
    doc.save(bio)
    bio.seek(0)
    return bio


class TestDrawingAnalyzer(object):
    def test_default_tolerance_lookup(self) -> None:
        def design_page(page: pymupdf.Page) -> None:
            page.insert_text((50, 100), "10±1", fontsize=12)
            page.insert_text((50, 150), "20", fontsize=12)
            page.insert_text((50, 200), "ISO 2768mK", fontsize=12)

        bio = create_pdf_doc_and_get_bytes(design=design_page)

        toolkit = QwDrawingToolkit()
        _, analysis = toolkit.analyse_pdf(bio, opts=DrawingAnalyzerOpts(), close_pdf_file=True)
        lengths_by_value = {item.length.value: item for item in analysis.result.lengths}

        assert analysis.result.norms == [DrawingGeneralNormIso2768(row_identifier_1="m", row_identifier_2="K")]

        assert lengths_by_value[10].length.explicit_tolerance is not None
        assert lengths_by_value[10].length.explicit_tolerance.lower == -1
        assert lengths_by_value[10].length.explicit_tolerance.upper == 1
        assert lengths_by_value[10].length.default_tolerance is not None
        assert lengths_by_value[10].length.default_tolerance.source == DrawingNorm.ISO_2768

        assert lengths_by_value[20].length.explicit_tolerance is None
        assert lengths_by_value[20].length.default_tolerance is not None
        assert lengths_by_value[20].length.default_tolerance.source == DrawingNorm.ISO_2768

    def test_fit_tolerance_lookup(self) -> None:
        def design_page(page: pymupdf.Page) -> None:
            page.insert_text((50, 100), "ø10a12", fontsize=12)
            page.insert_text((50, 150), "ø15E12", fontsize=12)

        bio = create_pdf_doc_and_get_bytes(design=design_page)

        toolkit = QwDrawingToolkit()
        _, analysis = toolkit.analyse_pdf(bio, opts=DrawingAnalyzerOpts(), close_pdf_file=True)
        diameters_by_value = {item.diameter.value: item for item in analysis.result.diameters}

        limit = 1  # we are expecting micrometer tolerance values

        assert diameters_by_value[10].diameter.unit == DrawingUnit.MILLIMETER
        exp_tol1 = diameters_by_value[10].diameter.explicit_tolerance
        assert isinstance(exp_tol1, DrawingExplicitIso286FitTolerance)
        assert exp_tol1.identifier == "a12"
        assert 0 < abs(exp_tol1.lower) < limit
        assert 0 < abs(exp_tol1.upper) < limit

        assert diameters_by_value[15].diameter.unit == DrawingUnit.MILLIMETER
        exp_tol2 = diameters_by_value[15].diameter.explicit_tolerance
        assert isinstance(exp_tol2, DrawingExplicitIso286FitTolerance)
        assert exp_tol2.identifier == "E12"
        assert not (exp_tol2.lower == 0 and exp_tol2.upper == 0)
        assert 0 < abs(exp_tol2.lower) < limit
        assert 0 < abs(exp_tol2.upper) < limit

    def test_report_status(self) -> None:
        def design_page(page: pymupdf.Page) -> None:
            page.insert_text((50, 100), "ø10", fontsize=10)

        bio1 = create_pdf_doc_and_get_bytes(design=design_page)

        toolkit = QwDrawingToolkit()
        _, analysis1 = toolkit.analyse_pdf(bio1, opts=DrawingAnalyzerOpts(), close_pdf_file=True)

        assert analysis1.report.analysis_status == DrawingReportStatus.COMPLETED
        assert len(analysis1.result.diameters) == 1

        # to trigger an exception in the drawing analysis step
        class MockOpts(DrawingAnalyzerOpts):  # type: ignore
            @property  # type: ignore
            def zoom(self) -> float:
                raise ValueError()

            @zoom.setter
            def zoom(self, value: float) -> None:
                raise ValueError()

        bio2 = create_pdf_doc_and_get_bytes(design=design_page)
        _, analysis2 = toolkit.analyse_pdf(bio2, opts=MockOpts(), close_pdf_file=True)

        assert analysis2.report.analysis_status == DrawingReportStatus.FAILED
        assert len(analysis2.result.diameters) == 0
        assert len(analysis2.document.pages) > 0
