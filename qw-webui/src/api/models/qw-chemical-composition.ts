/* tslint:disable */
/* eslint-disable */

/**
 * [schema: ChemicalComposition] Chemical composition with foreign key references.
 * Treated like a database table - always includes batch_number and heat_number.
 */
export interface QwChemicalComposition {

  /**
   * Aluminum in %
   */
  Al?: (number | string | null);

  /**
   * Arsenic in %
   */
  As?: (number | string | null);

  /**
   * Boron in %
   */
  B?: (number | string | null);

  /**
   * Carbon in %
   */
  C?: (number | string | null);

  /**
   * Calcium in %
   */
  Ca?: (number | string | null);

  /**
   * Carbon equivalent in %
   */
  Ceq?: (number | string | null);

  /**
   * Chromium in %
   */
  Cr?: (number | string | null);

  /**
   * Copper in %
   */
  Cu?: (number | string | null);

  /**
   * Hydrogen in %
   */
  H?: (number | string | null);

  /**
   * Manganese in %
   */
  Mn?: (number | string | null);

  /**
   * Molybdenum in %
   */
  Mo?: (number | string | null);

  /**
   * Nitrogen in %
   */
  N?: (number | string | null);

  /**
   * Niobium in %
   */
  Nb?: (number | string | null);

  /**
   * Nickel in %
   */
  Ni?: (number | string | null);

  /**
   * Phosphorus in %
   */
  P?: (number | string | null);

  /**
   * Sulfur in %
   */
  S?: (number | string | null);

  /**
   * Silicon in %
   */
  Si?: (number | string | null);

  /**
   * Tin in %
   */
  Sn?: (number | string | null);

  /**
   * Titanium in %
   */
  Ti?: (number | string | null);

  /**
   * Vanadium in %
   */
  V?: (number | string | null);

  /**
   * Batch/item identifier - must match HeaderDetails
   */
  batch_number: string;

  /**
   * Heat/Smelt number - must match HeaderDetails
   */
  heat_number?: (string | null);
}
