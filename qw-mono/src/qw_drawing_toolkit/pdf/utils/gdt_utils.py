"""
Utility functions for GDT Extraction
"""

import functools
import math
from collections import defaultdict
from typing import Dict, List, Set, Tuple, Union

from qw_drawing_toolkit.pdf.models.analysis_models import DatumBox, SymbolBox, TextBox
from qw_drawing_toolkit.pdf.models.base_models import T_BBOX, T_LINE_SEGMENT
from qw_drawing_toolkit.pdf.models.geometry_models import LineDirection
from qw_drawing_toolkit.pdf.parser.symbol_mapper import Symbol
from qw_drawing_toolkit.pdf.utils.common_utils import is_box_in_box
from qw_drawing_toolkit.pdf.utils.geometry_utils import (
    find_adjacent_lines,
    get_center_of_box,
    is_line_in_box,
    is_line_in_direction,
    length_of_line,
    scale_box_from_center,
)


def find_potential_datum_box(
    line: T_LINE_SEGMENT,
    adjacent_horizontal_1: T_LINE_SEGMENT,
    adjacent_horizontal_2: T_LINE_SEGMENT,
    text_boxes: List[TextBox],
    point_proximity_tolerance: float,
) -> DatumBox | None:
    """
    Identify a potential datum box from a set of three lines and text boxes.

    This function checks if three lines (one vertical and two horizontal) form a
    potential datum box structure by verifying their lengths are similar. If so,
    it creates a bounding box and checks if any text box is contained within it.

    Args:
        line: The primary line segment (typically vertical)
        adjacent_horizontal_1: First adjacent horizontal line
        adjacent_horizontal_2: Second adjacent horizontal line
        text_boxes: List of text boxes to check for datum labels
        point_proximity_tolerance: Tolerance for length comparison and box containment

    Returns:
        DatumBox: A datum box object if found, None otherwise
    """
    length = length_of_line(line)
    length_h_1 = length_of_line(adjacent_horizontal_1)
    length_h_2 = length_of_line(adjacent_horizontal_2)
    length_ok = (
        abs(length - length_h_1) < point_proximity_tolerance and abs(length - length_h_2) < point_proximity_tolerance
    )
    if length_ok:
        # adjacent_verticals_1 = self._find_adjacent_lines(
        #     adjacent_horizontal_1, self._lines_drawing, DrawingAnalyzerLineDirection.VERTICAL
        # )
        # adjacent_verticals_2 = self._find_adjacent_lines(
        #     adjacent_horizontal_2, self._lines_drawing, DrawingAnalyzerLineDirection.VERTICAL
        # )
        #
        # if len(adjacent_verticals_1) == len(adjacent_verticals_2) == 2:
        min_x = min(min(line[0][0], adjacent_horizontal_1[0][0]), adjacent_horizontal_2[0][0])
        min_y = min(min(line[0][1], adjacent_horizontal_1[0][1]), adjacent_horizontal_2[0][1])
        max_x = max(max(line[0][0], adjacent_horizontal_1[0][0]), adjacent_horizontal_2[0][0])
        max_y = max(max(line[0][1], adjacent_horizontal_1[0][1]), adjacent_horizontal_2[0][1])
        bbox = (min_x, min_y, max_x, max_y)

        # Check if any text box is inside this bounding box
        for text_box in text_boxes:
            if is_box_in_box(text_box.bbox, bbox, point_proximity_tolerance):
                return DatumBox(text_box.text.strip(), bbox, text_box)

    return None


def is_gd_t_candidate(
    bbox: T_BBOX, text_boxes_measurements_no_symbol: List[TextBox], point_proximity_tolerance: float
) -> bool:
    """
    Determine if a bounding box is a candidate for a GD&T feature control frame.

    This function checks if any measurement text box is contained within the
    given bounding box and has its center y-coordinate aligned with the center
    of the bounding box (within the specified tolerance).

    Args:
        bbox: Bounding box to check
        text_boxes_measurements_no_symbol: List of measurement text boxes
        point_proximity_tolerance: Tolerance for box containment and center alignment

    Returns:
        bool: True if the box is a GD&T candidate, False otherwise
    """
    for text_box in text_boxes_measurements_no_symbol:
        if is_box_in_box(text_box.bbox, bbox, point_proximity_tolerance):
            # Check if centers are aligned vertically
            center_y_tbb = text_box.bbox[1] + abs(text_box.bbox[1] - text_box.bbox[3]) / 2
            center_y_bb = bbox[1] + abs(bbox[1] - bbox[3]) / 2
            if abs(center_y_tbb - center_y_bb) <= point_proximity_tolerance:
                return True
    return False


def rescan_for_missing_datum_box_alternate(
    datum_string: str,
    string_to_datum_box_map: Dict[str, DatumBox],
    datum_boxes: Set[DatumBox],
    text_boxes_unfiltered: List[TextBox],
    max_width: float,
    max_height: float,
    point_proximity_tolerance: float,
    lines: List[T_LINE_SEGMENT],
) -> Tuple[Dict[str, DatumBox], Set[DatumBox], DatumBox | None]:
    """
    Attempt to find a missing datum box by searching through text boxes and adjacent lines.

    This function looks for text boxes matching the datum string, then examines nearby
    lines to identify potential datum box structures. It uses a different approach than
    the primary datum box detection method, focusing on horizontal lines with adjacent
    vertical lines.

    Args:
        datum_string: The datum identifier to search for
        string_to_datum_box_map: Map of datum strings to datum boxes
        datum_boxes: Set of all datum boxes
        text_boxes_unfiltered: List of all text boxes to search through
        max_width: Maximum width of the drawing
        max_height: Maximum height of the drawing
        point_proximity_tolerance: Tolerance for geometric comparisons
        lines: List of line segments to search through

    Returns:
        Tuple: (
            updated_string_to_datum_box_map: Updated map with new datum box if found
            updated_datum_boxes: Updated set with new datum box if found
            found_datum_box: The found datum box or None if not found
        )
    """
    updated_string_to_datum_box_map = string_to_datum_box_map.copy()
    updated_datum_boxes = datum_boxes.copy()
    found_datum_box = None

    # Find text boxes matching the datum string
    matching_text_boxes: List[TextBox] = []
    for box in text_boxes_unfiltered:
        if box.text.strip() == datum_string.strip():
            matching_text_boxes.append(box)

    # For each matching text box, look for surrounding lines that form a datum box
    for box in matching_text_boxes:
        bbox_scaled = scale_box_from_center(box.bbox, 2)
        lines_in_bbox = []

        # Find lines near the text box
        for line in lines:
            if is_line_in_box(line, bbox_scaled, point_proximity_tolerance):
                lines_in_bbox.append(line)

        # Look for horizontal lines with two adjacent vertical lines
        for line in lines_in_bbox:
            if is_line_in_direction(line, LineDirection.HORIZONTAL, point_proximity_tolerance):
                adjacent_verticals = find_adjacent_lines(
                    line,
                    lines_in_bbox,
                    LineDirection.VERTICAL,
                    max_width,
                    max_height,
                    point_proximity_tolerance=point_proximity_tolerance,
                )

                if len(adjacent_verticals) == 2:
                    # Check if lines have similar lengths (with increased tolerance)
                    length = length_of_line(line)
                    length_v_1 = length_of_line(adjacent_verticals[0])
                    length_v_2 = length_of_line(adjacent_verticals[1])

                    length_ok = (
                        abs(length - length_v_1) < point_proximity_tolerance * 3
                        and abs(length - length_v_2) < point_proximity_tolerance * 3
                    )

                    if length_ok:
                        # Create bounding box from the lines
                        min_x = min(min(line[0][0], adjacent_verticals[0][0][0]), adjacent_verticals[1][0][0])
                        min_y = min(min(line[0][1], adjacent_verticals[0][0][1]), adjacent_verticals[1][0][1])
                        max_x = max(max(line[1][0], adjacent_verticals[0][1][0]), adjacent_verticals[1][1][0])
                        max_y = max(max(line[1][1], adjacent_verticals[0][1][1]), adjacent_verticals[1][1][1])
                        bbox = (min_x, min_y, max_x, max_y)

                        # Create and return the datum box
                        found_datum_box = DatumBox(datum_string.strip(), bbox, box)
                        updated_string_to_datum_box_map[datum_string.strip()] = found_datum_box
                        updated_datum_boxes.add(found_datum_box)

                        return updated_string_to_datum_box_map, updated_datum_boxes, found_datum_box

    return updated_string_to_datum_box_map, updated_datum_boxes, None


def merge_adjacent_text_boxes(
    text_boxes_measurements_no_symbol: List[TextBox],
    text_boxes_measurement_symbols: List[SymbolBox | TextBox],
    text_box: TextBox,
    point_proximity_tolerance: float,
) -> Dict[TextBox | SymbolBox, Set[Union[TextBox, SymbolBox]]]:
    """
    Merge text boxes that are adjacent or related to each other.

    This function identifies text boxes and symbols that should be considered
    part of the same measurement or annotation. It handles special cases for
    symbol boxes, which may be positioned differently than regular text.

    The merging is based on:
    1. Direction of text
    2. Proximity in the direction of text flow
    3. Special handling for symbol boxes

    Args:
        text_boxes_measurements_no_symbol: List of measurement text boxes
        text_boxes_measurement_symbols: List of symbol boxes
        text_box: The primary text box to find merges for

    Returns:
        Dict: Mapping of each text box to the set of text boxes it should be merged with
    """
    merged_text_box_map: Dict[TextBox | SymbolBox, Set[Union[TextBox, SymbolBox]]] = defaultdict(set)
    text_boxes: Set[Union[TextBox, SymbolBox]] = set()
    text_boxes.update(text_boxes_measurements_no_symbol)
    text_boxes.update(text_boxes_measurement_symbols)

    for other_text_box in text_boxes:
        merge_symbol = False

        # Special handling for symbol boxes
        if text_box.is_symbol and Symbol.from_string(text_box.text.strip()) != Symbol.UNKNOWN:
            # this is done to reliably catch symbols that are a bit further away from the dimension values

            symbol_box_merging_direction_extension_multiplier = 5  # could be moved to options parameter
            x_multiplier = abs(symbol_box_merging_direction_extension_multiplier * other_text_box.direction[0])
            y_multiplier = abs(symbol_box_merging_direction_extension_multiplier * other_text_box.direction[1])

            x_multiplier = 1 if x_multiplier == 0 else x_multiplier
            y_multiplier = 1 if y_multiplier == 0 else y_multiplier

            extended_bbox = scale_box_from_center(other_text_box.bbox, x_multiplier, True, False)
            extended_bbox = scale_box_from_center(extended_bbox, y_multiplier, False, True)

            center_text_box = get_center_of_box(text_box.bbox)
            merge_symbol = is_line_in_box((center_text_box, center_text_box), extended_bbox, point_proximity_tolerance)

        merged_text_box_map[text_box].add(text_box)
        other_text_box_center = get_center_of_box(other_text_box.bbox)
        point_to_check_1 = (
            other_text_box_center[0] + text_box.direction[0] * abs(text_box.bbox[0] - text_box.bbox[2]) * 0.6,
            other_text_box_center[1] + text_box.direction[1] * abs(text_box.bbox[1] - text_box.bbox[3]) * 0.6,
        )
        point_to_check_2 = (
            other_text_box_center[0] - text_box.direction[0] * abs(text_box.bbox[0] - text_box.bbox[2]) * 0.6,
            other_text_box_center[1] - text_box.direction[1] * abs(text_box.bbox[1] - text_box.bbox[3]) * 0.6,
        )

        # Merge if symbol condition is met or points are in the box, and directions are compatible
        if (
            (text_box.is_symbol and merge_symbol)
            or is_line_in_box((point_to_check_1, point_to_check_1), text_box.bbox, point_proximity_tolerance)
            or is_line_in_box((point_to_check_2, point_to_check_2), text_box.bbox, point_proximity_tolerance)
        ) and (
            text_box.direction == other_text_box.direction
            or text_box.direction == (0, 0)
            or other_text_box.direction == (0, 0)
        ):
            # Update merge sets in both directions
            merged_text_box_map[text_box].add(other_text_box)
            merged_text_box_map[other_text_box] = merged_text_box_map[other_text_box].union(
                merged_text_box_map[text_box]
            )
            merged_text_box_map[text_box] = merged_text_box_map[text_box].union(merged_text_box_map[other_text_box])

    return merged_text_box_map


def get_box_height_relative_to_text_direction(text_box: TextBox) -> float:
    """
    Calculate the height of a text box relative to its text direction.

    This function computes the height of the text box in the coordinate system
    aligned with the text direction, which is useful for comparing heights of
    text boxes with different orientations.

    Args:
        text_box: The text box to measure

    Returns:
        float: Height of the text box relative to its text direction
    """
    return abs(
        get_box_y_relative_to_text_direction(text_box, True) - get_box_y_relative_to_text_direction(text_box, False)
    )


def get_box_y_relative_to_text_direction(text_box: TextBox, max_y_output: bool = True) -> float:
    """
    Get the y-coordinate of a text box relative to its text direction.

    This function rotates the text box to align with its text direction and
    returns either the maximum or minimum y-coordinate of the rotated box.

    Args:
        text_box: The text box to process
        max_y_output: If True, returns the maximum y-coordinate; if False, returns the minimum

    Returns:
        float: The requested y-coordinate in the rotated coordinate system
    """
    direction = normalize(text_box.direction)
    angle = math.atan2(direction[1], direction[0])

    # Get corners of the box
    min_x, min_y, max_x, max_y = text_box.bbox
    corners = [(min_x, min_y), (max_x, min_y), (min_x, max_y), (max_x, max_y)]

    # Rotate corners to align with text direction
    rotated_corners = [rotate_point_free_angle(corner, -angle) for corner in corners]

    max_y_rotated = max(corner[1] for corner in rotated_corners)
    min_y_rotated = min(corner[1] for corner in rotated_corners)

    return max_y_rotated if max_y_output else min_y_rotated


def normalize(vector: tuple[float, float]) -> tuple[float, float]:
    """
    Normalize a 2D vector to unit length.

    Args:
        vector: The vector to normalize (x, y)

    Returns:
        tuple: Normalized vector with unit length, or (0, 0) if input has zero magnitude
    """
    mag = magnitude(vector)
    if mag == 0:
        return 0, 0
    return vector[0] / mag, vector[1] / mag


def magnitude(v: tuple[float, float]) -> float:
    """
    Calculate the magnitude (length) of a 2D vector.

    Args:
        v: The vector (x, y)

    Returns:
        float: Magnitude of the vector
    """
    return math.sqrt(v[0] ** 2 + v[1] ** 2)


def rotate_point_free_angle(point: tuple[float, float], angle: float) -> tuple[float, float]:
    """
    Rotate a point around the origin by a specified angle.

    Args:
        point: The point (x, y) to rotate
        angle: Rotation angle in radians

    Returns:
        tuple: Rotated point coordinates (x', y')
    """
    cos_angle = math.cos(angle)
    sin_angle = math.sin(angle)
    return point[0] * cos_angle - point[1] * sin_angle, point[0] * sin_angle + point[1] * cos_angle


def sort_text_boxes(
    text_boxes: Set[Union[TextBox, SymbolBox]], point_proximity_tolerance: float, by_height: bool = False
) -> List[Union[TextBox, SymbolBox]]:
    """
    Sort text boxes based on their position and optionally height.

    This function sorts text boxes primarily by their position along their text direction,
    and optionally by their height if specified. It uses a custom comparator function.

    Args:
        text_boxes: Set of text boxes to sort
        point_proximity_tolerance: Tolerance for height comparison
        by_height: If True, sort by height first, then by position

    Returns:
        List: Sorted list of text boxes
    """
    comparator = functools.partial(
        compare_text_boxes, point_proximity_tolerance=point_proximity_tolerance, by_height=by_height
    )
    return sorted(text_boxes, key=functools.cmp_to_key(comparator))


def compare_text_boxes(
    text_box_1: TextBox, text_box_2: TextBox, point_proximity_tolerance: float, by_height: bool
) -> int:
    """
    Compare two text boxes for sorting based on position and height.

    This function implements the comparison logic for sort_text_boxes:
    1. If by_height is True and the boxes have different heights (beyond tolerance),
       sort by height
    2. Otherwise, sort by position along the text direction

    Args:
        text_box_1: First text box to compare
        text_box_2: Second text box to compare
        point_proximity_tolerance: Tolerance for height comparison
        by_height: Whether to prioritize height in the comparison

    Returns:
        int: -1 if text_box_1 comes before text_box_2, 1 if after, 0 if equivalent
    """
    height_1 = get_box_y_relative_to_text_direction(text_box_1)
    height_2 = get_box_y_relative_to_text_direction(text_box_2)
    primary_pos_1 = get_primary_position(text_box_1)
    primary_pos_2 = get_primary_position(text_box_2)

    # can put things out of whack if ppt is changed
    if (
        by_height
        and get_center_height_difference_relative_to_direction(text_box_1, text_box_2) > point_proximity_tolerance
    ):
        if abs(height_1 - height_2) < 2 * point_proximity_tolerance:
            height_comparison = 0
        else:
            height_comparison = (height_1 > height_2) - (height_1 < height_2)

        if height_comparison != 0:
            return height_comparison

    # Sort by position along text direction
    return (primary_pos_1 > primary_pos_2) - (primary_pos_1 < primary_pos_2)


def get_primary_position(text_box: TextBox) -> float:
    """
    Get the primary position of a text box along its text direction.

    This function projects the center of the text box onto its direction vector,
    which gives a scalar value representing its position along that direction.

    Args:
        text_box: The text box to get the position for

    Returns:
        float: Scalar position value along the text direction
    """
    center = get_center_of_box(text_box.bbox)
    return project_point_onto_vector(center, text_box.direction)


def project_point_onto_vector(point: tuple[float, float], vector: tuple[float, float]) -> float:
    """
    Project a point onto a vector, returning the scalar projection value.

    Args:
        point: The point (x, y) to project
        vector: The vector (dx, dy) to project onto

    Returns:
        float: Scalar projection value
    """
    norm_vector = normalize(vector)
    return dot_product(point, norm_vector)


def dot_product(v1: tuple[float, float], v2: tuple[float, float]) -> float:
    """
    Calculate the dot product of two 2D vectors.

    Args:
        v1: First vector (x1, y1)
        v2: Second vector (x2, y2)

    Returns:
        float: Dot product v1·v2 = x1*x2 + y1*y2
    """
    return v1[0] * v2[0] + v1[1] * v2[1]


def get_center_height_difference_relative_to_direction(text_box_1: TextBox, text_box_2: TextBox) -> float:
    """
    Calculate the vertical distance between two text boxes relative to text direction.

    This function:
    1. Gets the centers of both text boxes
    2. Rotates the coordinate system to align with the text direction
    3. Returns the absolute difference in y-coordinates in this rotated system

    This is useful for determining if text boxes are on the same "line" of text.

    Args:
        text_box_1: First text box
        text_box_2: Second text box

    Returns:
        float: Absolute vertical distance between the text boxes in the rotated system
    """
    center_1 = get_center_of_box(text_box_1.bbox)
    center_2 = get_center_of_box(text_box_2.bbox)

    direction = normalize(text_box_1.direction)
    angle = math.atan2(direction[1], direction[0])

    rotated_center_1 = rotate_point_free_angle(center_1, -angle)
    rotated_center_2 = rotate_point_free_angle(center_2, -angle)

    return abs(rotated_center_1[1] - rotated_center_2[1])
