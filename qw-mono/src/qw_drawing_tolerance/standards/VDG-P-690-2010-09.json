{"name": "VDG_P_690:2010-09a_(D, A, T)", "tables": [{"name": "Tabelle 1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.22, "lower": -0.22}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.26, "lower": -0.26}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 3.6, "lower": -3.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.17, "lower": -0.17}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.31, "lower": -0.31}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.37, "lower": -0.37}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.44, "lower": -0.44}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.17, "lower": -0.17}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 80, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 80, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.8, "lower": -0.8}}]}, {"name": "Tabelle 2", "valueType": "angular", "valueUnit": "deg", "toleranceUnit": "deg", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["1 <= 30"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 30, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["1 > 30"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 100, "inclusive": true}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["1 > 100"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 200, "inclusive": true}, "upperValueLimit": {"value": 9999, "inclusive": true}, "rowIdentifiers": [["1 > 200"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["2 <= 30"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 30, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["2 > 30"]], "tolerance": {"upper": 0.166, "lower": -0.166}}, {"lowerValueLimit": {"value": 100, "inclusive": true}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["2 > 100"]], "tolerance": {"upper": 0.125, "lower": -0.125}}, {"lowerValueLimit": {"value": 200, "inclusive": true}, "upperValueLimit": {"value": 9999, "inclusive": true}, "rowIdentifiers": [["2 > 200"]], "tolerance": {"upper": 0.125, "lower": -0.125}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["3 <= 30"]], "tolerance": {"upper": 0.166, "lower": -0.166}}, {"lowerValueLimit": {"value": 30, "inclusive": true}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["3 > 30"]], "tolerance": {"upper": 0.125, "lower": -0.125}}, {"lowerValueLimit": {"value": 100, "inclusive": true}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["3 > 100"]], "tolerance": {"upper": 0.083, "lower": -0.083}}, {"lowerValueLimit": {"value": 200, "inclusive": true}, "upperValueLimit": {"value": 9999, "inclusive": true}, "rowIdentifiers": [["3 > 200"]], "tolerance": {"upper": 0.083, "lower": -0.083}}]}, {"name": "Tabelle 3", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 5, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 5, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 800, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["1"]], "tolerance": {"upper": 3.6, "lower": -3.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 5, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 5, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 315, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 500, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 500, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 800, "inclusive": true}, "rowIdentifiers": [["2"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 5, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 5, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 315, "inclusive": true}, "rowIdentifiers": [["3"]], "tolerance": {"upper": 0.8, "lower": -0.8}}]}]}