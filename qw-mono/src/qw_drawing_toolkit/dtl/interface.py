from qw_drawing_toolkit.norm.tolerance_collection import ToleranceCollection
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysisResult
from qw_log_interface import NO_LOGGER, Logger


class DefaultToleranceLookupInterface(object):
    def __init__(self, tolerance_collection: ToleranceCollection, logger: Logger = NO_LOGGER):
        self.tolerance_collection = tolerance_collection
        self.standard_by_name = {ts.name: ts for ts in tolerance_collection.list_available_standards()}
        self.logger = logger

    def lookup_and_update(self, result: DrawingAnalysisResult, overwrite_general: bool, overwrite_fits: bool) -> None:
        raise NotImplementedError()
