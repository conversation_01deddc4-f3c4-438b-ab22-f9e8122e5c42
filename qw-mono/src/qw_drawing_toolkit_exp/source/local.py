import io
from pathlib import Path
from typing import Sequence

import yaml

from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysis
from qw_drawing_toolkit_exp.source.interface import ExperimentSampleInterface, ExperimentSourceInterface


class LocalExperimentSample(ExperimentSampleInterface):
    def __init__(self, path_pdf: Path, path_analysis: Path):
        self.path_pdf = path_pdf
        self.path_analysis = path_analysis
        with path_analysis.open("r") as f:
            data = yaml.safe_load(f)
            self.analysis = DrawingAnalysis(**data)

    def get_id(self) -> str:
        return str(self.path_pdf)

    def get_analysis(self) -> DrawingAnalysis:
        return self.analysis

    def get_pdf_bytes(self) -> io.BytesIO:
        with self.path_pdf.open("rb") as f:
            return io.BytesIO(f.read())


class LocalExperimentSource(ExperimentSourceInterface):
    def __init__(self, samples: Sequence[LocalExperimentSample]):
        self.samples = samples

    @classmethod
    def from_directory(
        cls, paths: Sequence[Path], ignore_samples_with_missing_analysis: bool = False
    ) -> "LocalExperimentSource":
        file_paths = []

        for p in paths:
            if p.is_file():
                file_paths.append(p)
            else:
                file_paths.extend(p.rglob("**/*.[pP][dD][fF]"))

        samples = []
        for path_pdf in file_paths:
            path_analysis = path_pdf.with_suffix(".yaml")
            if not path_analysis.exists() or not path_analysis.is_file():
                if ignore_samples_with_missing_analysis:
                    continue
                raise FileNotFoundError(f"Expected analysis file {path_analysis} could not be found")
            samples.append(LocalExperimentSample(path_pdf, path_analysis))
        return cls(samples)

    def get_count(self) -> int:
        return len(self.samples)

    def get_sample(self, index: int) -> ExperimentSampleInterface:
        return self.samples[index]
