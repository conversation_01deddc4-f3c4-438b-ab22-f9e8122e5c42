import io
import time
from typing import Tuple

import pymupdf

from qw_drawing_toolkit.dtl.simple import DefaultToleranceLookup
from qw_drawing_toolkit.norm.tolerance_collection import ToleranceCollection
from qw_drawing_toolkit.pdf.models.config_models import DrawingAnalyzerOpts
from qw_drawing_toolkit.pdf.models.drawing_models import (
    DrawingAnalysis,
    DrawingAnalysisReport,
    DrawingAnalysisResult,
    DrawingDocument,
    DrawingPage,
    DrawingReportStatus,
)
from qw_drawing_toolkit.pdf.parser.drawing_analyzer import run_drawing_analysis
from qw_drawing_toolkit.pdf.services.pdf_validator import DrawingPdfValidationError, open_and_validate
from qw_log_interface import NO_LOG_FACTORY, LogFactory

POINTS_TO_MM = 0.352778


class QwDrawingToolkit(object):
    def __init__(self, lf: LogFactory = NO_LOG_FACTORY):
        self.logger = lf.get_logger(__name__)
        self.tolerance_collection = ToleranceCollection.from_default_directory()
        self.dtl = DefaultToleranceLookup(self.tolerance_collection, logger=self.logger)

    def analyse_pdf(
        self, pdf_file_data: io.BytesIO, opts: DrawingAnalyzerOpts, close_pdf_file: bool = True
    ) -> Tuple[pymupdf.Document | None, DrawingAnalysis]:
        try:
            pdf_doc, has_vectors, vector_pages = open_and_validate(pdf_file_data, self.logger)

            pages = [
                DrawingPage(width_mm=page.mediabox_size.x * POINTS_TO_MM, height_mm=page.mediabox_size.y * POINTS_TO_MM)
                for page in pdf_doc
            ]

            if not has_vectors:
                self.logger.info("PDF contains no vector graphics, skipping analysis")
                empty_analysis = DrawingAnalysis(
                    document=DrawingDocument(pages=pages),
                    result=DrawingAnalysisResult(),
                    report=DrawingAnalysisReport(
                        analysis_status=DrawingReportStatus.UNSUPPORTED,
                        default_tolerance_lookup_status=DrawingReportStatus.UNSUPPORTED,
                    ),
                )

                if close_pdf_file:
                    pdf_doc.close()

                return pdf_doc, empty_analysis

            self.logger.info(f"PDF contains vector graphics on pages {vector_pages}, proceeding with analysis")
            t0 = time.time()
            analysis = run_drawing_analysis(pdf_doc, opts=opts, dtl=self.dtl, logger=self.logger)
            dt = time.time() - t0
            self.logger.info(f"Analysed drawing pdf with {len(analysis.document.pages)} pages in {dt:.3}s")

            if close_pdf_file:
                pdf_doc.close()

            return pdf_doc, analysis

        except DrawingPdfValidationError:
            raise
        except Exception as e:
            self.logger.error("Failed to analyze PDF", exc_info=e)
            error_analysis = DrawingAnalysis(
                document=DrawingDocument(pages=[]),
                result=DrawingAnalysisResult(),
                report=DrawingAnalysisReport(
                    analysis_status=DrawingReportStatus.FAILED,
                    default_tolerance_lookup_status=DrawingReportStatus.FAILED,
                ),
            )
            return None, error_analysis
