from pydantic import BaseModel, <PERSON>

from qw_basic_s3.impl.local import LocalS3StorageConfig
from qw_basic_s3.impl.memory import MemoryS3StorageConfig
from qw_basic_s3.impl.minio import MinioS3StorageConfig
from qw_basic_s3.interface import S3Storage
from qw_basic_s3.schema import S3StorageSchema
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class S3StorageConfig(BaseModel):
    backend: MinioS3StorageConfig | MemoryS3StorageConfig | LocalS3StorageConfig = Field(discriminator="type")
    verify_buckets: bool = True
    create_buckets_if_not_exist: bool = False  # development option

    def build(self, storage_schema: S3StorageSchema, lf: LogFactory = NO_LOG_FACTORY) -> S3Storage:
        s3 = self.backend.build(storage_schema, lf=lf)

        if self.create_buckets_if_not_exist:
            for bucket in storage_schema.buckets:
                if not s3.bucket_exists(bucket.name):
                    s3.create_bucket(bucket.name)
                # else:
                #     # TODO: check and set properties in the future?

        if self.verify_buckets:
            for bucket in storage_schema.buckets:
                if not s3.bucket_exists(bucket.name):
                    raise ValueError(f"Bucket {bucket.name} does not exist in {s3.get_name()}")

        return s3
