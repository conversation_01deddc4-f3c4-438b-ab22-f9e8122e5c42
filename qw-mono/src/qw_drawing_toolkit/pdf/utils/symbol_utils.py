"""Symbol utils"""

import math
from collections import defaultdict
from typing import Dict, List, Set

from qw_drawing_toolkit.pdf.models.base_models import T_LINE_SEGMENT, T_POINT
from qw_drawing_toolkit.pdf.models.geometry_models import LineDirection, LineSelection
from qw_drawing_toolkit.pdf.parser.geometry import LineCollection
from qw_drawing_toolkit.pdf.utils.geometry_utils import is_line_in_direction, length_of_line


def find_circles(lines: List[T_LINE_SEGMENT], point_proximity_tolerance: float) -> List[Set[T_LINE_SEGMENT]]:
    """
    Find circles in a collection of line segments.

    Args:
        lines: List of line segments to search for circles
        point_proximity_tolerance: Maximum allowed deviation for points to be considered part of a circle

    Returns:
        List of sets, where each set contains line segments forming a circle
    """
    lines_dict = defaultdict(list)
    for line in lines:
        (p1, p2) = line
        lines_dict[p1].append(line)
        lines_dict[p2].append(line)

    visited: Set[T_LINE_SEGMENT] = set()
    closed_loops = []
    for line in lines:
        if line not in visited:
            connected_lines = dfs(line, visited, lines_dict)
            if is_closed_loop(connected_lines):
                closed_loops.append(connected_lines)

    circles = []
    for loop in closed_loops:
        if approximate_circle_by_lengths(loop, point_proximity_tolerance):
            circles.append(loop)

    return circles


def dfs(
    line: T_LINE_SEGMENT,
    visited: Set[T_LINE_SEGMENT],
    lines_dict: Dict[T_POINT, List[T_LINE_SEGMENT]],
) -> Set[T_LINE_SEGMENT]:
    """
    Perform depth-first search to find connected line segments.

    Args:
        line: Starting line segment for the search
        visited: Set of already visited line segments
        lines_dict: Dictionary mapping points to line segments that contain them

    Returns:
        Set of connected line segments
    """
    stack = [line]
    connected_lines = set()
    while stack:
        current_line = stack.pop()
        if current_line not in visited:
            visited.add(current_line)
            connected_lines.add(current_line)
            for next_line in lines_dict[current_line[1]]:
                if next_line not in visited:
                    stack.append(next_line)
            for next_line in lines_dict[current_line[0]]:
                if next_line not in visited:
                    stack.append(next_line)
    return connected_lines


def is_closed_loop(line_set: Set[T_LINE_SEGMENT]) -> bool:
    """
    Check if a set of line segments forms a closed loop.

    Args:
        line_set: Set of line segments to check

    Returns:
        True if the line segments form a closed loop, False otherwise
    """
    points = [point for line_in_set in line_set for point in line_in_set]
    start_points = points[::2]
    end_points = points[1::2]
    start_points.sort()
    end_points.sort()
    return start_points == end_points


def approximate_circle_by_lengths(line_set: Set[T_LINE_SEGMENT], point_proximity_tolerance: float) -> bool:
    """
    Check if a set of line segments approximates a circle based on line lengths.

    Args:
        line_set: Set of line segments to check
        point_proximity_tolerance: Maximum allowed deviation for line lengths

    Returns:
        True if the line segments approximate a circle, False otherwise
    """
    line_lengths = [distance(line[0], line[1]) for line in line_set]
    avg_length = sum(line_lengths) / len(line_lengths)
    return all(abs(length - avg_length) <= point_proximity_tolerance for length in line_lengths)


def distance(point_1: tuple[float, float], point_2: tuple[float, float]) -> float:
    """
    Calculate the Euclidean distance between two points.

    Args:
        point_1: First point as (x, y) tuple
        point_2: Second point as (x, y) tuple

    Returns:
        Euclidean distance between the points
    """
    return math.sqrt((point_1[0] - point_2[0]) ** 2 + (point_1[1] - point_2[1]) ** 2)


def calculate_centroid(line_set: Set[T_LINE_SEGMENT]) -> tuple[float, float]:
    """
    Calculate the centroid (average point) of a set of line segments.

    Args:
        line_set: Set of line segments

    Returns:
        Centroid as (x, y) tuple
    """
    points = [point for line in line_set for point in line]
    x_coords = [p[0] for p in points]
    y_coords = [p[1] for p in points]
    return sum(x_coords) / len(points), sum(y_coords) / len(points)


def calculate_average_radius(line_set: Set[T_LINE_SEGMENT], centroid: tuple[float, float]) -> float:
    """
    Calculate the average radius of a circle approximated by line segments.

    Args:
        line_set: Set of line segments forming the circle
        centroid: Center point of the circle as (x, y) tuple

    Returns:
        Average radius of the circle
    """
    cx, cy = centroid
    distances = [math.sqrt((x - cx) ** 2 + (y - cy) ** 2) for line in line_set for x, y in line]
    return sum(distances) / len(distances)


def filter_circles_with_center_line(
    circles: List[Set[T_LINE_SEGMENT]],
    lines: List[T_LINE_SEGMENT],
    point_proximity_tolerance: float,
    line_collection: LineCollection,
) -> List[Set[T_LINE_SEGMENT]]:
    """
    Filter circles that have a center line passing through them.

    Args:
        circles: List of sets, where each set contains line segments forming a circle
        lines: List of all line segments
        point_proximity_tolerance: Maximum allowed deviation for points
        line_collection: Collection of lines for efficient querying

    Returns:
        Filtered list of circles that have a center line
    """

    def line_intersects_point(line: T_LINE_SEGMENT, point: tuple[float, float], tolerance: float) -> bool:
        """
        Check if a line intersects with a point within a given tolerance.

        Args:
            line: Line segment to check
            point: Point to check for intersection
            tolerance: Maximum allowed distance for intersection

        Returns:
            True if the line intersects with the point, False otherwise
        """
        (lx1, ly1), (lx2, ly2) = line
        (px, py) = point

        if lx1 == lx2:
            return abs(px - lx1) <= tolerance and min(ly1, ly2) <= py <= max(ly1, ly2)
            # return False
        elif ly1 == ly2:
            return abs(px - lx1) <= tolerance and min(ly1, ly2) <= py <= max(ly1, ly2)
            # return False
        else:
            slope = (ly2 - ly1) / (lx2 - lx1)
            intercept = ly1 - slope * lx1
            return abs(py - (slope * px + intercept)) <= tolerance and min(lx1, lx2) <= px <= max(lx1, lx2)

    filtered_circles = []

    for circle in circles:
        centroid = calculate_centroid(circle)
        radius = calculate_average_radius(circle, centroid)
        radius_multiplier = 2.5

        lines_from_collection = (
            line_collection.select(
                LineSelection(
                    min_x1=centroid[0] - radius_multiplier * radius,
                    max_x1=centroid[0] + radius_multiplier * radius,
                    min_y1=centroid[1] - radius_multiplier * radius,
                    max_y1=centroid[1] + radius_multiplier * radius,
                )
            )
            .select(
                LineSelection(
                    min_x2=centroid[0] - radius_multiplier * radius,
                    max_x2=centroid[0] + radius_multiplier * radius,
                    min_y2=centroid[1] - radius_multiplier * radius,
                    max_y2=centroid[1] + radius_multiplier * radius,
                )
            )
            .get_all_as_line_segment()
        )

        intersecting_lines = [
            line for line in lines_from_collection if line_intersects_point(line, centroid, point_proximity_tolerance)
        ]

        ignored_intersecting_lines = [
            line
            for line in intersecting_lines
            if is_line_in_direction(line, LineDirection.VERTICAL, point_proximity_tolerance)
            or is_line_in_direction(line, LineDirection.HORIZONTAL, point_proximity_tolerance)
        ]

        intersecting_lines = [line for line in intersecting_lines if line not in ignored_intersecting_lines]

        if (
            len(intersecting_lines) == 1
            and len(ignored_intersecting_lines) == 0
            and 2 * radius <= length_of_line(intersecting_lines[0]) <= 4 * radius
        ):
            filtered_circles.append(circle)

    return filtered_circles
