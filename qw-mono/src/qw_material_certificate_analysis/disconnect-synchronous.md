# Material Certificate Analysis - Disconnect Synchronous Analysis Migration Plan

## 📋 **Current State Analysis**

### **Material Certificate Processing (Current)**
```
PDF Upload → File Hook → Phase 1 (Sync): Traditional Analysis + DB Storage
                     → Phase 2 (Async): Queue Agentic Task → Debug Files
```

### **Technical Drawing Processing (Target Pattern)**
```
PDF Upload → File Hook → Phase 1 (Sync): Page/Tile Generation + Analysis Record Creation
                     → Phase 2 (Async): Queue OCR Tasks → Analysis Results + DB Storage
```

---

## 🔍 **Key Differences Analysis**

### **Database Models Comparison**

#### **Technical Drawing Models**
```python
class DrawingFileAnalysis(Base):
    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    page_index: Mapped[int | None] = mapped_column(nullable=True)  # Per-page analysis
    analysis_obj_id: Mapped[int | None] = mapped_column(ForeignKey("s3_object_reference.id"), nullable=True)
    analysis_status: Mapped[DrawingFileAnalysisStatus | None]

class DrawingFilePage(Base):
    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    page_index: Mapped[int]
    image_obj_id: Mapped[int] = mapped_column(ForeignKey("s3_object_reference.id"))
```

#### **Material Certificate Models (Current)**
```python
class MaterialCertificateAnalysis(Base):
    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    analysis_obj_id: Mapped[int | None] = mapped_column(ForeignKey("s3_object_reference.id"))
    total_pages: Mapped[int]
    analysis_status: Mapped[MaterialCertificateAnalysisStatus]  # Document-level analysis

class MaterialCertificatePage(Base):
    id: Mapped[int] = mapped_column(primary_key=True)
    file_resource_revision_id: Mapped[int] = mapped_column(ForeignKey("file_resource_revision.id"))
    analysis_id: Mapped[int] = mapped_column(ForeignKey("material_certificate_analysis.id"))
    page_index: Mapped[int]
    image_obj_id: Mapped[int] = mapped_column(ForeignKey("s3_object_reference.id"))
```

### **Processing Flow Differences**

#### **Technical Drawing Flow**
1. **Phase 1 (Sync)**: 
   - Generate page images and tiles
   - Create `DrawingFilePage` records
   - Create `DrawingFileAnalysis` records (per page, status=PENDING)
   - Prepare task parameters
2. **Phase 2 (Async)**: 
   - Queue OCR tasks per page
   - Tasks update analysis status and store results

#### **Material Certificate Flow (Current)**
1. **Phase 1 (Sync)**: 
   - Generate page images
   - Create `MaterialCertificatePage` records
   - Run traditional analysis immediately
   - Store results and set status=COMPLETED/FAILURE
2. **Phase 2 (Async)**: 
   - Queue agentic task (debug only)
   - No database updates

---

## 🎯 **Migration Strategy**

### **Goal**: Transform material certificate processing to match technical drawing pattern:
- Remove synchronous traditional analysis
- Make agentic analysis the primary processing method
- Follow async-first pattern with proper status tracking
- Add 10+ page validation during upload

---

## 📝 **Step-by-Step Migration Plan**

### **Step 1: Database Schema Updates**
**Objective**: Align material certificate models with drawing pattern

#### **1.1 Update MaterialCertificateAnalysis Model**
- **File**: `qw-mono/src/qw_monodb/table/trunk/material_certificate.py`
- **Changes**:
  - Remove `total_pages` field (not needed, can be calculated)
  - Keep document-level analysis (unlike drawing's per-page analysis)
  - Material certificates are analyzed as complete documents, not per-page

#### **1.2 Database Migration**
- Create migration to remove `total_pages` column
- Update existing records to maintain compatibility

### **Step 2: Update File Hook to Follow Drawing Pattern**
**Objective**: Remove synchronous analysis, add page limit validation

#### **2.1 Modify AddMaterialCertificateRevisionHook**
- **File**: `qw-mono/src/qw_trunk/service/material_certificate/file_hook.py`
- **Changes**:
  - **Add page limit validation** in `before_file_resource_revision_stored()`
  - **Remove traditional analysis** from Phase 1
  - **Simplify Phase 1** to only create pages and analysis record (status=PENDING)
  - **Update Phase 2** to queue agentic task as primary processing method

#### **2.2 Page Limit Validation Implementation**
```python
def before_file_resource_revision_stored(self, rev_data: FileRevisionData, session: RdbSession) -> None:
    # Read PDF and validate page count
    pdf_bio = io.BytesIO(rev_data.data.read())
    pdf_doc = fitz.open(stream=pdf_bio, filetype="pdf")
    
    try:
        page_count = len(pdf_doc)
        if page_count >= 10:  # Reject 10+ pages
            raise InvalidPdfError(f"Material certificate has {page_count} pages. Maximum allowed is 9 pages.")
        
        # Store for later processing
        self.certificate_container = MaterialCertificateContainer(...)
    finally:
        pdf_doc.close()
        rev_data.data.seek(0)
```

### **Step 3: Update Analysis Client**
**Objective**: Make agentic analysis the primary method

#### **3.1 Modify MaterialCertificateAgenticAnalysisClient**
- **File**: `qw-mono/src/qw_trunk/service/material_certificate/agentic_analysis_client.py`
- **Changes**:
  - Rename to `MaterialCertificateAnalysisClient` (remove "Agentic")
  - Add `start_analysis()` method similar to `TechnicalDrawingAnalysisClient`
  - Create analysis record with status=PENDING
  - Return task parameters for queueing

#### **3.2 New start_analysis() Method**
```python
def start_analysis(
    self,
    file_resource_revision_id: int,
    pdf_data: bytes,
    session: RdbSession,
) -> Dict[str, Any]:
    # Create analysis record
    analysis = MaterialCertificateAnalysis(
        file_resource_revision_id=file_resource_revision_id,
        analysis_obj_id=None,
        analysis_status=MaterialCertificateAnalysisStatus.PENDING,
    )
    session.add(analysis)
    session.flush([analysis])
    
    # Return task parameters
    return {
        "analysis_id": analysis.id,
        "task_name": "qw_worker.tasks.material_certificate_3_1_v1.analyze_material_certificate_agentic",
        "kwargs": {
            "file_resource_revision_id": file_resource_revision_id,
            "analysis_record_id": analysis.id,
            "pdf_data": pdf_data,
        },
    }
```

### **Step 4: Update Worker Task**
**Objective**: Make worker task update database like drawing analysis

#### **4.1 Modify Worker Task**
- **File**: `qw-mono/src/qw_worker/tasks/material_certificate_3_1_v1.py`
- **Changes**:
  - Add `analysis_record_id` parameter
  - Update analysis status to ANALYZING at start
  - Store results in S3 and update analysis_obj_id
  - Set status to COMPLETED/FAILURE based on outcome
  - Remove debug-only mode, make it production-ready

#### **4.2 Database Integration Pattern**
```python
def analyze_material_certificate_agentic(
    self: Task,
    file_resource_revision_id: int,
    analysis_record_id: int,
    pdf_data: bytes,
) -> Dict[str, Any]:
    # Update status to ANALYZING
    with begin_or_use_session(rdb, strategy=TransactionStrategy.REUSE_OR_NEW) as session:
        analysis_record = session.execute(
            select(MaterialCertificateAnalysis).where(MaterialCertificateAnalysis.id == analysis_record_id)
        ).scalar_one_or_none()
        
        if analysis_record:
            analysis_record.analysis_status = MaterialCertificateAnalysisStatus.ANALYZING
            session.flush([analysis_record])
    
    # Run agentic analysis
    result = await agentic_service.analyze_certificate(pdf_data)
    
    # Store results and update status
    with begin_or_use_session(rdb, strategy=TransactionStrategy.REUSE_OR_NEW) as session:
        # Store in S3
        obj_id = s3_service.add_object(...)
        
        # Update analysis record
        analysis_record.analysis_obj_id = obj_id
        analysis_record.analysis_status = MaterialCertificateAnalysisStatus.COMPLETED
        session.flush([analysis_record])
```

### **Step 5: Remove Traditional Analysis Components**
**Objective**: Clean up unused traditional analysis code

#### **5.1 Files to Remove/Modify**
- **Remove**: `qw-mono/src/qw_trunk/service/material_certificate/analysis.py` (traditional analysis)
- **Remove**: `qw-mono/src/qw_trunk/service/material_certificate/prompts.py` (traditional prompts)
- **Modify**: `qw-mono/src/qw_trunk/service/material_certificate/models.py` (keep only if used elsewhere)
- **Update**: `qw-mono/src/qw_trunk/service/material_certificate/material_certificate.py` (remove traditional service)

#### **5.2 Update MaterialCertificateService**
- **File**: `qw-mono/src/qw_trunk/service/material_certificate/material_certificate.py`
- **Changes**:
  - Remove dependency on `MaterialCertificateAnalysisService`
  - Simplify to only provide file hook generation
  - Follow `DrawingService` pattern

### **Step 6: Update Database Service**
**Objective**: Align with drawing service patterns

#### **6.1 Update MaterialCertificateDatabaseService**
- **File**: `qw-mono/src/qw_trunk/service/material_certificate/db.py`
- **Changes**:
  - Add methods similar to `DrawingDatabaseService`
  - Add `find_material_certificate_analysis()` method
  - Remove `total_pages` references
  - Add proper error handling and status checking

---

## 🔧 **Implementation Order**

### **Phase A: Preparation (Safe Changes)**
1. Update database models (remove total_pages)
2. Create database migration
3. Update MaterialCertificateDatabaseService
4. Add page limit validation to file hook

### **Phase B: Core Migration (Breaking Changes)**
5. Remove traditional analysis from file hook
6. Update analysis client to create PENDING records
7. Update worker task for database integration
8. Remove traditional analysis components

### **Phase C: Cleanup**
9. Update service configuration
10. Remove unused imports and dependencies
11. Update tests and documentation

---

## ⚠️ **Risk Mitigation**

### **Backward Compatibility**
- Keep existing analysis records intact
- Ensure API endpoints continue to work
- Maintain S3 object structure

### **Rollback Plan**
- Database migration should be reversible
- Keep traditional analysis code in git history
- Test thoroughly in development environment

### **Validation**
- Add comprehensive error handling
- Ensure proper status transitions
- Validate S3 storage paths match expectations

---

This migration will transform material certificate processing from a dual-track approach to a clean, async-first pattern that matches the technical drawing processing workflow while maintaining the sophisticated PydanticAI multi-agent analysis as the primary processing method.
