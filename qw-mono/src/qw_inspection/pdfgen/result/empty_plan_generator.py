import io
import logging

import jinja2
import weasyprint

from qw_inspection.pdfgen.result.empty_plan_input import EmptyPlanReportContextMapperInput
from qw_inspection.pdfgen.result.empty_plan_mapper import EmptyPlanHtmlContextMapper
from qw_inspection.pdfgen.result.generator import InspectionPdfGenerationError
from qw_inspection.pdfgen.result.strings import INSPECTION_STRINGS
from qw_inspection.pdfgen.result.template import TEMPLATES_DIR, HtmlContext
from qw_log_interface import NO_LOGGER, Logger


class EmptyPlanPdfGenerator:
    """
    Generator for empty inspection plan PDFs.
    """

    def __init__(self, logger: Logger = NO_LOGGER):
        self.logger = logger
        self.mapper = EmptyPlanHtmlContextMapper()
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(TEMPLATES_DIR),
            autoescape=True,
        )

    def map_and_generate_pdf(self, ctx_input: EmptyPlanReportContextMapperInput) -> io.BytesIO:
        """
        Map the input to a context and generate a PDF.

        Args:
            ctx_input: The input for the empty plan report context mapper

        Returns:
            The generated PDF as a BytesIO object
        """
        # Map the input to a context - always use A4 for empty plans
        inspection_ctx = self.mapper.map(ctx_input)

        # Get the strings for the selected language
        strings = INSPECTION_STRINGS[ctx_input.opts.lang]

        # Create the HTML context
        html_ctx = HtmlContext(
            inspection=inspection_ctx,
            strings=strings,
        )

        # Use the template based on report_type, ensuring lowercase for consistency
        template_name = f"{ctx_input.opts.report_type.lower()}.jinja2"

        try:
            template = self.jinja_env.get_template(template_name)
        except jinja2.exceptions.TemplateNotFound:
            self.logger.error(f"Template {template_name} not found")
            raise InspectionPdfGenerationError(f"Template {template_name} not found")

        # Render the template - pass context components directly instead of nested
        try:
            # Extract all attributes from html_ctx and pass them directly to the template
            # This makes 'strings' directly accessible in the template instead of via ctx.strings
            html = template.render(
                inspection=html_ctx.inspection,
                strings=html_ctx.strings,
                action_type_measurement=html_ctx.action_type_measurement,
                action_type_image=html_ctx.action_type_image,
                action_type_video=html_ctx.action_type_video,
                action_type_q_and_a=html_ctx.action_type_q_and_a,
                action_result_type_binary=html_ctx.action_result_type_binary,
                action_result_type_continuous=html_ctx.action_result_type_continuous,
                action_result_type_continuous_or_binary=html_ctx.action_result_type_continuous_or_binary,
            )
        except Exception as e:
            self.logger.error(f"Error rendering template {template_name}", exc_info=e)
            raise InspectionPdfGenerationError(f"Error rendering template: {str(e)}")

        # Generate the PDF using WeasyPrint
        try:
            # Configure logging for font processing - otherwise too noisy
            font_loggers = ["fontTools.subset", "fontTools.ttLib.ttFont", "fontTools.subset.timer"]
            for logger_name in font_loggers:
                logger = logging.getLogger(logger_name)
                logger.setLevel(logging.WARNING)

            # Keep WeasyPrint warnings visible for debugging
            weasyprint_logger = logging.getLogger("weasyprint")
            weasyprint_logger.setLevel(logging.WARNING)

            self.logger.info("Generating empty plan PDF with WeasyPrint")

            # Use WeasyPrint for PDF generation with base_url for relative asset paths
            base_url = TEMPLATES_DIR.resolve().as_uri()  # Use templates dir as base for ../assets/ paths
            pdf_document = weasyprint.HTML(string=html, base_url=base_url).write_pdf()
            if pdf_document is None:
                raise InspectionPdfGenerationError("WeasyPrint returned None for PDF document")
            pdf_buffer = io.BytesIO(pdf_document)
            pdf_buffer.seek(0)
            return pdf_buffer
        except Exception as e:
            self.logger.error("Error generating PDF with WeasyPrint", exc_info=e)
            raise InspectionPdfGenerationError(f"Could not generate PDF with WeasyPrint, error: {e}")
