import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@angular/common"
import { Component, inject } from "@angular/core"
import { MaterialCertificateResourceStateService } from "@qw-shared-services/material-certificate-resource-state.service"
import { DropdownComponent } from "@qw-shared-ui/dropdown/dropdown.component"
import { Filter } from "@qw-shared-utils/qw-check-filter-value"

import { QwChemicalComposition } from "@qw-api/models"

interface DropdownOption<T> {
  label: string
  value: T
}

type ChemicalElement = keyof QwChemicalComposition
type FilterOperator = Filter["operator"]

@Component({
  selector: "qw-material-certificate-analysis-results-filter",
  templateUrl: "./material-certificate-analysis-results-filter.component.html",
  standalone: true,
  imports: [NgIf, NgFor, DropdownComponent],
})
export class MaterialCertificateAnalysisResultsFilterComponent {
  protected readonly stateService = inject(
    MaterialCertificateResourceStateService,
  )

  protected readonly elements: DropdownOption<ChemicalElement>[] = [
    { label: "Aluminum (Al)", value: "Al" },
    { label: "Boron (B)", value: "B" },
    { label: "Carbon (C)", value: "C" },
    { label: "Calcium (Ca)", value: "Ca" },
    { label: "Chromium (Cr)", value: "Cr" },
    { label: "Copper (Cu)", value: "Cu" },
    { label: "Manganese (Mn)", value: "Mn" },
    { label: "Molybdenum (Mo)", value: "Mo" },
    { label: "Nitrogen (N)", value: "N" },
    { label: "Niobium (Nb)", value: "Nb" },
    { label: "Nickel (Ni)", value: "Ni" },
    { label: "Phosphorus (P)", value: "P" },
    { label: "Sulfur (S)", value: "S" },
    { label: "Silicon (Si)", value: "Si" },
    { label: "Titanium (Ti)", value: "Ti" },
    { label: "Vanadium (V)", value: "V" },
  ]

  protected readonly operators: DropdownOption<FilterOperator>[] = [
    { label: ">", value: ">" },
    { label: "<", value: "<" },
    { label: "=", value: "=" },
    { label: ">=", value: ">=" },
    { label: "<=", value: "<=" },
  ]

  protected selectedElement: DropdownOption<ChemicalElement> | null = null
  protected selectedOperator: DropdownOption<FilterOperator> | null = null

  protected onElementSelect(option: DropdownOption<ChemicalElement>): void {
    this.selectedElement = option
  }

  protected onOperatorSelect(option: DropdownOption<FilterOperator>): void {
    this.selectedOperator = option
  }

  protected addFilter(
    element: DropdownOption<ChemicalElement> | null,
    operator: DropdownOption<FilterOperator> | null,
    value: string,
  ): void {
    if (!element?.value || !operator?.value || !value) return

    const numValue = parseFloat(value)
    if (isNaN(numValue)) return

    const newFilter: Filter = {
      element: element.value,
      operator: operator.value,
      value: numValue,
    }

    this.stateService.updateState({
      filters: [...this.stateService.filters(), newFilter],
    })
  }

  protected removeFilter(index: number): void {
    const currentFilters = [...this.stateService.filters()]
    currentFilters.splice(index, 1)

    this.stateService.updateState({
      filters: currentFilters,
    })
  }
}
