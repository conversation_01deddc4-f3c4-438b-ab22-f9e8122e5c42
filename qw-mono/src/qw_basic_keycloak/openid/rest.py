from typing import Sequence

import requests
from joserfc import jwk
from pydantic import AnyHttpUrl

from qw_basic_keycloak.interface import Code<PERSON><PERSON><PERSON>uth<PERSON>nit, OpenIdConnectError
from qw_basic_keycloak.openid.dtos.authorization import SCOPE_OPEN_ID, AuthenticationRequest
from qw_basic_keycloak.openid.dtos.token import (
    GrantType,
    InitiateLogoutRequest,
    RefreshTokenRequest,
    TokenRequest,
    TokenResponse,
)
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class IdentityProviderConfiguration:
    def __init__(
        self,
        authorization_endpoint: AnyHttpUrl,
        token_endpoint: AnyHttpUrl,
        userinfo_endpoint: AnyHttpUrl,
        end_session_endpoint: AnyHttpUrl,
        key_data: jwk.KeyFlexible,
    ):
        self.authorization_endpoint = authorization_endpoint
        self.token_endpoint = token_endpoint
        self.userinfo_endpoint = userinfo_endpoint
        self.end_session_endpoint = end_session_endpoint
        self.key_data = key_data


class OpenIdRestClient(object):
    """
    Implementation of the OpenID Connect spec for apps that have exposure to users and perform authentication.
    """

    def __init__(
        self,
        client_id: str,
        redirect_uri: str,
        idp_config: IdentityProviderConfiguration,
        client_secret: str | None = None,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.idp_config = idp_config
        self.logger = lf.get_logger(__name__)

    def get_redirect_uri(self) -> str:
        return self.redirect_uri

    def flow_code_initiate(self, state: str, nonce: str, additional_scopes: Sequence[str] = ()) -> CodeFlowAuthInit:
        scopes = [SCOPE_OPEN_ID] + list(additional_scopes)
        req = AuthenticationRequest(
            scope=" ".join(scopes),
            response_type="code",
            redirect_uri=AnyHttpUrl(self.redirect_uri),
            state=state,
            nonce=nonce,
            client_id=self.client_id,
        )
        return CodeFlowAuthInit(auth_url=self.idp_config.authorization_endpoint, req=req)

    def flow_code_conclude(self, authorization_code: str, timeout_seconds: float = 10) -> TokenResponse:
        req = TokenRequest(
            grant_type=GrantType.AUTHORIZATION_CODE,
            code=authorization_code,
            redirect_uri=AnyHttpUrl(self.redirect_uri),
            client_id=self.client_id,
            client_secret=self.client_secret,
        )

        # will be sent as application/x-www-form-urlencoded
        resp = requests.post(
            str(self.idp_config.token_endpoint),
            data=req.model_dump(mode="json", exclude_none=True),
            timeout=timeout_seconds,
        )
        if not 200 <= resp.status_code < 400:
            raise OpenIdConnectError(f"Could not conclude code flow ({resp.status_code})\n{resp.text}")
        return TokenResponse(**resp.json())

    def try_refresh_token(self, refresh_token: str, timeout_seconds: float = 10) -> TokenResponse:
        req = RefreshTokenRequest(
            refresh_token=refresh_token,
            client_id=self.client_id,
            client_secret=self.client_secret,
        )

        # will be sent as application/x-www-form-urlencoded
        resp = requests.post(
            str(self.idp_config.token_endpoint),
            data=req.model_dump(mode="json", exclude_none=True),
            timeout=timeout_seconds,
        )
        if not 200 <= resp.status_code < 400:
            raise OpenIdConnectError(f"Could not refresh token ({resp.status_code})\n{resp.text}")
        return TokenResponse(**resp.json())

    # def try_silent_auth(self, id_token_b64: str) -> TokenResponse:
    #     req = AuthenticationRequest(
    #         response_type="none",
    #         client_id=self.client_id,
    #         redirect_uri=AnyHttpUrl(self.redirect_uri),
    #         prompt="none",
    #         id_token_hint=id_token_b64,
    #         # state="state",
    #         # nonce="123",
    #     )

    def initiate_logout(
        self, id_token: str, post_logout_redirect_uri: str | None = None, timeout_seconds: float = 10
    ) -> None:
        # https://stackoverflow.com/a/46769801

        req = InitiateLogoutRequest(
            id_token_hint=id_token,
            client_id=self.client_id,
            post_logout_redirect_uri=None if post_logout_redirect_uri is None else AnyHttpUrl(post_logout_redirect_uri),
        )

        # will be sent as application/x-www-form-urlencoded
        resp = requests.post(
            str(self.idp_config.end_session_endpoint),
            data=req.model_dump(mode="json", exclude_none=True),
            timeout=timeout_seconds,
        )
        if not 200 <= resp.status_code < 400:
            raise OpenIdConnectError(f"Could not initiate logout ({resp.status_code})\n{resp.text}")
