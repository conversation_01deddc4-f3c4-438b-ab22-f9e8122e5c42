{"name": "Tolerances_based_on_decimal_places", "tables": [{"name": "Tabelle 1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 2, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/-/0.01/0.005/0.0005"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/-/0.01/0.005/0.0005"]], "tolerance": {"upper": 0.005, "lower": -0.005}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["-/-/0.01/0.005/0.0005"]], "tolerance": {"upper": 0.0005, "lower": -0.0005}}, {"lowerValueLimit": {"value": 2, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/-/0.03/0.01/0.0005"]], "tolerance": {"upper": 0.03, "lower": -0.03}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/-/0.03/0.01/0.0005"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["-/-/0.03/0.01/0.0005"]], "tolerance": {"upper": 0.0005, "lower": -0.0005}}, {"lowerValueLimit": {"value": 1, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["-/-/0.03/0.005/-"]], "tolerance": {"upper": 0.03, "lower": -0.03}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/-/0.03/0.005/-"]], "tolerance": {"upper": 0.005, "lower": -0.005}}, {"lowerValueLimit": {"value": 2, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/-/0.03/0.01/-"]], "tolerance": {"upper": 0.03, "lower": -0.03}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/-/0.03/0.01/-"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 1, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["-/0.02/0.01/0.005/-"]], "tolerance": {"upper": 0.02, "lower": -0.02}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/0.02/0.01/0.005/-"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/0.02/0.01/0.005/-"]], "tolerance": {"upper": 0.005, "lower": -0.005}}, {"lowerValueLimit": {"value": 1, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["-/0.025/0.01/0.005/-"]], "tolerance": {"upper": 0.025, "lower": -0.025}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/0.025/0.01/0.005/-"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/0.025/0.01/0.005/-"]], "tolerance": {"upper": 0.005, "lower": -0.005}}, {"lowerValueLimit": {"value": 1, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["-/0.1/0.03/0.005/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/0.1/0.03/0.005/-"]], "tolerance": {"upper": 0.03, "lower": -0.03}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/0.1/0.03/0.005/-"]], "tolerance": {"upper": 0.005, "lower": -0.005}}, {"lowerValueLimit": {"value": 1, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["-/0.1/0.01/0.005/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/0.1/0.01/0.005/-"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/0.1/0.01/0.005/-"]], "tolerance": {"upper": 0.005, "lower": -0.005}}, {"lowerValueLimit": {"value": 1, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["-/0.26/0.13/0.025/-"]], "tolerance": {"upper": 0.26, "lower": -0.26}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/0.26/0.13/0.025/-"]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/0.26/0.13/0.025/-"]], "tolerance": {"upper": 0.025, "lower": -0.025}}, {"lowerValueLimit": {"value": 1, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["-/1/0.5/0.1/-"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/1/0.5/0.1/-"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/1/0.5/0.1/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 1, "inclusive": true}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["-/2.5/0.25/0.13/-"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["-/2.5/0.25/0.13/-"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["-/2.5/0.25/0.13/-"]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["0.1/0.01/0.005/-/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["0.1/0.01/0.005/-/-"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["0.1/0.01/0.005/-/-"]], "tolerance": {"upper": 0.005, "lower": -0.005}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["0.1/0.05/0.01/0.002/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["0.1/0.05/0.01/0.002/-"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["0.1/0.05/0.01/0.002/-"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["0.1/0.05/0.01/0.002/-"]], "tolerance": {"upper": 0.002, "lower": -0.002}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["0.1/0.1/0.1/-/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["0.1/0.1/0.1/-/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["0.1/0.1/0.1/-/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["0.2/0.1/0.05/-/-"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["0.2/0.1/0.05/-/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["0.2/0.1/0.05/-/-"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["0.25/0.1/0.01/0.002/-"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["0.25/0.1/0.01/0.002/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["0.25/0.1/0.01/0.002/-"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["0.25/0.1/0.01/0.002/-"]], "tolerance": {"upper": 0.002, "lower": -0.002}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["0.5/0.1/0.05/-/-"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["0.5/0.1/0.05/-/-"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["0.5/0.1/0.05/-/-"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["0.5/0.1/0.25/1/2"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["0.5/0.1/0.25/1/2"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["0.5/0.1/0.25/1/2"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["0.5/0.1/0.25/1/2"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["0.5/0.1/0.25/1/2"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 0, "inclusive": true}, "rowIdentifiers": [["1/0.1/0.01/0.005/0.002"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 1, "inclusive": false}, "upperValueLimit": {"value": 1, "inclusive": true}, "rowIdentifiers": [["1/0.1/0.01/0.005/0.002"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["1/0.1/0.01/0.005/0.002"]], "tolerance": {"upper": 0.01, "lower": -0.01}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["1/0.1/0.01/0.005/0.002"]], "tolerance": {"upper": 0.005, "lower": -0.005}}, {"lowerValueLimit": {"value": 4, "inclusive": false}, "upperValueLimit": {"value": 4, "inclusive": true}, "rowIdentifiers": [["1/0.1/0.01/0.005/0.002"]], "tolerance": {"upper": 0.002, "lower": -0.002}}]}]}