from dataclasses import dataclass
from enum import Enum, unique
from typing import Any, Dict

from qw_drawing_toolkit.pdf.models.base_models import T_BBOX, GdtSymbol
from qw_drawing_toolkit.pdf.parser.symbol_mapper import Symbol

# |------------------------------|
# |          Enums               |
# |------------------------------|


@unique
class DimensionType(str, Enum):
    UNKNOWN = "unknown"
    LENGTH = "length"
    DIAMETER = "diameter"
    RADIUS = "radius"
    ANGLE = "angle"
    THREAD = "thread"
    COUNTERSINK = "countersink"
    ROUGHNESS = "roughness"

    @classmethod
    def from_string(cls, value: str) -> "DimensionType":
        try:
            return DimensionType(value)
        except ValueError:
            return DimensionType.UNKNOWN


@unique
class RoughnessCategory(str, Enum):
    UNKNOWN = "unknown"
    ARITHMETICAL_MEAN = "ra"
    MAXIMUM_HEIGHT = "ry"
    TEN_POINT = "rz"
    MEAN_SPACING_OF_PROFILE_IRREGULARITIES = "sm"
    MEAN_SPACING_OF_LOCAL_PEAKS_OF_PROFILE = "s"
    PROFILE_BEARING_LENGTH_RATIO = "tp"

    @classmethod
    def from_string(cls, value: str) -> "RoughnessCategory":
        try:
            return RoughnessCategory(value)
        except ValueError:
            return RoughnessCategory.UNKNOWN


# |------------------------------|
# |          Models              |
# |------------------------------|


class TextBox:
    def __init__(
        self,
        text: str,
        font: str,
        font_size: float,
        bbox: T_BBOX,
        direction: tuple[float, float],
        is_gd_t: bool = False,
        is_symbol: bool = False,
    ):
        self.text = text
        self.font = font
        self.font_size = font_size
        self.is_gd_t = is_gd_t
        self.is_symbol = is_symbol

        min_x = min(bbox[0], bbox[2])
        max_x = max(bbox[0], bbox[2])
        min_y = min(bbox[1], bbox[3])
        max_y = max(bbox[1], bbox[3])

        self.bbox = (min_x, min_y, max_x, max_y)
        self.direction = direction

    def to_dict(self) -> Dict[str, Any]:
        return {
            "text": self.text,
            "font": self.font,
            "bbox": self.bbox,
            "is_gd_t": self.is_gd_t,
            "is_symbol": self.is_symbol,
        }


class SymbolBox(TextBox):
    def __init__(self, symbol: Symbol, bbox: T_BBOX):
        super().__init__(
            symbol.get_symbol_string_prefix() + str(symbol.value),
            symbol.get_symbol_dummy_font(),
            min(bbox[2] - bbox[0], bbox[3] - bbox[1]),
            bbox,
            (0, 0),
            False,
            True,
        )
        self.symbol = symbol
        self.bbox = bbox

    def to_dict(self) -> Dict[str, Any]:
        return {
            "text": self.text,
            "font": self.font,
            "bbox": self.bbox,
            "is_gd_t": self.is_gd_t,
            "is_symbol": self.is_symbol,
            "symbol": self.symbol.name,
        }


class DimensionTextBox:
    def __init__(
        self,
        bounding_box: T_BBOX,
        value: float | None = None,
        dimension_type: DimensionType = DimensionType.UNKNOWN,
        length: float | None = None,
        angle: float | None = None,
        depth: float | None = None,
        thru: bool = False,
        thru_all: bool = False,
        diameter: float | None = None,
        fit: str | None = None,
        thread: str | None = None,
        pitch: float | None = None,
        tolerance_grade: str | None = None,
        radius: float | None = None,
        roughness_category: RoughnessCategory | None = None,
        multiplier: float | None = None,
        original_text: str | None = None,
        unmatched_text: str | None = None,
        upper_tolerance: float | None = None,
        lower_tolerance: float | None = None,
    ):
        self.value = value
        self.dimension_type = dimension_type
        self.bounding_box = bounding_box
        self.length = length
        self.angle = angle
        self.depth = depth
        self.thru = thru
        self.thru_all = thru_all
        self.diameter = diameter
        self.fit = fit
        self.thread = thread
        self.pitch = pitch
        self.tolerance_grade = tolerance_grade
        self.radius = radius
        self.roughness_category = roughness_category
        self.multiplier = multiplier
        self.original_text = original_text
        self.unmatched_text = unmatched_text
        self.upper_tolerance = upper_tolerance
        self.lower_tolerance = lower_tolerance

    def to_dict(self) -> Dict[str, Any]:
        result: Dict[str, Any] = {}

        if self.value:
            result["value"] = self.value
        if self.dimension_type:
            result["dimension_type"] = self.dimension_type
        if self.bounding_box:
            result["bounding_box"] = self.bounding_box
        if self.length:
            result["length"] = self.length
        if self.angle:
            result["angle"] = self.angle
        if self.depth:
            result["depth"] = self.depth
        if self.thru:
            result["thru"] = self.thru
        if self.thru_all:
            result["thru_all"] = self.thru_all
        if self.diameter:
            result["diameter"] = self.diameter
        if self.fit:
            result["fit"] = self.fit
        if self.thread:
            result["thread"] = self.thread
        if self.pitch:
            result["pitch"] = self.thread
        if self.tolerance_grade:
            result["tolerance_grade"] = self.tolerance_grade
        if self.radius:
            result["radius"] = self.radius
        if self.roughness_category:
            result["roughness_category"] = self.roughness_category.value
        if self.multiplier:
            result["multiplier"] = self.multiplier
        if self.original_text:
            result["original_text"] = self.original_text
        if self.unmatched_text:
            result["unmatched_text"] = self.unmatched_text
        if self.upper_tolerance:
            result["upper_tolerance"] = self.upper_tolerance
        if self.lower_tolerance:
            result["lower_tolerance"] = self.lower_tolerance

        return result


class DatumBox:
    def __init__(self, datum: str, bbox: T_BBOX, text_box: TextBox):
        self.datum = datum
        self.bbox = bbox
        self.text_box = text_box

    def to_dict(self) -> Dict[str, Any]:
        return {"datum": self.datum, "bbox": self.bbox, "text_box": self.text_box.to_dict()}


class SymbolMapperBox:
    def __init__(self, symbol: GdtSymbol, bbox: T_BBOX):
        self.symbol = symbol
        self.bbox = bbox

    def to_dict(self) -> Dict[str, Any]:
        return {"symbol": self.symbol.name, "bbox": self.bbox}


@dataclass
class PdfPageSizeInfo:
    width_in_pts: float
    height_in_pts: float
    width_in_mm: float
    height_in_mm: float
    dpi: float
