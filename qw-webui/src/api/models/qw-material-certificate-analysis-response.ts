/* tslint:disable */
/* eslint-disable */
import { QwCertificateMetadata } from '../models/qw-certificate-metadata';
import { QwProduct } from '../models/qw-product';

/**
 * [schema: MaterialCertificateAnalysisResponse] Final response from the redesigned agentic analysis.
 * Includes metadata context and elegantly aggregated products.
 */
export interface QwMaterialCertificateAnalysisResponse {

  /**
   * Certificate metadata and analysis context
   */
  metadata: QwCertificateMetadata;

  /**
   * List of products with aggregated data
   */
  products: Array<QwProduct>;
}
