"""Client for interacting with material certificate analysis tasks."""
from typing import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy import select
from sqlalchemy.orm import Session as RdbSession

from qw_basic_rdb.common import TransactionStrategy, begin_or_use_session
from qw_basic_rdb.interface import RelationalDatabase
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_monodb.table.trunk.material_certificate import MaterialCertificateAnalysis, MaterialCertificateAnalysisStatus


class MaterialCertificateAnalysisClient:
    """Client for interacting with material certificate analysis tasks."""

    def __init__(
        self,
        rdb: RelationalDatabase,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.rdb = rdb
        self.logger = lf.get_logger(__name__)

    def start_analysis(
        self,
        bucket: str,
        file_resource_revision_id: int,
        pdf_data: bytes,
        session: RdbSession,
    ) -> Dict[str, Any]:
        """
        Create analysis record and return task parameters.
        Task queueing is handled by the caller after transaction commits.

        Args:
            file_resource_revision_id: ID of the file revision to analyze
            pdf_data: PDF file data to pass to the task
            session: Database session (required - must be within a transaction)

        Returns:
            Dictionary with task parameters for later queueing
        """
        # Check if analysis already exists for this file revision
        existing = session.execute(
            select(MaterialCertificateAnalysis).where(
                MaterialCertificateAnalysis.file_resource_revision_id == file_resource_revision_id
            )
        ).scalar_one_or_none()

        if existing:
            # If analysis exists but failed, we can retry
            if existing.analysis_status == MaterialCertificateAnalysisStatus.FAILURE:
                existing.analysis_status = MaterialCertificateAnalysisStatus.PENDING
                existing.analysis_obj_id = None
                session.flush([existing])
                analysis_id = existing.id
            else:
                # Analysis already exists and is not in failed state
                analysis_id = existing.id
        else:
            # Create new analysis record
            analysis = MaterialCertificateAnalysis(
                file_resource_revision_id=file_resource_revision_id,
                analysis_obj_id=None,
                analysis_status=MaterialCertificateAnalysisStatus.PENDING,
            )
            session.add(analysis)
            session.flush([analysis])
            analysis_id = analysis.id

        # Return task parameters instead of queueing immediately
        return {
            "analysis_id": analysis_id,
            "task_name": "qw_worker.tasks.material_certificate_3_1_v1.analyze_material_certificate_agentic",
            "kwargs": {
                "bucket": bucket,
                "file_resource_revision_id": file_resource_revision_id,
                "analysis_record_id": analysis_id,
                "pdf_data": pdf_data,
            },
        }

    def get_analysis_data(
        self,
        file_resource_revision_id: int,
        session: Optional[RdbSession] = None,
    ) -> Tuple[Optional[int], Optional[MaterialCertificateAnalysisStatus]]:
        """
        Get the analysis object ID and status for a file revision.

        This method is designed to avoid DetachedInstanceError by returning
        the data needed by endpoints rather than the database object itself.

        Args:
            file_resource_revision_id: ID of the file revision
            session: Optional database session

        Returns:
            Tuple of (analysis_obj_id, analysis_status) or (None, None) if not found
        """
        with begin_or_use_session(self.rdb, session, strategy=TransactionStrategy.REUSE_OR_NEW) as s:
            analysis = s.execute(
                select(MaterialCertificateAnalysis).where(
                    MaterialCertificateAnalysis.file_resource_revision_id == file_resource_revision_id
                )
            ).scalar_one_or_none()

            if analysis:
                return analysis.analysis_obj_id, analysis.analysis_status
            return None, None
