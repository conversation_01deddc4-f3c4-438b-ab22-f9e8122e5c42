"""Task registry for worker module."""
from typing import Any, Callable, Dict

from celery import Celery

from qw_basic_rdb.interface import RelationalDatabase
from qw_basic_s3.interface import S3Storage
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_tenant_config.registry import TenantConfigRegistry
from qw_worker.config import QwWorker<PERSON>piKeys
from qw_worker.tasks.material_certificate_3_1_v1 import create_material_certificate_3_1_analysis_v1_task
from qw_worker.tasks.technical_drawing_analysis_v2 import create_technical_drawing_analysis_v2_task
from qw_worker.tasks.test import create_test_task


class TaskRegistry:
    """Registry for Celery tasks.

    This class is responsible for creating and registering all tasks
    with the Celery application.
    """

    def __init__(
        self,
        app: Celery,
        s3_storage: S3Storage,
        rdb: RelationalDatabase,
        tenant_registry: TenantConfigRegistry,
        api_keys: QwWorkerApiKeys,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """Initialize the task registry.

        Args:
            app: The Celery application
            s3_storage: S3 storage instance for accessing files
            rdb: Relational database instance
            tenant_registry: Tenant configuration registry
            api_keys: API keys for external services
            lf: Log factory
        """
        self.app = app
        self.s3_storage = s3_storage
        self.rdb = rdb
        self.tenant_registry = tenant_registry
        self.api_keys = api_keys
        self.lf = lf
        self.tasks: Dict[str, Callable[..., Any]] = {}

        # Register tasks
        self._register_tasks()

    def _register_tasks(self) -> None:
        """Register all tasks with Celery."""
        logger = self.lf.get_logger(__name__)
        logger.info("Registering tasks with Celery")

        # Register tasks / manually maintained
        self.tasks["test_task"] = create_test_task(self.app, self.lf)

        # Import necessary services for OCR task
        from qw_trunk.service.resource.s3_layout import S3BucketLayout
        from qw_trunk.service.resource.s3_object import S3ObjectService

        # Create S3 layout and service
        s3_layout = S3BucketLayout()
        s3_service = S3ObjectService(self.rdb, self.s3_storage, lf=self.lf)

        # Register OCR task
        self.tasks["process_technical_drawing_analysis_v2"] = create_technical_drawing_analysis_v2_task(
            self.app, self.s3_storage, self.api_keys, self.rdb, s3_layout, s3_service, self.lf
        )

        # Register material certificate analysis task
        self.tasks["analyze_material_certificate_agentic"] = create_material_certificate_3_1_analysis_v1_task(
            self.app, self.s3_storage, self.api_keys, self.rdb, s3_layout, s3_service, self.lf
        )

        logger.info(f"Registered {len(self.tasks)} tasks with Celery")

    def get_task(self, name: str) -> Callable[..., Any]:
        """Get a task by name.

        Args:
            name: The name of the task

        Returns:
            The task function

        Raises:
            KeyError: If the task is not found
        """
        if name not in self.tasks:
            raise KeyError(f"Task '{name}' not found in registry")

        return self.tasks[name]
