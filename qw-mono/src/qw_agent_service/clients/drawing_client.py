"""HTTP client for technical drawing analysis services."""
from typing import Any, Dict, Optional, Tu<PERSON>, Union

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_agent_service.clients.base_client import BaseHttpClient


class DrawingAnalysisHttpClient:
    """HTTP client for technical drawing analysis operations."""
    
    def __init__(
        self,
        base_url: str,
        session_token: str,
        timeout: float = 60.0,  # Longer timeout for analysis operations
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the drawing analysis HTTP client.
        """
        self.client = BaseHttpClient(base_url, session_token, timeout, lf)
        self.logger = lf.get_logger(__name__)
    
    async def get_analysis_data(
        self, 
        file_resource_revision_id: int
    ) -> Optional[Union[Tuple[Optional[int], Optional[str]], Dict[int, Tuple[Optional[int], Optional[str]]]]]:
        """
        Get technical drawing analysis data for a file resource revision.
        
        This method mimics the interface of TechnicalDrawingAnalysisClient.get_analysis_data()
        but uses HTTP calls to the existing Falcon endpoints.
        """
        try:
            self.logger.info(f"Getting drawing analysis data for revision: {file_resource_revision_id}")
            
            # Call the existing Falcon endpoint for drawing analysis
            # Note: This endpoint may need to be created in the Falcon service
            # For now, we'll simulate the expected response structure
            response = await self.client.get(
                f"/api/v1/file-resource/{file_resource_revision_id}/drawing-analysis"
            )
            
            if response.get("status") == "SUCCESS":
                analysis_data = response.get("analysis_data")
                if analysis_data:
                    # Convert HTTP response to expected format
                    if isinstance(analysis_data, dict):
                        # Multi-page format
                        result = {}
                        for page_idx, page_data in analysis_data.items():
                            obj_id = page_data.get("obj_id")
                            status = page_data.get("status")
                            result[int(page_idx)] = (obj_id, status)
                        return result
                    else:
                        # Single page format
                        obj_id = analysis_data.get("obj_id")
                        status = analysis_data.get("status")
                        return (obj_id, status)
                
            elif response.get("status") == "NOT_FOUND":
                self.logger.info(f"No analysis found for revision: {file_resource_revision_id}")
                return None
            
            elif response.get("status") == "ERROR":
                self.logger.error(f"Error getting analysis data: {response.get('error')}")
                return None
            
            else:
                self.logger.warning(f"Unexpected response status: {response.get('status')}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting drawing analysis data: {e}")
            return None
    
    async def start_analysis(
        self,
        file_resource_revision_id: int,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Start technical drawing analysis for a file resource revision.
        """
        try:
            self.logger.info(f"Starting drawing analysis for revision: {file_resource_revision_id}")
            
            response = await self.client.post(
                f"/api/v1/file-resource/{file_resource_revision_id}/drawing-analysis/start",
                json_data={"file_resource_revision_id": file_resource_revision_id, **kwargs}
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error starting drawing analysis: {e}")
            return {"status": "ERROR", "error": str(e)}
    
    async def get_analysis_status(
        self,
        file_resource_revision_id: int
    ) -> Dict[str, Any]:
        """
        Get the status of technical drawing analysis.
        """
        try:
            response = await self.client.get(
                f"/api/v1/file-resource/{file_resource_revision_id}/drawing-analysis/status"
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error getting analysis status: {e}")
            return {"status": "ERROR", "error": str(e)}
    
    @classmethod
    def from_config(
        cls,
        base_url: str,
        session_token: str,
        timeout: float = 60.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> "DrawingAnalysisHttpClient":
        """
        Create DrawingAnalysisHttpClient from configuration.
        """
        return cls(
            base_url=base_url,
            session_token=session_token,
            timeout=timeout,
            lf=lf
        )
