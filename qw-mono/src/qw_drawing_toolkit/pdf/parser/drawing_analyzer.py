import time
from typing import List

import pymupdf

from qw_drawing_toolkit.dtl.interface import DefaultToleranceLookupInterface
from qw_drawing_toolkit.pdf.models.config_models import DrawingAnalyzerOpts
from qw_drawing_toolkit.pdf.models.drawing_models import (
    DrawingAnalysis,
    DrawingAnalysisContext,
    DrawingAnalysisReport,
    DrawingAnalysisResult,
    DrawingDocument,
    DrawingPage,
    DrawingReportStatus,
)
from qw_drawing_toolkit.pdf.models.geometry_models import LineDirection, LineSelection
from qw_drawing_toolkit.pdf.parser.geometry import LineCollection
from qw_drawing_toolkit.pdf.services.dimension_service import DimensionService
from qw_drawing_toolkit.pdf.services.gdt_service import GdtService
from qw_drawing_toolkit.pdf.services.geometry_service import GeometryService
from qw_drawing_toolkit.pdf.services.result_assembler import ResultAssembler
from qw_drawing_toolkit.pdf.services.symbol_service import SymbolService
from qw_drawing_toolkit.pdf.services.text_service import TextService
from qw_drawing_toolkit.pdf.services.tolerance_service import ToleranceService
from qw_drawing_toolkit.pdf.utils.page_utils import get_pdf_page_size
from qw_log_interface import NO_LOGGER, Logger


class DrawingAnalyzerPage:
    def __init__(
        self,
        page: pymupdf.Page,
        page_index: int,
        drawing_analysis_result: DrawingAnalysisResult,
        point_proximity_tolerance: float,
        zoom: float,
        resolution_booster: int = 3,
        bezier_segments: int = 20,
        debug: bool = False,
    ):
        """Initialize the DrawingAnalyzerPage."""
        self.debug = debug
        self.ctx = DrawingAnalysisContext(
            page=page,
            page_index=page_index,
            drawing_analysis_result=drawing_analysis_result,
            point_proximity_tolerance=point_proximity_tolerance,
            zoom=zoom,
            resolution_booster=resolution_booster,
            bezier_segments=bezier_segments,
        )
        self.geometry_service = GeometryService(debug=debug)
        self.text_service = TextService(debug=debug, geometry_service=self.geometry_service)
        self.symbol_service = SymbolService(debug=debug)
        self.gdt_service = GdtService(debug=debug)
        self.dimension_service = DimensionService(debug=debug)
        self.tolerance_service = ToleranceService(debug=debug)
        self.result_assembler = ResultAssembler(debug=debug)

        self._process_drawing_page()

    def _process_drawing_page(self) -> None:
        """Process the drawing page."""

        # Initialize timing and debug output
        start_time = time.time()
        if self.debug:
            print("------------------------------------------")
            print("DrawingAnalyzer analyzing page " + str(self.ctx.page_index))
            print("------------------------------------------")

        # STEP 1: Extract basic geometric elements from the page
        _lines, _lines_unfiltered, _rectangles_raw = self.geometry_service.extract_drawings(self.ctx)
        self.ctx.lines = _lines
        self.ctx.lines_unfiltered = _lines_unfiltered
        self.ctx.rectangles_raw = _rectangles_raw

        # STEP 2: Build line collections for efficient geometric analysis
        self.ctx.line_collection = LineCollection.build(
            lines=self.ctx.lines,
            proximity_tolerance=self.ctx.point_proximity_tolerance,
            extend_lines=True,
        )
        self.ctx.line_collection_horizontal = self.ctx.line_collection.select(
            LineSelection(
                only_dir=LineDirection.HORIZONTAL,
            )
        )
        self.ctx.line_collection_vertical = self.ctx.line_collection.select(
            LineSelection(
                only_dir=LineDirection.VERTICAL,
            )
        )
        self.ctx.lines = self.ctx.line_collection.get_all_as_line_segment()

        # STEP 3: Determine drawing boundaries and extract text
        self.ctx.drawing_box = self.geometry_service.find_drawing_box_dimensions(self.ctx)
        _text_boxes, _direction_to_text_boxes_map, _text_boxes_unfiltered = self.text_service.extract_text_boxes(
            self.ctx
        )
        self.ctx.text_boxes = _text_boxes
        self.ctx.direction_to_text_boxes_map = _direction_to_text_boxes_map
        self.ctx.text_boxes_unfiltered = _text_boxes_unfiltered

        # STEP 4: Group lines by orientation for structural analysis
        _horizontals_by_y, _verticals_by_x = self.geometry_service.group_lines(self.ctx)
        self.ctx.horizontals_by_y = _horizontals_by_y
        self.ctx.verticals_by_x = _verticals_by_x

        # STEP 5: Handle page rotation if necessary
        if self.ctx.rotation != 0:
            (
                self.ctx.lines,
                self.ctx.lines_drawing,
                self.ctx.verticals_by_x,
                self.ctx.horizontals_by_y,
                self.ctx.drawing_box,
                self.ctx.line_collection,
                self.ctx.line_collection_horizontal,
                self.ctx.line_collection_vertical,
                (self.ctx.max_width, self.ctx.max_height),
                self.ctx.text_boxes,
            ) = self.geometry_service.adjust_lines_to_rotation(self.ctx)

            self.ctx.text_boxes_unfiltered = self.ctx.text_boxes

        # STEP 6: Process text boxes - remove coordinates and identify title block
        _text_boxes = self.text_service.remove_section_coordinate_text_boxes(self.ctx)
        self.ctx.text_boxes = _text_boxes

        _title_block_sections = self.geometry_service.find_title_block_sections(self.ctx)
        self.ctx.title_block_sections = _title_block_sections

        _text_boxes_title_block, _title_block_sections = self.text_service.find_title_block_text_boxes(self.ctx)
        self.ctx.text_boxes_title_block = _text_boxes_title_block
        self.ctx.title_block_sections = _title_block_sections

        # STEP 7: Identify measurement text and symbols
        _text_boxes_measurements_no_symbol = self.text_service.find_measurement_text_boxes(self.ctx)
        self.ctx.text_boxes_measurements_no_symbol = _text_boxes_measurements_no_symbol

        (
            _text_boxes_measurements_no_symbol,
            _symbol_map,
            _text_boxes_measurement_symbols,
        ) = self.symbol_service.find_symbol_text_boxes(self.ctx)
        self.ctx.text_boxes_measurements_no_symbol = _text_boxes_measurements_no_symbol
        self.ctx.symbol_map = _symbol_map
        self.ctx.text_boxes_measurement_symbols = _text_boxes_measurement_symbols

        (
            _symbol_map,
            _text_boxes_measurements_no_symbol,
            _text_boxes_measurement_symbols,
        ) = self.symbol_service.find_symbol_bounding_boxes(self.ctx)
        self.ctx.symbol_map = _symbol_map
        self.ctx.text_boxes_measurements_no_symbol = _text_boxes_measurements_no_symbol
        self.ctx.text_boxes_measurement_symbols = _text_boxes_measurement_symbols

        # STEP 8: Identify and process actual drawing lines (vs border/annotation lines)
        _lines_drawing = self.geometry_service.find_drawing_lines(self.ctx)
        self.ctx.lines_drawing = _lines_drawing

        (
            _text_boxes_measurement_symbols,
            _text_boxes_measurements_no_symbol,
            _lines_drawing,
        ) = self.text_service.handle_table_structures(self.ctx)
        self.ctx.text_boxes_measurement_symbols = _text_boxes_measurement_symbols
        self.ctx.text_boxes_measurements_no_symbol = _text_boxes_measurements_no_symbol
        self.ctx.lines_drawing = _lines_drawing

        # STEP 9: Build a line collection specific to drawing lines for GD&T analysis
        _line_collection_drawing_only = LineCollection.build(
            lines=self.ctx.lines_drawing,
            proximity_tolerance=self.ctx.point_proximity_tolerance,
            extend_lines=False,
        )

        # STEP 10: Process GD&T elements - frames, datums, and references
        (
            self.ctx.gd_t_boxes,
            self.ctx.text_boxes_measurements_no_symbol,
            self.ctx.text_boxes_measurement_symbols,
            self.ctx.string_to_datum_box_map,
            self.ctx.datum_reference_boxes_left_to_map,
            self.ctx.datum_boxes,
        ) = self.gdt_service.find_gd_t_boxes(self.ctx, _line_collection_drawing_only)

        (
            _datum_reference_boxes_left_to_map,
            _string_to_datum_box_map,
        ) = self.gdt_service.map_datum_reference_boxes_left_to_map(self.ctx)
        self.ctx.datum_reference_boxes_left_to_map = _datum_reference_boxes_left_to_map
        self.ctx.string_to_datum_box_map = _string_to_datum_box_map

        # STEP 11: Merge related text elements and process dimensions
        _text_boxes_measurements_merged = self.gdt_service.merge_segmented_text_boxes(self.ctx)
        self.ctx.text_boxes_measurements_merged = _text_boxes_measurements_merged

        _dimension_boxes = self.dimension_service.assemble_dimension_boxes(self.ctx)
        self.ctx.dimension_boxes = _dimension_boxes

        # STEP 12: Extract tolerance standards and assemble final results
        _default_norms = self.tolerance_service.extract_general_tolerance_standards(self.ctx)
        self.ctx.drawing_analysis_result.norms = _default_norms

        _assembled_drawing_analysis = self.result_assembler.assemble_drawing_analysis_result(self.ctx)

        # STEP 13: Update analysis results with all extracted elements
        self.ctx.drawing_analysis_result.diameters = _assembled_drawing_analysis.diameters
        self.ctx.drawing_analysis_result.countersinks = _assembled_drawing_analysis.countersinks
        self.ctx.drawing_analysis_result.radii = _assembled_drawing_analysis.radii
        self.ctx.drawing_analysis_result.angles = _assembled_drawing_analysis.angles
        self.ctx.drawing_analysis_result.threads = _assembled_drawing_analysis.threads
        self.ctx.drawing_analysis_result.lengths = _assembled_drawing_analysis.lengths
        self.ctx.drawing_analysis_result.roughness = _assembled_drawing_analysis.roughness
        self.ctx.drawing_analysis_result.gdt_frames = _assembled_drawing_analysis.gdt_frames
        self.ctx.drawing_analysis_result.gdt_datums = _assembled_drawing_analysis.gdt_datums

        if self.debug:
            print(
                "DrawingAnalyzerPage: "
                + str(self.ctx.page_index)
                + " initialized:"
                + str(time.time() - start_time)
                + " seconds"
            )


def run_drawing_analysis(
    pdf_doc: pymupdf.Document,
    opts: DrawingAnalyzerOpts,
    dtl: DefaultToleranceLookupInterface | None,
    logger: Logger = NO_LOGGER,
) -> DrawingAnalysis:
    drawing_analysis_result = DrawingAnalysisResult()
    drawing_pages: List[DrawingPage] = []
    analyzer_pages_raw: List[DrawingAnalyzerPage] = []

    start_time = time.time()
    if opts.debug:
        print("----------------------------------------------------------")
        print("DrawingAnalyzer starting analyzing " + str(pdf_doc.page_count) + " pages.")
        print("----------------------------------------------------------")

    pages_by_index = {}

    try:
        for page_index in range(pdf_doc.page_count):
            pdf_page = pdf_doc.load_page(page_index)
            pages_by_index[page_index] = pdf_page
            info = get_pdf_page_size(pdf_doc, pdf_page)
            drawing_pages.append(DrawingPage(width_mm=info.width_in_mm, height_mm=info.height_in_mm))

    except Exception as e:
        logger.error(f"Could not parse page dimensions for {pdf_doc.name}", exc_info=e)

    try:
        # Create a temporary result for each page that we'll merge later
        page_results = []

        for page_index, pdf_page in pages_by_index.items():
            # Create a separate DrawingAnalysisResult for each page
            page_result = DrawingAnalysisResult()

            analyzer_page = DrawingAnalyzerPage(
                page=pdf_page,
                page_index=page_index,
                drawing_analysis_result=page_result,  # Pass the page-specific result
                point_proximity_tolerance=opts.point_proximity_tolerance,
                zoom=opts.zoom,
                bezier_segments=opts.bezier_segments,
                debug=opts.debug,
            )

            # Store the page's result for later merging
            page_results.append(page_result)

            # For debugging purposes
            analyzer_pages_raw.append(analyzer_page)

        # Merge all page results into the final result
        for page_result in page_results:
            # Combine all lists from each page
            drawing_analysis_result.lengths.extend(page_result.lengths)
            drawing_analysis_result.angles.extend(page_result.angles)
            drawing_analysis_result.radii.extend(page_result.radii)
            drawing_analysis_result.diameters.extend(page_result.diameters)
            drawing_analysis_result.threads.extend(page_result.threads)
            drawing_analysis_result.counterbores.extend(page_result.counterbores)
            drawing_analysis_result.countersinks.extend(page_result.countersinks)
            drawing_analysis_result.roughness.extend(page_result.roughness)
            drawing_analysis_result.gdt_frames.extend(page_result.gdt_frames)
            drawing_analysis_result.gdt_datums.extend(page_result.gdt_datums)

            # For norms, we need to avoid duplicates
            for norm in page_result.norms:
                if norm not in drawing_analysis_result.norms:
                    drawing_analysis_result.norms.append(norm)

        analysis_status = DrawingReportStatus.COMPLETED

    except Exception as e:
        logger.error("Failed to analyze drawing", exc_info=e)
        analysis_status = DrawingReportStatus.FAILED

    if dtl is not None:
        try:
            dtl.lookup_and_update(drawing_analysis_result, overwrite_general=False, overwrite_fits=True)
            dtl_status = DrawingReportStatus.COMPLETED
        except Exception as e:
            logger.error(f"Default tolerance lookup failed for {pdf_doc.name}", exc_info=e)
            dtl_status = DrawingReportStatus.FAILED
    else:
        dtl_status = DrawingReportStatus.SKIPPED

    analysis = DrawingAnalysis(
        document=DrawingDocument(pages=drawing_pages),
        result=drawing_analysis_result,
        report=DrawingAnalysisReport(
            analysis_status=analysis_status,
            default_tolerance_lookup_status=dtl_status,
        ),
    )

    if opts.debug:
        print("----------------------------------------------------------")
        print("DrawingAnalyzer analysis took " + str(time.time() - start_time) + " seconds.")
        print("----------------------------------------------------------")

    return analysis
