"""
Standards Extractor
This module provides functionality to extract ISO standards information from drawing text.
"""
import re
from typing import List

from qw_drawing_toolkit_ocr.post_processing.models import ExtractedGeneralTolerance
from qw_drawing_toolkit_ocr.pre_processing.models import ClassifiedDocumentText, PmiClassification
from qw_log_interface import NO_LOGGER, Logger


class StandardsExtractor:
    """
    Extractor for ISO standards information from drawing text.
    Uses regex patterns to identify and extract standards information.
    """

    def __init__(self, logger: Logger = NO_LOGGER):
        """
        Initialize the standards extractor.

        Args:
            logger: Logger instance
        """
        self.logger = logger
        # Regex pattern for ISO 2768 standards
        self.pattern_iso2768 = re.compile(
            r"\b[Ii][Ss][Oo][-\s]*2768(:\d{4})?[-\s]*(?P<RI1>[fmcv])?[-\s]*(?P<RI2>[HKLhkl])?\b"
        )
        # Regex pattern for ISO 286 standards
        self.pattern_iso286 = re.compile(r"\b[Ii][Ss][Oo][-\s]*286\b")

    def extract_standards(self, classified_doc: ClassifiedDocumentText) -> List[ExtractedGeneralTolerance]:
        """
        Extract standards from text blocks classified as PMI_EXPLICIT_STANDARDS.

        Args:
            classified_doc: ClassifiedDocumentText containing blocks to process

        Returns:
            List of ExtractedGeneralTolerance objects
        """
        standards: List[ExtractedGeneralTolerance] = []

        # Collect all blocks classified as PMI_EXPLICIT_STANDARDS
        for block in classified_doc.text_blocks:
            if block.classification == PmiClassification.PMI_EXPLICIT_STANDARDS:
                self.logger.info(f"Processing PMI_EXPLICIT_STANDARDS block: {block.text}")

                # Check for ISO 2768 standards
                iso2768_match = self.pattern_iso2768.search(block.text)
                if iso2768_match:
                    ri1 = iso2768_match.group("RI1")
                    ri2 = iso2768_match.group("RI2")

                    # Get the raw text of the standard
                    raw_text = iso2768_match.group(0)

                    # Create row identifiers list
                    row_identifiers: List[str] = []
                    if ri1:
                        row_identifiers.append(ri1.lower())
                    else:
                        # Default to 'm' if not specified
                        row_identifiers.append("m")

                    if ri2:
                        row_identifiers.append(ri2.upper())
                    else:
                        # Default to 'K' if not specified
                        row_identifiers.append("K")

                    self.logger.info(f"Found ISO 2768 standard: {raw_text}, row identifiers: {row_identifiers}")

                    standards.append(
                        ExtractedGeneralTolerance(
                            standard_type="ISO_2768", raw_text=raw_text, row_identifiers=row_identifiers
                        )
                    )
                    continue

                # Check for ISO 286 standards
                iso286_match = self.pattern_iso286.search(block.text)
                if iso286_match:
                    # Get the raw text of the standard
                    raw_text = iso286_match.group(0)

                    self.logger.info(f"Found ISO 286 standard: {raw_text}")

                    standards.append(
                        ExtractedGeneralTolerance(
                            standard_type="ISO_286",
                            raw_text=raw_text,
                            row_identifiers=None,  # ISO 286 doesn't use row identifiers in the same way
                        )
                    )
                    continue

                # If no specific standard was identified but it's classified as a standard
                if not iso2768_match and not iso286_match:
                    self.logger.info(f"Found unrecognized standard: {block.text}")

                    standards.append(
                        ExtractedGeneralTolerance(standard_type="OTHER", raw_text=block.text, row_identifiers=None)
                    )

        return standards
