"""
API endpoint for processing agent prompts.
"""

from qw_basic_iam_policy.common import AllowAllSessions
from qw_falcon_openapi.controller import OpenApiPathController
from qw_falcon_openapi.operation import NoParams, OpenApiOpOutput, OpenApiOpParams
from qw_log_interface import NO_LOG_FACTORY, LogFactory, Logger
from qw_pfoertner.api.auth import AuthContext, AuthOpenApiOpJsonInJsonOut
from qw_pfoertner.service.session import SessionService
from qw_trunk.service.agent.agent_service import AgentService
from qw_trunk.service.agent.models import AgentPromptRequest, AgentPromptResponse


class ProcessAgentPrompt(
    AuthOpenApiOpJsonInJsonOut[AgentPromptRequest, AgentPromptResponse, NoParams, NoParams, NoParams]
):
    """Operation to process an agent prompt and return a response with actions."""

    def __init__(
        self,
        agent_service: AgentService,
        session_service: SessionService,
        logger: Logger,
    ):
        super().__init__(AgentPromptRequest, AgentPromptResponse, NoParams, NoParams, NoParams, session_service, logger)
        self.agent_service = agent_service

    def on_request_after_auth(
        self, auth_ctx: AuthContext, body: AgentPromptRequest, params: OpenApiOpParams[NoParams, NoParams, NoParams]
    ) -> OpenApiOpOutput[AgentPromptResponse]:
        """Process the agent prompt after authentication."""
        # Evaluate policy to ensure the user has permission to use the agent
        auth_ctx.eval_policy(AllowAllSessions())

        # Process the prompt using the agent service
        response = self.agent_service.process_prompt(body)
        return OpenApiOpOutput(response)


class ProcessAgentPromptController(OpenApiPathController):
    """Controller for the agent prompt processing API endpoint."""

    def __init__(
        self,
        agent_service: AgentService,
        session_service: SessionService,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        logger = lf.get_logger(__name__)
        super().__init__(
            route="/api/v1/agent/process-prompt",
            post=ProcessAgentPrompt(agent_service, session_service, logger),
            logger=logger,
        )
