import io

from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysis


class ExperimentSampleInterface(object):
    def get_id(self) -> str:
        raise NotImplementedError()

    def get_analysis(self) -> DrawingAnalysis:
        raise NotImplementedError()

    def get_pdf_bytes(self) -> io.BytesIO:
        raise NotImplementedError()


class ExperimentSourceInterface(object):
    def get_count(self) -> int:
        raise NotImplementedError()

    def get_sample(self, index: int) -> ExperimentSampleInterface:
        raise NotImplementedError()
