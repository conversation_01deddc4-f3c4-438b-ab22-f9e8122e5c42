import re
from pathlib import Path
from typing import Dict, List, Set

import falcon.testing
from pydantic import BaseModel
from typing_extensions import Self

from qw_drawing_toolkit.pdf.models.base_models import DrawingAnnotationType
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysis, DrawingAnalysisResult
from qw_drawing_toolkit_editor.app import DrawingInfo, EditorDrawingItem, EditorServerApp
from qw_drawing_toolkit_test.conftest import TEST_RESOURCES


class JsDocObjectProperty(BaseModel):
    name: str
    type: Set[str]


class JsDocObject(BaseModel):
    name: str
    properties: List[JsDocObjectProperty]

    def get_property(self, name: str) -> JsDocObjectProperty:
        for prop in self.properties:
            if prop.name == name:
                return prop
        raise KeyError(f"Property {name} not found")

    @classmethod
    def find_in_file(cls, p: Path) -> Dict[str, Self]:
        with p.open("r") as f:
            content = f.read()

        objs_by_name = {}
        for m1 in re.finditer(
            r"\/*\*\s+@typedef\s*{object}\s*(?P<TYPENAME>[a-zA-Z]+)\s*(?P<PROPS>(.*?|\n)+?)\*\/",
            content,
            flags=re.MULTILINE | re.IGNORECASE,
        ):
            type_name = m1.group("TYPENAME").strip()
            props = m1.group("PROPS").strip()

            properties = []
            for m2 in re.finditer(
                r"@property\s{(?P<PROPTYPE>.+)} (?P<PROPNAME>[a-zA-Z]+)", props, flags=re.MULTILINE | re.IGNORECASE
            ):
                prop_name = m2.group("PROPNAME").strip()
                type_set = {pt.strip() for pt in m2.group("PROPTYPE").split("|")}
                properties.append(JsDocObjectProperty(name=prop_name, type=type_set))

            objs_by_name[type_name] = cls(name=type_name, properties=properties)

        return objs_by_name


class TestDrawingPdfService(object):
    def test_editor_app_happy_path(self) -> None:
        items = [
            EditorDrawingItem(
                drawing_path=TEST_RESOURCES / "demo_drawing.pdf",
                analysis_path=TEST_RESOURCES / "demo_drawing.yaml",
            )
        ]
        app = EditorServerApp(items)
        client = falcon.testing.TestClient(app.build())

        # get drawing infos
        get_drawings_resp = client.simulate_get(EditorServerApp.ROUTE_DRAWINGS)
        assert get_drawings_resp.status_code == 200
        infos = [DrawingInfo(**entry) for entry in get_drawings_resp.json["drawingInfos"]]
        assert len(infos) == 1
        info = infos[0]

        # get drawing analysis
        url = EditorServerApp.ROUTE_DRAWING_ANALYSIS.replace("{drawing_id}", info.id)
        get_analysis_resp = client.simulate_get(url)
        assert get_analysis_resp.status_code == 200
        analysis = DrawingAnalysis(**get_analysis_resp.json)
        assert len(analysis.result.lengths) > 0

        # get drawing images
        for i in range(info.page_count):
            url = EditorServerApp.ROUTE_DRAWING_IMAGE.replace("{drawing_id}", info.id).replace("{page_index}", str(i))
            get_image_resp = client.simulate_get(url)
            assert get_image_resp.status_code == 200
            assert get_image_resp.headers["content-type"] == "image/png"

        # validate some stuff

        post_validate_resp = client.simulate_post(EditorServerApp.ROUTE_VALIDATE, json={"test": "dummy", "data": 123})
        assert post_validate_resp.status_code == 200
        assert post_validate_resp.json.get("errorMessage") is not None

    def test_frontend_type_compatibility(self) -> None:
        # this is a super neurotic approximate check to see if the frontend is broken, basically to get notified
        # when the analysis spec is changed, but the client is still on the old one :scream_cat:
        # we are parsing some JSDoc strings from the frontend source code and compare them against the spec,
        # so you can imagine that this is a delicate contraption here :)

        objs = JsDocObject.find_in_file(EditorServerApp.DEFAULT_FRONTEND_PATH / "client.js")
        result_type = objs.get(DrawingAnalysisResult.__name__)
        assert result_type is not None

        result_type_objs_act = {prop.name for prop in result_type.properties}
        result_type_objs_exp = {field.alias for field in DrawingAnalysisResult.__pydantic_fields__.values()}
        assert result_type_objs_act == result_type_objs_exp

        annotation_container = objs.get("DataWithBoundingBox")
        assert annotation_container is not None
        assert annotation_container.get_property("boundingBox") is not None
        expected_js_type = set(f"'{t.value}'" for t in DrawingAnnotationType)
        assert annotation_container.get_property("annotationType").type == expected_js_type
