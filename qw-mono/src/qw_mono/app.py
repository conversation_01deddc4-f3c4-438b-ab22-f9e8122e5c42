from dataclasses import dataclass
from typing import Protocol

import falcon
from pydantic import BaseModel, Field

from qw_drawing_tolerance.module import QwDrawingToleranceModule
from qw_log.factory import QwLogFactory
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_mono.config import QwMonoConfig
from qw_pfoertner.module import QwPfoertnerModule
from qw_tenant_config.registry import TenantConfigRegistry
from qw_trunk.module import QwTrunkModule


class QwAppInfo(BaseModel):
    name: str
    version: str = Field(pattern=r"^\d+.\d+.\d+.\d+$")
    commit: str = Field(pattern=r"^[0-9a-f]{40}$")


class QwAppContext(Protocol):
    log_factory: QwLogFactory
    config: QwMonoConfig
    info: QwAppInfo


@dataclass
class SimpleAppContext:
    log_factory: QwLogFactory
    config: QwMonoConfig
    info: QwAppInfo


class QwMonoApp(object):
    def __init__(
        self,
        info: QwAppInfo,
        pfoertner: QwPfoertnerModule,
        trunk: QwTrunkModule,
        tenant_registry: TenantConfigRegistry,
        log_factory: LogFactory = NO_LOG_FACTORY,
    ):
        self.info = info
        self.pfoertner = pfoertner
        self.trunk = trunk
        self.tenant_registry = tenant_registry
        self.log_factory = log_factory

    @classmethod
    def from_context(cls, ctx: QwAppContext) -> "QwMonoApp":
        log_factory = ctx.log_factory
        tenant_registry = TenantConfigRegistry.from_config(ctx.config.tenants, log_factory)
        pfoertner = QwPfoertnerModule.from_config(ctx.config.pfoertner, tenant_registry, log_factory)
        trunk = QwTrunkModule.from_config(ctx.config.trunk, tenant_registry, log_factory)
        return cls(ctx.info, pfoertner, trunk, tenant_registry, log_factory)

    def build(self) -> falcon.App:
        logger = self.log_factory.get_logger(__name__)
        app = falcon.App()

        # Place to integrate independent service modules for pfoertner
        drawing_tolerance_module = QwDrawingToleranceModule.from_config(self.log_factory)

        self.pfoertner.configure_app(
            app=app,
            app_info=self.info,
            tenant_registry=self.tenant_registry,
            trunk_module=self.trunk,
            drawing_tolerance_module=drawing_tolerance_module,
        )

        logger.info(f"name={self.info.name}")
        logger.info(f"version={self.info.version}")
        logger.info(f"commit={self.info.commit}")
        logger.info(f"{self.info.name} is up")

        return app
