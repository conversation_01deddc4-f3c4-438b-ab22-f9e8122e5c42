{"name": "DIN_EN_ISO_8062-3:2008-09", "tables": [{"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 1"]], "tolerance": {"upper": 0.045, "lower": -0.045}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 1"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 1"]], "tolerance": {"upper": 0.055, "lower": -0.055}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 1"]], "tolerance": {"upper": 0.06, "lower": -0.06}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 1"]], "tolerance": {"upper": 0.065, "lower": -0.065}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 1"]], "tolerance": {"upper": 0.07, "lower": -0.07}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 1"]], "tolerance": {"upper": 0.075, "lower": -0.075}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 2"]], "tolerance": {"upper": 0.065, "lower": -0.065}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 2"]], "tolerance": {"upper": 0.07, "lower": -0.07}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 2"]], "tolerance": {"upper": 0.075, "lower": -0.075}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 2"]], "tolerance": {"upper": 0.085, "lower": -0.085}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 2"]], "tolerance": {"upper": 0.09, "lower": -0.09}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 2"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 2"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 2"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 3"]], "tolerance": {"upper": 0.09, "lower": -0.09}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 3"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 3"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 3"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 3"]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 3"]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 3"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 3"]], "tolerance": {"upper": 0.17, "lower": -0.17}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 3"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.13, "lower": -0.13}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.16, "lower": -0.16}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.22, "lower": -0.22}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.28, "lower": -0.28}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 4"]], "tolerance": {"upper": 0.32, "lower": -0.32}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.19, "lower": -0.19}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.21, "lower": -0.21}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.23, "lower": -0.23}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.28, "lower": -0.28}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.31, "lower": -0.31}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.39, "lower": -0.39}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 5"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.26, "lower": -0.26}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.29, "lower": -0.29}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.32, "lower": -0.32}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.39, "lower": -0.39}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.44, "lower": -0.44}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 6"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.37, "lower": -0.37}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.39, "lower": -0.39}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.41, "lower": -0.41}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 7"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 1.9, "lower": -1.9}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["DCTG 8"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 2.7, "lower": -2.7}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["DCTG 9"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 1.3, "lower": -1.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 6300, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["DCTG 10"]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 3.1, "lower": -3.1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 6300, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["DCTG 11"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 2.1, "lower": -2.1}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 6.5, "lower": -6.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 7.5, "lower": -7.5}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 8.5, "lower": -8.5}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 6300, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["DCTG 12"]], "tolerance": {"upper": 11.5, "lower": -11.5}}, {"lowerValueLimit": {"value": 16, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 10.5, "lower": -10.5}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 12, "lower": -12}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 14, "lower": -14}}, {"lowerValueLimit": {"value": 6300, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["DCTG 13"]], "tolerance": {"upper": 16, "lower": -16}}, {"lowerValueLimit": {"value": 16, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 11.5, "lower": -11.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 13, "lower": -13}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 15, "lower": -15}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 17.5, "lower": -17.5}}, {"lowerValueLimit": {"value": 6300, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["DCTG 14"]], "tolerance": {"upper": 20, "lower": -20}}, {"lowerValueLimit": {"value": 16, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 11, "lower": -11}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 12.5, "lower": -12.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 14.5, "lower": -14.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 16.5, "lower": -16.5}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 19, "lower": -19}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 22, "lower": -22}}, {"lowerValueLimit": {"value": 6300, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["DCTG 15"]], "tolerance": {"upper": 25, "lower": -25}}, {"lowerValueLimit": {"value": 16, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 11, "lower": -11}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 12.5, "lower": -12.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 14, "lower": -14}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 16, "lower": -16}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 18.5, "lower": -18.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 21, "lower": -21}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 4000, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 24.5, "lower": -24.5}}, {"lowerValueLimit": {"value": 4000, "inclusive": false}, "upperValueLimit": {"value": 6300, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 28, "lower": -28}}, {"lowerValueLimit": {"value": 6300, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["DCTG 16"]], "tolerance": {"upper": 32, "lower": -32}}]}, {"name": "Tabelle 3", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.08, "lower": -0.08}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 12, "lower": -12}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 16, "lower": -16}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 12, "lower": -12}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 24, "lower": -24}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 18, "lower": -18}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 36, "lower": -36}}]}, {"name": "Tabelle 4", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 16, "lower": -16}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 12, "lower": -12}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 24, "lower": -24}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 18, "lower": -18}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 36, "lower": -36}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 14, "lower": -14}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 28, "lower": -28}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 56, "lower": -56}}]}, {"name": "Tabelle 5", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.12, "lower": -0.12}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 8, "lower": -8}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 16, "lower": -16}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 12, "lower": -12}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 24, "lower": -24}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 18, "lower": -18}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 36, "lower": -36}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 14, "lower": -14}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 28, "lower": -28}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 56, "lower": -56}}]}, {"name": "Tabelle 6", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.27, "lower": -0.27}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 2"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 3"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 4"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 9, "lower": -9}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 18, "lower": -18}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 5"]], "tolerance": {"upper": 36, "lower": -36}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 14, "lower": -14}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 28, "lower": -28}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 6"]], "tolerance": {"upper": 56, "lower": -56}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 20, "lower": -20}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 40, "lower": -40}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 7"]], "tolerance": {"upper": 80, "lower": -80}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 7, "lower": -7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 10, "lower": -10}}, {"lowerValueLimit": {"value": 300, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 15, "lower": -15}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 3000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 30, "lower": -30}}, {"lowerValueLimit": {"value": 3000, "inclusive": false}, "upperValueLimit": {"value": 6000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 60, "lower": -60}}, {"lowerValueLimit": {"value": 6000, "inclusive": false}, "upperValueLimit": {"value": 10000, "inclusive": true}, "rowIdentifiers": [["GCTG 8"]], "tolerance": {"upper": 120, "lower": -120}}]}, {"name": "Tabelle 7", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": []}]}