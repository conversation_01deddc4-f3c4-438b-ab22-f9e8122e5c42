"""Base HTTP client for communicating with Falcon services."""
from typing import Any, Dict, Optional
import httpx

from qw_log_interface import NO_LOG_FACTORY, LogFactory


class BaseHttpClient:
    """Base HTTP client for making authenticated requests to Falcon services."""
    
    def __init__(
        self,
        base_url: str,
        session_token: str,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """
        Initialize the base HTTP client.
        """
        self.base_url = base_url.rstrip("/")
        self.session_token = session_token
        self.timeout = timeout
        self.logger = lf.get_logger(__name__)
        
        # Default headers
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        
        # Session cookies
        self.cookies = {
            "session": session_token
        }
    
    async def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Make a GET request to the specified endpoint.
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    url,
                    headers=self.headers,
                    cookies=self.cookies,
                    params=params
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 404:
                    self.logger.warning(f"Resource not found: {url}")
                    return {"status": "NOT_FOUND", "message": "Resource not found"}
                elif response.status_code == 401:
                    self.logger.error(f"Authentication failed for: {url}")
                    raise httpx.HTTPStatusError(
                        "Authentication failed", 
                        request=response.request, 
                        response=response
                    )
                else:
                    self.logger.error(f"HTTP error {response.status_code} for: {url}")
                    response.raise_for_status()
                    
        except httpx.TimeoutException:
            self.logger.error(f"Request timeout for: {url}")
            return {"status": "ERROR", "error": "Request timeout"}
        except httpx.RequestError as e:
            self.logger.error(f"Request error for {url}: {e}")
            return {"status": "ERROR", "error": str(e)}
        except Exception as e:
            self.logger.error(f"Unexpected error for {url}: {e}")
            return {"status": "ERROR", "error": str(e)}
    
    async def post(
        self, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a POST request to the specified endpoint.
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    url,
                    headers=self.headers,
                    cookies=self.cookies,
                    data=data,
                    json=json_data
                )
                
                if response.status_code in [200, 201]:
                    return response.json()
                elif response.status_code == 401:
                    self.logger.error(f"Authentication failed for: {url}")
                    raise httpx.HTTPStatusError(
                        "Authentication failed", 
                        request=response.request, 
                        response=response
                    )
                else:
                    self.logger.error(f"HTTP error {response.status_code} for: {url}")
                    response.raise_for_status()
                    
        except httpx.TimeoutException:
            self.logger.error(f"Request timeout for: {url}")
            return {"status": "ERROR", "error": "Request timeout"}
        except httpx.RequestError as e:
            self.logger.error(f"Request error for {url}: {e}")
            return {"status": "ERROR", "error": str(e)}
        except Exception as e:
            self.logger.error(f"Unexpected error for {url}: {e}")
            return {"status": "ERROR", "error": str(e)}
