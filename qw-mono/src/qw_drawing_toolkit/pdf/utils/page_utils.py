"""Page utils"""

import pymupdf

from qw_drawing_toolkit.pdf.models.analysis_models import PdfPageSizeInfo


def get_pdf_page_dpi(doc: pymupdf.Document, page: pymupdf.Page) -> float:
    """
    Get the dots per inch (DPI) value for a PDF page.

    Currently returns a default value of 72 DPI, which is the standard for PDF documents.
    Future implementation should parse the actual DPI from the document or page.

    Args:
        doc: PyMuPDF Document object
        page: PyMuPDF Page object to get DPI for

    Returns:
        DPI value as a float (currently fixed at 72)

    Note:
        Custom UserUnit values would need to be parsed from the PDF.
        See: https://stackoverflow.com/a/********
    """
    # assuming default unit of 72dpi, custom UserUnit would need to be parsed from
    # https://stackoverflow.com/a/********
    # TODO: parse dpi from document or page somehow
    dpi = 72
    return dpi


def get_pdf_page_size(doc: pymupdf.Document, page: pymupdf.Page) -> PdfPageSizeInfo:
    """
    Get comprehensive size information for a PDF page.

    Calculates page dimensions in points and millimeters, accounting for page rotation.

    Args:
        doc: PyMuPDF Document object
        page: PyMuPDF Page object to get size information for

    Returns:
        PdfPageSizeInfo object containing:
        - width_in_pts: Page width in points (1/72 inch)
        - height_in_pts: Page height in points
        - width_in_mm: Page width in millimeters
        - height_in_mm: Page height in millimeters
        - dpi: Dots per inch value

    Note:
        If the page has a 90° or 270° rotation, width and height are swapped
        to return the dimensions as they appear visually.
    """
    dpi = get_pdf_page_dpi(doc, page)
    dots_per_mm = dpi / 25.4

    width_in_dots, height_in_dots = page.mediabox_size
    if (page.rotation // 90) % 2 == 1:
        width_in_dots, height_in_dots = height_in_dots, width_in_dots

    return PdfPageSizeInfo(
        width_in_pts=width_in_dots,
        height_in_pts=height_in_dots,
        width_in_mm=width_in_dots / dots_per_mm,
        height_in_mm=height_in_dots / dots_per_mm,
        dpi=dpi,
    )
