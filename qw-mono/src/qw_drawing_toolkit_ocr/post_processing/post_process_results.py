"""
Post-processing results processor for PMI analysis.
This module provides functionality to process and normalize PMI analysis results.
"""
import json
import re
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import numpy as np
from numpy.typing import NDArray

from qw_drawing_tolerance.model import ToleranceUnit
from qw_drawing_tolerance.tolerance_service import ToleranceService
from qw_drawing_toolkit_ocr.post_processing.models import BoundingPoly as PostBoundingPoly
from qw_drawing_toolkit_ocr.post_processing.models import (
    DiameterDetail,
    ExtractedGeneralTolerance,
    NormalizedPMIBlockAnalysisResult,
    NormalizedPoint,
    NormalizedPolygon,
    PMIBlockAnalysisResultWithNonNormalizedBoundingPoly,
)

# Import json and datetime directly in the file
from qw_log_interface import NO_LOG_FACTORY, NO_LOGGER, LogFactory, Logger


class PostProcessResults:
    """
    Post-processor for PMI analysis results.
    Handles normalization and serialization of PMI results.
    """

    def __init__(
        self,
        save_json_for_debugging: bool,
        logger: Logger = NO_LOGGER,
        lf: LogFactory = NO_LOG_FACTORY,
        output_dir: Optional[Path] = None,
    ):
        """
        Initialize the post-processor.

        Args:
            save_json_for_debugging: Whether to save results to JSON file
            logger: Logger instance
            lf: Log factory for initializing the tolerance service
            output_dir: Optional directory to save results to
        """
        self.logger = logger
        self.output_dir = output_dir
        self.save_json_for_debugging = save_json_for_debugging

        # Initialize the tolerance service
        self.tolerance_service = ToleranceService(lf=lf)

    def post_process_pmi_results(
        self,
        results: Dict[str, List[PMIBlockAnalysisResultWithNonNormalizedBoundingPoly]],
        image: NDArray[np.uint8],
        extracted_standards: Optional[List[ExtractedGeneralTolerance]] = None,
    ) -> Dict[str, List[NormalizedPMIBlockAnalysisResult]]:
        """
        Post-process PMI results to prepare them for serialization and further processing.
        This is the main entry point for post-processing.

        Args:
            results: Dictionary of processed results from agent_processor
            image: Original image for normalizing coordinates
            extracted_standards: Optional list of extracted standards from the drawing

        Returns:
            Dictionary mapping classifications to lists of NormalizedPMIBlockAnalysisResult objects
        """
        normalized_results = self._process_results(results, image)

        # Process tolerance sources if standards are provided
        if extracted_standards:
            self._process_tolerance_sources(normalized_results, extracted_standards)

        return normalized_results

    def _process_results(
        self,
        results: Dict[str, List[PMIBlockAnalysisResultWithNonNormalizedBoundingPoly]],
        image: NDArray[np.uint8],
    ) -> Dict[str, List[NormalizedPMIBlockAnalysisResult]]:
        """
        Process the agent results to prepare them for serialization.
        Also saves the results to a JSON file if save_json_for_debugging is True.
        Creates NormalizedPMIBlockAnalysisResult objects with normalized polygons.

        Args:
            results: Dictionary of processed results
            image: Original image for normalizing coordinates

        Returns:
            Dictionary mapping classifications to lists of NormalizedPMIBlockAnalysisResult objects
        """
        # Convert results to serializable format and clean up fields
        serializable_results = {}

        # Create normalized results
        normalized_results: Dict[str, List[NormalizedPMIBlockAnalysisResult]] = {}

        for classification, block_results in results.items():
            cleaned_results = []
            normalized_results[classification] = []

            for block in block_results:
                # Normalize the bounding_poly to 0-1 range
                normalized_poly = self._normalize_bounding_poly(block.bounding_poly, image)

                # Create a NormalizedPMIBlockAnalysisResult using the from_original factory method
                normalized_block = NormalizedPMIBlockAnalysisResult.from_original(
                    original_result=block, polygon=normalized_poly
                )

                normalized_results[classification].append(normalized_block)

                result_dict = block.model_dump()

                # Clean up fields for serialization
                detail_type = result_dict.get("detail", {}).get("type")
                if detail_type:
                    # Keep only the fields that are expected for this detail type
                    # Define the expected fields for each type
                    # Include the shared fields from SharedPMIValues in all types
                    shared_fields = ["nominal_value", "lower_tol", "upper_tol", "unit", "raw_callout"]

                    expected_fields_map = {
                        "DIAMETER": ["type", "iso286_identifier"] + shared_fields,
                        "THREAD": ["type", "pitch", "iso965_identifier", "thread_standard", "depth", "depth_unit"]
                        + shared_fields,
                        "GDT": ["type", "symbol", "datum_references", "material_condition", "diameter_modifier"]
                        + shared_fields,
                        "ANGLE": ["type"] + shared_fields,
                        "LINEAR": ["type"] + shared_fields,
                        "RADIUS": ["type"] + shared_fields,
                        "CHAMFER": ["type", "angle", "length", "length_unit"] + shared_fields,
                        "ROUGHNESS": ["type", "roughness_category"] + shared_fields,
                    }

                    # Get the expected fields for this type
                    expected_fields = expected_fields_map.get(detail_type, ["type"])

                    # Remove any fields that aren't expected
                    detail_dict = result_dict.get("detail", {})
                    for field in list(detail_dict.keys()):
                        if field not in expected_fields:
                            detail_dict.pop(field, None)

                    # Replace commas with periods in raw_callout
                    if "raw_callout" in detail_dict and detail_dict["raw_callout"]:
                        detail_dict["raw_callout"] = detail_dict["raw_callout"].replace(",", ".")

                cleaned_results.append(result_dict)

            serializable_results[classification] = cleaned_results

        # Save to JSON file if debugging is enabled and output_dir is provided
        if self.save_json_for_debugging and self.output_dir is not None:
            try:
                # This will include the full PMIBlockAnalysisResultWithNonNormalizedBoundingPoly objects
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path_4 = self.output_dir / f"4_agent_results_{timestamp}.json"
                with open(output_path_4, "w") as f:
                    json.dump(serializable_results, f, indent=2)

                # Create serializable normalized results
                normalized_serializable: Dict[str, List[Dict[str, Any]]] = {
                    classification: [
                        {
                            "result": {
                                "importance": block.result.importance,
                                "count": block.result.count,
                                "detail": block.result.detail.model_dump(),
                            },
                            "polygon": block.polygon.model_dump(),
                            "tolerance_source": block.tolerance_source,
                        }
                        for block in blocks
                    ]
                    for classification, blocks in normalized_results.items()
                }

                # Save normalized results
                output_path_5 = self.output_dir / f"5_final_results_{timestamp}.json"
                with open(output_path_5, "w") as f:
                    json.dump(normalized_serializable, f, indent=2)

                self.logger.info(f"Saved PMI results to {output_path_5}")
            except Exception as e:
                self.logger.error(f"Error saving PMI results to JSON: {str(e)}")
        elif self.save_json_for_debugging and self.output_dir is None:
            self.logger.info("Cannot save PMI results: output_dir is None")

        # Return the normalized results
        return normalized_results

    def _normalize_bounding_poly(
        self,
        bounding_poly: PostBoundingPoly,
        image: NDArray[np.uint8],
    ) -> NormalizedPolygon:
        """
        Normalize a BoundingPoly to NormalizedPolygon with coordinates in 0-1 range.

        Args:
            bounding_poly: The BoundingPoly with integer coordinates
            image: Full image to get dimensions for normalization (required)

        Returns:
            NormalizedPolygon with normalized coordinates (0-1 range)
        """
        # Get full image dimensions
        h, w = image.shape[:2]

        # Ensure we don't divide by zero
        if w == 0 or h == 0:
            self.logger.warning("Invalid image dimensions for normalization")
            # Return a default polygon with 4 points at corners
            return NormalizedPolygon(
                points=[
                    NormalizedPoint(x=0.0, y=0.0),
                    NormalizedPoint(x=1.0, y=0.0),
                    NormalizedPoint(x=1.0, y=1.0),
                    NormalizedPoint(x=0.0, y=1.0),
                ]
            )

        # Convert vertices to normalized points
        normalized_points: List[NormalizedPoint] = []

        for vertex in bounding_poly.vertices:
            x = vertex.get("x", 0)
            y = vertex.get("y", 0)

            # Normalize to 0-1 range
            nx = max(0.0, min(1.0, float(x) / float(w)))
            ny = max(0.0, min(1.0, float(y) / float(h)))
            normalized_points.append(NormalizedPoint(x=nx, y=ny))

        # Ensure we have at least 4 points
        while len(normalized_points) < 4:
            # Add duplicate of last point if we don't have enough
            if normalized_points:
                last_point = normalized_points[-1]
                normalized_points.append(NormalizedPoint(x=last_point.x, y=last_point.y))
            else:
                # If no points at all, add a default point
                normalized_points.append(NormalizedPoint(x=0.0, y=0.0))

        # Create and return the NormalizedPolygon
        return NormalizedPolygon(points=normalized_points)

    def _process_tolerance_sources(
        self,
        normalized_results: Dict[str, List[NormalizedPMIBlockAnalysisResult]],
        extracted_standards: List[ExtractedGeneralTolerance],
    ) -> None:
        """
        Process tolerance sources for PMI blocks based on extracted standards.

        This method identifies the source of tolerance values for each PMI block:
        - "explicit": When upper_tol or lower_tol is explicitly specified
        - "ISO_2768": When tolerances are derived from ISO 2768 standard
        - "ISO_286": When tolerances are derived from ISO 286 standard (for diameters)

        ISO 2768 standard parts and applicable feature types:
        - ISO 2768-1 (1.1): Linear dimensions (length, width, height)
        - ISO 2768-1 (1.2): External radii and chamfer heights
        - ISO 2768-1 (1.3): Angular dimensions
        For the part below: Cannot be inferred from PMI notation only.
        Needs drawing context, which our pipeline is lacking
        - ISO 2768-2 (2.1): Straightness and flatness
        - ISO 2768-2 (2.2): Perpendicularity
        - ISO 2768-2 (2.3): Symmetry
        - ISO 2768-2 (2.4): Circular run-out

        Args:
            normalized_results: Dictionary mapping classifications to lists of normalized PMI results
            extracted_standards: List of extracted standards from the drawing
        """
        # Check if we have any standards
        iso_2768_standards = [s for s in extracted_standards if s.standard_type == "ISO_2768"]
        iso_286_standards = [s for s in extracted_standards if s.standard_type == "ISO_286"]

        if iso_2768_standards:
            self.logger.info(f"Found ISO 2768 standards: {[s.raw_text for s in iso_2768_standards]}")
        if iso_286_standards:
            self.logger.info(f"Found ISO 286 standards: {[s.raw_text for s in iso_286_standards]}")

        # Process each classification
        for _, blocks in normalized_results.items():  # Using _ for unused variable
            for block in blocks:
                # Get the detail type
                detail_type = block.result.detail.type if hasattr(block.result.detail, "type") else None

                # Skip if no detail type
                if not detail_type:
                    continue

                # First check for explicit tolerances for all types
                if hasattr(block.result.detail, "upper_tol") and hasattr(block.result.detail, "lower_tol"):
                    if block.result.detail.upper_tol is not None or block.result.detail.lower_tol is not None:
                        block.tolerance_source = "explicit"
                        continue

                # Process based on detail type and applicable standards
                if detail_type == "LINEAR":
                    # Linear dimensions use ISO 2768-1 (1.1)
                    if iso_2768_standards and hasattr(block.result.detail, "nominal_value"):
                        # Apply ISO 2768 tolerances for linear dimensions
                        self._apply_iso2768_tolerance(block, iso_2768_standards[0], "Table 1.1")
                        if block.result.detail.upper_tol is not None or block.result.detail.lower_tol is not None:
                            block.tolerance_source = "ISO_2768"

                elif detail_type == "ANGLE":
                    # Angular dimensions use ISO 2768-1 (1.3)
                    # NOTE: We can't use Table 1.3 with just the angle.
                    # You need the associated linear length (in mm) to determine the tolerance from Table 1.3.
                    # For now, we'll skip applying tolerances to angles.
                    self.logger.info(
                        f"Skipping ISO 2768 tolerance for ANGLE {block.result.detail.nominal_value} "
                        "- requires associated linear length"
                    )
                    # We don't set tolerance_source for angles

                elif detail_type in ["RADIUS", "CHAMFER"]:
                    # External radii and chamfer heights use ISO 2768-1 (1.2)
                    if iso_2768_standards and hasattr(block.result.detail, "nominal_value"):
                        # Apply ISO 2768 tolerances for radii and chamfers
                        self._apply_iso2768_tolerance(block, iso_2768_standards[0], "Table 1.2")
                        if block.result.detail.upper_tol is not None or block.result.detail.lower_tol is not None:
                            block.tolerance_source = "ISO_2768"

                elif detail_type == "DIAMETER":
                    # Diameters can use ISO 286 or ISO 2768-1 (1.1)

                    # First check for ISO 286 identifier
                    if (
                        isinstance(block.result.detail, DiameterDetail)
                        and hasattr(block.result.detail, "iso286_identifier")
                        and block.result.detail.iso286_identifier
                    ):
                        # Apply ISO 286 tolerances for diameters with ISO 286 identifier
                        self._apply_iso286_tolerance(block)
                        if block.result.detail.upper_tol is not None or block.result.detail.lower_tol is not None:
                            block.tolerance_source = "ISO_286"
                        continue

                    # If no ISO 286 identifier but ISO 2768 is present, use that
                    # Treating diameter as linear dimensions is a gray area in the industry
                    # Best practice is to use explicit ISO 286 for diameters
                    if iso_2768_standards and hasattr(block.result.detail, "nominal_value"):
                        # Apply ISO 2768 tolerances for diameters (as linear dimensions)
                        self._apply_iso2768_tolerance(block, iso_2768_standards[0], "Table 1.1")
                        if block.result.detail.upper_tol is not None or block.result.detail.lower_tol is not None:
                            block.tolerance_source = "ISO_2768"

    def _apply_iso2768_tolerance(
        self, block: NormalizedPMIBlockAnalysisResult, standard: ExtractedGeneralTolerance, table_name: str
    ) -> None:
        """
        Apply ISO 2768 tolerance to a PMI block.

        Args:
            block: The PMI block to apply tolerance to
            standard: The ISO 2768 standard to use
            table_name: The table name to use (1.1, 1.2, 1.3)
        """
        try:
            # Get the nominal value and unit
            nominal_value = block.result.detail.nominal_value
            unit_str = block.result.detail.unit

            # Convert unit to ToleranceUnit
            unit_map = {
                "MILLIMETER": ToleranceUnit.MILLIMETER,
                "DEGREE": ToleranceUnit.DEGREE,
            }
            tolerance_unit = unit_map.get(unit_str)

            if tolerance_unit is None:
                self.logger.warning(f"Unsupported unit: {unit_str}")
                return

            # Special case for Table 1.3 (angular dimensions)
            # In the ISO 2768 standard file, Table 1.3 has valueUnit="mm"
            # but toleranceUnit="deg"
            # That means we need the associated linear length (in mm)
            # to determine the tolerance from Table 1.3.
            # You can't use Table 1.3 with just the angle.
            # For now, we'll skip applying tolerances to angles.
            if table_name == "Table 1.3":
                self.logger.info(
                    f"Skipping angle tolerance lookup for {nominal_value} degrees - requires associated linear length"
                )
                return

            # For all other tables, use the same unit for value and tolerance
            value_unit = tolerance_unit

            # Get row identifiers from the standard
            row_identifiers = standard.row_identifiers
            if not row_identifiers:
                # Default to 'm' if not specified
                row_identifiers = ["m"]

            # For ISO 2768, we only need the first identifier (e.g., 'm' from 'ISO 2768 -m')
            # and it should be lowercase for Table 1.1, 1.2, 1.3
            if len(row_identifiers) > 0:
                first_identifier = row_identifiers[0].lower()
                # The tolerance service expects a tuple of strings
                row_identifiers_tuple = (first_identifier,)
            else:
                # Fallback to default
                row_identifiers_tuple = ("m",)

            # Look up tolerance from the tolerance service
            tolerance_result = self.tolerance_service.lookup_tolerance(
                tolerance_name="ISO_2768",
                table_name=table_name,
                value=nominal_value,
                row_identifiers=row_identifiers_tuple,
                value_unit=value_unit,
                tolerance_unit=tolerance_unit,
            )

            if tolerance_result:
                # Apply the tolerance values
                block.result.detail.upper_tol = tolerance_result.upper
                block.result.detail.lower_tol = tolerance_result.lower
            else:
                self.logger.warning(
                    f"Failed to look up ISO 2768 tolerance for {block.result.detail.type} "
                    f"with nominal value {nominal_value}"
                )
        except Exception as e:
            self.logger.error(f"Error applying ISO 2768 tolerance: {str(e)}")

    def _apply_iso286_tolerance(self, block: NormalizedPMIBlockAnalysisResult) -> None:
        """
        Apply ISO 286 tolerance to a diameter PMI block.

        Args:
            block: The PMI block to apply tolerance to
        """
        try:
            # Get the nominal value, unit, and ISO 286 identifier

            if not isinstance(block.result.detail, DiameterDetail):
                return

            nominal_value = block.result.detail.nominal_value
            unit_str = block.result.detail.unit
            iso286_identifier = block.result.detail.iso286_identifier

            if not iso286_identifier:
                self.logger.warning("No ISO 286 identifier found")
                return

            # Convert unit to ToleranceUnit
            unit_map = {
                "MILLIMETER": ToleranceUnit.MILLIMETER,
            }
            tolerance_unit = unit_map.get(unit_str)

            if tolerance_unit is None:
                self.logger.warning(f"Unsupported unit: {unit_str}")
                return

            # ISO 286 identifiers are typically in the format of "H7" or "g6"
            # We need to extract the letter and number parts

            match = re.match(r"([A-Za-z]+)([0-9]+)", iso286_identifier)
            if not match:
                self.logger.warning(f"Invalid ISO 286 identifier format: {iso286_identifier}")
                return

            letter_part = match.group(1)
            number_part = match.group(2)

            # Look up tolerance from the tolerance service
            tolerance_result = self.tolerance_service.lookup_tolerance(
                tolerance_name="ISO_286",
                table_name="basic",
                value=nominal_value,
                row_identifiers=(letter_part, number_part),
                value_unit=tolerance_unit,
                tolerance_unit=tolerance_unit,
            )

            if tolerance_result:
                # Apply the tolerance values
                block.result.detail.upper_tol = tolerance_result.upper
                block.result.detail.lower_tol = tolerance_result.lower
                self.logger.info(
                    f"Applied ISO 286 tolerance to diameter with nominal value {nominal_value} and "
                    f"identifier {iso286_identifier}: upper={tolerance_result.upper}, lower={tolerance_result.lower}"
                )
            else:
                self.logger.warning(
                    f"Failed to look up ISO 286 tolerance for diameter with nominal value {nominal_value} "
                    f"and identifier {iso286_identifier}"
                )
        except Exception as e:
            self.logger.error(f"Error applying ISO 286 tolerance: {str(e)}")
