from pathlib import Path
from typing import Any, Callable, Dict, List, Sequence, Type, TypeVar

import yaml
from pydantic import BaseModel

CONFIG_KEY_DIR = "${DIR}"
CONFIG_KEY_OVERWRITE = "${OVERWRITE}"

C = TypeVar("C", bound=BaseModel)


class DictPath(object):
    def __init__(self, parts: Sequence[str | int]):
        self.parts = parts
        if len(parts) == 0:
            raise ValueError(f"{self.__class__.__name__} is empty")
        if not isinstance(parts[0], str):
            raise ValueError(f"{self.__class__.__name__} needs to start with a string part")

    @classmethod
    def find_all_in(
        cls, d: Dict[str, Any], match: Callable[[Sequence[str | int], Any], bool] | None = None
    ) -> List["DictPath"]:
        def match_any(parts: Sequence[str | int], x: Any) -> bool:
            return True

        if match is None:
            match = match_any

        collection: List[DictPath] = []
        cls.collect_paths(d, [], collection, match)
        return collection

    @classmethod
    def collect_paths(
        cls,
        d: Dict[str, Any] | Sequence[Any] | Any,
        current_parts: Sequence[str | int],
        collection: List["DictPath"],
        match: Callable[[Sequence[str | int], Any], bool],
    ) -> None:
        if isinstance(d, dict):
            for k, v in d.items():
                cls.collect_paths(v, list(current_parts) + [k], collection, match)
        elif isinstance(d, (list, tuple)):
            for i, v in enumerate(d):
                cls.collect_paths(v, list(current_parts) + [i], collection, match)
        elif match(current_parts, d):
            collection.append(cls(current_parts))

    def __str__(self) -> str:
        return self.to_str()

    def to_str(self) -> str:
        str_parts = []
        for p in self.parts:
            if isinstance(p, str):
                str_parts.append(f"s:{p}")
            else:
                str_parts.append(f"i:{p}")
        return "/".join(str_parts)

    @classmethod
    def from_str(cls, s: str) -> "DictPath":
        parts: List[str | int] = []
        for p in s.split("/"):
            if p.startswith("s:"):
                parts.append(p)
            elif p.startswith("i:"):
                parts.append(int(p))
            else:
                raise ValueError(f"Cannot parse path string {s}")
        return cls(parts)

    def set_in_dict(self, d: Dict[str, Any], value: Any) -> None:
        d2 = d
        try:
            for p in self.parts[:-1]:
                d2 = d2[p]  # type: ignore
            d2[self.parts[-1]] = value  # type: ignore
        except (TypeError, ValueError, IndexError):
            raise ValueError(f"Could set value for path {self}")

    def get_from_dict(self, d: Dict[str, Any]) -> Any:
        d2 = d
        try:
            for p in self.parts:
                d2 = d2[p]  # type: ignore
        except (TypeError, ValueError, IndexError):
            raise ValueError(f"Could get value for path {self}")
        return d2

    def get_str_from_dict(self, d: Dict[str, Any]) -> str:
        v = self.get_from_dict(d)
        if not isinstance(v, str):
            raise ValueError(f"Expected string at {self}, but got {type(v)}")
        return v


def match_dir(parts: Sequence[str | int], x: Any) -> bool:
    if isinstance(x, str):
        return CONFIG_KEY_DIR in x
    return False


def match_overwrite(parts: Sequence[str | int], x: Any) -> bool:
    if isinstance(x, str):
        return CONFIG_KEY_OVERWRITE == x.strip()
    return False


def load_config(config_cls: Type[C], path: Path, path_overwrite: Path | None = None) -> C:
    if not path.exists():
        raise FileNotFoundError(f"No config file found at {path}")
    with path.open("r") as f:
        data = yaml.safe_load(f)

    data_overwrite: Dict[str, Any] | None = None
    if path_overwrite is not None:
        if not path_overwrite.exists():
            raise FileNotFoundError(f"No config overwrite file found at {path_overwrite}")
        with path_overwrite.open("r") as f:
            data_overwrite = yaml.safe_load(f)

    return load_config_from_data(config_cls, data, path, data_overwrite, path_overwrite)


def load_config_from_data(
    config_cls: Type[C],
    data: Dict[str, Any],
    path: Path,
    data_overwrite: Dict[str, Any] | None = None,
    path_overwrite: Path | None = None,
) -> C:
    dict_paths_overwrite = DictPath.find_all_in(data, match_overwrite)
    if len(dict_paths_overwrite) > 0:
        if data_overwrite is None:
            raise ValueError(
                f"{len(dict_paths_overwrite)} config paths need to be replaced, but no overwrite data was provided"
            )

        missed_paths = []
        for p_overwrite in dict_paths_overwrite:
            try:
                new_value = p_overwrite.get_from_dict(data_overwrite)
            except ValueError:
                missed_paths.append(path_overwrite)
            else:
                p_overwrite.set_in_dict(data, new_value)

        if len(missed_paths) > 0:
            msg_lines = [str(p) for p in missed_paths]
            msg_ext = ""
            if path_overwrite is not None:
                msg_ext = f"from {path_overwrite}"
            raise ValueError("\n".join([f"Could not get values to overwrite following paths {msg_ext}"] + msg_lines))

    dict_paths_dir = DictPath.find_all_in(data, match_dir)
    dir_path = str(path.parent.resolve())
    for p_dir in dict_paths_dir:
        new_value = p_dir.get_str_from_dict(data).replace(CONFIG_KEY_DIR, dir_path)
        p_dir.set_in_dict(data, new_value)

    return config_cls(**data)
