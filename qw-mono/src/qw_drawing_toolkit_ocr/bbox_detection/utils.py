from pathlib import Path
from typing import Optional, Union

import cv2
import numpy as np
from numpy.typing import NDArray

from qw_drawing_toolkit_ocr.bbox_detection.models import DetectionItem, OcrResult
from qw_log_interface import NO_LOGGER, <PERSON><PERSON>


def visualize_text_regions(
    image: NDArray[np.uint8],
    ocr_result: OcrResult,
    output_path: Optional[Union[str, Path]] = None,
    logger: Logger = NO_LOGGER,
) -> NDArray[np.uint8]:
    """
    Visualize OCR text regions with simple transparent rectangles.
    Coordinates in OCR results are assumed to be normalized (0-1).

    Args:
        image: Original image
        ocr_result: OCR result with normalized coordinates
        output_path: Path to save visualization image

    Returns:
        Image with visualized results
    """
    # Create a copy of the original image
    viz_img = image.copy()
    h, w = viz_img.shape[:2]

    # Draw simple semi-transparent rectangles for each detection
    for i, detection in enumerate(ocr_result.detections):
        try:
            # Get detection details
            det_item = DetectionItem.from_detection(detection)
            box_coords = det_item.box_coords

            # Convert normalized coordinates to absolute values
            absolute_box_coords = []
            for point in box_coords:
                if len(point) >= 2:
                    # Convert normalized coordinates back to absolute values
                    abs_x = int(float(point[0]) * w)
                    abs_y = int(float(point[1]) * h)
                    absolute_box_coords.append([abs_x, abs_y])

            # Convert polygon to rectangle for simplicity
            pts = np.array(absolute_box_coords, dtype=np.int32)

            # Check if we have valid points
            if pts.size > 0:
                x_min = int(np.min(pts[:, 0]))
                y_min = int(np.min(pts[:, 1]))
                x_max = int(np.max(pts[:, 0]))
                y_max = int(np.max(pts[:, 1]))

                # Create a simple transparent overlay for this rectangle
                rect_overlay = viz_img.copy()
                cv2.rectangle(rect_overlay, (x_min, y_min), (x_max, y_max), (0, 0, 255), -1)  # Filled red rectangle

                # Blend this rectangle with the main image
                alpha = 0.3  # Transparency level
                cv2.addWeighted(rect_overlay, alpha, viz_img, 1 - alpha, 0, viz_img)

                # Draw white border
                cv2.rectangle(viz_img, (x_min, y_min), (x_max, y_max), (0, 0, 255), 1)

            else:
                logger.warning(f"Skipping visualization for detection {i}: Invalid coordinates")

        except Exception as e:
            logger.warning(f"Error visualizing detection {i}: {str(e)}")

    # Save visualization if output path is provided
    if output_path:
        output_path = str(output_path) if isinstance(output_path, Path) else output_path
        cv2.imwrite(output_path, viz_img)

    return viz_img
