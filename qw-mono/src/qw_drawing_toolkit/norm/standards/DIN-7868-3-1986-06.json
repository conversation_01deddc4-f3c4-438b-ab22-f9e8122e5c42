{"name": "DIN_7868-3:1986-06", "tables": [{"name": "Tabelle 1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.04, "lower": -0.04}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.05, "lower": -0.05}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.06, "lower": -0.06}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.07, "lower": -0.07}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.08, "lower": -0.08}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.06, "lower": -0.06}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.07, "lower": -0.07}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.09, "lower": -0.09}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.23, "lower": -0.23}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 3, "lower": -3}}]}, {"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.06, "lower": -0.06}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.07, "lower": -0.07}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.09, "lower": -0.09}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.11, "lower": -0.11}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.14, "lower": -0.14}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.18, "lower": -0.18}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.23, "lower": -0.23}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["XXP"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.1, "lower": -0.1}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["P"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["H"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 2.3, "lower": -2.3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 5, "lower": -5}}]}, {"name": "Tabelle 3", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 2500, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["XP"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Q"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["N"]], "tolerance": {"upper": 3.5, "lower": -3.5}}]}]}