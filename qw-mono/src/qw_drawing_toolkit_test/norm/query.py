from qw_drawing_toolkit.norm.model import ToleranceUnit
from qw_drawing_toolkit.norm.query import FROM_DRAWING_UNIT, TO_DRAWING_UNIT
from qw_drawing_toolkit.pdf.models.base_models import DrawingUnit


class TestToleranceQuery(object):
    def test_unit_map_completeness(self) -> None:
        items_drawing_unit = set(DrawingUnit)
        assert set(TO_DRAWING_UNIT.values()) == items_drawing_unit
        assert set(FROM_DRAWING_UNIT.keys()) == items_drawing_unit

        items_tolerance_unit = set(ToleranceUnit)
        assert set(TO_DRAWING_UNIT.keys()) == items_tolerance_unit
        assert set(FROM_DRAWING_UNIT.values()) == items_tolerance_unit
