import time
from collections import defaultdict
from typing import Dict, List, Set, Tuple

import numpy as np

from qw_drawing_toolkit.pdf.models.analysis_models import TextBox
from qw_drawing_toolkit.pdf.models.base_models import T_BBOX, T_LINE_SEGMENT
from qw_drawing_toolkit.pdf.models.drawing_models import DrawingAnalysisContext
from qw_drawing_toolkit.pdf.models.geometry_models import LineDirection, LineSelection
from qw_drawing_toolkit.pdf.parser.geometry import LineCollection
from qw_drawing_toolkit.pdf.utils.geometry_utils import (
    bezier_cubic,
    find_lowest_line_with_adjacent_verticals,
    find_next_connected_vertical,
    find_title_block_lines,
    is_line_in_direction,
    length_of_line,
    normalize_by_tolerance,
    rotate_bbox,
    rotate_lines,
    rotate_point,
    rotate_text_boxes,
)


class GeometryService:
    def __init__(self, debug: bool):
        """Initialize the GeometryService"""
        self.debug = debug

    def extract_drawings(
        self, ctx: DrawingAnalysisContext
    ) -> tuple[list[T_LINE_SEGMENT], list[T_LINE_SEGMENT], list[T_BBOX]]:
        """
        Extract geometric elements from a PDF page

        Args:
            page: The PDF page to extract geometric elements from

        Returns:
            Tuple of (filtered lines, unfiltered lines, rectangles)
        """
        start_time = time.time()
        curve_segmenting = ctx.bezier_segments

        lines_unfiltered = []
        lines = []
        rectangles = []
        for shape in ctx.page.get_drawings():
            for item in shape["items"]:
                if item[0] == "l":  # lines
                    start = (float(item[1].x) * ctx.zoom, float(item[1].y) * ctx.zoom)
                    end = (float(item[2].x) * ctx.zoom, float(item[2].y) * ctx.zoom)
                    line_no_zoom = ((float(item[1].x), float(item[1].y)), (float(item[2].x), float(item[2].y)))
                    line = (start, end)
                    lines_unfiltered.append(line)
                    if length_of_line(line_no_zoom) > 2 * ctx.point_proximity_tolerance:
                        lines.append(line)

                elif item[0] == "re":  # rectangles
                    rect = item[1]
                    rect = (
                        ctx.zoom * float(rect.x0),
                        ctx.zoom * float(rect.y0),
                        ctx.zoom * float(rect.x1),
                        ctx.zoom * float(rect.y1),
                    )
                    rectangles.append(rect)
                elif item[0] == "el":  # ellipses (circles)
                    rect = item[1]
                    rect = (
                        ctx.zoom * float(rect.x0),
                        ctx.zoom * float(rect.y0),
                        ctx.zoom * float(rect.x1),
                        ctx.zoom * float(rect.y1),
                    )
                    rectangles.append(rect)
                # we are converting the curves into lines here in order to not having to account for these
                # different types in the algo
                elif item[0] == "c":  # cubic Bezier curves
                    p0, p1, p2, p3 = item[1], item[2], item[3], item[4]
                    p0, p1, p2, p3 = (
                        np.array([p0.x, p0.y]),
                        np.array([p1.x, p1.y]),
                        np.array([p2.x, p2.y]),
                        np.array([p3.x, p3.y]),
                    )
                    for i in range(curve_segmenting):
                        t1, t2 = i / curve_segmenting, (i + 1) / curve_segmenting
                        start_nd = bezier_cubic(p0, p1, p2, p3, t1)
                        end_nd = bezier_cubic(p0, p1, p2, p3, t2)
                        line_no_zoom = ((float(start_nd[0]), float(start_nd[1])), (float(end_nd[0]), float(end_nd[1])))
                        line = (
                            (float(start_nd[0] * ctx.zoom), float(start_nd[1] * ctx.zoom)),
                            (float(end_nd[0] * ctx.zoom), float(end_nd[1] * ctx.zoom)),
                        )
                        lines_unfiltered.append(line)
                        if length_of_line(line_no_zoom) > 2 * ctx.point_proximity_tolerance:
                            lines.append(line)

                elif item[0] == "qu":  # quadrilateral
                    quad = item[1]
                    p0, p1, p2, p3 = quad[0], quad[1], quad[2], quad[3]
                    p0 = (p0.x * ctx.zoom, p0.y * ctx.zoom)
                    p1 = (p1.x * ctx.zoom, p1.y * ctx.zoom)
                    p2 = (p2.x * ctx.zoom, p2.y * ctx.zoom)
                    p3 = (p3.x * ctx.zoom, p3.y * ctx.zoom)
                    lines.append((p0, p1))
                    lines.append((p1, p3))
                    lines.append((p3, p2))
                    lines.append((p2, p0))
                    lines_unfiltered.append((p0, p1))
                    lines_unfiltered.append((p1, p3))
                    lines_unfiltered.append((p3, p2))
                    lines_unfiltered.append((p2, p0))

        if self.debug:
            print("GeometryService.extract_drawings: " + str(time.time() - start_time) + " Seconds")
        return lines, lines_unfiltered, rectangles

    def find_drawing_box_dimensions(self, ctx: DrawingAnalysisContext) -> T_BBOX:
        """
        Find the dimensions of the drawing box based on the longest horizontal and vertical lines

        Args:
            ctx: DrawingAnalysisContext

        Returns:
            Bounding box coordinates (min_x, min_y, max_x, max_y)
        """
        start_time = time.time()
        max_horizontal_line_length = 0.0
        max_vertical_line_length = 0.0
        max_horizontal_line: T_LINE_SEGMENT | None = None
        max_vertical_line: T_LINE_SEGMENT | None = None
        max_width = ctx.max_width - ctx.point_proximity_tolerance
        max_height = ctx.max_height - ctx.point_proximity_tolerance
        min_width = min_height = ctx.point_proximity_tolerance

        for line_index in range(len(ctx.lines)):
            line = ctx.lines[line_index]
            (lx1, ly1), (lx2, ly2) = line

            line_length = length_of_line(line)
            if is_line_in_direction(line, LineDirection.HORIZONTAL, ctx.point_proximity_tolerance):
                min_x = min(lx1, lx2)
                max_x = max(lx1, lx2)
                if (
                    line_length > max_horizontal_line_length
                    and min_width < min_x < max_width
                    and min_width < max_x < max_width
                ):
                    max_horizontal_line_length = line_length
                    max_horizontal_line = line

            elif is_line_in_direction(line, LineDirection.VERTICAL, ctx.point_proximity_tolerance):
                min_y = min(ly1, ly2)
                max_y = max(ly1, ly2)
                if (
                    line_length > max_vertical_line_length
                    and min_height < min_y < max_height
                    and min_height < max_y < max_height
                ):
                    max_vertical_line_length = line_length
                    max_vertical_line = line

        if max_horizontal_line is None or max_vertical_line is None:
            raise ValueError("Could not find max horizontal and/or max vertical line")

        (mhx1, mhy1), (mhx2, mhy2) = max_horizontal_line
        (mxv1, mvy1), (mvx2, mvy2) = max_vertical_line

        min_y = min(min(mhy1, mhy2), min(mvy1, mvy2))
        max_y = max(max(mhy1, mhy2), max(mvy1, mvy2))

        min_x = min(min(mhx1, mhx2), min(mxv1, mvx2))
        max_x = max(max(mhx1, mhx2), max(mxv1, mvx2))

        if self.debug:
            print("GeometryService.find_drawing_box_dimensions: " + str(time.time() - start_time) + " Seconds")

        return min_x, min_y, max_x, max_y

    def group_lines(
        self, ctx: DrawingAnalysisContext
    ) -> Tuple[Dict[float, List[T_LINE_SEGMENT]], Dict[float, List[T_LINE_SEGMENT]]]:
        """
        Group lines by their orientation (horizontal or vertical) and position

        Args:
            ctx: DrawingAnalysisContext

        Returns:
            Tuple of (horizontals_by_y, verticals_by_x) dictionaries mapping positions to lists of lines
        """
        start_time = time.time()
        horizontals_by_y: Dict[float, List[T_LINE_SEGMENT]] = {}
        verticals_by_x: Dict[float, List[T_LINE_SEGMENT]] = {}
        for line in ctx.lines:
            (x1, y1), (x2, y2) = line
            min_x = min(x1, x2)
            min_y = min(y1, y2)

            if is_line_in_direction(line, LineDirection.VERTICAL, ctx.point_proximity_tolerance):
                normalized_x = normalize_by_tolerance(min_x, ctx.point_proximity_tolerance)
                if normalized_x not in verticals_by_x:
                    verticals_by_x[normalized_x] = []
                verticals_by_x[normalized_x].append(line)

            if is_line_in_direction(line, LineDirection.HORIZONTAL, ctx.point_proximity_tolerance):
                normalized_y = normalize_by_tolerance(min_y, ctx.point_proximity_tolerance)
                if normalized_y not in horizontals_by_y:
                    horizontals_by_y[normalized_y] = []
                horizontals_by_y[normalized_y].append(line)

        if self.debug:
            print("GeometryService.group_lines: " + str(time.time() - start_time) + " Seconds")

        return horizontals_by_y, verticals_by_x

    def adjust_lines_to_rotation(
        self, ctx: DrawingAnalysisContext
    ) -> Tuple[
        List[T_LINE_SEGMENT],
        List[T_LINE_SEGMENT],
        Dict[float, List[T_LINE_SEGMENT]],
        Dict[float, List[T_LINE_SEGMENT]],
        T_BBOX,
        LineCollection,
        LineCollection,
        LineCollection,
        Tuple[float, float],
        List[TextBox],
    ]:
        """
        Adjust all geometric elements to account for page rotation.
        Note: This function should only be called when rotation != 0.

        Args:
            ctx: DrawingAnalysisContext

        Returns:
            Tuple of (
                rotated_lines,
                rotated_drawing_lines,
                rotated_verticals_by_x,
                rotated_horizontals_by_y,
                rotated_drawing_box,
                line_collection,
                horizontal_line_collection,
                vertical_line_collection,
                new_dimensions,
                rotated_text_boxes
            )
        """
        start_time = time.time()

        if not ctx.drawing_box:
            print("GeometryService.adjust_lines_to_rotation: Drawing box is not set")
            return (
                [],
                [],
                {},
                {},
                (0, 0, 0, 0),
                LineCollection.empty(),
                LineCollection.empty(),
                LineCollection.empty(),
                (0, 0),
                [],
            )

        rotated_lines = rotate_lines(ctx.lines, ctx.max_width, ctx.max_height, ctx.rotation)

        line_collection = LineCollection.build(
            lines=rotated_lines, proximity_tolerance=ctx.point_proximity_tolerance, extend_lines=False
        )
        horizontal_line_collection = line_collection.select(LineSelection(only_dir=LineDirection.HORIZONTAL))
        vertical_line_collection = line_collection.select(LineSelection(only_dir=LineDirection.VERTICAL))

        # Rotate drawing lines
        rotated_drawing_lines = rotate_lines(ctx.lines_drawing, ctx.max_width, ctx.max_height, ctx.rotation)

        # Rotate text boxes
        rotated_text_boxes = rotate_text_boxes(ctx.text_boxes_unfiltered, ctx.max_width, ctx.max_height, ctx.rotation)

        # Rotate verticals_by_x using utility functions
        rotated_verticals_by_x: Dict[float, List[T_LINE_SEGMENT]] = defaultdict(list)
        for x in ctx.verticals_by_x.keys():
            rotated_x_lines = rotate_lines(ctx.verticals_by_x[x], ctx.max_width, ctx.max_height, ctx.rotation)
            rotated_x = rotate_point((x, 0), ctx.max_width, ctx.max_height, ctx.rotation)[0]
            rotated_verticals_by_x[rotated_x] = rotated_x_lines

        # Rotate horizontals_by_y using utility functions
        rotated_horizontals_by_y: Dict[float, List[T_LINE_SEGMENT]] = defaultdict(list)
        for y in ctx.horizontals_by_y.keys():
            rotated_y_lines = rotate_lines(ctx.horizontals_by_y[y], ctx.max_width, ctx.max_height, ctx.rotation)
            rotated_y = rotate_point((0, y), ctx.max_width, ctx.max_height, ctx.rotation)[1]
            rotated_horizontals_by_y[rotated_y] = rotated_y_lines

        # Rotate drawing box using utility function
        rotated_drawing_box = rotate_bbox(ctx.drawing_box, ctx.max_width, ctx.max_height, ctx.rotation)

        # Adjust dimensions if needed for 90/270 degree rotations
        new_dimensions = (ctx.max_width, ctx.max_height)
        if ctx.rotation in [90, 270]:
            new_dimensions = (ctx.max_height, ctx.max_width)

        if self.debug:
            print(f"GeometryService.adjust_lines_to_rotation: {time.time() - start_time} seconds")

        return (
            rotated_lines,
            rotated_drawing_lines,
            rotated_verticals_by_x,
            rotated_horizontals_by_y,
            rotated_drawing_box,
            line_collection,
            horizontal_line_collection,
            vertical_line_collection,
            new_dimensions,
            rotated_text_boxes,
        )

    def find_title_block_sections(self, ctx: DrawingAnalysisContext, use_drawing_box: bool = False) -> List[T_BBOX]:
        """
        Find the sections of the title block

        Args:
            ctx: DrawingAnalysisContext
            use_drawing_box: Whether to use the drawing box as the lowest line

        Returns:
            List of bounding boxes representing title block sections
        """
        start_time = time.time()

        if not ctx.drawing_box:
            print("GeometryService.find_title_block_sections: Drawing box is not set")
            return []

        lowest_line: T_LINE_SEGMENT | None
        if use_drawing_box:
            lowest_line = (ctx.drawing_box[0], ctx.drawing_box[3]), (ctx.drawing_box[2], ctx.drawing_box[3])
        else:
            lowest_line = find_lowest_line_with_adjacent_verticals(
                ctx.lines, ctx.max_width, ctx.max_height, ctx.point_proximity_tolerance
            )

        if lowest_line is None:
            return []

        section_lines: Set[T_LINE_SEGMENT] = set()
        next_vertical = find_next_connected_vertical(
            lowest_line, ctx.lines, ctx.drawing_box, ctx.point_proximity_tolerance
        )
        len_sec_lines_prev = len(section_lines)
        tries_till_change = 0

        while next_vertical:
            section_lines.add(next_vertical)
            len_sec_lines = len(section_lines)
            if len_sec_lines != len_sec_lines_prev:
                len_sec_lines_prev = len_sec_lines
                tries_till_change = 0
            tries_till_change += 1
            if tries_till_change >= 10:
                break

            next_vertical = find_next_connected_vertical(
                next_vertical, ctx.lines, ctx.drawing_box, ctx.point_proximity_tolerance
            )

        result = []
        for section_line in section_lines:
            (ax1, ay1), (ax2, ay2) = section_line
            result.append((min(ax1, ax2), min(ay1, ay1), ctx.drawing_box[2], max(ay1, ay2)))

        if self.debug:
            print(f"GeometryService.find_title_block_sections: {time.time() - start_time} seconds")

        return result

    def find_drawing_lines(self, ctx: DrawingAnalysisContext) -> List[T_LINE_SEGMENT]:
        """
        Find lines that are part of the actual drawing (not title block)

        Args:
            ctx: DrawingAnalysisContext

        Returns:
            List of lines that are part of the drawing
        """
        if not ctx.drawing_box:
            print("GeometryService.find_drawing_lines: Drawing box is not set")
            return []

        title_block_lines = find_title_block_lines(ctx.lines, ctx.title_block_sections, ctx.point_proximity_tolerance)
        result = []

        dx1, dy1, dx2, dy2 = ctx.drawing_box
        db_height = dy2 - dy1
        db_width = dx2 - dx1
        start_time = time.time()

        for line in ctx.lines:
            if line not in title_block_lines:
                line_length = length_of_line(line)
                if not (
                    abs(line_length - db_height) <= ctx.point_proximity_tolerance
                    or abs(line_length - db_width) <= ctx.point_proximity_tolerance
                ):
                    result.append(line)

        if self.debug:
            print(f"GeometryService.find_drawing_lines: {time.time() - start_time} seconds")

        return result
