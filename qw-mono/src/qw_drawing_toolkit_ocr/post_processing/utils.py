"""
Image processing utilities for PMI post-processing.
"""
import base64
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import cv2
import numpy as np
from numpy.typing import NDArray

from qw_drawing_toolkit_ocr.post_processing.models import NormalizedPMIBlockAnalysisResult
from qw_log_interface import NO_LOGGER, Logger

# Color mapping for PMI types in BGR format
PMI_TYPE_COLORS = {
    "DIAMETER": (255, 0, 0),  # Blue
    "ANGLE": (0, 255, 0),  # Green
    "LINEAR": (0, 0, 255),  # Red
    "RADIUS": (0, 255, 255),  # Yellow
    "THREAD": (255, 0, 255),  # Purple
    "GDT": (255, 128, 0),  # Light Blue
    "CHAMFER": (0, 165, 255),  # Orange
    "ROUGHNESS": (128, 128, 128),  # Gray
}


def visualize_pmi_results(
    image: NDArray[np.uint8],
    pmi_results: Dict[str, List[NormalizedPMIBlockAnalysisResult]],
    output_dir: Optional[Path] = None,
    logger: Logger = NO_LOGGER,
) -> Optional[NDArray[np.uint8]]:
    """
    Visualize PMI detection results with colored bounding boxes for each PMI type.

    Args:
        image: Original image as numpy array
        pmi_results: Dictionary mapping classifications to lists of normalized PMI results
        output_dir: Optional directory to save visualization
        logger: Logger instance

    Returns:
        Visualized image as numpy array or None if visualization fails
    """
    try:
        # Create a copy of the image to avoid modifying the original
        vis_image = image.copy()

        # Process each PMI classification
        for classification, results in pmi_results.items():
            for normalized_result in results:
                try:
                    # Get the original result
                    result = normalized_result.result

                    # Use normalized polygon
                    h, w = image.shape[:2]
                    vertices = []
                    for point in normalized_result.polygon.points:
                        # Convert normalized coordinates to absolute values
                        x = int(point.x * w)
                        y = int(point.y * h)
                        vertices.append([x, y])

                    # Get color based on PMI type
                    pmi_type = result.detail.type if result.detail else "unknown"
                    color = PMI_TYPE_COLORS.get(pmi_type, (200, 200, 200))  # Default to light gray

                    if len(vertices) < 3:
                        continue

                    pts = np.array(vertices, dtype=np.int32)

                    # Draw filled polygon with transparency
                    overlay = vis_image.copy()
                    cv2.fillPoly(overlay, [pts], color)

                    # Blend with original image
                    alpha = 0.3
                    cv2.addWeighted(overlay, alpha, vis_image, 1 - alpha, 0, vis_image)

                    # Draw outline
                    cv2.polylines(vis_image, [pts], True, color, 2)

                except Exception as e:
                    logger.warning(f"Error visualizing PMI result: {str(e)}")
                    continue

        # Save visualization if output directory is provided
        if output_dir is not None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = output_dir / f"4_agent_results_{timestamp}.png"
            cv2.imwrite(str(output_path), vis_image)

        return vis_image

    except Exception as e:
        logger.error(f"Error in PMI visualization: {str(e)}")
        return None


def encode_image_to_base64(image: NDArray[np.uint8], logger: Logger = NO_LOGGER) -> Optional[str]:
    """
    Encode an image to base64 string.

    Args:
        image: Image as numpy array
        logger: Logger instance

    Returns:
        Base64 encoded string or None if encoding fails
    """
    try:
        if image is None or image.size == 0:
            return None

        # Convert BGR to RGB (OpenCV default)
        if len(image.shape) == 3 and image.shape[2] == 3:
            img_rgb = cv2.cvtColor(image.copy(), cv2.COLOR_BGR2RGB)
        else:
            img_rgb = image.copy()

        # Encode directly to PNG
        _, buffer = cv2.imencode(".png", img_rgb)
        return base64.b64encode(buffer.tobytes()).decode("utf-8")

    except Exception as e:
        logger.error(f"Error encoding image to base64: {str(e)}")
        return None
