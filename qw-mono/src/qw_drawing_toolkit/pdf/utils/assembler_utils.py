"""Utility functions for the result assembler."""

import math

from qw_drawing_toolkit.pdf.models.analysis_models import DimensionTextBox
from qw_drawing_toolkit.pdf.models.base_models import T_BBOX, DrawingBoundingBox, DrawingPoint
from qw_drawing_toolkit.pdf.models.drawing_models import (
    DrawingDepth,
    DrawingDepthThru,
    DrawingDepthValue,
    DrawingExplicitIso286FitTolerance,
    DrawingExplicitTolerance,
    DrawingLengthValue,
)


def prepare_drawing_explicit_iso286_tolerance(box: DimensionTextBox) -> DrawingExplicitIso286FitTolerance | None:
    """
    Create an ISO 286 fit tolerance object from dimension text box data.

    ISO 286 defines standardized fits between mating parts (e.g., H7/h6).
    This function extracts the fit identifier from the dimension text box
    and creates a corresponding tolerance object.

    Args:
        box: Dimension text box containing diameter and fit information

    Returns:
        DrawingExplicitIso286FitTolerance: Object representing the ISO fit tolerance,
            or None if required data is missing
    """
    if box.diameter is not None and box.fit is not None:
        return DrawingExplicitIso286FitTolerance(
            upper=0,
            lower=0,
            identifier=box.fit,
        )
    return None


def prepare_drawing_explicit_tolerance(box: DimensionTextBox) -> DrawingExplicitTolerance | None:
    """
    Create an explicit tolerance object from dimension text box data.

    This function extracts upper and lower tolerance values from the dimension
    text box and creates a corresponding tolerance object. It validates that
    the lower tolerance doesn't exceed the upper tolerance.

    Args:
        box: Dimension text box containing tolerance information

    Returns:
        DrawingExplicitTolerance: Object representing the explicit tolerance values,
            or None if required data is missing or invalid
    """
    if box.upper_tolerance is not None or box.lower_tolerance is not None:
        upper = 0 if not box.upper_tolerance else box.upper_tolerance
        lower = 0 if not box.lower_tolerance else box.lower_tolerance
        if lower > upper:
            return None
        return DrawingExplicitTolerance(upper=upper, lower=lower)
    return None


def prepare_normalized_drawing_bounding_box(
    page_index: int, bbox: T_BBOX, max_width: float, max_height: float
) -> DrawingBoundingBox:
    """
    Create a normalized bounding box with coordinates in the range [0,1].

    This function converts absolute pixel coordinates to normalized coordinates
    by dividing by the maximum width and height. This makes the coordinates
    independent of the actual drawing size.

    Args:
        page_index: Index of the page containing the bounding box
        bbox: Tuple of (x1, y1, x2, y2) coordinates in absolute units
        max_width: Maximum width of the drawing in absolute units
        max_height: Maximum height of the drawing in absolute units

    Returns:
        DrawingBoundingBox: Normalized bounding box with coordinates in range [0,1]
    """
    return DrawingBoundingBox(
        page_index=page_index,
        p1=DrawingPoint(x=bbox[0] / max_width, y=bbox[1] / max_height),
        p2=DrawingPoint(x=bbox[2] / max_width, y=bbox[3] / max_height),
    )


def prepare_drawing_depth(box: DimensionTextBox) -> DrawingDepth | None:
    """
    Create a depth specification object from dimension text box data.

    This function handles two types of depth specifications:
    1. Explicit depth values (e.g., "5mm deep")
    2. Through-hole specifications (e.g., "THRU" or "THRU ALL")

    Args:
        box: Dimension text box containing depth information

    Returns:
        DrawingDepth: Object representing either a specific depth value or
            a through-hole specification, or None if no depth information is present
    """
    depth = box.depth
    if depth is not None and not math.isinf(depth):
        return DrawingDepthValue(depth=DrawingLengthValue(value=depth))
    if box.thru:
        return DrawingDepthThru(all=box.thru_all)
    return None
