{"name": "DIN_EN_10243-1:2000-06", "tables": [{"name": "Tabelle 1", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 6.7, "lower": -3.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 6.7, "lower": -3.3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 7.3, "lower": -3.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 6.7, "lower": -3.3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 7.3, "lower": -3.7}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 8, "lower": -4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 6.7, "lower": -3.3}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 7.3, "lower": -3.7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 8, "lower": -4}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 9.3, "lower": -4.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 4.5, "lower": -4.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 5, "lower": -5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 5.5, "lower": -5.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 6, "lower": -6}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 7, "lower": -7}}]}, {"name": "Tabelle 2", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.5, "lower": -0.2}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.5, "lower": -0.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.6, "lower": -0.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 1 Ma"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.5, "lower": -0.3}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.6, "lower": -0.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 2 Ma"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.6, "lower": -0.3}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 3 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 4 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 5 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 6 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 7 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 8 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 9 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 10 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 11 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 12 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 13 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 14 Ma"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 32, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 32, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 2.25, "lower": -2.25}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 400, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 2.8, "lower": -2.8}}, {"lowerValueLimit": {"value": 400, "inclusive": false}, "upperValueLimit": {"value": 630, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 3.15, "lower": -3.15}}, {"lowerValueLimit": {"value": 630, "inclusive": false}, "upperValueLimit": {"value": 1000, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 1000, "inclusive": false}, "upperValueLimit": {"value": 1600, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 1600, "inclusive": false}, "upperValueLimit": {"value": 2500, "inclusive": true}, "rowIdentifiers": [["Reihe 15 Ma"]], "tolerance": {"upper": 4.5, "lower": -4.5}}]}, {"name": "Tabelle 3", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 6.7, "lower": -3.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 6.7, "lower": -3.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 7.3, "lower": -3.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 5.3, "lower": -2.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 6, "lower": -3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 6.7, "lower": -3.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 7.3, "lower": -3.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 8, "lower": -4}}]}, {"name": "Tabelle 4", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.4, "lower": -0.2}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.5, "lower": -0.2}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.5, "lower": -0.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.6, "lower": -0.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 1"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.5, "lower": -0.2}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.5, "lower": -0.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.6, "lower": -0.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 2"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.5, "lower": -0.3}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.6, "lower": -0.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 3"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.6, "lower": -0.3}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 4"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 0.7, "lower": -0.3}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 5"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 0.7, "lower": -0.4}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 6"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 7"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 0.9, "lower": -0.5}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 8"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.1, "lower": -0.5}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 9"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.2, "lower": -0.6}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 10"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 1.3, "lower": -0.7}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 11"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 1.5, "lower": -0.7}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 12"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 1.7, "lower": -0.8}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 13"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 1.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 14"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 2.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 15"]], "tolerance": {"upper": 4.2, "lower": -2.1}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 6.7, "lower": -3.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 16"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 16, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 2.7, "lower": -1.3}}, {"lowerValueLimit": {"value": 16, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 3, "lower": -1.5}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 63, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 3.3, "lower": -1.7}}, {"lowerValueLimit": {"value": 63, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 3.7, "lower": -1.9}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 160, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 6.7, "lower": -3.3}}, {"lowerValueLimit": {"value": 160, "inclusive": false}, "upperValueLimit": {"value": 250, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 4.7, "lower": -2.3}}, {"lowerValueLimit": {"value": 250, "inclusive": false}, "upperValueLimit": {"value": 99999, "inclusive": true}, "rowIdentifiers": [["Reihe 17"]], "tolerance": {"upper": 5.3, "lower": -2.7}}]}]}