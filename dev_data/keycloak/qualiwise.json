{"id": "6da1418d-4e49-447f-ae51-3a7da464249e", "realm": "qualiwise", "displayName": "", "displayNameHtml": "", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "none", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "5c53fb6b-0186-4259-ac3f-4cf02a21e590", "name": "default-roles-dev", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "6da1418d-4e49-447f-ae51-3a7da464249e", "attributes": {}}, {"id": "83d5413b-d3ae-4b1b-9d50-e653bb2d25af", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "6da1418d-4e49-447f-ae51-3a7da464249e", "attributes": {}}, {"id": "6ddb02c2-ba9c-4432-8c78-b5b1c0abc165", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "6da1418d-4e49-447f-ae51-3a7da464249e", "attributes": {}}], "client": {"realm-management": [{"id": "ccb93062-a5c9-47e5-9dbc-695bb091207d", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "8389f269-efef-4fd4-856e-249d875f7874", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "e472aa83-1c1a-423a-bd83-51c2e5ade94a", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "f43022b1-825e-4efa-a18c-bd5a9a27d642", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "2ec61df8-517a-4523-b63c-ac6c6ee9d6b1", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "e5dd3c3e-6e90-4da1-8d9a-a0d33a610180", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "a209e40f-6f76-44cb-9fe1-f90990ff2efc", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "92839d46-da5d-46a0-8f67-ac7da6757c1e", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "bad18929-d4a8-4342-817e-8a6d1b4289a5", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "030090f0-3703-45dd-922b-3b3b2c5a47e2", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "a7eb5594-8b64-482e-890e-f88ab467a361", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "ad0e00b1-c94e-465a-8779-7a26dc0abd04", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "771b7168-7fe2-4905-a431-8dbdf7723023", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "faa71067-0fbf-4ae5-981b-2df1888479c1", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "d947adc0-951d-4234-af48-0c2be6c02f74", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-users", "query-users", "impersonation", "view-identity-providers", "view-authorization", "query-clients", "view-clients", "manage-realm", "view-realm", "manage-identity-providers", "manage-authorization", "manage-clients", "view-events", "manage-users", "query-groups", "manage-events", "query-realms", "create-client"]}}, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "268eb717-6b46-4b1e-9d37-03727e26800f", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "cda937ff-e028-4d81-9404-5371e9e3ac69", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "c9d06cc1-bb00-44ce-983e-c8ea7c44e1e4", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}, {"id": "6f88cd07-79a5-4732-8d35-5577bedad2b8", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "6faa729b-bb36-4e4f-81af-97558b55a98b", "attributes": {}}], "qualiwise-pfoertner": [{"id": "74ef8167-12f9-4a28-a07a-f436c99903bd", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "composite": false, "clientRole": true, "containerId": "307ae14d-231c-4c80-ba76-b5c29e053666", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "f743e8be-b4bb-42b6-8f65-58a6e02ffa9d", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "ecfd3a01-2b7b-4011-8165-9ffd851af607", "attributes": {}}], "account": [{"id": "67f00f82-a27c-431c-ac69-0db0a39a3607", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "af8a3324-ff8c-4b91-82e6-a5f958e16698", "attributes": {}}, {"id": "a4fa9f5a-b11f-4708-a78e-aad898395a0a", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "af8a3324-ff8c-4b91-82e6-a5f958e16698", "attributes": {}}, {"id": "6ab112fa-1a26-4e6a-9e48-74ed4908bfdb", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "af8a3324-ff8c-4b91-82e6-a5f958e16698", "attributes": {}}, {"id": "614518e1-2e17-4b58-b65a-e72f096805a9", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "af8a3324-ff8c-4b91-82e6-a5f958e16698", "attributes": {}}, {"id": "73fbe052-ccdf-469d-b291-d327a913ccb2", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "af8a3324-ff8c-4b91-82e6-a5f958e16698", "attributes": {}}, {"id": "fcc6806e-4e85-423d-9851-22fcb43c4ffc", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "af8a3324-ff8c-4b91-82e6-a5f958e16698", "attributes": {}}, {"id": "********-6679-4c31-826c-51f72dc306b5", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "af8a3324-ff8c-4b91-82e6-a5f958e16698", "attributes": {}}, {"id": "d38b3c1b-46cc-4264-a05e-41ae20fe8244", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "af8a3324-ff8c-4b91-82e6-a5f958e16698", "attributes": {}}]}}, "groups": [{"id": "f5d0b145-e619-4500-8ef7-cfcaa4e45fd7", "name": "tenants", "path": "/tenants", "subGroups": [{"id": "6feb187d-8e20-4b68-8647-91000c7981bb", "name": "metal_mates", "path": "/tenants/metal_mates", "parentId": "f5d0b145-e619-4500-8ef7-cfcaa4e45fd7"}, {"id": "26b46127-d7b8-4aea-acd1-175467c47208", "name": "precision_parts", "path": "/tenants/precision_parts", "parentId": "f5d0b145-e619-4500-8ef7-cfcaa4e45fd7"}, {"id": "7f0455fb-1771-4f6e-89ee-5f9bbed63bbd", "name": "quality_manufacturing", "path": "/tenants/quality_manufacturing", "parentId": "f5d0b145-e619-4500-8ef7-cfcaa4e45fd7"}]}], "defaultRole": {"id": "5c53fb6b-0186-4259-ac3f-4cf02a21e590", "name": "default-roles-dev", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "6da1418d-4e49-447f-ae51-3a7da464249e"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "dca953ed-16da-4562-8813-de62fcf51b7f", "createdTimestamp": 1703261384080, "username": "<EMAIL>", "enabled": true, "totp": false, "emailVerified": true, "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "credentials": [{"id": "9dae137c-ed73-446c-a715-d58fe045d05d", "type": "password", "userLabel": "My password", "createdDate": 1703261637898, "secretData": "{\"value\":\"rG8ggJYjp/zTcLRFDz0ZGdxAhNOYriEBBRSskG+ChE4=\",\"salt\":\"rmCRnQdvlAQtky5e8vnylA==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-dev"], "notBefore": 0, "groups": ["/tenants/metal_mates"]}, {"id": "a85d7cdd-d5f9-46a7-ba68-5801fbf65a88", "createdTimestamp": 1703261354360, "username": "<EMAIL>", "enabled": true, "totp": false, "emailVerified": true, "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>", "credentials": [{"id": "300527a4-8d2c-4a74-bbad-fb84c414f200", "type": "password", "userLabel": "My password", "createdDate": 1703261648629, "secretData": "{\"value\":\"QvGf3acebTsdHBFDhp87D6HTHd+Vij9DyApgGtIBh7o=\",\"salt\":\"oZhLnVp2Dp2i9/VSoA4aVg==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-dev"], "notBefore": 0, "groups": ["/tenants/precision_parts"]}, {"id": "c168f2ff-4cf2-4712-8cf9-9a7ff0969b3f", "createdTimestamp": 1703261215139, "username": "<EMAIL>", "enabled": true, "totp": false, "emailVerified": true, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "credentials": [{"id": "3a7e6ba5-7cd1-4638-8ad9-30697190db74", "type": "password", "userLabel": "My password", "createdDate": 1703261657918, "secretData": "{\"value\":\"RGWZX5N3Me9s0FDlGNBcX9ajZh4meB6YB5nLd89wRnU=\",\"salt\":\"HNso/GvOvS3zJR+/RQrOMg==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-dev"], "notBefore": 0, "groups": ["/tenants/quality_manufacturing"]}, {"id": "2cd5cf5e-2ee6-43c8-9b62-6617b1b7e263", "createdTimestamp": *************, "username": "service-account-qualiwise-pfoertner", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "qualiwise-pfoertner", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-dev"], "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "af8a3324-ff8c-4b91-82e6-a5f958e16698", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/qualiwise/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/qualiwise/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["acr", "email"], "optionalClientScopes": []}, {"id": "bd2f16e6-c570-493c-9452-c58a35b1a53b", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/dev/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/dev/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "2df5aab8-62d5-44a1-a2a4-73ec5d83df5f", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["acr", "email"], "optionalClientScopes": []}, {"id": "16927da9-5ce2-49dd-aee7-6f2063a3f9d9", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["acr", "email"], "optionalClientScopes": []}, {"id": "ecfd3a01-2b7b-4011-8165-9ffd851af607", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["acr", "email"], "optionalClientScopes": []}, {"id": "307ae14d-231c-4c80-ba76-b5c29e053666", "clientId": "qualiwise-pfoertner", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "4qYgrCNSoxLc3I73okdUZr69BRYUlJ8u", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "display.on.consent.screen": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "ee80e776-d80d-4f5f-95ed-439d9eb0f944", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "39bedbaa-26d9-42ca-a189-61a0e626619f", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}, {"id": "7a031be9-a24e-4d76-a5dc-d42c98b8076d", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["acr", "qw_groups", "qw_roles", "given_name", "family_name", "email"], "optionalClientScopes": [], "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "Default Resource", "type": "urn:qualiwise-pfoertner:resources:default", "ownerManagedAccess": false, "attributes": {}, "_id": "2b33f033-d3ef-464a-9a96-c6a48c202f55", "uris": ["/*"]}], "policies": [], "scopes": [], "decisionStrategy": "UNANIMOUS"}}, {"id": "6faa729b-bb36-4e4f-81af-97558b55a98b", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["acr", "email"], "optionalClientScopes": []}, {"id": "a3d47808-5f51-4c53-a94e-3b736fdf0d5c", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/qualiwise/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/qualiwise/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "d0e7cb25-00b8-4cd8-b209-f745ce36e036", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["acr", "email"], "optionalClientScopes": []}], "clientScopes": [{"id": "572f094e-ef3f-443a-9a6f-6856fe9d61f0", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "3f58280f-ff52-41b8-8623-4234395aade5", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"id": "ad53cc8a-818e-4df0-86cb-4294641a9832", "name": "given_name", "description": "OpenID Connect built-in scope: given_name", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "45b2fe67-94ab-4ace-b8ff-feddb17ccb6e", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"aggregate.attrs": "false", "userinfo.token.claim": "true", "multivalued": "false", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "false", "claim.name": "given_name", "jsonType.label": "String"}}]}, {"id": "81130dc4-872a-407a-84a6-2ef89ea541c8", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "7a87e47e-34ae-4522-ab61-a34194049355", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "false", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "bdf9399a-fe44-49ed-8485-bfd9066f2f42", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"aggregate.attrs": "false", "userinfo.token.claim": "true", "multivalued": "false", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "false", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "c3957672-086c-49d6-8750-8fa64682ebc4", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "d297f33f-bedb-48c3-8ecf-ebfddfd548ca", "name": "family_name", "description": "OpenID Connect built-in scope: family_name", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "4784e2fc-a713-46b7-84ee-60cc5b1816ff", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"aggregate.attrs": "false", "userinfo.token.claim": "true", "multivalued": "false", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "false", "claim.name": "family_name", "jsonType.label": "String"}}]}, {"id": "2ff0b35b-e8b1-48d1-ac6d-eac2638b9303", "name": "qw_roles", "description": "OpenID Connect scope for qualiwise roles of an access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "82543709-9a55-4b13-806e-bcef208cc296", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "false", "user.attribute": "foo", "id.token.claim": "false", "access.token.claim": "true", "claim.name": "qw_roles", "jsonType.label": "String", "usermodel.clientRoleMapping.clientId": "qualiwise-pfoertner"}}]}, {"id": "7865c21d-854e-4c92-8d1b-a45dbffa048b", "name": "qw_groups", "description": "OpenID Connect scope for qualiwise groups of an access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "bcc7b4af-ff46-4c56-a5f1-e9225bcf474d", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-group-membership-mapper", "consentRequired": false, "config": {"full.path": "true", "userinfo.token.claim": "false", "multivalued": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "qw_groups"}}]}], "defaultDefaultClientScopes": ["email", "acr", "given_name", "family_name", "qw_groups", "qw_roles"], "defaultOptionalClientScopes": ["offline_access"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "449ad96a-fcb8-430d-841e-8e6177826cea", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "dfda2ca7-2fee-4b86-adf4-1d37fcddcf94", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "aa0e9c96-3119-4ed7-98e2-95a316f99226", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "ec6890d5-d108-4c3c-9104-402235564c96", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper", "oidc-address-mapper", "saml-role-list-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper"]}}, {"id": "6af6f24e-aacf-4d37-a559-a968ab2de55c", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "312df248-e65d-4c25-bcb8-69813a410c4a", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "237476db-772c-46bb-a8cf-0d4c359146f3", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "4d650e90-259f-4b11-8970-55edfbeffbbb", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper", "oidc-address-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper"]}}], "org.keycloak.keys.KeyProvider": [{"id": "0235b4c5-ca6d-4c9a-abf2-713275e6a97e", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["eb51e6d5-ac25-4be2-89ca-eb5e2c9c2a63"], "secret": ["k0DSqtURTY9hA408_5Dv6A"], "priority": ["100"]}}, {"id": "17192415-6a7a-4089-82a2-b8152c4d77df", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["35eaace1-5548-441b-8b22-97051f13144d"], "secret": ["fem5whMgBXPFGiiSVCQJ-Iy023Tv5w1NZJgYpPSCyUJqrKKnQtQ-45h6Arnm5WysnUZYLwBCE3UK41J5vc7riw"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "d7d46018-fc4d-4974-a98a-0ebd220fc82c", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAymAJ7W3xC+5ODopePb78yr1GNEfVunjPr2c7vPMeUhctZsQuakfyBL2SH+emdDc+P2HPGPVYg1Iu9ePIRQXfNxTkcMiLEHldaJbosaqoTQWVkXXXfQZ2gSKiOMwJ2XzE8yR1Zlq4G79y1RApdddiow80MjZaMFrEP4KN8etyYjrIn98wjB8JLteRTtNh0J9qO0JT0hfJUfHSuSURYonNDQDXbk79ieGWSOFqWN2+9oprD3SP5OS0qSFO20sz/m1SCIrDdJSB/U527xiJ8mh8gzpbWqd9skgOwJjSRa5XPdR/GRKFVGDD0SUAJFCB24sEvbHpv3UDW0PZv2RjzgSANQIDAQABAoIBAActC8PtqMm+OsV6pkf5XZWn8DUzzStVHQ3gd1+q1GZD7PxaBn9PtFlgruR8ueeOew9KS5PJ++zIuNAi+236jy5n02OSsgOrrFAbP355262eWYBdo35UJVa6nBbTxn0QMkDFdcFjOvqY527HhO6Nx+gH/Y33ZNlNPFo4KeXHt1x3Z41GJZV8KFWXz1NdxAv7t7AXGcg1Z+MtA3w7SjFxe/1s0sT88bydnV8MGPmMiJDJcnVKF3nXM4dfFCWISWq3/I1N6GcHxaGNh8P2cvyJG/h5ajBLJ4nRF6RomkLrY1GVgQlzD89HYWpoJDbNrJlO9cIS0sfoUQGDaht+YSTxlQECgYEA8F6od8KYL2YMq/bK6+xhduVFqzQgdmoKHB49F6wTLcxGc3ycpRdM01HHX8zyFJ13rJBrtfPJ6kB7INJfJyfIIeXZtOKIUJG7MvabW4lvHBn3aZZYgVxkqRB30sWY6LwRXxszR7KhN4eNhOFyrWdy2UHLetyud/eNY1Kkwp99M/UCgYEA14jmNZP4LgXGV0tq1itGaACmktF0MHyyfs4Hu+DlOVDpNBN4GawkJXSeX9nDvihRQeqqN4IMzoAYB6mpxRdUJGUVjoGpWjULDgwbCGfsxzTYVEyaiyvQ0uG4zkN0aXlYtA3ZsDm0A8Hz6txtsC5E31fEGC/QqE1VyEz/9OqOs0ECgYBXMh9vAlpup1hlcWt/VgqtNNKnHovAKQtt09JZqgRzEq1j64pvoRovdnUa3Z/btpvZL7LDGKLgpW+FIot+CsJg+Z6WLa9BevwrqqYE+MAjs4+6790/YCLr6ZtuDFxB4Sd0FsvtcHFnUXcXAym0mdhh06pa8X8g6d6Sb0Jxj6W9mQKBgAQp3tiWHSs2bVhuWof8mgWUTaN0bFwrbH9Kl5enhoYO7BJXdfB5BNG8aPrNCBz6RmeRkuwzlO/ARbaQLnliM/vn//yawJy/fZEt5q/I4igO+0bVm/6oQAZy2dESOIN5Fp+TzOq8kp+n9YBJ9cx7eFUgKueUtOErbUbLRf6DMXKBAoGBAOai8N0e5A5DxQJyvIrOTmy5DZLbUbGsDeYqwkSRDH7c4tx9Hj9wapCRCrXJ3a0pvYyrJq/v7Gp9nRBd4zlQzTXq84wVJqUDcrWDko/NEByohyQ7B3WtXaSkjcw2QY8pGF4WPao12DVcnZE76aUWpy0RAp3lpfcQ7EcBvbf7kkQB"], "certificate": ["MIIClTCCAX0CBgGKHx21DzANBgkqhkiG9w0BAQsFADAOMQwwCgYDVQQDDANkZXYwHhcNMjMwODIyMjExNzM1WhcNMzMwODIyMjExOTE1WjAOMQwwCgYDVQQDDANkZXYwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDKYAntbfEL7k4Oil49vvzKvUY0R9W6eM+vZzu88x5SFy1mxC5qR/IEvZIf56Z0Nz4/Yc8Y9ViDUi7148hFBd83FORwyIsQeV1oluixqqhNBZWRddd9BnaBIqI4zAnZfMTzJHVmWrgbv3LVECl112KjDzQyNlowWsQ/go3x63JiOsif3zCMHwku15FO02HQn2o7QlPSF8lR8dK5JRFiic0NANduTv2J4ZZI4WpY3b72imsPdI/k5LSpIU7bSzP+bVIIisN0lIH9TnbvGInyaHyDOltap32ySA7AmNJFrlc91H8ZEoVUYMPRJQAkUIHbiwS9sem/dQNbQ9m/ZGPOBIA1AgMBAAEwDQYJKoZIhvcNAQELBQADggEBACBfp4r5vHK+4R2sEX93X9+8/J2akhrVQ/DDJYm7PBIrD5QTt4wkNCXYu9Sz7dKjzasNVK7BKIxJjta1ef8QznrIAQJbdEquKDSO6TPuiYdm61fU3aLCulAtN4AZshtoPn+Tbc+yhh4yqhufw4k6FjoclZlYNvHkTJwfhT8zUHIpQQD4cynwc3tWAyuz7DJZdfEfTFAmCvn3P77SPLVTlqs8XVU0ArKdnS1bTUrDOvrQ8trnxc9M1a7kG76j/ll5y9+gFLVETjVCv/hC0xqcWdw5wx3YEXHas/TPmAj+VXeSSdJ/WAtGAqb3DNBCM8DPxOIpd/2CeYDhFlp6bEIw+/I="], "priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "78d13997-caee-4543-ac55-201e8b806de9", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpAIBAAKCAQEAxtqFe/RWgHkpE8SN3Hn0XnF0a2G5nx9a3g58IaqIf9peJCasCIW9rZDkSZkWvPFfrYBGoiofVdA2i7/3pyUX8cjU72UExwfk7t0LtcwYrzK3mpCzkNYz4+WzB/2IJ0j+M4OK9SK2+R05UuoyCzQ6lPdl0YhGOfcsgxOzpUDW0nVieeWvrBve69jydDDM6HK+3kLNDIl/HqVg1jHWS2wYnjXoctAopZyXDGEIntAC5XQ4mlT0HEwKJ7Te6mkDnjuuDGci/uyATlCZOJJ7r/uvPHQbyCPkbNdEUSElSvlSTV3pMHl02Anuk3mroWHbAdyP7h+bKSyj7uTC8Ei8JArFZwIDAQABAoIBAAYBmLGPRkOYJUT7FVvY9SgbHiyWJZCoVkrJvjcYGwpdkqVd46lYPMvuye5tKNIpKj4n1zpTsmGZqeQbtsO+K5R6f/ABKZ1HYENUuuNRWFeGXSRWhY304llaWUd/AWtKWZ89tGdGFqe8mbwm5b8P40sXk+Q5zQE4NYh6Al2V8UcnBcccvP8PGDcV65H0i0oIDfeauqdShl7YkdiqBvJA5u28cducwgp5/RjbbVeEPGo2uqvn3jnxlTnqMovv+3zUcsoR7KLjhy4pbAzTKmQlkHC0eaFaJOUVC8hfDQ2e+1+bmTcafYxX0SgHn5VlI9CvyScJ38zhrlmXU/e22wy8VHECgYEA/9QRMSAJHxKzrMut5I/Jp9Tmp1nQFyP8519TRZ/6zwvJR5+CJhVwciDs8Qn4mj9E3D+QbHi0o+8hP9jM567Y0zBVjBkOk+aAZGNR1bHwIPYwrZbRbuILQ7XkKqfR0L4QOnD0OSPy6VJVawUBAfqTvwmBkaQl59skYDxuC/zuWzECgYEAxvyrjHnTmmKZ51TSq30miTXjITrQ+6+HFWaXOk7+jVIZ6ZBMkU7sgaqHZoq+dTNCtbN63KFaVe0gAl+W4ZGiJPX4icuoPpE/uOg8Sb4qACztMQtD0DwcIxUVm/LhBjx5WlZJmDFb833z/9D8I/68Y6K49S60MewXFzxQyvnc1BcCgYBf6SeQ3UvOJHrMG4hUGjwl+cGHmDpVkp5sN+bGQZYMLYUGnxxyHrOLdiCetXhIi9PLJuGEaTSACVTT1PntGjYb5xELdxFqDY6xFDNIBG/cS7AwREy8X26NshGalFQrCDaZUmOdUIbvRnjTqIqaLVFa27uB2KKFbDkVA1LWl7rc4QKBgQCVNSWVgkxvOXxaADqwKHna7TNQ2X+XGjlajMDdEhrB8UxI3NDtLJcfa7V36JWD4FonUVvRUJQiVlpoLTFAFynUOTSSbsnLkLKNfmamjReFZiq9WcsRu+EFVWwMsgSV3obIfGdW3bwrSsjJi/VlMEAnHf8+qjSoMnXmEG4spUs3JQKBgQCuIh5ttZkcuSam1YFhYHgTWJozARyfUa0Bd2mN3Ze/HcZiJRpQ6hpcnVdFsCbtUgIe+nuLxP/yUcJ0wRXPLFJZDCaFLi+xc4LpYCtx7UL7ZVLf+r95LiTprQy4WLcyvr3LG+pdYksWp7LQbyF3mnTcpMqeAUO23ZWIqUvd8N8m4w=="], "certificate": ["MIIClTCCAX0CBgGKHx22rDANBgkqhkiG9w0BAQsFADAOMQwwCgYDVQQDDANkZXYwHhcNMjMwODIyMjExNzM1WhcNMzMwODIyMjExOTE1WjAOMQwwCgYDVQQDDANkZXYwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDG2oV79FaAeSkTxI3cefRecXRrYbmfH1reDnwhqoh/2l4kJqwIhb2tkORJmRa88V+tgEaiKh9V0DaLv/enJRfxyNTvZQTHB+Tu3Qu1zBivMreakLOQ1jPj5bMH/YgnSP4zg4r1Irb5HTlS6jILNDqU92XRiEY59yyDE7OlQNbSdWJ55a+sG97r2PJ0MMzocr7eQs0MiX8epWDWMdZLbBieNehy0CilnJcMYQie0ALldDiaVPQcTAontN7qaQOeO64MZyL+7IBOUJk4knuv+688dBvII+Rs10RRISVK+VJNXekweXTYCe6TeauhYdsB3I/uH5spLKPu5MLwSLwkCsVnAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAFjvMeKKFm603fqBT01nwkA7wKW9wxIY4ghdgY4FElf5q29ysfo9aFep0RUEGv0MKh6ki68NpoEU4B/G1r7aWcf7SvN3cXznZwB55RgqPC16VX5vpyc6rig93pz/XCI2HNsfpbdMxbmHfPkUMdGz2mvvXwFZ3GifxmljYA7RtcfBvCvKutNtBevRRPtOOqUZ8QuN4qiRgWWVcvfIcXx9Oyk2VC3yyJZ4znijzHep724Sl6n1lOUaHQa4iajJJu0OV745woiqGVUrbdXCFxdZQH0VrmmKU/JQfDwR7KIThxUzCjAv5xubQbxklqTjuxf50IAWgZIR0aSrYKMe3VBU2Ps="], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "d459a42e-f2ee-439b-80e3-a39ad4fb8b62", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "aa90e16e-3870-49b2-861b-ccf3b692409c", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "9131a2b2-2122-4c3f-85c5-200fea84b904", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "1a421972-fb89-4a5f-869c-f2d9570421ea", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "2e0b736f-25bb-444a-af70-adc3434c4c33", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "7bcb452e-1505-4c31-af94-d24ae75e6f39", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "7d61622b-2b53-4a22-8970-a8efb90543ea", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "8e6ebae5-0de8-4b07-bb4c-f7d0c693cf2a", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "647d0934-de75-4c4b-8c57-377359da29eb", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "9f6f1484-4da6-4866-97c6-8d9e641ae3a9", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "1ab82d54-7221-42cc-a976-82c4cfc0816f", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "aa707b51-6507-4441-bce9-62ab43a779f5", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "039d9d7a-01ad-45ff-82b4-faaa8f848070", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "dbd3949b-a170-4456-aeb5-c459648d5b0a", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "7031bbe5-968b-4856-aad2-6d5e2a0ad36b", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "fe665e5f-026a-4d81-aaf7-a174331730a3", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "d7090179-6c3d-4105-8878-6a2a982f7dc5", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "628b31f0-a2d6-4b29-857b-8047bf8336fe", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "8924177f-f205-4f2f-a04f-0ea43daf9e7c", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "5b49214a-ceb6-4545-ab7d-5418b34a822f", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaAuthRequestedUserHint": "login_hint", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5", "realmReusableOtpCode": "false", "cibaExpiresIn": "120", "oauth2DeviceCodeLifespan": "600", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "frontendUrl": "", "acr.loa.map": "{}"}, "keycloakVersion": "23.0.3", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}