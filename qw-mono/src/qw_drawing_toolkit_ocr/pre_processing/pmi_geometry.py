"""
Geometry utilities for PMI processing.
This module provides functions for handling geometric calculations
in technical drawings, including text orientation and height measurements.
"""
import math
from typing import List, Optional, Tuple

import cv2
import numpy as np
from numpy.typing import NDArray
from pydantic import Field
from pydantic.dataclasses import dataclass

from qw_drawing_toolkit_ocr.pre_processing.models import Vertices
from qw_log_interface import NO_LOGGER, <PERSON>gger


@dataclass
class Point2D:
    """Represents a 2D point with x and y coordinates."""

    x: float = Field(description="X coordinate")
    y: float = Field(description="Y coordinate")


@dataclass
class MinAreaRect:
    """Information about a minimum area rectangle calculated by OpenCV."""

    center: Point2D = Field(description="Center point of the rectangle")
    width: float = Field(description="Width of the rectangle (longer dimension)")
    height: float = Field(description="Height of the rectangle (shorter dimension)")
    angle: float = Field(description="Angle of the rectangle in degrees")


def extract_coordinates(vertices: Vertices) -> Tuple[List[float], List[float]]:
    """Extract x and y coordinates from vertices.

    Args:
        vertices: List of vertices from a bounding polygon

    Returns:
        Tuple of (x_coords, y_coords) as lists of float values
    """
    x_coords = [float(vertex.get("x", 0)) for vertex in vertices if "x" in vertex]
    y_coords = [float(vertex.get("y", 0)) for vertex in vertices if "y" in vertex]
    return x_coords, y_coords


def vertices_to_points(vertices: Vertices) -> NDArray[np.float32]:
    """
    Convert vertices to a numpy array of points for OpenCV functions.

    Args:
        vertices: List of vertices from a bounding polygon

    Returns:
        Numpy array of points in the format expected by OpenCV
    """
    points = []
    for vertex in vertices:
        if "x" in vertex and "y" in vertex:
            points.append([float(vertex["x"]), float(vertex["y"])])

    if not points:
        return np.array([], dtype=np.float32)

    return np.array(points, dtype=np.float32)


def calculate_min_area_rect(vertices: Vertices, logger: Logger = NO_LOGGER) -> Optional[MinAreaRect]:
    """
    Calculate the minimum area rectangle that encloses the vertices.

    Args:
        vertices: List of vertices from a bounding polygon
        logger: Logger instance

    Returns:
        MinAreaRect object containing rect properties or None if calculation fails
    """
    try:
        # Convert vertices to points for OpenCV
        points = vertices_to_points(vertices)
        if points.size == 0:
            logger.warning("No valid points found for minimum area rectangle calculation")
            return None

        # Calculate minimum area rectangle
        rect = cv2.minAreaRect(points)

        # Extract properties from the rect
        center_tuple, (width, height), angle = rect

        # Create Point2D for center
        center = Point2D(x=float(center_tuple[0]), y=float(center_tuple[1]))

        # Normalize the angle to be between -90 and 0 degrees
        # By OpenCV convention, the angle is in the range [-90, 0)
        # This means 0 degrees represents a horizontal rectangle,
        # and -90 degrees represents a vertical rectangle

        # Ensure the width is always the longer side
        # This simplifies our interpretation of the angle
        if width < height:
            width, height = height, width
            angle -= 90

        # Adjust angle to be between -90 and 0
        while angle < -90:
            angle += 180
        while angle > 0:
            angle -= 180

        return MinAreaRect(center=center, width=float(width), height=float(height), angle=float(angle))

    except Exception as e:
        logger.warning(f"Error calculating minimum area rectangle: {str(e)}")
        return None


def calculate_text_orientation(vertices: Vertices, logger: Logger = NO_LOGGER) -> float:
    """
    Calculate the orientation angle of text based on its bounding polygon using OpenCV's minAreaRect.

    Args:
        vertices: List of vertices from a bounding polygon
        logger: Logger instance

    Returns:
        Angle in degrees (0 means perfectly horizontal, 90 means perfectly vertical)
    """
    rect = calculate_min_area_rect(vertices, logger)
    if not rect:
        logger.warning("Could not calculate minimum area rectangle, defaulting to 0 degrees")
        return 0.0

    # The angle from minAreaRect is between -90 and 0
    # For our purposes, convert to a 0-90 range where:
    # 0 degrees = horizontal text
    # 90 degrees = vertical text
    angle = abs(rect.angle)

    logger.info(f"Text orientation: {angle:.1f} degrees from horizontal")
    return angle


def calculate_true_height(vertices: Vertices, logger: Logger = NO_LOGGER) -> float:
    """
    Calculate true height of text perpendicular to its baseline using OpenCV's minAreaRect.

    Args:
        vertices: List of vertices from a bounding polygon
        logger: Logger instance

    Returns:
        True height perpendicular to text baseline
    """
    rect = calculate_min_area_rect(vertices, logger)
    if not rect:
        logger.warning("Could not calculate minimum area rectangle, defaulting to 0 height")
        return 0.0

    # The height is always the shorter dimension of the rectangle
    # regardless of the text orientation
    height = rect.height

    # logger.info(f"True text height: {height:.1f} pixels")
    return height


def calculate_iou(poly1: Vertices, poly2: Vertices) -> float:
    """
    Calculate Intersection over Union (IoU) for two bounding polygons.
    For simplicity, we convert the polygons to rectangles and then calculate IoU.

    Args:
        poly1: First polygon vertices
        poly2: Second polygon vertices

    Returns:
        IoU value between 0 and 1
    """

    # Convert polygons to axis-aligned rectangles
    def get_rect_from_poly(poly: Vertices) -> List[float]:
        x_coords, y_coords = extract_coordinates(poly)
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        return [x_min, y_min, x_max, y_max]

    rect1 = get_rect_from_poly(poly1)
    rect2 = get_rect_from_poly(poly2)

    # Calculate intersection area
    x_left = max(rect1[0], rect2[0])
    y_top = max(rect1[1], rect2[1])
    x_right = min(rect1[2], rect2[2])
    y_bottom = min(rect1[3], rect2[3])

    # Check if there's an actual intersection
    if x_right < x_left or y_bottom < y_top:
        return 0.0

    intersection_area = (x_right - x_left) * (y_bottom - y_top)

    # Calculate union area
    rect1_area = (rect1[2] - rect1[0]) * (rect1[3] - rect1[1])
    rect2_area = (rect2[2] - rect2[0]) * (rect2[3] - rect2[1])
    union_area = rect1_area + rect2_area - intersection_area

    # Calculate IoU
    if union_area <= 0:
        return 0.0
    return intersection_area / union_area


def calculate_aspect_ratio(vertices: Vertices, logger: Logger = NO_LOGGER) -> float:
    """
    Calculate the aspect ratio (width/height) of a bounding box using minAreaRect.

    Args:
        vertices: List of vertices from a bounding polygon
        logger: Logger instance

    Returns:
        Aspect ratio (width/height) of the bounding box
    """
    rect = calculate_min_area_rect(vertices, logger)
    if not rect:
        logger.warning("Could not calculate minimum area rectangle, defaulting to aspect ratio of 1.0")
        return 1.0

    # The width is always the longer dimension by our convention
    # and height is the shorter dimension
    width = rect.width
    height = rect.height

    # Avoid division by zero
    if height == 0:
        logger.warning("Zero height detected in aspect ratio calculation")
        return float("inf")

    aspect_ratio = width / height
    logger.info(f"Aspect ratio: {aspect_ratio:.2f}")
    return aspect_ratio


def expand_bounding_box(vertices: Vertices, height_value: float, logger: Logger, use_equal_expansion: bool) -> Vertices:
    """
    Expand a bounding box taking text orientation into account.

    Args:
        vertices: List of vertices from a bounding polygon
        height_value: Height value to use for expansion calculations
        logger: Logger instance
        use_equal_expansion: If True, apply equal expansion on all sides.
                             If False, apply directional expansion (more on left)

    Returns:
        New vertices of the expanded bounding box
    """
    if not vertices or len(vertices) < 4:
        logger.warning("Not enough vertices to expand bounding box")
        return vertices

    try:
        # Get the minimum area rectangle information
        rect = calculate_min_area_rect(vertices, logger)
        if not rect:
            logger.warning("Could not calculate minimum area rectangle, returning original vertices")
            return vertices

        # Get center, dimensions, and angle from the rectangle
        center_x = rect.center.x
        center_y = rect.center.y
        width = rect.width
        height = rect.height
        angle_deg = rect.angle
        angle_rad = math.radians(angle_deg)

        # Calculate the direction vector for "left" side expansion
        # For text, the main orientation is width-wise, and "left" depends on text direction
        # For horizontal text (~0 degrees): left = -x direction
        # For vertical text (~-90 degrees): left = +y direction

        # Convert angle to be in 0-360 range for easier logic
        normalized_angle = angle_deg % 360
        if normalized_angle < 0:
            normalized_angle += 360

        # Determine if we should invert left/right direction based on angle
        # Note: In OpenCV, angle is measured counter-clockwise
        # For angles near 0 or 180: left = -x | For angles near 90 or 270: left = ±y
        invert_direction = False
        if 90 <= normalized_angle < 270:
            invert_direction = True

        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)

        # Calculate half-dimensions of the original rectangle
        half_width = width / 2
        half_height = height / 2

        # Set expansion values based on expansion type
        if use_equal_expansion:
            # Equal expansion of 1.5x height in all directions
            expansion_left = height_value
            expansion_right = height_value
            expansion_top = height_value
            expansion_bottom = height_value
        else:
            # Standard directional expansion (more on left/right)
            expansion_left = height_value * 2.25
            expansion_right = height_value * 2.25
            expansion_top = height_value * 0.75
            expansion_bottom = height_value * 0.75

        # Swap left/right if we're in an inverted direction quadrant
        if invert_direction:
            expansion_left, expansion_right = expansion_right, expansion_left

        # Calculate new half-dimensions with the expansion
        new_half_width_left = half_width + expansion_left
        new_half_width_right = half_width + expansion_right
        new_half_height_top = half_height + expansion_top
        new_half_height_bottom = half_height + expansion_bottom

        # Calculate the corners of the expanded rectangle in the rotated coordinate system
        # then rotate them back to the original coordinate system
        # Calculate each point's coordinates separately to improve type safety
        top_left_x = center_x + (-new_half_width_left * cos_a + new_half_height_top * sin_a)
        top_left_y = center_y + (-new_half_width_left * sin_a - new_half_height_top * cos_a)

        top_right_x = center_x + (new_half_width_right * cos_a + new_half_height_top * sin_a)
        top_right_y = center_y + (new_half_width_right * sin_a - new_half_height_top * cos_a)

        bottom_right_x = center_x + (new_half_width_right * cos_a - new_half_height_bottom * sin_a)
        bottom_right_y = center_y + (new_half_width_right * sin_a + new_half_height_bottom * cos_a)

        bottom_left_x = center_x + (-new_half_width_left * cos_a - new_half_height_bottom * sin_a)
        bottom_left_y = center_y + (-new_half_width_left * sin_a + new_half_height_bottom * cos_a)

        expanded_corners: Vertices = [
            # Top-left
            {"x": int(top_left_x), "y": int(top_left_y)},
            # Top-right
            {"x": int(top_right_x), "y": int(top_right_y)},
            # Bottom-right
            {"x": int(bottom_right_x), "y": int(bottom_right_y)},
            # Bottom-left
            {"x": int(bottom_left_x), "y": int(bottom_left_y)},
        ]

        return expanded_corners

    except Exception as e:
        logger.warning(f"Error expanding bounding box: {str(e)}")
        return vertices  # Return original vertices on error
