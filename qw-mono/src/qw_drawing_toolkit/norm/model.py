from enum import Enum
from typing import List, <PERSON><PERSON>

from pydantic import BaseModel, ConfigDict, model_validator
from pydantic.alias_generators import to_camel
from typing_extensions import Self

from qw_log_interface import NO_LOGGER, Logger


class ToleranceUnit(str, Enum):
    MICROMETER = "micrometer"
    MILLIMETER = "mm"
    DEGREE = "deg"


class NormDatabaseBaseModel(BaseModel):
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)


class Tolerance(NormDatabaseBaseModel):
    upper: float
    lower: float

    @model_validator(mode="after")
    def ensure_that_upper_is_larger_than_lower(self) -> Self:
        if self.upper < self.lower:
            raise ValueError(f"Upper needs to be larger than lower tolerance, {self.upper} < {self.lower}")
        return self

    @classmethod
    def unary(cls, value: float) -> "Tolerance":
        return cls(upper=value, lower=-value)


class ToleranceValueType(str, Enum):
    LINEAR = "linear"
    ANGULAR = "angular"
    CHAMFER = "chamfer"
    STRAIGHTNESS = "straightness"
    SYMMETRY = "symmetry"
    PERPENDICULARITY = "perpendicularity"
    RUNOUT = "runout"


class ValueRangeLimit(NormDatabaseBaseModel):
    value: float
    inclusive: bool

    def value_is_below_limit(self, value: float) -> bool:
        if self.inclusive:
            return value <= self.value
        return value < self.value


class ValueToToleranceRange(NormDatabaseBaseModel):
    lower_value_limit: ValueRangeLimit | None
    upper_value_limit: ValueRangeLimit | None
    row_identifiers: List[Tuple[str, ...]]
    tolerance: Tolerance

    def value_is_in_range(self, value: float) -> bool:
        checks = []
        if self.lower_value_limit is not None:
            checks.append(not self.lower_value_limit.value_is_below_limit(value))
        if self.upper_value_limit is not None:
            checks.append(self.upper_value_limit.value_is_below_limit(value))
        return all(checks)

    def is_finite(self) -> bool:
        return self.lower_value_limit is not None and self.upper_value_limit is not None


class ValueToToleranceTable(NormDatabaseBaseModel):
    name: str
    value_type: ToleranceValueType
    value_unit: ToleranceUnit
    tolerance_unit: ToleranceUnit
    ranges: List[ValueToToleranceRange]

    def lookup_range(
        self, row_identifier: Tuple[str, ...], value: float, logger: Logger = NO_LOGGER
    ) -> ValueToToleranceRange | None:
        ranges = [r for r in self.ranges if row_identifier in r.row_identifiers]

        # finite ranges first have priority
        finite_ranges = [r for r in ranges if r.value_is_in_range(value)]
        if len(finite_ranges) > 0:
            if len(finite_ranges) > 1:
                logger.error(f"Matched {len(finite_ranges)} ranges for {value}")
            return finite_ranges[0]

        infinite_ranges = [r for r in ranges if r.value_is_in_range(value)]
        if len(infinite_ranges) > 0:
            if len(infinite_ranges) > 1:
                logger.error(f"Matched {len(infinite_ranges)} ranges for {value}")
            return infinite_ranges[0]

        return None


class ToleranceStandard(NormDatabaseBaseModel):
    name: str
    tables: List[ValueToToleranceTable]
