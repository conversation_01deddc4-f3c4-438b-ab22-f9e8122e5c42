import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  NgT<PERSON>plateOutlet,
} from "@angular/common"
import { Component, computed, signal } from "@angular/core"
import { takeUntilDestroyed } from "@angular/core/rxjs-interop"
import { FormsModule } from "@angular/forms"
import { ActivatedRoute, Router } from "@angular/router"
import { FaIconComponent } from "@fortawesome/angular-fontawesome"
import {
  faBoltLightning,
  faBook,
  faChevronDown,
  faDrawPolygon,
  faImage,
  faRulerCombined,
  faVideo,
  faXmark,
} from "@fortawesome/free-solid-svg-icons"
import { AppRoutes } from "@qw-core/constants/app-routes"
import { ScreenSizeService } from "@qw-core/services/screen-size.service"
import { DIALOG_MESSAGES } from "@qw-core/ui/dialog-temp/app-messages"
import { QwSwipeDirective } from "@qw-shared-directives/qw-swipe/qw-swipe.directive"
import { QwCapitalizeAllWordsPipe } from "@qw-shared-pipes/qw-capitalize-all-words/qw-capitalize-all-words.pipe"
import { QwUnderscoreToWordsPipe } from "@qw-shared-pipes/qw-underscore-to-words/qw-underscore-to-words.pipe"
import {
  DrawingResourceState,
  DrawingResourceViewMode,
  DrawingViewerStateService,
  InspectionPlanState,
  PlanActions,
} from "@qw-shared-services/drawing-resource-state.service"
import { FileResourcesDataService } from "@qw-shared-services/file-resources-data.service"
import { InspectionTaskDataService } from "@qw-shared-services/inspection-task-data.service"
import { PolStateService } from "@qw-shared-services/pol-state.service"
import { UserInterfaceService } from "@qw-shared-services/user-interface.service"
import { WindowSelectionService } from "@qw-shared-services/window-selection.service"
import { BtnClassicComponent } from "@qw-shared-ui/btn-classic/btn-classic.component"
import { BtnDropdownComponent } from "@qw-shared-ui/btn-dropdown/btn-dropdown.component"
import { DropdownComponent } from "@qw-shared-ui/dropdown/dropdown.component"
import { ActionDimensionEditorComponent } from "@qw-shared-ui/viewer-file-resource/viewer-drawing/drawing-page/action-dimension-editor/action-dimension-editor.component"
import { ActionInstructionEditorComponent } from "@qw-shared-ui/viewer-file-resource/viewer-drawing/drawing-page/action-instruction-editor/action-instruction-editor.component"
import { ViewerDrawingComponent } from "@qw-shared-ui/viewer-file-resource/viewer-drawing/viewer-drawing.component"
import { ActionFlags } from "@qw-shared-utils/qw-set-action-flags"
import {
  catchError,
  combineLatest,
  filter,
  map,
  Observable,
  of,
  shareReplay,
  switchMap,
  take,
  tap,
} from "rxjs"

import { InspectionPlanStepViewerComponent } from "../inspection-plan-step-viewer/inspection-plan-step-viewer.component"

import {
  QwFileResourceView,
  QwInspectionPlanActionImage,
  QwInspectionPlanActionType,
  QwInspectionPlanActionVideo,
  QwInspectionPlanStep,
  QwNewInspectionPlanOutput,
  QwOrderType,
} from "@qw-api/models"

import { QwCapitalizeFirstWordPipe } from "@qw-webui/_shared/pipes/qw-capitalize-first-word/qw-capitalize-first-word.pipe"

interface StepEditingState {
  isEditing: boolean
  stepIndex: number | null
  isSelectingNewCrop: boolean
}

@Component({
  selector: "qw-inspection-plan-create",
  templateUrl: "./inspection-plan-create.component.html",
  standalone: true,
  providers: [
    QwCapitalizeFirstWordPipe,
    QwUnderscoreToWordsPipe,
    QwCapitalizeAllWordsPipe,
  ],
  imports: [
    NgIf,
    NgTemplateOutlet,
    BtnClassicComponent,
    BtnDropdownComponent,
    FormsModule,
    NgFor,
    InspectionPlanStepViewerComponent,
    DropdownComponent,
    NgClass,
    FaIconComponent,
    ActionInstructionEditorComponent,
    ActionDimensionEditorComponent,
    ViewerDrawingComponent,
    QwSwipeDirective,
    AsyncPipe,
  ],
})
export class InspectionPlanCreateComponent {
  // Initialization ============================================================
  constructor(
    private capitalizeFirstWordPipe: QwCapitalizeFirstWordPipe,
    private fileResourcesDataService: FileResourcesDataService,
    private fileResourcesViewerStateService: DrawingViewerStateService,
    private inspectionTaskDataService: InspectionTaskDataService,
    private polStateService: PolStateService,
    private route: ActivatedRoute,
    private router: Router,
    private screenSizeService: ScreenSizeService,
    private userInterfaceService: UserInterfaceService,
    private windowSelectionService: WindowSelectionService,
  ) {
    this.initState$.subscribe()
    this.loadAvailableInspectionPlanTemplates()
  }

  // Static References =========================================================
  protected readonly QwOrderType = QwOrderType
  // Bindings ==================================================================
  faVideo = faVideo
  faImage = faImage
  faBook = faBook
  faRulerCombined = faRulerCombined
  faXmark = faXmark
  faDrawPolygon = faDrawPolygon
  faBoltLightning = faBoltLightning
  faChevronDown = faChevronDown
  // Data Streams and State ====================================================
  stepEditingState = signal<StepEditingState>({
    isEditing: false,
    stepIndex: null,
    isSelectingNewCrop: false,
  })

  // Template dropdown state
  availableInspectionPlanTemplates = signal<
    Array<{ id: number; title: string }>
  >([])

  // Load available templates - will be initialized after polState is available
  private loadAvailableInspectionPlanTemplates(): void {
    this.polStateService
      .getPolState()
      .pipe(
        filter((polState) => !!polState.purchaseOrderLine),
        switchMap((polState) => {
          const currentOrderLineId = polState.purchaseOrderLine.id
          const materialId = polState.purchaseOrderLine.materialId
          return this.inspectionTaskDataService.listInspectionPlansAsTemplates(
            currentOrderLineId,
            materialId,
          )
        }),
        tap((templates) => {
          this.availableInspectionPlanTemplates.set(
            templates.map((template) => ({
              id: template.id,
              title: template.title,
            })),
          )
        }),
        takeUntilDestroyed(),
      )
      .subscribe()
  }

  queryParams = computed(() => {
    const params = this.route.snapshot.queryParamMap

    // Get polId
    const polIdParam = params.get("polId")
    if (!polIdParam) {
      throw new Error("Order ID required")
    }
    const polId = parseInt(polIdParam, 10)
    if (isNaN(polId)) {
      throw new Error("Invalid Purchase Order Line ID format")
    }

    // Get editPlanId
    const editPlanIdParam = params.get("editPlanId")
    const editPlanId = editPlanIdParam ? parseInt(editPlanIdParam, 10) : null
    if (editPlanId !== null && isNaN(editPlanId)) {
      throw new Error("Invalid Edit Plan ID format")
    }

    // Get if the plan is shared or private
    const sharedParam = params.get("shared")
    const shared = sharedParam !== null ? sharedParam === "true" : undefined

    return { polId, editPlanId, shared }
  })

  protected drawingDataInit$: Observable<QwFileResourceView[]> =
    this.polStateService.getPolState().pipe(
      switchMap((polState) =>
        this.fileResourcesDataService.getTechnicalDrawingsByOrderLineAndMaterialId(
          polState.purchaseOrderLine.id,
          polState.purchaseOrderLine.materialId,
        ),
      ),
      filter(
        (fileResources): fileResources is QwFileResourceView[] =>
          !!fileResources && fileResources.length > 0,
      ),
      catchError((error) => {
        logger.error("Error in drawing data processing:", error)
        return of([])
      }),
    )

  private isUpdatingExistingPlan$ = of(this.queryParams().editPlanId).pipe(
    switchMap((editPlanId) =>
      editPlanId
        ? this.inspectionTaskDataService
            .getInspectionPlanDetail(editPlanId)
            .pipe(
              catchError((error) => {
                logger.error("Error fetching plan details:", error)
                return of(null)
              }),
            )
        : of(null),
    ),
  )

  private initState$ = combineLatest([
    this.drawingDataInit$,
    this.isUpdatingExistingPlan$,
  ]).pipe(
    take(1), // Initialize state once
    tap(([technicalDrawingFileResource, planDetails]) => {
      if (technicalDrawingFileResource.length > 0) {
        const selectedRevisionId = technicalDrawingFileResource[0].id
        const dimModeState = this.getDimModeState()

        // Setup state without analysis data first
        const baseState = {
          ...dimModeState,
          inspectionPlanState: {
            ...dimModeState.inspectionPlanState,
            orderId: this.queryParams().polId,
            technicalDrawingFileResource,
            selectedRevisionId,
            // currently shared status cannot be overwritten with updateInspectionPlan endpoint
            // thus, it's hidden from the UI when updating existing plans
            // when this is possible show this option again in the template
            shareWithTenant:
              this.queryParams().shared !== undefined
                ? this.queryParams().shared
                : true,
            editTimestamp: planDetails?.editTimestamp,
            ...(planDetails && {
              inspectionPlanConfig: {
                title: planDetails.inspectionPlan.title,
                steps: planDetails.inspectionPlan.steps,
              },
            }),
          },
        }

        // Update state without analysis data
        this.fileResourcesViewerStateService.updateState(baseState)
      }
    }),
  )

  vm$ = combineLatest({
    fileResourceState: this.fileResourcesViewerStateService.getState(),
    polState: this.polStateService.getPolState(),
    isPortrait: this.screenSizeService.isPortrait$,
  }).pipe(
    map(({ fileResourceState, polState, isPortrait }) => {
      const steps =
        fileResourceState.inspectionPlanState.inspectionPlanConfig?.steps ?? []
      const totalSteps = steps.length

      // Process steps - preserve original index for step numbering but reverse display order
      const processedSteps = [...steps]
        .reverse()
        .map((step, reversedIndex) => ({
          step,
          stepIndex: totalSteps - 1 - reversedIndex, // This gives us 0,1,2... numbering while still showing newest first
          canMoveForward: reversedIndex < totalSteps - 1 && totalSteps > 1,
          canMoveBackward: reversedIndex > 0 && totalSteps > 1,
        }))

      return {
        fileResourceState,
        polState,
        isPortrait,
        processedSteps,
      }
    }),
    shareReplay(1),
  )

  // Actions ===================================================================
  selectDrawingResourceFromDropdown(
    drawingFiles: QwFileResourceView[],
    selectedOption: any,
  ) {
    const selectedDrawing = drawingFiles.find((d) => d.id === selectedOption.id)
    if (selectedDrawing) {
      this.fileResourcesViewerStateService.updateState({
        inspectionPlanState: {
          selectedRevisionId: selectedDrawing.revisions[0].id,
          selectedTechnicalDrawingResource: selectedDrawing,
        },
      })
    }
  }

  toggleMode(mode: string): void {
    switch (mode) {
      case "dim":
        const dimModeState = this.getDimModeState()
        this.fileResourcesViewerStateService.updateState(dimModeState)
        break
      case "custom":
        const customModeState = this.getCustomModeState()
        this.fileResourcesViewerStateService.updateState(customModeState)
        break
      default:
        this.fileResourcesViewerStateService.updateStateForDefaultMode()
        break
    }
  }

  toggleAction(ds: DrawingResourceState, action: keyof PlanActions) {
    const isSelecting = !ds.action[action]

    const updatedActionState = {
      ...ds.action,
      [action]: isSelecting,
    }

    // =========================================================================
    // Control if drawing should allow selection or just appear for convenience in custom mode
    // Selection not allowed if only instruction step is selected or no action is selected
    // Selected is allowed for dim, photo, and video actions
    const isOnlyInstructionSelected = Object.entries(updatedActionState).every(
      ([key, value]) => (key === "instruction" ? value : !value),
    )

    const noneSelected = Object.values(updatedActionState).every(
      (value) => !value,
    )

    const canSelectFreeQuad =
      !isOnlyInstructionSelected && !noneSelected && ds.mode === "custom"

    if (!canSelectFreeQuad) {
      this.fileResourcesViewerStateService.resetQuadInCurrentStep()
      this.windowSelectionService.resetSelectionState()
    }

    // Update the inspectionPlanConfigDraft
    let updatedActions = [...ds.inspectionPlanConfigDraft.actions]

    if (isSelecting) {
      // Add the corresponding action if it's being selected
      switch (action) {
        case "video":
          updatedActions.push(this.createMediaAction("video"))
          break
        case "photo":
          updatedActions.push(this.createMediaAction("photo"))
          break
      }
    } else {
      // Remove the corresponding action if it's being deselected
      switch (action) {
        case "dimension":
          updatedActions = updatedActions.filter(
            (a) => a.type !== QwInspectionPlanActionType.Measurement,
          )
          break
        case "video":
          updatedActions = updatedActions.filter(
            (a) => a.type !== QwInspectionPlanActionType.Video,
          )
          break
        case "photo":
          updatedActions = updatedActions.filter(
            (a) => a.type !== QwInspectionPlanActionType.Image,
          )
          break
        case "instruction":
          updatedActions = updatedActions.filter(
            (a) => a.type !== QwInspectionPlanActionType.QuestionAnswer,
          )
          break
      }
    }

    const stateUpdates: Partial<DrawingResourceState> = {
      action: updatedActionState,
      drawingPageOptions: {
        ...ds.drawingPageOptions,
        canSelectFreeQuad,
      },
      inspectionPlanConfigDraft: {
        ...ds.inspectionPlanConfigDraft,
        actions: updatedActions,
      },
    }

    this.fileResourcesViewerStateService.updateState(stateUpdates)
  }

  // Step edit mode
  selectNewCropInEditMode() {
    this.fileResourcesViewerStateService.resetQuadInCurrentStep()
    this.stepEditingState.update((current) => ({
      ...current,
      isSelectingNewCrop: true,
    }))
  }

  handleStepEdit(
    event: {
      step: QwInspectionPlanStep
      stepIndex: number
      flags: ActionFlags
      mode: DrawingResourceViewMode
    },
    state: DrawingResourceState,
  ) {
    const { step, stepIndex, flags, mode } = event

    // Set edit mode
    this.stepEditingState.set({
      isEditing: true,
      stepIndex: stepIndex,
      isSelectingNewCrop: false,
    })

    const newState: Partial<DrawingResourceState> = {
      mode: mode,
      action: {
        dimension: flags.hasMeasurement,
        video: flags.hasVideo,
        photo: flags.hasImage,
        instruction: flags.hasQuestionAnswer,
      },
      inspectionPlanConfigDraft: {
        ...step,
      },
      inspectionPlanState: {
        ...state.inspectionPlanState,
        selectedRevisionId: step.drawingQuad?.drawingRevisionId ?? null,
      },
      drawingPageOptions: {
        canSelectFreeQuad: true,
        hideQuads: false,
        canZoom: true,
      },
      // edit mode starts as valid by default because the step is already saved
      isDimensionActionValid: true,
      isInstructionActionValid: true,
      isMediaActionValid: true,
    }

    this.fileResourcesViewerStateService.updateState(newState)
  }

  moveInspectionPlanStep(
    state: Partial<InspectionPlanState>,
    stepIndex: number,
    newStepIndex: number | null,
  ) {
    const steps = state.inspectionPlanConfig?.steps
    if (!steps) {
      return
    }

    const newSteps = [...steps]
    const [step] = newSteps.splice(stepIndex, 1) // Remove the step at stepIndex

    if (newStepIndex !== null) {
      newSteps.splice(newStepIndex, 0, step) // Insert the step at newStepIndex
    }

    this.fileResourcesViewerStateService.updateState({
      inspectionPlanState: {
        ...state,
        inspectionPlanConfig: {
          ...state.inspectionPlanConfig,
          steps: newSteps,
        },
      },
    })

    this.resetEditingState()
  }

  onPlanTitleChange(ds: DrawingResourceState, event: Event) {
    const target = event.target as HTMLInputElement
    const newTitle = target.value.trim()

    this.fileResourcesViewerStateService.updateState({
      inspectionPlanState: {
        ...ds.inspectionPlanState,
        inspectionPlanConfig: {
          ...ds.inspectionPlanState.inspectionPlanConfig,
          title: newTitle,
        },
      },
    })
  }

  onshareWithTenantChange(ds: DrawingResourceState, event: Event): void {
    const checked = (event.target as HTMLInputElement).checked
    this.fileResourcesViewerStateService.updateState({
      inspectionPlanState: {
        ...ds.inspectionPlanState,
        shareWithTenant: checked,
      },
    })
  }

  // add all finished and unfinished steps together before sending as final
  confirmStep(ds: DrawingResourceState) {
    const draftStep: QwInspectionPlanStep = {
      repeatForEachSample: true,
      drawingQuad: ds.inspectionPlanConfigDraft.drawingQuad,
      actions: ds.inspectionPlanConfigDraft.actions,
    }

    // If we're editing an existing step, update it; otherwise, add a new step
    const stepEditingState = this.stepEditingState()
    let newSteps: QwInspectionPlanStep[]

    if (stepEditingState.isEditing && stepEditingState.stepIndex !== null) {
      newSteps = [...(ds.inspectionPlanState.inspectionPlanConfig?.steps ?? [])]
      newSteps[stepEditingState.stepIndex] = draftStep
    } else {
      newSteps = ds.inspectionPlanState.inspectionPlanConfig?.steps
        ? [...ds.inspectionPlanState.inspectionPlanConfig.steps, draftStep]
        : [draftStep]
    }

    // Prepare the new state
    const newState: Partial<DrawingResourceState> = {
      ...this.getDimModeState(),
      inspectionPlanState: {
        ...ds.inspectionPlanState,
        inspectionPlanConfig: {
          ...ds.inspectionPlanState.inspectionPlanConfig,
          steps: newSteps,
        },
      },
    }
    this.fileResourcesViewerStateService.updateState(newState)
    this.windowSelectionService.resetSelectionState()
    this.resetEditingState()
  }

  resetEditingState() {
    this.stepEditingState.set({
      isEditing: false,
      stepIndex: null,
      isSelectingNewCrop: false,
    })
  }

  submitInspectionPlan(state: Partial<InspectionPlanState>) {
    if (
      state.orderId &&
      state.inspectionPlanConfig?.title &&
      state.inspectionPlanConfig.steps?.length
    ) {
      const polId = state.orderId
      const plan = {
        title: this.capitalizeFirstWordPipe.transform(
          state.inspectionPlanConfig.title,
        ),
        steps: state.inspectionPlanConfig.steps,
        version: "1.0", // TODO: Implement versioning
      }

      const data = {
        orderLineId: polId,
        inspectionPlan: plan,
        shareWithOtherTenant: state.shareWithTenant, // Plans can be internal/private too
      }

      const params = this.queryParams()
      if (params.editPlanId && state.editTimestamp) {
        this.inspectionTaskDataService
          .updateInspectionPlanFE(params.editPlanId, state.editTimestamp, data)
          .subscribe({
            next: () => {
              this.router.navigateByUrl(
                `${AppRoutes.fullPath("polInspections", polId)}?planId=${
                  params.editPlanId
                }`,
              )
            },
            error: (error: Error) => {
              logger.error("Error updating inspection plan:", error)
            },
          })
      } else {
        this.inspectionTaskDataService.addInspectionPlanFE(data).subscribe({
          next: (response: QwNewInspectionPlanOutput) => {
            this.router.navigateByUrl(
              `${AppRoutes.fullPath("polInspections", polId)}?planId=${
                response.inspectionPlanId
              }`,
            )
          },
          error: (error: Error) => {
            logger.error("Error creating inspection plan:", error)
          },
        })
      }
    }
  }

  // Currently doesn't beg for a new component
  private createMediaAction(
    type: "video" | "photo",
  ): QwInspectionPlanActionVideo | QwInspectionPlanActionImage {
    if (type === "video") {
      return {
        type: QwInspectionPlanActionType.Video,
        optional: false,
        instruction: null,
      }
    } else {
      return {
        type: QwInspectionPlanActionType.Image,
        optional: false,
        instruction: null,
      }
    }
  }

  // Template states for different modes
  private getCustomModeState(): Partial<DrawingResourceState> {
    return {
      mode: "custom",
      drawingPageOptions: {
        canSelectFreeQuad: false,
        hideQuads: false,
        canZoom: true,
      },
      action: {
        dimension: false,
        video: false,
        photo: false,
        instruction: false,
      },
      inspectionPlanConfigDraft: {
        actions: [],
        drawingQuad: null,
        repeatForEachSample: false,
      },
      // make sure to reset valid checks
      isDimensionActionValid: false,
      isInstructionActionValid: false,
      isMediaActionValid: false,
    }
  }

  private getDimModeState(): Partial<DrawingResourceState> {
    return {
      mode: "dim",
      drawingPageOptions: {
        canSelectFreeQuad: false,
        hideQuads: false,
        canZoom: true,
      },
      action: {
        video: false,
        photo: false,
        instruction: false,
      },
      inspectionPlanConfigDraft: {
        actions: [],
        drawingQuad: null,
        repeatForEachSample: false,
      },
    }
  }

  ngOnDestroy(): void {
    this.fileResourcesViewerStateService.resetState()
  }

  // UI/View ===================================================================
  // Hint user on what type of actions are expected based on the current mode
  // showActionSummaryToUser(ds: DrawingResourceState): string {
  //   const actions = []
  //   if (ds.action.dimension) {
  //     actions.push("measurement")
  //   }
  //   if (ds.action.video) {
  //     actions.push("video")
  //   }
  //   if (ds.action.photo) {
  //     actions.push("photo")
  //   }
  //   // QW-127 needs to be fixed
  //   if (ds.action.instruction && ds.inspectionPlanConfigDraft.actions) {
  //     const instructionAction = ds.inspectionPlanConfigDraft.actions.find(
  //       (action) => action.type === QwInspectionPlanActionType.QuestionAnswer,
  //     ) as QwInspectionPlanActionQuestionAnswer | undefined

  //     if (instructionAction && !instructionAction.optional) {
  //       actions.push("answer")
  //     }
  //     // If it's optional, we don't add anything to the actions array
  //   }
  //   if (ds.mode === "dim") {
  //     return actions.length > 0
  //       ? `Additionally expecting: ${actions.join(", ")}`
  //       : ""
  //   } else if (ds.mode === "custom" && actions.length === 0) {
  //     return "At least one option must be selected."
  //   } else {
  //     return actions.length > 0 ? `Expecting: ${actions.join(", ")}` : ""
  //   }
  // }

  showDimensionRequiredMessage(): void {
    this.userInterfaceService.showPopup(DIALOG_MESSAGES.CANNOT_UNCHECK_REQUIRED)
  }

  // Navigation
  trackByStepIndex(
    _index: number,
    item: { step: QwInspectionPlanStep; stepIndex: number },
  ): number {
    return item.stepIndex
  }

  shouldShowAreaNotSelectedError = computed(
    () => (drawingResourceState: DrawingResourceState) => {
      const { mode, action, isMediaActionValid } = drawingResourceState
      const editingState = this.stepEditingState()

      // In editing mode, show error if media action is not valid
      if (editingState.isEditing) {
        return !isMediaActionValid
      }

      // Only check in custom mode
      if (mode === "custom") {
        // No action selected - never show error
        const noActionSelected =
          !action.instruction &&
          !action.dimension &&
          !action.photo &&
          !action.video
        if (noActionSelected) {
          logger.log("No action selected")
          return false
        }

        // Only instruction selected - never show error
        const onlyInstructionSelected =
          action.instruction &&
          !action.dimension &&
          !action.photo &&
          !action.video
        if (onlyInstructionSelected) {
          return false
        }

        // For all other cases (non-instruction actions or mixed actions),
        // show error if media action is not valid
        return !isMediaActionValid
      }

      return false
    },
  )

  // Checks if custom step is submittable
  // Only on custom mode. Auto mode doesn't need a confirm button.
  isStepComplete(ds: DrawingResourceState): boolean {
    let isDimensionComplete = true
    let isInstructionComplete = true
    let isMediaComplete = true

    if (
      !ds.action.dimension &&
      !ds.action.video &&
      !ds.action.photo &&
      !ds.action.instruction
    ) {
      return false
    }

    // Check if a dimension is selected
    if (ds.action.dimension) {
      isDimensionComplete = ds.isDimensionActionValid
    }

    // Check if an instruction is selected
    if (ds.action.instruction) {
      isInstructionComplete = ds.isInstructionActionValid
    }

    // Check if a crop area is selected
    if (ds.action.video || ds.action.photo || ds.action.dimension) {
      isMediaComplete = ds.isMediaActionValid
    }

    // Return true if all required conditions are met
    return isInstructionComplete && isMediaComplete && isDimensionComplete
  }

  // Check if the inspection plan is submittable
  isDraftPlanComplete(state: Partial<InspectionPlanState>): boolean {
    if (!state.inspectionPlanConfig) {
      return false
    }

    const hasTitle = state.inspectionPlanConfig.title !== ""
    const hasSteps = (state.inspectionPlanConfig.steps?.length ?? 0) > 0

    return hasTitle && hasSteps
  }

  // Template dropdown actions
  useInspectionPlanTemplate(selectedTemplate: {
    id: number
    title: string
  }): void {
    // Fetch the template details and apply them
    this.inspectionTaskDataService
      .getInspectionPlanDetail(selectedTemplate.id)
      .subscribe({
        next: (templateDetails) => {
          if (templateDetails) {
            // Update the current plan with template data
            this.fileResourcesViewerStateService.updateState({
              inspectionPlanState: {
                inspectionPlanConfig: {
                  title: `${templateDetails.inspectionPlan.title} (Copy)`,
                  steps: templateDetails.inspectionPlan.steps,
                },
              },
            })

            this.userInterfaceService.showPopup(
              $localize`Template applied successfully`,
            )
          }
        },
        error: (error) => {
          logger.error("Error loading template:", error)
          this.userInterfaceService.showPopup($localize`Error loading template`)
        },
      })
  }

  // UI/View ===================================================================
  readonly localizedUpdateOrCreateButton = computed(
    () =>
      (planEditMode: boolean): string => {
        if (planEditMode) {
          return $localize`Update`
        }
        return $localize`Create`
      },
  )

  readonly localizedShareInfo = computed(
    () =>
      (isSupplier: boolean): string => {
        if (isSupplier) {
          return $localize`Share with my customer`
        }
        return $localize`Share with my supplier`
      },
  )

  readonly localizedUpdateOrAddStepButtonText = computed(
    () =>
      (isEditing: boolean, editingStepIndex: number | null): string => {
        if (isEditing && editingStepIndex !== null) {
          return $localize`Update Step` + ` ${editingStepIndex + 1}`
        }
        return $localize`Add Step`
      },
  )
}
