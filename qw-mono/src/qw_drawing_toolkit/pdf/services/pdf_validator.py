import io
from enum import Enum, unique
from pathlib import Path
from typing import List, Tuple

import pymupdf

from qw_log_interface import NO_LOGGER, Logger


@unique
class DrawingPdfValidationErrorCode(str, Enum):
    COULD_NOT_OPEN = "COULD_NOT_OPEN"


class DrawingPdfValidationError(Exception):
    def __init__(self, code: DrawingPdfValidationErrorCode):
        super().__init__(code.value)
        self.code = code


class PdfValidator:
    def __init__(self, logger: Logger = NO_LOGGER):
        """Initialize the PdfValidator"""
        self.logger = logger
        self.image_to_page_reject_ratio = 0.8

    def is_vector_page(self, page: pymupdf.Page) -> bool:
        """Check if the page contains vector content"""
        try:
            drawings = page.get_drawings()
            if not drawings:
                return False

            for drawing in drawings:
                if drawing.get("items", []):
                    return True
            return False

        except Exception as e:
            self.logger.error(f"Error checking vector content: {e}")
            return False

    def has_large_images(self, page: pymupdf.Page) -> bool:
        """Check if the page contains large images"""
        try:
            page_area = page.mediabox_size.x * page.mediabox_size.y
            for image in page.get_images():
                image_name = image[7]
                image_bbox = page.get_image_bbox(image_name)
                if image_bbox is None:
                    continue

                image_area = image_bbox.width * image_bbox.height
                if image_area / page_area > self.image_to_page_reject_ratio:
                    return True
            return False

        except Exception as e:
            self.logger.error(f"Error checking image sizes: {e}")
            return False

    def analyze_pdf(self, pdf_doc: pymupdf.Document) -> Tuple[bool, List[int]]:
        """Analyze the PDF document to determine if it contains vector content"""
        if pdf_doc is None or len(pdf_doc) == 0:
            return False, []

        vector_pages = []
        try:
            for i, page in enumerate(pdf_doc):
                if self.is_vector_page(page) and not self.has_large_images(page):
                    vector_pages.append(i)

            return len(vector_pages) > 0, vector_pages

        except Exception as e:
            self.logger.error(f"Error analyzing PDF: {e}")
            return False, []


def validate_basic_pdf_structure(data: bytes) -> bool:
    """Check if data has basic PDF file structure (header and EOF marker)"""
    if not data.startswith(b"%PDF-"):
        return False
    # Check for EOF marker in last 1024 bytes to handle various EOF formats
    if not any(marker in data[-1024:] for marker in [b"%%EOF", b"%EOF"]):
        return False
    return True


def open_and_validate(file_bytes: io.BytesIO, logger: Logger = NO_LOGGER) -> Tuple[pymupdf.Document, bool, List[int]]:
    """Open and validate the PDF document"""
    data = file_bytes.getvalue()
    if not validate_basic_pdf_structure(data):
        logger.error("Invalid PDF structure: Missing PDF header or EOF marker")
        raise DrawingPdfValidationError(DrawingPdfValidationErrorCode.COULD_NOT_OPEN)

    try:
        pdf_doc = pymupdf.Document(stream=io.BytesIO(data), filetype=".pdf")
    except Exception as e:
        logger.error("Could not open pdf file", exc_info=e)
        raise DrawingPdfValidationError(DrawingPdfValidationErrorCode.COULD_NOT_OPEN)

    if len(pdf_doc) == 0:
        logger.error("Could not find any pages")
        raise DrawingPdfValidationError(DrawingPdfValidationErrorCode.COULD_NOT_OPEN)

    validator = PdfValidator(logger)
    has_vectors, vector_pages = validator.analyze_pdf(pdf_doc)

    return pdf_doc, has_vectors, vector_pages


def open_and_validate_from_path(path: Path, logger: Logger = NO_LOGGER) -> Tuple[pymupdf.Document, bool, List[int]]:
    """Open and validate the PDF document from a path"""
    with path.open("rb") as f:
        file_bytes = io.BytesIO(f.read())
    return open_and_validate(file_bytes, logger)
