{"name": "DIN_7715-40:1998-04", "tables": [{"name": "Tabelle 1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["Gren<PERSON>b<PERSON>�<PERSON>"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["Gren<PERSON>b<PERSON>�<PERSON>"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["Gren<PERSON>b<PERSON>�<PERSON>"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["Gren<PERSON>b<PERSON>�<PERSON>"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 18, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["Gren<PERSON>b<PERSON>�<PERSON>"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["Gren<PERSON>b<PERSON>�<PERSON>"]], "tolerance": {"upper": 1.5, "lower": -1.5}}]}]}