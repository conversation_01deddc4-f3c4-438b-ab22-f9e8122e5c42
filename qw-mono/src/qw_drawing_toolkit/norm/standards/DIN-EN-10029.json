{"name": "DIN_EN_10029:2009-05", "tables": [{"name": "Tabelle 1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 4.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 0.8, "lower": -0.4}}, {"lowerValueLimit": {"value": 4.999, "inclusive": false}, "upperValueLimit": {"value": 7.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 1.1, "lower": -0.4}}, {"lowerValueLimit": {"value": 7.999, "inclusive": false}, "upperValueLimit": {"value": 14.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 1.2, "lower": -0.5}}, {"lowerValueLimit": {"value": 14.999, "inclusive": false}, "upperValueLimit": {"value": 24.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 1.3, "lower": -0.6}}, {"lowerValueLimit": {"value": 24.999, "inclusive": false}, "upperValueLimit": {"value": 39.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 1.4, "lower": 0.8}}, {"lowerValueLimit": {"value": 39.999, "inclusive": false}, "upperValueLimit": {"value": 79.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 1.8, "lower": -1}}, {"lowerValueLimit": {"value": 79.999, "inclusive": false}, "upperValueLimit": {"value": 149.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 2.2, "lower": -1}}, {"lowerValueLimit": {"value": 149.999, "inclusive": false}, "upperValueLimit": {"value": 249.999, "inclusive": true}, "rowIdentifiers": [["Klasse A"]], "tolerance": {"upper": 2.4, "lower": -1.2}}, {"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 4.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 0.9, "lower": -0.3}}, {"lowerValueLimit": {"value": 4.999, "inclusive": false}, "upperValueLimit": {"value": 7.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 1.2, "lower": -0.3}}, {"lowerValueLimit": {"value": 7.999, "inclusive": false}, "upperValueLimit": {"value": 14.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 1.4, "lower": -0.3}}, {"lowerValueLimit": {"value": 14.999, "inclusive": false}, "upperValueLimit": {"value": 24.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 1.6, "lower": -0.3}}, {"lowerValueLimit": {"value": 24.999, "inclusive": false}, "upperValueLimit": {"value": 39.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 1.9, "lower": -0.3}}, {"lowerValueLimit": {"value": 39.999, "inclusive": false}, "upperValueLimit": {"value": 79.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 2.5, "lower": -0.3}}, {"lowerValueLimit": {"value": 79.999, "inclusive": false}, "upperValueLimit": {"value": 149.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 2.9, "lower": -0.3}}, {"lowerValueLimit": {"value": 149.999, "inclusive": false}, "upperValueLimit": {"value": 249.999, "inclusive": true}, "rowIdentifiers": [["Klasse B"]], "tolerance": {"upper": 3.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 2.999, "inclusive": true}, "upperValueLimit": {"value": 4.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 4.999, "inclusive": false}, "upperValueLimit": {"value": 7.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 7.999, "inclusive": false}, "upperValueLimit": {"value": 14.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 14.999, "inclusive": false}, "upperValueLimit": {"value": 24.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 24.999, "inclusive": false}, "upperValueLimit": {"value": 39.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 39.999, "inclusive": false}, "upperValueLimit": {"value": 79.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 79.999, "inclusive": false}, "upperValueLimit": {"value": 149.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 1.6, "lower": -1.6}}, {"lowerValueLimit": {"value": 149.999, "inclusive": false}, "upperValueLimit": {"value": 249.999, "inclusive": true}, "rowIdentifiers": [["Klasse D"]], "tolerance": {"upper": 1.8, "lower": -1.8}}]}]}