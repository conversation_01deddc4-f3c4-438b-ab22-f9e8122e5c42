{"name": "DIN_17615-3:1987-01", "tables": [{"name": "Tabelle 1", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 45, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 45, "inclusive": false}, "upperValueLimit": {"value": 60, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 60, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 240, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 240, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["bis 40:1"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 45, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 45, "inclusive": false}, "upperValueLimit": {"value": 60, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 60, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 240, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 240, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 40:1"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 45, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 45, "inclusive": false}, "upperValueLimit": {"value": 60, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 60, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 0.9, "lower": -0.9}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 240, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 240, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["bis 6:1"]], "tolerance": {"upper": 1.4, "lower": -1.4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 45, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 45, "inclusive": false}, "upperValueLimit": {"value": 60, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 60, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 240, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 240, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON> 6:1"]], "tolerance": {"upper": 1.5, "lower": -1.5}}]}, {"name": "Tabelle 2", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["Wandd<PERSON><PERSON> s"]], "tolerance": {"upper": 0.15, "lower": -0.15}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["Wandd<PERSON><PERSON> s"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["Wandd<PERSON><PERSON> s"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["Wandd<PERSON><PERSON> s"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["Wandd<PERSON><PERSON> s"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["Wandd<PERSON><PERSON> s"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 20, "inclusive": false}, "upperValueLimit": {"value": 30, "inclusive": true}, "rowIdentifiers": [["Wandd<PERSON><PERSON> s"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 30, "inclusive": false}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["Wandd<PERSON><PERSON> s"]], "tolerance": {"upper": 0.7, "lower": -0.7}}]}, {"name": "Tabelle 3", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["du<=75"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["du<=75"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["du<=75"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["du<=75"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 9, "inclusive": true}, "rowIdentifiers": [["du<=75"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 9, "inclusive": false}, "upperValueLimit": {"value": 12, "inclusive": true}, "rowIdentifiers": [["du<=75"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 12, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["du<=75"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["du<=130"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["du<=130"]], "tolerance": {"upper": 0.25, "lower": -0.25}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["du<=130"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["du<=130"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 9, "inclusive": true}, "rowIdentifiers": [["du<=130"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 9, "inclusive": false}, "upperValueLimit": {"value": 12, "inclusive": true}, "rowIdentifiers": [["du<=130"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 12, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["du<=130"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["du<=130"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 15, "inclusive": true}, "upperValueLimit": {"value": 2, "inclusive": true}, "rowIdentifiers": [["du<=250"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 2, "inclusive": false}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["du<=250"]], "tolerance": {"upper": 0.35, "lower": -0.35}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["du<=250"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 9, "inclusive": true}, "rowIdentifiers": [["du<=250"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 9, "inclusive": false}, "upperValueLimit": {"value": 12, "inclusive": true}, "rowIdentifiers": [["du<=250"]], "tolerance": {"upper": 0.85, "lower": -0.85}}, {"lowerValueLimit": {"value": 12, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["du<=250"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["du<=250"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 2, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["du<=300"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["du<=300"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 9, "inclusive": true}, "rowIdentifiers": [["du<=300"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 9, "inclusive": false}, "upperValueLimit": {"value": 12, "inclusive": true}, "rowIdentifiers": [["du<=300"]], "tolerance": {"upper": 0.95, "lower": -0.95}}, {"lowerValueLimit": {"value": 12, "inclusive": false}, "upperValueLimit": {"value": 15, "inclusive": true}, "rowIdentifiers": [["du<=300"]], "tolerance": {"upper": 1.1, "lower": -1.1}}, {"lowerValueLimit": {"value": 15, "inclusive": false}, "upperValueLimit": {"value": 20, "inclusive": true}, "rowIdentifiers": [["du<=300"]], "tolerance": {"upper": 1.3, "lower": -1.3}}]}, {"name": "Tabelle 4", "valueType": "runout", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 3, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.5, "lower": -0.5}}, {"lowerValueLimit": {"value": 3, "inclusive": false}, "upperValueLimit": {"value": 6, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.6, "lower": -0.6}}, {"lowerValueLimit": {"value": 6, "inclusive": false}, "upperValueLimit": {"value": 10, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 10, "inclusive": false}, "upperValueLimit": {"value": 18, "inclusive": true}, "rowIdentifiers": [["<PERSON><PERSON><PERSON><PERSON><PERSON>"]], "tolerance": {"upper": 1, "lower": -1}}]}, {"name": "Tabelle 5", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 40, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.2, "lower": -0.2}}, {"lowerValueLimit": {"value": 40, "inclusive": false}, "upperValueLimit": {"value": 60, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.3, "lower": -0.3}}, {"lowerValueLimit": {"value": 60, "inclusive": false}, "upperValueLimit": {"value": 90, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.4, "lower": -0.4}}, {"lowerValueLimit": {"value": 90, "inclusive": false}, "upperValueLimit": {"value": 120, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.45, "lower": -0.45}}, {"lowerValueLimit": {"value": 120, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.55, "lower": -0.55}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 180, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.65, "lower": -0.65}}, {"lowerValueLimit": {"value": 180, "inclusive": false}, "upperValueLimit": {"value": 210, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.7, "lower": -0.7}}, {"lowerValueLimit": {"value": 210, "inclusive": false}, "upperValueLimit": {"value": 240, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.75, "lower": -0.75}}, {"lowerValueLimit": {"value": 240, "inclusive": false}, "upperValueLimit": {"value": 270, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.8, "lower": -0.8}}, {"lowerValueLimit": {"value": 270, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["e"]], "tolerance": {"upper": 0.9, "lower": -0.9}}]}, {"name": "Tabelle 7", "valueType": "linear", "valueUnit": "mm", "toleranceUnit": "mm", "ranges": [{"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["L<=1000"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["L<=1000"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 75, "inclusive": true}, "rowIdentifiers": [["L<=1000"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 75, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L<=1000"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 125, "inclusive": true}, "rowIdentifiers": [["L<=1000"]], "tolerance": {"upper": 1, "lower": -1}}, {"lowerValueLimit": {"value": 125, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["L<=1000"]], "tolerance": {"upper": 1.25, "lower": -1.25}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L<=1000"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["L<=1000"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["L<=2000"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["L<=2000"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 75, "inclusive": true}, "rowIdentifiers": [["L<=2000"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 75, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L<=2000"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 125, "inclusive": true}, "rowIdentifiers": [["L<=2000"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 125, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["L<=2000"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L<=2000"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["L<=2000"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["L<=3000"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["L<=3000"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 75, "inclusive": true}, "rowIdentifiers": [["L<=3000"]], "tolerance": {"upper": 1.2, "lower": -1.2}}, {"lowerValueLimit": {"value": 75, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L<=3000"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 125, "inclusive": true}, "rowIdentifiers": [["L<=3000"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 125, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["L<=3000"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L<=3000"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["L<=3000"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["L<=4000"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["L<=4000"]], "tolerance": {"upper": 1.8, "lower": -1.8}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 75, "inclusive": true}, "rowIdentifiers": [["L<=4000"]], "tolerance": {"upper": 1.5, "lower": -1.5}}, {"lowerValueLimit": {"value": 75, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L<=4000"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 125, "inclusive": true}, "rowIdentifiers": [["L<=4000"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 125, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["L<=4000"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L<=4000"]], "tolerance": {"upper": 2.6, "lower": -2.6}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["L<=4000"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["L<=5000"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["L<=5000"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 75, "inclusive": true}, "rowIdentifiers": [["L<=5000"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 75, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L<=5000"]], "tolerance": {"upper": 2.2, "lower": -2.2}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 125, "inclusive": true}, "rowIdentifiers": [["L<=5000"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 125, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["L<=5000"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L<=5000"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["L<=5000"]], "tolerance": {"upper": 4, "lower": -4}}, {"lowerValueLimit": {"value": 0, "inclusive": true}, "upperValueLimit": {"value": 25, "inclusive": true}, "rowIdentifiers": [["L<=6000"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 25, "inclusive": false}, "upperValueLimit": {"value": 50, "inclusive": true}, "rowIdentifiers": [["L<=6000"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 50, "inclusive": false}, "upperValueLimit": {"value": 75, "inclusive": true}, "rowIdentifiers": [["L<=6000"]], "tolerance": {"upper": 2, "lower": -2}}, {"lowerValueLimit": {"value": 75, "inclusive": false}, "upperValueLimit": {"value": 100, "inclusive": true}, "rowIdentifiers": [["L<=6000"]], "tolerance": {"upper": 2.5, "lower": -2.5}}, {"lowerValueLimit": {"value": 100, "inclusive": false}, "upperValueLimit": {"value": 125, "inclusive": true}, "rowIdentifiers": [["L<=6000"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 125, "inclusive": false}, "upperValueLimit": {"value": 150, "inclusive": true}, "rowIdentifiers": [["L<=6000"]], "tolerance": {"upper": 3, "lower": -3}}, {"lowerValueLimit": {"value": 150, "inclusive": false}, "upperValueLimit": {"value": 200, "inclusive": true}, "rowIdentifiers": [["L<=6000"]], "tolerance": {"upper": 3.5, "lower": -3.5}}, {"lowerValueLimit": {"value": 200, "inclusive": false}, "upperValueLimit": {"value": 300, "inclusive": true}, "rowIdentifiers": [["L<=6000"]], "tolerance": {"upper": 4.5, "lower": -4.5}}]}]}